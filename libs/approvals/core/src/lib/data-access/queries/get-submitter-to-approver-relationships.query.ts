import { QueryObject, QueryParams } from '@clickup-legacy/utils/interfaces/QueryObject';

import { ApprovalType } from '../../utils/constants';

export function getSubmitterToApproverRelationshipsQuery(
    workspaceId: number,
    approvalType: ApprovalType,
    submitterFilter?: number[],
    approverFilter?: number
): QueryObject {
    const params: QueryParams = [workspaceId, approvalType];
    let query = `
        SELECT submitter_to_approver.submitter, submitter_to_approver.approver
        FROM task_mgmt.submitter_to_approver
        WHERE workspace_id = $1 AND type = $2`;

    if (submitterFilter != null) {
        query += ` AND submitter_to_approver.submitter = ANY($${params.push(submitterFilter)})`;
    }

    if (approverFilter != null) {
        query += ` AND submitter_to_approver.approver = $${params.push(approverFilter)}`;
    }

    return {
        query,
        params,
    };
}
