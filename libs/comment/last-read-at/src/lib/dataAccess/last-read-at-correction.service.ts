import { Injectable, Optional } from '@nestjs/common';
import knex, { Knex } from 'knex';
import { keyBy } from 'lodash';
import fs from 'node:fs';

import { DD_SERVICE_NAME, tracer } from '@clickup/shared/utils-tracing';
import type { SimpleClient } from '@clickup/utils/db-types';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';

import { CHAT_LAST_READ_AT_CORRECTION_CONFIG } from '../feature-flag';
import { LastReadAtRepo, LastReadAtRepoOptions } from './last-read-at.repo';
import { LastReadAtDbEntity } from './last-read-at-db.entity';

enum MetricNames {
    LAST_READ_AT_DIFFERENCE_COUNT = 'chat.last_read_at.correction.difference',
    LAST_READ_AT_CALCULATED_COUNT = 'chat.last_read_at.correction.calculated',
    LAST_READ_AT_TO_CORRECT_COUNT = 'chat.last_read_at.correction.to_correct',
}

const CONFIG_UPDATE_FREQUENCY_MS = 60 * 1000; // 60 seconds

type LastReadAtCorrectionConfig = {
    samplingRate: number; // 0-1 float
    usersToLog: number[];
    workspacesToLog: number[];
    usersToCorrect: number[];
    workspacesToCorrect: number[];
    shouldLogSampled: boolean;
    shouldCorrectSampled: boolean;
};

const DEFAULT_CONFIG: LastReadAtCorrectionConfig = {
    samplingRate: 0,
    usersToLog: [],
    workspacesToLog: [],
    usersToCorrect: [],
    workspacesToCorrect: [],
    shouldLogSampled: false,
    shouldCorrectSampled: false,
};

@Injectable()
export class LastReadAtCorrectionService {
    private readonly knex: Knex;

    private config: LastReadAtCorrectionConfig | undefined;

    private _configFetchInterval: NodeJS.Timeout | undefined;

    constructor(
        private readonly repo: LastReadAtRepo,
        private readonly featureFlagService: FeatureFlagService,
        private readonly logger: FormatLogger,
        private readonly metricsService: MetricsService,
        @Optional() config?: LastReadAtCorrectionConfig
    ) {
        this.knex = knex({ client: 'pg', wrapIdentifier: value => value });
        if (config) {
            this.config = config;
        } else {
            this.startConfigLoop();
        }
    }

    private startConfigLoop() {
        if (!this._configFetchInterval) {
            this.config = this.getCorrectionConfig();
            this._configFetchInterval = setInterval(() => {
                this.config = this.getCorrectionConfig();
            }, CONFIG_UPDATE_FREQUENCY_MS);
        }
    }

    private getCorrectionConfig() {
        return (
            this.featureFlagService.getObject<LastReadAtCorrectionConfig>({
                flag: CHAT_LAST_READ_AT_CORRECTION_CONFIG,
            }) ?? DEFAULT_CONFIG
        );
    }

    private async getParentsWithSubtypeAndDate(
        rowsToUpdate: LastReadAtDbEntity[],
        options: LastReadAtRepoOptions
    ): Promise<{ id: string; type: number; viewType?: number; date?: number }[]> {
        const { parents } = options;

        const viewTypesByParentId = rowsToUpdate.reduce<Record<string, number>>((acc, row) => {
            if (row.parent_subtype) {
                acc[row.parent_id] = row.parent_subtype;
            }
            return acc;
        }, {});

        const dateByParentId = rowsToUpdate.reduce<Record<string, number>>((acc, row) => {
            if (row.date) {
                acc[row.parent_id] = +row.date;
            }
            return acc;
        }, {});

        return parents
            .map(p => ({
                id: p.id,
                type: p.type,
                viewType: viewTypesByParentId[p.id],
                date: dateByParentId[p.id],
            }))
            .filter(p => !!p.viewType);
    }

    /**
     * Compares row values with the expected calculated values
     */
    private compareRowValues(
        clraRows: LastReadAtDbEntity[],
        calculatedValues: LastReadAtDbEntity[],
        fieldsToCompare: (keyof LastReadAtDbEntity)[] = ['has_unread', 'badge_count', 'thread_count', 'mention_count']
    ): {
        clraRow?: LastReadAtDbEntity;
        calculatedRow: LastReadAtDbEntity;
        differences: Record<string, { actual: unknown; expected: unknown }>;
    }[] {
        const clraByKey = keyBy(clraRows, row => `${row.parent_id}:${row.parent_type}`);
        const calculatedByKey = keyBy(calculatedValues, calc => `${calc.parent_id}:${calc.parent_type}`);

        const differences = [];

        for (const key of Object.keys(clraByKey)) {
            const clraRow = clraByKey[key];
            const calculatedRow = calculatedByKey[key];

            if (!calculatedRow) continue;

            const diff: Record<string, { actual: unknown; expected: unknown }> = {};
            let hasDifference = false;

            for (const field of fieldsToCompare) {
                const rowValue = clraRow?.[field];
                const calcValue = calculatedRow?.[field];

                // First convert both values to numbers if possible, then compare
                const numRowValue = rowValue !== undefined && !Number.isNaN(+rowValue) ? +rowValue : rowValue;
                const numCalcValue = calcValue !== undefined && !Number.isNaN(+calcValue) ? +calcValue : calcValue;

                // Now compare the normalized values
                const isValueMismatch = numRowValue !== numCalcValue;

                const areBothValues100OrMore =
                    numRowValue && numCalcValue && +numRowValue >= 100 && +numCalcValue >= 100;

                if (isValueMismatch && !areBothValues100OrMore) {
                    diff[field] = {
                        actual: +(rowValue ?? 0),
                        expected: +(calcValue ?? 0),
                    };
                    hasDifference = true;
                }
            }

            if (hasDifference) {
                differences.push({
                    clraRow,
                    calculatedRow,
                    differences: diff,
                });
            }
        }

        return differences.map(diff => ({
            clraRow: diff.clraRow,
            calculatedRow: diff.calculatedRow,
            differences: diff.differences,
        }));
    }

    public shouldSample(options: { userId: number; workspaceId: number }): boolean {
        if (!this.config) return false;

        return (
            (this.config.samplingRate > 0 && Math.random() < this.config.samplingRate) ||
            this.config.usersToLog.includes(options.userId) ||
            this.config.workspacesToLog.includes(options.workspaceId) ||
            this.config.workspacesToLog.includes(-1) || // Control value to log all workspaces
            this.config.usersToCorrect.includes(options.userId) ||
            this.config.workspacesToCorrect.includes(options.workspaceId) ||
            this.config.workspacesToCorrect.includes(-1) // Control value to correct all workspaces
        );
    }

    private shouldCorrect(): boolean {
        if (!this.config) return false;

        return this.config.shouldCorrectSampled;
    }

    private shouldLog(): boolean {
        if (!this.config) return false;

        return this.config.shouldLogSampled;
    }

    /**
     * Gets the correct badge counts for parents and compares them with current values
     */
    @tracer.Decorator('lastReadAtCorrectionService.getCorrectBadgeCountsForParents', { service: DD_SERVICE_NAME })
    public async getCorrectBadgeCountsForParents(
        rowsToCheck: LastReadAtDbEntity[],
        options: LastReadAtRepoOptions & { bdrSimpleClient: SimpleClient; writeToFile?: boolean }
    ): Promise<{
        rowsToCorrect: LastReadAtDbEntity[];
        calculatedValues: LastReadAtDbEntity[];
    }> {
        if (rowsToCheck.length === 0) {
            return {
                rowsToCorrect: [],
                calculatedValues: [],
            };
        }

        const parentsWithSubtypeAndDate = await this.getParentsWithSubtypeAndDate(rowsToCheck, options);

        const groupIds = await this.repo.getAllUserGroupIdsForUser({
            userId: options.userId,
            workspaceId: options.workspaceId,
            bdrSimpleClient: options.bdrSimpleClient,
        });

        const calculatedValues = [];

        for (const parent of parentsWithSubtypeAndDate) {
            const updatedDms = await this.repo.bulkUpdateDms({
                ...options,
                parents: [parent],
                checkOnly: true,
                date: parent.date ?? 0,
            });
            if (updatedDms?.rows?.length > 0) {
                calculatedValues.push(...updatedDms.rows);
            }

            const updatedNonDms = await this.repo.bulkUpdateNonDms({
                ...options,
                parents: [parent],
                groupIds,
                checkOnly: true,
                date: parent.date ?? 0,
            });
            if (updatedNonDms?.rows?.length > 0) {
                calculatedValues.push(...updatedNonDms.rows);
            }
        }

        const differences = this.compareRowValues(rowsToCheck, calculatedValues);

        if (differences.length > 0 && options.writeToFile) {
            const currentDay = new Date().toISOString().split('T')[0];
            fs.mkdirSync(`./clra-diffs/${currentDay}`, { recursive: true });

            fs.writeFileSync(
                `./clra-diffs/${currentDay}/${Date.now()}-${options.userId}-${options.workspaceId}.json`,
                JSON.stringify(differences, null, 2)
            );
        }

        const parentsWithIncorrectThreads = differences.filter(diff => diff.differences?.thread_count);

        if (parentsWithIncorrectThreads.length > 0) {
            const threadClraRowsToCheck = await options.simpleClient.queryAsync<LastReadAtDbEntity>(
                `
                SELECT DISTINCT cla.parent_id FROM task_mgmt.comments_last_read_at cla JOIN task_mgmt.comments c ON cla.parent_id = c.parent
                WHERE c.root_parent_id = ANY($3)
                AND cla.parent_type = 2
                AND cla.workspace_id = $1
                AND cla.user_id = $2
                AND cla.has_unread
                AND c.workspace_id = $1
            `,
                [options.workspaceId, options.userId, parentsWithIncorrectThreads.map(p => p.calculatedRow.parent_id)]
            );

            const { rowsToCorrect: threadRowsToCorrect, calculatedValues: threadCalculatedValues } =
                await this.getCorrectBadgeCountsForParents(threadClraRowsToCheck.rows, options);

            threadRowsToCorrect.forEach(row => {
                rowsToCorrect.push(row);
            });
            threadCalculatedValues.forEach(row => {
                calculatedValues.push(row);
            });
        }

        this.metricsService.client.distribution(MetricNames.LAST_READ_AT_DIFFERENCE_COUNT, differences.length);
        this.metricsService.client.distribution(MetricNames.LAST_READ_AT_CALCULATED_COUNT, calculatedValues.length);

        if (differences.length > 0 && this.shouldLog()) {
            this.logger.warn('Incorrect badge counts found!', 'chat.last-read-at-correction.service', {
                differences,
            });
        }

        const shouldCorrect = this.shouldCorrect();

        const rowsToCorrect = shouldCorrect ? differences.map(diff => diff.clraRow ?? diff.calculatedRow) : [];
        this.metricsService.client.distribution(MetricNames.LAST_READ_AT_TO_CORRECT_COUNT, rowsToCorrect.length);

        return {
            rowsToCorrect,
            calculatedValues,
        };
    }
}
