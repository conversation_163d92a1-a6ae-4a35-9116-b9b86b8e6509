import { buildPaginationInfo, CursorHandler } from '@clickup/utils/cursor';

import { CommentCursor } from './comment-cursor';

describe('CommentCursor', () => {
    const items = [
        { id: '3', date: '1705709447001' },
        { id: '4', date: '1705709447002' },
        { id: '5', date: '1705709447003' },
        { id: '6', date: '1705709447004' },
    ];

    it('forComment', () => {
        expect(CommentCursor.forComment(items[0])).toEqual(
            'eyJpZCI6IjMiLCJkYXRlIjoiMTcwNTcwOTQ0NzAwMSIsImluY2x1c2l2ZSI6dHJ1ZX0='
        );
    });

    it('getNextCursorString should return the correct cursor string', () => {
        const pageInfo = buildPaginationInfo({
            rows: items,
            limit: 3,
            reverse: false,
            fromExistingCursor: false,
        });
        const cursor = CommentCursor.getNextCursorString(pageInfo);
        const decoded = CursorHandler.decode(cursor);
        expect(decoded).toEqual({
            id: '5',
            date: '1705709447003',
            limit: 3,
            inclusive: false,
        });
    });

    it('getPrevCursorString should return the correct cursor string', () => {
        const pageInfo = buildPaginationInfo({
            rows: items,
            limit: 3,
            reverse: false,
            fromExistingCursor: true,
        });
        const cursor = CommentCursor.getPrevCursorString(pageInfo);
        const decoded = CursorHandler.decode(cursor);
        expect(decoded).toEqual({
            id: '3',
            date: '1705709447001',
            limit: 3,
            reverse: true,
            inclusive: false,
        });
    });

    describe('getCursor', () => {
        it('should return the correct cursor', () => {
            const spy = jest.spyOn(CommentCursor, 'getCursor');
            const getCursorImplementation = spy.getMockImplementation();
            expect(
                getCursorImplementation({
                    comment: items[0],
                })
            ).toEqual({
                id: '3',
                date: '1705709447001',
                inclusive: false,
            });
        });

        it('should return the correct cursor when reverse enabled', () => {
            const spy = jest.spyOn(CommentCursor, 'getCursor');
            const getCursorImplementation = spy.getMockImplementation();
            expect(
                getCursorImplementation({
                    comment: items[0],
                    reverse: true,
                })
            ).toEqual({
                id: '3',
                date: '1705709447001',
                inclusive: false,
                limit: undefined,
                reverse: true,
            });
        });

        it('should return the correct cursor when limit enabled', () => {
            const spy = jest.spyOn(CommentCursor, 'getCursor');
            const getCursorImplementation = spy.getMockImplementation();
            expect(
                getCursorImplementation({
                    comment: items[0],
                    reverse: true,
                    limit: 10,
                })
            ).toEqual({
                id: '3',
                date: '1705709447001',
                inclusive: false,
                limit: 10,
                reverse: true,
            });
        });
    });
});
