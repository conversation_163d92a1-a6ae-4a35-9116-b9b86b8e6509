export class ChatLastReadAtUpdatedObjectId {
    readonly userId: number;

    readonly objectId: string;

    readonly objectType: number;

    constructor(userId: number, objectId: string, objectType: number) {
        this.userId = userId;
        this.objectId = objectId;
        this.objectType = objectType;
    }

    static fromString(id: string): ChatLastReadAtUpdatedObjectId {
        const [userId, ...idTypeAndSubtypeParts] = id.split('-');
        const objectType = +(idTypeAndSubtypeParts.pop() || 0);
        const objectId = idTypeAndSubtypeParts.join('-');

        if (userId === undefined || objectId === undefined || objectType === undefined) {
            throw new Error(`Invalid ChatLastReadAtUpdatedObjectId: ${id}`);
        }

        return new ChatLastReadAtUpdatedObjectId(+userId, objectId, objectType);
    }

    public toString(): string {
        return `${this.userId}-${this.objectId}-${this.objectType}`;
    }
}
