/* tslint:disable */
/* eslint-disable */
/**
 * Comment Service
 * Comment Service API
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface UserEmailSettings
 */
export interface UserEmailSettings {
    /**
     * The user\'s email address verification status.
     * @type {boolean}
     * @memberof UserEmailSettings
     */
    'email_is_verified': boolean;
    /**
     * True if the user\'s email address has bounced.
     * @type {boolean}
     * @memberof UserEmailSettings
     */
    'email_is_bounced'?: boolean;
}

