/* tslint:disable */
/* eslint-disable */
/**
 * Comment Service
 * Comment Service API
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface UserProfileStatusDto
 */
export interface UserProfileStatusDto {
    /**
     * The users status text.
     * @type {string}
     * @memberof UserProfileStatusDto
     */
    'status_text'?: string;
    /**
     * The users status icon.
     * @type {string}
     * @memberof UserProfileStatusDto
     */
    'status_icon'?: string;
    /**
     * The users status valid until.
     * @type {string}
     * @memberof UserProfileStatusDto
     */
    'valid_until'?: string;
    /**
     * The users status out of office.
     * @type {boolean}
     * @memberof UserProfileStatusDto
     */
    'out_of_office'?: boolean;
    /**
     * The users status pause notifications until.
     * @type {string}
     * @memberof UserProfileStatusDto
     */
    'pause_notifications_until'?: string;
}

