import { type LoggerService, Inject, Injectable } from '@nestjs/common';

import { StatsDClient } from '@clickup/shared/utils-metrics';
import { DD_SERVICE_NAME, tracer } from '@clickup/shared/utils-tracing';
import { WorkspaceMembershipService } from '@clickup/user/core';
import { MetricsService } from '@clickup/utils/metrics';

import { IncompleteProfileError, ProfileTaskNameNotFoundError, WorkspaceUserProfileErrorCodes } from '../exceptions';
import { WorkspaceUserProfileFieldValueSyncService } from '../fields/workspace-user-profile-field-value-sync-service';
import { WorkspaceUserProfileHierarchyLoadService } from '../hierarchy/workspace-user-profile-hierarchy-load-service';
import { LockService } from '../lock-service';
import {
    ProcessingStatus,
    trackProfileSyncOnLoadCount,
    trackProfileSyncOnLoadDuration,
} from '../metrics/workspace-user-profile-metrics';
import { WorkspaceUserProfile } from '../models/workspace-user-profile';
import { WorkspaceUserProfileAttributes } from '../models/workspace-user-profile-attributes';
import { WorkspaceUserProfileHierarchy } from '../models/workspace-user-profile-hierarchy';
import { WorkspaceUserProfileClient } from './workspace-user-profile-client';
import { WorkspaceUserProfileRepository } from './workspace-user-profile-repository';

/**
 * Workspace user profiles are stored as tasks in a special space.
 * This service provides methods for loading such profiles.
 * Task creation is done under the hood, if needed.
 */
@Injectable()
export class WorkspaceUserProfileLoadService {
    private readonly metricsClient: StatsDClient;

    constructor(
        private readonly workspaceUserProfileHierarchyLoadService: WorkspaceUserProfileHierarchyLoadService,
        private readonly workspaceUserProfileRepository: WorkspaceUserProfileRepository,
        private readonly workspaceUserProfileClient: WorkspaceUserProfileClient,
        private readonly workspaceUserProfileFieldValueSyncService: WorkspaceUserProfileFieldValueSyncService,
        private readonly lockService: LockService,
        private readonly workspaceMembershipService: WorkspaceMembershipService,
        @Inject('logger') private readonly logger: LoggerService,
        metricsService: MetricsService
    ) {
        this.metricsClient = metricsService.client;
    }

    @tracer.Decorator('workspaceUserProfile.loadService.loadWorkspaceUserProfile', {
        service: DD_SERVICE_NAME,
    })
    async loadWorkspaceUserProfile(
        params: {
            workspaceId: number;
            userId: number;
            allowMissingWorkspaceMembership?: boolean;
        },
        options?: { hierarchyInfo?: WorkspaceUserProfileHierarchy }
    ): Promise<{
        profile: WorkspaceUserProfile | null;
        newTaskCreated: boolean;
        hierarchyInfo: WorkspaceUserProfileHierarchy;
    }> {
        const hierarchyInfo =
            options?.hierarchyInfo ??
            (await this.workspaceUserProfileHierarchyLoadService.loadWorkspaceUserProfileHierarchy(params.workspaceId))
                .hierarchy;

        let profile = await this.workspaceUserProfileRepository.getWorkspaceUserProfile({
            userId: params.userId,
            hierarchyInfo,
        });
        let newTaskCreated = false;

        if (profile?.isComplete()) {
            profile = await this.ensureConsistentTaskId(profile);
            return { profile, newTaskCreated: false, hierarchyInfo };
        }

        const startTime = Date.now();
        let processingStatus: ProcessingStatus = 'success';

        try {
            const syncResult = await this.syncWorkspaceUserProfile({
                ...params,
                hierarchyInfo,
                allowMissingWorkspaceMembership: params.allowMissingWorkspaceMembership ?? false,
            });
            ({ newTaskCreated, profile } = syncResult);
            const { isWorkspaceMember } = syncResult;

            if (profile === null) {
                processingStatus = 'not_found';
                return { profile, newTaskCreated: false, hierarchyInfo };
            }

            if (!profile?.isComplete()) {
                throw new IncompleteProfileError({
                    workspaceId: params.workspaceId,
                    userId: params.userId,
                    retriable: false,
                });
            }

            if (newTaskCreated && isWorkspaceMember === true) {
                this.syncInitialFieldValues({ profile, hierarchyInfo }).catch((err: unknown) => {
                    this.logger.error({
                        message:
                            'WorkspaceUserProfileLoadService#loadWorkspaceUserProfile: failed to sync custom field values',
                        ECODE: WorkspaceUserProfileErrorCodes.FailedToSyncCustomFieldValuesAfterTaskCreation,
                        err,
                        workspaceId: params.workspaceId,
                        listId: hierarchyInfo.listId,
                        profileId: profile.id,
                    });
                });
            }

            return { profile, newTaskCreated, hierarchyInfo };
        } catch (err) {
            processingStatus = 'error';
            throw err;
        } finally {
            trackProfileSyncOnLoadCount(this.metricsClient, { status: processingStatus });
            trackProfileSyncOnLoadDuration(this.metricsClient, { duration: Date.now() - startTime });
        }
    }

    @tracer.Decorator('workspaceUserProfile.loadService.ensureConsistentTaskId', {
        service: DD_SERVICE_NAME,
    })
    private async ensureConsistentTaskId(profile: WorkspaceUserProfile): Promise<WorkspaceUserProfile> {
        if (profile.hasTaskIdMismatch()) {
            const updatedProfile = await this.workspaceUserProfileRepository.updateWorkspaceUserProfileTaskId({
                profile,
                taskId: profile.taskId,
            });

            return updatedProfile;
        }

        return profile;
    }

    /**
     * Synchronizes the workspace user profile, which means creating the corresponding task if necessary.
     * The execution is put under a lock to prevent race conditions.
     */
    @tracer.Decorator('workspaceUserProfile.loadService.syncWorkspaceUserProfile', {
        service: DD_SERVICE_NAME,
    })
    private async syncWorkspaceUserProfile(params: {
        workspaceId: number;
        userId: number;
        hierarchyInfo: WorkspaceUserProfileHierarchy;
        allowMissingWorkspaceMembership: boolean;
    }): Promise<{ profile: WorkspaceUserProfile; newTaskCreated: boolean; isWorkspaceMember: boolean | 'unknown' }> {
        return this.lockService.withLock({
            key: `wup:${params.workspaceId}:${params.userId}`,
            duration: 5000,
            settings: {
                retryCount: 20,
                retryDelay: 500,
                retryJitter: 100,
                automaticExtensionThreshold: 1000,
            },
            onLock: async signal => {
                let profile = await this.workspaceUserProfileRepository.getWorkspaceUserProfile(
                    {
                        userId: params.userId,
                        hierarchyInfo: params.hierarchyInfo,
                    },
                    {
                        useReplica: false,
                    }
                );

                // The profile might have been created while we were waiting for the lock.
                if (profile?.isComplete()) {
                    return { profile, newTaskCreated: false, isWorkspaceMember: 'unknown' };
                }

                const isWorkspaceMember = Boolean(
                    await this.workspaceMembershipService.getWorkspaceMembership(params.userId, params.workspaceId, {
                        useMaster: true,
                    })
                );

                if (!isWorkspaceMember && !profile && !params.allowMissingWorkspaceMembership) {
                    return { profile: null, newTaskCreated: false, isWorkspaceMember: false };
                }

                const { userFound, taskName } = await this.workspaceUserProfileRepository.getTaskNameForUser(
                    params.userId,
                    { useReplica: false }
                );

                if (!taskName) {
                    if (userFound) {
                        throw new ProfileTaskNameNotFoundError({
                            workspaceId: params.workspaceId,
                            userId: params.userId,
                            retriable: false,
                        });
                    } else {
                        this.logger.log({
                            message: 'WorkspaceUserProfileLoadService#syncWorkspaceUserProfile: user not found',
                            workspaceId: params.workspaceId,
                            userId: params.userId,
                        });

                        return { profile, newTaskCreated: false, isWorkspaceMember };
                    }
                }

                // If something has gone wrong and we've lost the lock,
                // throw an error so that we don't create a duplicate.
                if (signal.aborted) {
                    throw signal.error;
                }

                const shouldProfileBeDeleted = !isWorkspaceMember;

                if (!profile || profile.deleted !== shouldProfileBeDeleted) {
                    profile = await this.workspaceUserProfileRepository.upsertWorkspaceUserProfile({
                        profileId: profile?.id,
                        workspaceId: params.workspaceId,
                        userId: params.userId,
                        isWorkspaceMember,
                    });
                }

                // If something has gone wrong and we've lost the lock,
                // throw an error so that we don't create a duplicate.
                if (signal.aborted) {
                    throw signal.error;
                }

                const { taskId } = await this.workspaceUserProfileClient.createWorkspaceUserProfileTask({
                    listId: params.hierarchyInfo.listId,
                    taskName,
                    archived: !isWorkspaceMember,
                });

                const updatedProfile = await this.workspaceUserProfileRepository.updateWorkspaceUserProfileTaskId({
                    profile,
                    taskId,
                });

                return { profile: updatedProfile, newTaskCreated: true, isWorkspaceMember };
            },
        });
    }

    @tracer.Decorator('workspaceUserProfile.loadService.syncInitialFieldValues', {
        service: DD_SERVICE_NAME,
    })
    private async syncInitialFieldValues(params: {
        profile: WorkspaceUserProfile;
        hierarchyInfo: WorkspaceUserProfileHierarchy;
    }): Promise<void> {
        await this.workspaceUserProfileFieldValueSyncService.syncWorkspaceUserProfileFieldValues({
            profile: params.profile,
            hierarchyInfo: params.hierarchyInfo,
            attributeValues: {
                // Title and Manager are resolved dynamically by fetching the values from the DB
                // We just need to pass null here so that the sync function tries to synchronize them
                [WorkspaceUserProfileAttributes.Title]: null,
                [WorkspaceUserProfileAttributes.Manager]: null,
            },
            skipSyncOnEmptyValues: true,
        });
    }
}
