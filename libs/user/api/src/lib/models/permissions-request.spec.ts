import { validate } from 'class-validator';

import { buildValidationException } from '../utils/validation';
import { PermissionsBulkObjectRequest, PermissionsBulkRequest } from './permissions-request';
import { ValidationException } from './validation-exception';

describe('PermissionsRequest', () => {
    it('should validate a valid request', async () => {
        const request = new PermissionsBulkRequest();
        request.objects = [new PermissionsBulkObjectRequest()];
        const errors = await validate(request);
        expect(errors).toHaveLength(0);
    });

    it('should validate a valid request with permissions', async () => {
        const request = new PermissionsBulkRequest();
        request.objects = [new PermissionsBulkObjectRequest()];
        request.permissions = ['perm-1', 'perm-2'];
        const errors = await validate(request);
        expect(errors).toHaveLength(0);
    });

    it('should validate a valid request with permissions and options', async () => {
        const request = new PermissionsBulkRequest();
        request.objects = [new PermissionsBulkObjectRequest()];
        request.permissions = ['perm-1', 'perm-2'];
        request.options = {
            include_access_requests: true,
        };
        const errors = await validate(request);
        expect(errors).toHaveLength(0);
    });

    it('should validate an empty request', async () => {
        const request = new PermissionsBulkRequest();
        const errors = await validate(request);
        expect(errors).toHaveLength(1);

        const transformed = buildValidationException(errors[0]!);
        expect(transformed).toBeInstanceOf(ValidationException);
        expect(transformed.getResponse()).toEqual({
            ECODE: 'AUTHZ_PERM_REQ_003',
            message: 'Objects must be at most 100 entries long',
            property: 'objects',
            value: undefined,
        });
    });

    it('should validate an empty objects array', async () => {
        const request = new PermissionsBulkRequest();
        request.objects = [];
        const errors = await validate(request);
        expect(errors).toHaveLength(1);

        const transformed = buildValidationException(errors[0]!);
        expect(transformed).toBeInstanceOf(ValidationException);
        expect(transformed.getResponse()).toEqual({
            ECODE: 'AUTHZ_PERM_REQ_002',
            message: 'Objects must be at least 1 entry long',
            property: 'objects',
            value: [],
        });
    });

    it('should validate a large objects array', async () => {
        const request = new PermissionsBulkRequest();
        request.objects = Array.from({ length: 101 }, () => new PermissionsBulkObjectRequest());
        const errors = await validate(request);
        expect(errors).toHaveLength(1);

        const transformed = buildValidationException(errors[0]!);
        expect(transformed).toBeInstanceOf(ValidationException);
        expect(transformed.getResponse()).toEqual({
            ECODE: 'AUTHZ_PERM_REQ_003',
            message: 'Objects must be at most 100 entries long',
            property: 'objects',
            value: request.objects,
        });
    });

    it('should validate a large permissions array', async () => {
        const request = new PermissionsBulkRequest();
        request.objects = [new PermissionsBulkObjectRequest()];
        request.permissions = Array.from({ length: 101 }, () => `perm-${Math.random()}`);
        const errors = await validate(request);
        expect(errors).toHaveLength(1);

        const transformed = buildValidationException(errors[0]!);
        expect(transformed).toBeInstanceOf(ValidationException);
        expect(transformed.getResponse()).toEqual({
            ECODE: 'AUTHZ_PERM_REQ_004',
            message: 'Permissions must be at most 100 entries long',
            property: 'permissions',
            value: request.permissions,
        });
    });
});
