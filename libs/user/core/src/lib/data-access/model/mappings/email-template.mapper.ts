import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';

import { EmailTemplate } from '../../../model/email-template';
import { EmailTemplateDBSchema } from '../email-template';

export class EmailTemplateMapper {
    public createDbToDomainMap(profileMapper: Mapper) {
        createMap(
            profileMapper,
            EmailTemplateDBSchema,
            EmailTemplate,
            forMember(
                destination => destination.emailHtml,
                mapFrom(source => source.email_text)
            )
        );
    }
}
