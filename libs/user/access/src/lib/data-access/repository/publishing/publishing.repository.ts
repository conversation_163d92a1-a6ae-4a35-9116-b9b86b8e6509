import { Injectable } from '@nestjs/common';
import { ObjectKey, ObjectType, ObjectVersionUpdateRequest, OperationType } from '@time-loop/ovm-object-version';

import { DBClient, getOnePrr } from '@clickup/utils/db';
import type { QueryObject } from '@clickup/utils/db-types';
import { FormatLogger } from '@clickup/utils/logging';

import { RepositoryFailedToUpdateResourcePublishing } from '../../../model/exceptions';
import { PublishingInfo } from '../../../model/publishing';
import { PublishingErrors } from '../../../utils/constants';
import { DataAccessAutomapperProfile } from '../../model/automapper';
import { PublishingDBSchema } from '../../model/publishing';

@Injectable()
export abstract class PublishingRespository {
    public abstract get objectType(): ObjectType;

    constructor(
        protected readonly mapperProfile: DataAccessAutomapperProfile,
        protected readonly db: DBClient,
        protected readonly logger: FormatLogger
    ) {}

    public async getResourcePublishing(objectKey: ObjectKey): Promise<PublishingInfo> {
        return getOnePrr<PublishingDBSchema, PublishingInfo>({
            db: this.db,
            query: this.fetchResourcePublishing(objectKey),
            mapRow: r => this.mapperProfile.mapper.map(r, PublishingDBSchema, PublishingInfo),
            options: { convertNumbers: true },
        });
    }

    public async patchPublishingResource(
        objectKey: ObjectKey,
        callerUserId: number,
        patchRequest: Partial<PublishingInfo>,
        beforePublishing: PublishingInfo
    ): Promise<void> {
        try {
            const versionUpdates: ObjectVersionUpdateRequest[] = [];

            await this.db.writeAsyncFunction(
                async client => {
                    // version update
                    const versionUpdate: ObjectVersionUpdateRequest = {
                        ...objectKey,
                        operation: OperationType.UPDATE,
                    };

                    // Update publishing
                    const updateQueryObject = this.updateResourcePublishing(objectKey.object_id, patchRequest);
                    await client.queryAsync(updateQueryObject.query, updateQueryObject.params);

                    // insert history items
                    const historyQueryObject = this.insertResourceHistory(
                        callerUserId,
                        objectKey,
                        patchRequest,
                        beforePublishing
                    );
                    if (historyQueryObject != null) {
                        const histIds = await client.queryAsync<{ history_id: string; field: string }>(
                            historyQueryObject.query,
                            historyQueryObject.params
                        );

                        versionUpdate.data = {
                            changes: histIds.rows,
                        };
                    }

                    versionUpdates.push(versionUpdate);
                },
                { versionUpdates }
            );
        } catch (error) {
            this.logger.error({
                msg: 'Failed to update publishing for resource',
                ECODE: PublishingErrors.RepositoryFailedToUpdateResourcePublishingInfo,
                error,
                objectKey,
                userid: callerUserId,
            });
            throw new RepositoryFailedToUpdateResourcePublishing(objectKey);
        }
    }

    protected abstract updateResourcePublishing(resourceId: string, patchRequest: Partial<PublishingInfo>): QueryObject;

    protected abstract fetchResourcePublishing(objectKey: ObjectKey): QueryObject;

    protected abstract insertResourceHistory(
        callerUserId: number,
        objectKey: ObjectKey,
        patchRequest: Partial<PublishingInfo>,
        beforePublishing: PublishingInfo
    ): QueryObject;
}
