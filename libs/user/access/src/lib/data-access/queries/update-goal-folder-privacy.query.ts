import type { QueryObject } from '@clickup/utils/db-types';

export function updateGoalFolderPrivacy(goalFolderId: string, goalFolderPrivacy: boolean): QueryObject {
    const UPDATE_GOAL_FOLDER_PRIVACY_QUERY = `
        UPDATE task_mgmt.goal_folders 
        SET private = $2 
        WHERE 
            id = $1
    `;

    return {
        query: UPDATE_GOAL_FOLDER_PRIVACY_QUERY,
        params: [goalFolderId, goalFolderPrivacy],
    };
}
