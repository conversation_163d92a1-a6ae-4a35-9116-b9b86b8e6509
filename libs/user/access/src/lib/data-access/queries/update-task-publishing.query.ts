import type { QueryObject, QueryParams } from '@clickup/utils/db-types';

import { PublishingInfo } from '../../model/publishing';

export function updateTaskPublishing(taskId: string, patchRequest: Partial<PublishingInfo>): QueryObject {
    const params: QueryParams = [taskId];
    const updateStatements: string[] = [];

    if (patchRequest.publish !== undefined) {
        updateStatements.push(`public = $${params.push(patchRequest.publish)}`);
    }

    if (patchRequest.publish_permission_level !== undefined) {
        updateStatements.push(`public_permission_level = $${params.push(patchRequest.publish_permission_level)}`);
    }

    if (patchRequest.publish_token !== undefined) {
        updateStatements.push(`public_token = $${params.push(patchRequest.publish_token)}`);
    }

    if (patchRequest.expires_at !== undefined) {
        updateStatements.push(`public_share_expires_on = $${params.push(patchRequest.expires_at)}`);
    }

    if (patchRequest.published_by !== undefined) {
        updateStatements.push(`made_public_by = $${params.push(patchRequest.published_by)}`);
    }

    if (patchRequest.published_at !== undefined) {
        updateStatements.push(`made_public_time = $${params.push(patchRequest.published_at)}`);
    }

    if (patchRequest.allow_seo_indexing !== undefined) {
        updateStatements.push(`seo_optimized = $${params.push(patchRequest.allow_seo_indexing)}`);
    }

    if (patchRequest.properties !== undefined) {
        updateStatements.push(`public_fields = $${params.push(patchRequest.properties)}`);
    }

    if (updateStatements.length) {
        updateStatements.push(`date_updated = $${params.push(Date.now())}`);
    }

    const UPDATE_TASK_PUBLISHING_QUERY = `
        UPDATE task_mgmt.tasks 
        SET ${updateStatements.join(', ')}
        WHERE 
            id = $1
        RETURNING *
    `;

    return {
        query: UPDATE_TASK_PUBLISHING_QUERY,
        params,
    };
}
