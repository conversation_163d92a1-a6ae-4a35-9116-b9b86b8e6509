import type { QueryObject, QueryParams } from '@clickup/utils/db-types';

import { AclEntry } from '../../model/acl';

export function upsertTaskMembers(workspaceId: number, taskId: string, upsertUserEntries: AclEntry[]): QueryObject {
    const params: QueryParams = [taskId, workspaceId, Date.now()];
    const insertStatements = upsertUserEntries
        .map(entry => ` ($1, $${params.push(entry.id)}, $3, $${params.push(entry.permissionLevel)}, $2)`)
        .join(',');

    const UPSERT_TASK_MEMBERS_QUERY = `
        INSERT INTO task_mgmt.task_members(
            task_id, 
            userid, 
            date_added, 
            permission_level, 
            workspace_id
        )
        VALUES ${insertStatements}
        ON CONFLICT ON CONSTRAINT task_members_pkey 
        DO UPDATE SET permission_level = EXCLUDED.permission_level
    `;

    return {
        query: UPSERT_TASK_MEMBERS_QUERY,
        params,
    };
}
