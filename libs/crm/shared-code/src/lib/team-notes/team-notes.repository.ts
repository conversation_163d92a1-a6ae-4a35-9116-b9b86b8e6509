import { Inject, Injectable } from '@nestjs/common';
import uuid from 'node-uuid';

import { DBClient } from '@clickup/utils/db';
import { SimpleClient } from '@clickup/utils/db-types';
import { GLOBAL_DATA_CLIENT } from '@clickup-legacy/utils/GlobalDataClient';

import { CrmUserId, TeamId } from '../types/id.interface';

@Injectable()
export class TeamNotesRepository {
    constructor(@Inject(GLOBAL_DATA_CLIENT) private readonly globalDb: DBClient) {}

    async createNote(
        teamId: TeamId,
        note: string,
        adminId: CrmUserId,
        action: string,
        sticky = false,
        client?: SimpleClient
    ): Promise<string> {
        const now = Date.now();
        const id = uuid.v4();
        const query = `INSERT INTO crm.team_notes (id, team_id, note, poster, sticky, date_posted, action)
                       VALUES($1, $2, $3, $4, $5, $6, $7) RETURNING id`;
        const params = [id, teamId, note, adminId, sticky, now, action];

        const result = client
            ? await client.queryAsync<{ id: string }>(query, params)
            : await this.globalDb.writeAsync<{ id: string }>(query, params);

        return result.rows[0]?.id;
    }
}
