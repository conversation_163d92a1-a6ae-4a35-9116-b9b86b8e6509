export enum MetricNames {
    AI_ERROR_INPUT_TOO_LONG = 'ai.error.too_long',
    AI_ERROR_INPUT_TOO_SHORT = 'ai.error.too_short',
    AI_ERROR_ALL = 'ai.error.all',
    AI_LONG_SUMMARY = 'ai.long-summary',
    AI_LONG_SUMMARY_CHUNKS = 'ai.long-summary.chunks',
    AI_MODEL_OVERRIDE = 'ai.model.override',
    AI_TTFB = 'ai.ttfb',
    AI_WEEKLY_RATE_LIMIT = 'ai.weekly_rate_limit',
    AI_TEMPLATE_CALL = 'ai.template.call',
    AI_WRITING_LENGTH = 'ai.ai_writing.length',
    AI_INPUT_LENGTH = 'ai.input.length',
    AI_OUTPUT_LENGTH = 'ai.output.length',
    AI_COST = 'ai.cost',

    AI_TRANSCRIPTION_TITLE_OK = 'ai.transcription.title.ok',
    AI_TRANSCRIPTION_TITLE_ERROR = 'ai.transcription.title.error',
    AI_TRANSCRIPTION_TOO_LARGE = 'ai.transcription.too_large',
    AI_TRANSCRIPTION_INVALID_INPUT = 'ai.transcription.invalid_input',
    AI_TRANSCRIPTION_ATTACHMENT_SIZE_RAW = 'ai.transcription.attachment_size.raw',
    AI_TRANSCRIPTION_ATTACHMENT_SIZE_PROCESSED = 'ai.transcription.attachment_size.processed',

    AI_AGENT_ACTION = 'ai.agent.action',
    AI_MENTION_BRAIN_ACTION = 'ai.mention_brain.action',
}
