import { Module } from '@nestjs/common';

import { GrowthDataServiceModule } from '@clickup/growth-data';
import { UtilsAuthorizationModule } from '@clickup/utils/authorization';
import { UtilsConfigModule } from '@clickup/utils/config';

import { onboardingServiceProvider } from './onboarding.providers';
import { OnboardingService } from './onboarding.service';

@Module({
    imports: [UtilsConfigModule, UtilsAuthorizationModule, GrowthDataServiceModule],
    controllers: [],
    providers: [onboardingServiceProvider],
    exports: [OnboardingService],
})
export class OnboardingServiceModule {}
