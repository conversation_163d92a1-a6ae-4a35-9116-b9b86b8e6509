export const hasErrorCode = (value: unknown): value is { ECODE: string } =>
    typeof value === 'object' && value !== null && 'ECODE' in value;

export const enum ErrorCodes {
    FreePlanOverLimit = 'CMAIL_001',
    UnlimitedPlanOverLimit = 'CMAIl_002',
    BusinessPlanOverLimit = 'CMAIL_003',
    CheckEmailPaywallFailure = 'CMAIL_004',
    EmailLimitReached = 'CMAIL_005',
    IncrementEmailLimitFailure = 'CMAIL_006',
    DeleteNylasAccountsFailure = 'CMAIL_007',
    GoogleUserInfoFailure = 'CMAIL_008',
    GoogleEmptyUserInfo = 'CMAIL_009',
    GoogleTokenExchangeFailure = 'CMAIL_010',
    GoogleMissingRefreshToken = 'CMAIL_011',
    GoogleNylasAuthFailure = 'CMAIL_012',
    GoogleNylasServerFailure = 'CMAIL_013',
    GoogleCustomAuthFailure = 'CMAIL_014',
    ImapAuthFailure = 'CMAIL_015',
    ImapNylasServerFailure = 'CMAIL_016',
    ImapCustomAuthFailure = 'CMAIL_017',
    MicrosoftUserInfoRestFailure = 'CMAIL_018',
    MicrosoftUserInfoUnexpectedResponse = 'CMAIL_019',
    MicrosoftUserInfoFailure = 'CMAIL_020',
    MicrosoftUserInfoNoEmail = 'CMAIL_021',
    MicrosoftTokenExchangeRestFailure = 'CMAIL_022',
    MicrosoftTokenExchangeUnexpectedResponse = 'CMAIL_023',
    MicrosoftTokenExchangeFailure = 'CMAIL_024',
    MicrosoftNylasAuthFailure = 'CMAIL_025',
    MicrosoftNylasServerFailure = 'CMAIL_026',
    MicrosoftCustomAuthFailure = 'CMAIL_027',
    EwsNylasAuthFailure = 'CMAIL_028',
    EwsNylasServerFailure = 'CMAIL_029',
    EwsCustomAuthFailure = 'CMAIL_030',
    MigrationEmailLookupFailure = 'CMAIL_031',
    MigrationEmailConflict = 'CMAIL_032',
    AccountMigrationNylasApiFailure = 'CMAIL_033',
    AccountMigrationNylasConnectionFailure = 'CMAIL_034',
    AccountMigrationResultMismatch = 'CMAIL_035',
    AccountMigrationResultWriteFailure = 'CMAIL_036',
    MigrationEmailAlreadyMigrated = 'CMAIL_037',
    NylasProviderIdApiFailure = 'CMAIL_038',
    NylasProviderIdConnectionFailure = 'CMAIL_039',
    MessageTranslationResultMismatch = 'CMAIL_040',
    NylasProviderIdWriteFailure = 'CMAIL_041',
    MessageMigrationThreadLookupFailure = 'CMAIL_042',
    ThreadTranslationResultMismatch = 'CMAIL_043',
    MessageTranslationRequestLimitExceeded = 'CMAIL_044',
    MessageTranslationResponseLimitExceeded = 'CMAIL_045',
    MigrationResourceTypeNotSupported = 'CMAIL_046',
    ContactsAuthFailure = 'CMAIL_047',
    ContactsFetchFailure = 'CMAIL_048',
    FailedToGetV3MessageByV2Id = 'CMAIL_049',
    FailedToGetCleanedV3MessageByV2Id = 'CMAIL_050',
    FailedToSaveEmailAttachments = 'CMAIL_051',
    MessageMigrationBatchFailure = 'CMAIL_052',
    MessageExistsLookupFailure = 'CMAIL_053',
    AccountMigrationFailure = 'CMAIL_054',
    MigrationEmailNotSupported = 'CMAIL_055',
    MigrationResourceTypeNotRecognized = 'CMAIL_056',
    GetExistingThreadsForBackSyncFailure = 'CMAIL_057',
    GetThreadsForBackSyncFailure = 'CMAIL_058',
    ThreadBackSyncError = 'CMAIL_062',
    ErrorGettingThreadForBackSync = 'CMAIL_063',
    GetExistingMessageIdsForBackSyncFailure = 'CMAIL_064',
    MessageBackSyncFailure = 'CMAIL_065',
    AccountMigrationLockUpdateFailure = 'CMAIL_066',
    AccountMigrationLockCheckFailure = 'CMAIL_67',
    AccountLockedForMigration = 'CMAIL_068',
    AccountUpgradeFailure = 'CMAIL_069',
    GetAccountsToCleanupFailure = 'CMAIL_070',
    DeleteNylasGrantFailure = 'CMAIL_071',
    UpdateNylasGrantAfterDeletionFailure = 'CMAIL_072',
    CleanupJobFailure = 'CMAIL_073',
    FailedToDeleteGrantForCleanup = 'CMAIL_074',
    InactiveAccountsCountFailure = 'CMAIL_075',
    MarkNylasGrantNotFoundFailure = 'CMAIL_076',
    InvalidAccountsCountFailure = 'CMAIL_077',
    FailedToGetReplyAccountAndHeaderV2 = 'CMAIL_078',
    FailedToGetReplyAccountAndHeaderV3 = 'CMAIL_079',
    FailedToFetchMessageDetails = 'CMAIL_080',
    ErrorGettingThreadStarterMessageId = 'CMAIL_081',
    ErrorGettingThreadStarterMessageData = 'CMAIL_082',
    AccountUpgradeMessageLookupFailure = 'CMAIL_083',
    AccountUpgradeTooManyMessages = 'CMAIL_084',
    ContactsForbidden = 'CMAIL_085',
    MigrationDataUpdateFailure = 'CMAIL_086',
    NylasProviderIdNoAccount = 'CMAIL_087',
    NylasProviderIdTimeout = 'CMAIL_088',
    AccountMigrationNoAccount = 'CMAIL_089',
    ReplyToBrokenThread = 'CMAIL_090',
    ReplyToNoDatabaseMessage = 'CMAIL_091',
    ReplyToDatabaseMessageFailure = 'CMAIL_092',
    ReplyToTokenDecryptionFailure = 'CMAIL_093',
    ReplyToNoRfc822MessageId = 'CMAIL_094',
    ReplyToRfc822MessageIdv2Failure = 'CMAIL_095',
    ReplyToRfc822MessageIdv3Failure = 'CMAIL_096',
    ReplyToProviderMessageIdFailure = 'CMAIL_097',
    ReplyToNoProviderMessageId = 'CMAIL_098',
    NylasProviderIdEasAccountNotSupported = 'CMAIL_099',
    NylasProviderIdEwsAccessTokenInvalid = 'CMAIL_100',
    ReplyToMessageSearchAmbiguous = 'CMAIL_101',
    ReplyToMessageSearchNoResult = 'CMAIL_102',
    ReplyToMessageSearchFailure = 'CMAIL_103',
    ReplyToProviderMessageIdAuthTypeLookupFailure = 'CMAIL_104',
    ReplyToProviderMessageIdAuthTypeUnsupported = 'CMAIL_105',
    SendFailureInvalidGrant = 'CMAIL_106',
    NylasProviderIdNoValidCredentials = 'CMAIL_107',
    AccountMigrationNoValidCredentials = 'CMAIL_108',
    // 200-299: Reserved for pattern matched provider sending errors
    // 300: Webhook Failures
    WebhookIncomingMessageThreadLookupFailure = 'CMAIL_300',
    WebhookIncomingMessageThreadNotFound = 'CMAIL_301',
    WebhookIncomingMessageLimitExceeded = 'CMAIL_302',
    WebhookIncomingMessageLookupFailure = 'CMAIL_303',
    WebhookIncomingMessageDupeCheckFailure = 'CMAIL_304',
    WebhookIncomingMessagePostCommentFailure = 'CMAIL_305',
}

export interface ErrorDefinition {
    ecode: string;
    message: string;
    canRetry: boolean;
}

export interface ErrorClassification extends ErrorDefinition {
    patterns: string[];
}

export interface ErrorClassifier {
    default?: ErrorDefinition;
    classifications: ErrorClassification[];
}

export const PROVIDER_SENDING_ERROR_CLASSIFIER: ErrorClassifier = {
    default: {
        ecode: 'CMAIL_200',
        message: 'Email Provider Sending Error',
        canRetry: false,
    },
    classifications: [
        {
            // Specific Nylas error indicating that insufficient scopes were granted at the time of initial auth.
            // Example:
            // Request had insufficient authentication scopes.
            ecode: 'CMAIL_201',
            message: 'Insufficient permissions. Please re-authenticate and ensure all email permissions granted.',
            patterns: ['insufficient authentication scopes'],
            canRetry: false,
        },
        {
            // Too much concurrent activity.  Tends to occur for SMTP and MS graph.
            // Examples:
            // 421 Too many concurrent SMTP connections; please try again later.
            // Application is over its MailboxConcurrencyLimit
            ecode: 'CMAIL_202',
            message: 'Too many requests at the same time. Please try again later.',
            patterns: ['concurrency', 'concurrent'],
            canRetry: true,
        },
        {
            // General throttling.  In most cases, it means the specific user mailbox is throttled but it can indicate our provider integration is throttled.
            // Examples:
            // Resource has been exhausted (e.g. check quota).
            // Quota exceeded for quota metric 'Queries' and limit 'Queries per minute' of service 'gmail.googleapis.com' for consumer 'project_number:{ID}'
            // 450 Requested mail action not taken: mailbox unavailable Mail send limit exceeded. {ID}
            // Request got rate limited. Please try again after {NUMBER} seconds: Max timeout reached (gave up when asked to back off {NUMBER} seconds)
            ecode: 'CMAIL_203',
            message: 'Too many requests. Please try again later.',
            patterns: ['quota', 'limit exceeded', 'rate limited', 'throttle'],
            canRetry: true,
        },
        {
            // Timeout
            // Example:
            // dial tcp {IP}:{PORT}: i/o timeout
            ecode: 'CMAIL_204',
            message: 'Timed out.  Please try again later.',
            patterns: ['timeout'],
            canRetry: true,
        },
        {
            // Specific gmail error. You can consent to email scopes even if you haven't enabled the google gmail api for your account.
            // Example:
            // Mail service not enabled
            ecode: 'CMAIL_205',
            message: 'Mailbox not setup. Please enable mail api.',
            patterns: ['mail service not enabled'],
            canRetry: false,
        },
        {
            // Specific gmail error. Attempting to use service account without configuring domain-wide delegation.
            // Example:
            // Precondition check failed.
            ecode: 'CMAIL_206',
            message: 'Mailbox delegation issue.  Please enable domain-wide delegation.',
            patterns: ['precondition check failed'],
            canRetry: false,
        },
        {
            // Generic unreachable mailbox problem.  Usually indicates a problem with the destination email address.
            // Examples:
            // 550 Requested action not taken: mailbox unavailable invalid DNS MX or A/AAAA resource record {ID}
            // dial tcp: lookup {EMAIL} on {IP:PORT}: no such host
            // 550 No Such User Here
            // 450 {VERSION} <{EMAIL}>: Recipient address rejected: Domain not found
            ecode: 'CMAIL_207',
            message: 'Could not reach mailbox. Please ensure mailbox is available.',
            patterns: ['mailbox unavailable', 'no such host', 'no such user', 'not found'],
            canRetry: false,
        },
        {
            // Specific Nylas error indicating IMAP settings problem.  Unusual to get into this state since initial setup had to have worked.
            // Example:
            // grant is missing imap username or password or smtphost or smtp port, unable to fetch connection details
            ecode: 'CMAIL_208',
            message: 'IMAP misconfiguration. Please validate IMAP settings.',
            patterns: ['missing imap username or password'],
            canRetry: false,
        },
        {
            // Typically indicates a reputation issue with content or spam
            // Examples:
            // Account suspended. Follow the instructions in your Inbox to verify your account., WASCL UserAction verdict is not None. Actual verdict is Suspend, ShowTierUpgrade.
            // 550 Outgoing mail from {EMAIL} has been suspended.
            ecode: 'CMAIL_209',
            message: 'Suspended. Please validate mailbox is still active.',
            patterns: ['suspended'],
            canRetry: false,
        },
        {
            // Catch-all for authentication issues that is intentionally low in ranking so more specific rules have an opportunity to pickup
            // Examples:
            // 535 {VERSION} Error: authentication failed: (reason unavailable)
            // 535 Authentication Failed for {EMAIL}
            // 535 Incorrect authentication data
            ecode: 'CMAIL_210',
            message: 'Authentication failed. Please re-authenticate.',
            patterns: ['authentication'],
            canRetry: false,
        },
        {
            // Catch-all for transient issues that are unlikely to repeat.  Also intentionally low in ranking.
            // Examples:
            // Post "https://graph.microsoft.com/v1.0/me/messages": read tcp {IP}:{PORT}->{IP}:{PORT}: read: connection reset by peer
            // smtp server unexpectedly closed, please try again later
            ecode: 'CMAIL_211',
            message: 'Message send failed.  Please try again later.',
            patterns: ['connection reset', 'transient', 'try again'],
            canRetry: true,
        },
    ],
};

export function classifyError(error_message: string, error_classifier: ErrorClassifier): ErrorDefinition {
    if (!error_message || !error_classifier) {
        return { ecode: null, message: null, canRetry: false };
    }

    const normalizedMessage = error_message.toLowerCase();

    for (const { ecode, message, patterns, canRetry } of error_classifier.classifications) {
        if (patterns.some(pattern => normalizedMessage.includes(pattern))) {
            return { ecode, message, canRetry };
        }
    }

    return error_classifier.default ?? { ecode: null, message: null, canRetry: false };
}

export class RedirectError extends Error {
    constructor() {
        super('Received redirect response instead of file content');
        this.name = 'RedirectError';
    }
}
