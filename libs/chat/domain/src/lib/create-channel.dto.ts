/* eslint-disable max-classes-per-file */
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsInt, IsObject, IsOptional, IsPositive, IsString, ValidateNested } from 'class-validator';

import { ViewsParentType } from '@clickup/utils/constants';

import { BaseCreateChannelDto } from './base-create-channel.dto';
import { ChatRoomType } from './chat-room.enum';

class ChatRoomParentCreateChannelDto {
    @ApiProperty()
    @IsString()
    id: string;

    @ApiProperty({
        description: 'Must be a space, folder, or list',
        enum: [ViewsParentType.PROJECT, ViewsParentType.CATEGORY, ViewsParentType.SUBCATEGORY],
    })
    @IsInt()
    @IsPositive()
    type: ViewsParentType;
}

export class CreateChannelDto extends BaseCreateChannelDto {
    @ApiProperty({
        type: ChatRoomParentCreateChannelDto,
        required: false,
        description:
            'The parent of the room: space, folder, or list.  If not specified, the room will be created in the workspace root.',
    })
    @IsObject()
    @IsOptional()
    @ValidateNested()
    @Type(() => ChatRoomParentCreateChannelDto)
    parent?: ChatRoomParentCreateChannelDto;
}

/**
 * Example for OpenAPI Spec
 */
export const exampleCreateChannel: CreateChannelDto = {
    type: ChatRoomType.CHANNEL,
    name: 'Sample',
    description: 'My sample channel',
    topic: 'example topic',
    parent: {
        id: 'string',
        type: 7,
    },
};

export const exampleCreateLocationlessChannel: CreateChannelDto = {
    type: ChatRoomType.CHANNEL,
    name: 'Sample',
    description: 'My sample channel',
    topic: 'example topic',
};
