{"name": "time-hub-shared-types", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/time-hub/shared-types/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/time-hub/shared-types/jest.config.ts"}}}, "tags": []}