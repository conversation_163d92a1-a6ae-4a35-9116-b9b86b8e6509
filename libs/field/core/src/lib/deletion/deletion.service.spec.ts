import { Test } from '@nestjs/testing';

import { DeletionRequest } from '@clickup/field/domain-model';
import { UtilsConfigModule } from '@clickup/utils/config';
import { ClickUpError } from '@clickup-legacy/utils/errors';

import { FieldParentsQueryService } from '../query/field-parents-query.service';
import { FieldDeletionRepository } from './data-access/deletion.repository';
import { DeletionServiceImpl } from './deletion.service.impl';

describe('DeletionService unit tests', () => {
    let service: DeletionServiceImpl;
    let parentQueryServiceMock: FieldParentsQueryService;
    let repositoryMock: FieldDeletionRepository;

    beforeAll(async () => {
        const app = await Test.createTestingModule({
            imports: [
                UtilsConfigModule.forTest({
                    parent_types: {
                        team: 7,
                    },
                }),
            ],
            providers: [
                DeletionServiceImpl,
                {
                    provide: FieldParentsQueryService,
                    useValue: {
                        getParentCount: () => jest.fn(),
                    },
                },
                {
                    provide: FieldDeletionRepository,
                    useValue: {
                        delete: jest.fn(),
                    },
                },
            ],
        }).compile();

        service = app.get(DeletionServiceImpl);
        parentQueryServiceMock = app.get(FieldParentsQueryService);
        repositoryMock = app.get(FieldDeletionRepository);
    });

    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('delete', () => {
        it('Should not delete if more locations are added. Reason: Cannot remove a workspace directly', async () => {
            const request: DeletionRequest = {
                userId: 300001,
                fieldId: 'field-id',
                workspaceId: 333,
                parent: {
                    parent_type: 7,
                    parent_id: 333,
                },
            };

            jest.spyOn(parentQueryServiceMock, 'getParentCount').mockResolvedValueOnce(2);

            await expect(service.delete(request)).rejects.toThrow(ClickUpError);
        });

        it('Should delete, workspace is the only location', async () => {
            const request: DeletionRequest = {
                userId: 300001,
                fieldId: 'field-id',
                workspaceId: 333,
                parent: {
                    parent_type: 7,
                    parent_id: 333,
                },
            };

            jest.spyOn(parentQueryServiceMock, 'getParentCount').mockResolvedValueOnce(1);

            await service.delete(request);

            expect(repositoryMock.delete).toHaveBeenCalledTimes(1);
        });
    });
});
