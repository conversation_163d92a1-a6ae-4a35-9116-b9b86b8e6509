import { Injectable } from '@nestjs/common';

import { Field, FieldTypeName } from '@clickup/field/domain-model';

import { OptionsFieldValuesMergeRepositoryImpl } from './options.field-values.merge-repository.impl';

@Injectable()
export class DropDownFieldValuesMergeRepositoryImpl extends OptionsFieldValuesMergeRepositoryImpl {
    public canExecute(field: Field): boolean {
        return field.type === FieldTypeName.DropDown;
    }

    protected getConditionToIgnoreRemovedValues(removedValuesParamIndex: number): string {
        return `source.value->>'value' NOT IN ($${removedValuesParamIndex})`;
    }

    protected getStatementToRetargetOptions(oldIdsParamIndex: number, newIdsParamIndex: number): string {
        return `value = value || jsonb_build_object('value', 
                ($${newIdsParamIndex}::text[])[array_position($${oldIdsParamIndex}::text[], value->>'value')]
            )`;
    }
}
