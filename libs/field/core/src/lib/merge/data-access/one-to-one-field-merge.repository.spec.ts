import { Field, FieldWithOptionsMergeRequest } from '@clickup/field/domain-model';
import { QueryObject } from '@clickup-legacy/utils/interfaces/QueryObject';
import type { SimpleClient } from '@clickup-legacy/utils/interfaces/TransactionClient';

import { FieldMergeFeature, FieldMergeFeatureFlagService } from '../field-merge-feature-flag.service';
import { OneToOneFieldMergeRepository } from './one-to-one-field-merge.repository';

describe('OneToOneFieldMergeRepository unit tests', () => {
    const workspaceId = 333;

    class TestRepositoryClass extends OneToOneFieldMergeRepository {
        feature = FieldMergeFeature.SkipMergeFilters;

        constructor(featureFlagMock: FieldMergeFeatureFlagService) {
            super(testTable, featureFlagMock);
        }

        protected getReadQueryObject({ newFieldId, sourceFieldId }: FieldWithOptionsMergeRequest): QueryObject {
            const query = `
             CREATE TEMP TABLE temp_${this.tableName} ON COMMIT DROP AS
                SELECT DISTINCT * FROM task_mgmt.${this.tableName} source 
                    WHERE source.field = $1
                    AND NOT EXISTS (
                        SELECT * FROM task_mgmt.${this.tableName} target
                            WHERE source.test_key = target.test_key 
                            AND target.field = $3
                    )
        `;
            const params = [this.formatCustomFieldId(sourceFieldId), this.formatCustomFieldId(newFieldId)];

            return {
                query,
                params,
            };
        }
    }

    const testTable = 'test_table';
    const targetFieldId = 'target_field_id';
    const newFieldId = 'new_field_id';
    const sourceFieldId = 'source_field_id';
    const targetField = {
        id: targetFieldId,
    } as unknown as Field;

    let clientMock: SimpleClient;
    let featureFlagMock: FieldMergeFeatureFlagService;
    let testRepository: TestRepositoryClass;

    beforeEach(() => {
        jest.restoreAllMocks();

        clientMock = {
            queryAsync: jest.fn(),
        } as unknown as SimpleClient;

        featureFlagMock = {
            shouldSkip: jest.fn(),
        } as unknown as FieldMergeFeatureFlagService;

        testRepository = new TestRepositoryClass(featureFlagMock);
    });

    it('Should call the feature flag mock', async () => {
        await testRepository.execute({ newFieldId, sourceFieldId, workspaceId, targetField }, clientMock);

        expect(featureFlagMock.shouldSkip).toHaveBeenCalledWith({
            feature: FieldMergeFeature.SkipMergeFilters,
            fieldId: sourceFieldId,
            newFieldId,
            workspaceId,
        });
    });

    it('Should call simple client 3 times', async () => {
        await testRepository.execute({ newFieldId, sourceFieldId, workspaceId, targetField }, clientMock);

        expect(clientMock.queryAsync).toHaveBeenCalledTimes(3);
    });

    describe('getRepositoryQueries', () => {
        it('ReadQueryObject should match snapshot ', () => {
            const [readQueryObject] = testRepository.getRepositoryQueries({
                newFieldId,
                sourceFieldId,
                targetField,
                workspaceId,
            });
            const { query, params } = readQueryObject;

            expect(query).toMatchInlineSnapshot(`
                "
                             CREATE TEMP TABLE temp_test_table ON COMMIT DROP AS
                                SELECT DISTINCT * FROM task_mgmt.test_table source 
                                    WHERE source.field = $1
                                    AND NOT EXISTS (
                                        SELECT * FROM task_mgmt.test_table target
                                            WHERE source.test_key = target.test_key 
                                            AND target.field = $3
                                    )
                        "
            `);
            expect(params).toMatchInlineSnapshot(`
                Array [
                  "cf_source_field_id",
                  "cf_new_field_id",
                ]
            `);
        });

        it('UpdateQueryObject should match snapshot ', () => {
            const [, updateQueryObject] = testRepository.getRepositoryQueries({
                newFieldId,
                sourceFieldId,
                targetField,
                workspaceId,
            });
            const { query, params } = updateQueryObject;

            expect(query).toMatchInlineSnapshot(`
                "
                            UPDATE temp_test_table
                                SET field = $1
                        "
            `);
            expect(params).toMatchInlineSnapshot(`
                Array [
                  "cf_new_field_id",
                ]
            `);
        });

        it('WriteQueryObject should match snapshot ', () => {
            const [, , writeQueryObject] = testRepository.getRepositoryQueries({
                newFieldId,
                sourceFieldId,
                targetField,
                workspaceId,
            });
            const { query, params } = writeQueryObject;

            expect(query).toMatchInlineSnapshot(`
                "
                            INSERT INTO task_mgmt.test_table
                                SELECT * FROM temp_test_table;
                        "
            `);
            expect(params).toMatchInlineSnapshot(`Array []`);
        });
    });
});
