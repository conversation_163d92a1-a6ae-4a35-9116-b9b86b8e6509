/* eslint-disable max-classes-per-file */
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Equals, IsNotEmpty, IsOptional, IsString, MaxLength, ValidateNested } from 'class-validator';

import { AiSlapdashRequestInputDto } from '@clickup/ai/core';
import { AiRequestDto, AiRequestType } from '@clickup/sd/api';
import { RelatedTasksConfig } from '@clickup-legacy/models/field/interfaces/FieldAi';

import { MaxAllowedPromptSize } from '../../max-allowed-prompt-size';
import { RelatedTasksConfigDto, Relationships } from './ai-field-task-relationships.dto';

export interface TaskSentimentInput {
    readonly instructions?: string;
    readonly relationships?: RelatedTasksConfig;
}

class TaskSentimentOptions implements TaskSentimentInput {
    @ApiPropertyOptional()
    @IsString()
    @IsOptional()
    @MaxLength(MaxAllowedPromptSize)
    readonly instructions?: string;

    @Relationships()
    readonly relationships?: RelatedTasksConfigDto;
}

class AiFieldTaskSentimentInput extends AiSlapdashRequestInputDto {
    @ValidateNested()
    @Type(() => TaskSentimentOptions)
    readonly opts!: TaskSentimentOptions;
}

export class AiFieldTaskSentimentDto extends AiRequestDto<AiFieldTaskSentimentInput> {
    @Equals(AiRequestType.TaskSentiment)
    @IsNotEmpty()
    override readonly type = AiRequestType.TaskSentiment;

    @ValidateNested()
    @Type(() => AiFieldTaskSentimentInput)
    @IsNotEmpty()
    override readonly input!: AiFieldTaskSentimentInput;
}
