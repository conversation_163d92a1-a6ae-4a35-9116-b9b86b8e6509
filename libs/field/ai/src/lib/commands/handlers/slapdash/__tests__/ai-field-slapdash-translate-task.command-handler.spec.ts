import { DeepMocked } from '@golevelup/ts-sinon';
import { Test } from '@nestjs/testing';

import { SlapdashAiResponseToStreamingResultSerializer } from '@clickup/ai/core';
import { aiMocker, sseStreamResponseMock } from '@clickup/ai/testing';
import { FieldClientToken } from '@clickup/field/service/client-instance';
import { TaskModel } from '@clickup/task/domain-model';
import { FieldType } from '@clickup/utils/constants';
import { UtilsFeatureFlagModule } from '@clickup/utils/feature-flag';
import { UtilsLoggingModule } from '@clickup/utils/logging';
import { FieldClient } from '@clickup-legacy/clients/FieldClient';
import { FieldAiTranslationField } from '@clickup-legacy/models/field/interfaces/FieldAi';

import { createCustomFieldDefinitionMock } from '../../../../../../__tests__/create-custom-field-definition.mock';
import { AiTranslateFieldInfo } from '../../../../ai-custom-field.definition';
import { AiFieldSlapdashClient } from '../../../../clients/ai-field.slapdash-client';
import { AiFieldTranslateTaskCommand } from '../../../definitions/ai-field-translate-task.command';
import { AiFieldSlapdashTranslateTaskCommandHandler } from '../ai-field-slapdash-translate-task.command-handler';

describe('AiFieldSlapdashTranslateTaskCommandHandler unit tests', () => {
    const workspaceId = '333';
    const userId = '300001';

    const task = { id: 'task-id' } as TaskModel;

    const customField = createCustomFieldDefinitionMock({
        type_id: FieldType.text,
        type_config: { ai: { source: 'translation', language: 'english', field: FieldAiTranslationField.Description } },
    }) as AiTranslateFieldInfo;

    const reasoning = 'Test reasoning';

    let handler: AiFieldSlapdashTranslateTaskCommandHandler;
    let slapdashClient: DeepMocked<AiFieldSlapdashClient>;
    let fieldClient: FieldClient;

    beforeAll(async () => {
        const app = await Test.createTestingModule({
            imports: [UtilsFeatureFlagModule, UtilsLoggingModule],
            providers: [
                AiFieldSlapdashTranslateTaskCommandHandler,
                AiFieldSlapdashClient,
                SlapdashAiResponseToStreamingResultSerializer,
                {
                    provide: FieldClientToken,
                    useValue: {
                        addFieldValue: jest.fn(),
                    },
                },
            ],
        })
            .useMocker(aiMocker())
            .compile();

        handler = app.get(AiFieldSlapdashTranslateTaskCommandHandler);
        slapdashClient = app.get(AiFieldSlapdashClient);
        fieldClient = app.get<FieldClient>(FieldClientToken);
    });

    beforeEach(() => {
        jest.resetAllMocks();
        jest.spyOn(fieldClient, 'addFieldValue');
    });

    it('services are defined', async () => {
        expect(handler).toBeInstanceOf(AiFieldSlapdashTranslateTaskCommandHandler);
    });

    it('Handler operation should succeed', async () => {
        jest.spyOn(slapdashClient, 'getTaskTranslation').mockResolvedValueOnce({
            aiRequest: {
                completionString: 'test',
                reasoning,
            },
        });

        const command = new AiFieldTranslateTaskCommand({
            workspaceId,
            userId,
            task,
            customField,
            res: sseStreamResponseMock,
            isAutoAI: false,
            isInitiatedByClickbot: false,
        });

        await handler.handle(command);

        expect(slapdashClient.getTaskTranslation).toHaveBeenCalledWith({
            userId,
            workspaceId,
            taskId: task.id,
            language: customField.type_config.ai.language,
            field: customField.type_config.ai.field,
            isAutoAI: false,
            customFieldId: customField.id,
        });
        expect(fieldClient.addFieldValue).toHaveBeenCalledTimes(1);
    });

    it('Handler should reject the Promise if Slapdash client has thrown an error', async () => {
        jest.spyOn(slapdashClient, 'getTaskTranslation').mockRejectedValueOnce(new Error());

        const command = new AiFieldTranslateTaskCommand({
            workspaceId,
            userId,
            task,
            customField,
            res: sseStreamResponseMock,
            isAutoAI: false,
            isInitiatedByClickbot: false,
        });

        await expect(handler.handle(command)).rejects.toThrowError();
        expect(slapdashClient.getTaskTranslation).toHaveBeenCalledTimes(1);
        expect(fieldClient.addFieldValue).not.toHaveBeenCalled();
    });

    it('Handler should not call the field API if the returned output is empty', async () => {
        jest.spyOn(slapdashClient, 'getTaskTranslation').mockResolvedValueOnce({
            aiRequest: {
                completionString: '',
                reasoning,
            },
        });

        const command = new AiFieldTranslateTaskCommand({
            workspaceId,
            userId,
            task,
            customField,
            res: sseStreamResponseMock,
            isAutoAI: false,
            isInitiatedByClickbot: false,
        });

        await handler.handle(command);

        expect(fieldClient.addFieldValue).not.toHaveBeenCalled();
    });
});
