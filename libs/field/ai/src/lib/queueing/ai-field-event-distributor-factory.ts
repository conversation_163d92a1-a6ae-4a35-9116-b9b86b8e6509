import { Injectable } from '@nestjs/common';

import { AiEventKind, AiEventType, AiJobEvent, AiJobEventDistributorFactory } from '@clickup/ai/core';
import { FeatureFlags } from '@clickup/field/core';

export interface AiFieldValueEvent extends AiJobEvent {
    taskId: string;
    fieldId: string;
}

export interface AiFieldValueCalculatedEvent extends AiFieldValueEvent {
    type: AiEventType.ProgressInfo;
    kind: AiEventKind.ValueCalculated;
    historyIds: string[];
}

export interface AiFieldJobCompletionEvent extends AiJobEvent {
    type: AiEventType.ProgressInfo;
    kind: AiEventKind.JobCompleted;
    updatedTasks: number;
    failedTasks: number;
}

export type AiFieldJobEvent = AiFieldValueEvent | AiFieldValueCalculatedEvent | AiFieldJobCompletionEvent;

@Injectable()
export class AiFieldEventDistributorFactory extends AiJobEventDistributorFactory<AiFieldJobEvent> {
    protected get featureFlag(): string {
        return FeatureFlags.asyncAiJobs;
    }
}
