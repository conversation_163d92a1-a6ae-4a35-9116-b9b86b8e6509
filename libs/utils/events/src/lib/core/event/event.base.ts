import { ClickUpEventWithRouting } from '../../publisher/router/eventWithRouting';
import { EventHeader, EventHeaderCreationParams } from './event.header';

export interface EventBaseParams<Data> {
    headerParams: EventHeaderCreationParams;
    data: Data;
    routing?: unknown;
}

export class ClickUpEventBase<Data = unknown> implements ClickUpEventWithRouting<Data> {
    header: EventHeader;

    data: Data;

    routing?: unknown;

    constructor({ headerParams: headers, data, routing }: EventBaseParams<Data>) {
        this.header = new EventHeader(headers);
        this.data = data;
        this.routing = routing;
    }
}
