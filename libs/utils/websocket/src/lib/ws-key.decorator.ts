import { createParamDecorator, ExecutionContext } from '@nestjs/common';

import type { ClickUpRequest } from '@clickup/utils/http-types';

export const WsKeyDecoratorFactory = (data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx?.switchToHttp()?.getRequest<ClickUpRequest>();
    return (request?.headers?.sessionid as string) ?? request?.decoded_token?.ws_key;
};

export const WsKey = createParamDecorator(WsKeyDecoratorFactory);
