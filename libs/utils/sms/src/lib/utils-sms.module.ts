import { Modu<PERSON> } from '@nestjs/common';
import { Twi<PERSON> } from 'twilio';

import { ConfigService, UtilsConfigModule } from '@clickup/utils/config';
import { FormatLogger, UtilsLoggingModule } from '@clickup/utils/logging';

import { SmsService } from './sms.service';
import { TwilioService } from './twilio.service';

@Module({
    imports: [UtilsLoggingModule, UtilsConfigModule],
    providers: [
        SmsService,
        {
            provide: TwilioService,
            useFactory: (configService: ConfigService, logger: FormatLogger): TwilioService => {
                const accountSid = configService.get<string>('twilio.acct_sid', '');
                const apiToken = configService.get<string>('twilio.token', '');
                const twilioClient = new Twilio(accountSid, apiToken);
                return new TwilioService(twilioClient, configService, logger);
            },
            inject: [ConfigService, FormatLogger],
        },
    ],
    exports: [SmsService, TwilioService],
})
export class UtilsSmsModule {}
