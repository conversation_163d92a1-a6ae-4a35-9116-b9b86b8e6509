import { assignCheckoutWatcherForDb2, CraftyClient2, PoolClientWithFlags } from '@clickup/data-platform/pg';
import { getLogger } from '@clickup/shared/utils-logging';
import { trimParamsForQueries } from '@clickup/utils/db-helpers';
import { ConnectionOptions } from '@clickup/utils/db-types';

const logger = getLogger('db-lite getConn');

export async function getConn(options: ConnectionOptions): Promise<PoolClientWithFlags> {
    // if (config.get('maintenance_mode')) {
    //     throw new DB2Error(
    //         'Sorry, ClickUp is currently undergoing scheduled maintenance. This action cannot be performed right now.',
    //         'MAINTENANCE_001',
    //         400
    //     );
    // }

    let client: PoolClientWithFlags;
    const poolName = options.pool.dbPoolHostMetricLabel ?? 'unknown';
    const cw = assignCheckoutWatcherForDb2(options, poolName, options.pool, null);

    try {
        client = (await options.pool.connect()) as PoolClientWithFlags;

        if (client && !client.error_listener_attached) {
            client.error_listener_attached = true;
            client.on('error', client_err => {
                logger.debug({ msg: 'Client conn error', client_err });
            });
        }

        client.pool = options.pool;
        client.masterId = options.pool.masterId;

        const timeoutLogs: NodeJS.Timeout[] = [];
        const clientGrantedTime = Date.now();

        const { timeoutInterval = 3000, forceTimeoutInterval = 15000 } = options || {};
        const queriesForLogs = trimParamsForQueries(options.queries);
        timeoutLogs.push(
            setTimeout(() => {
                logger.error({
                    msg: 'Very long running transaction on db2',
                    label: options.label,
                    queries: queriesForLogs,
                    duration: forceTimeoutInterval,
                });
            }, forceTimeoutInterval)
        );

        for (let i = 0; i < 8; i++) {
            timeoutLogs.push(
                setTimeout(() => {
                    logger.error({
                        msg: 'Long running transaction',
                        label: options.label,
                        queries: queriesForLogs,
                        totalTime: (i + 1) * timeoutInterval,
                    });
                }, (i + 1) * timeoutInterval)
            );
        }

        client.once('released', () => {
            cw?.notifyDoneCalled();

            timeoutLogs.forEach(timer => {
                clearTimeout(timer);
            });

            const txDuration = Date.now() - clientGrantedTime;
            const logOverTxDuration = 2 * 60 * 1000;

            if (txDuration >= logOverTxDuration) {
                logger.error({
                    msg: 'Long running transaction',
                    label: options.label,
                    queries: queriesForLogs,
                    complete: true,
                    totalTime: txDuration,
                });
            }
        });

        cw?.notifyClientAssigned(client);

        client = cw ? new CraftyClient2(client, cw) : client;

        return client;
    } catch (err) {
        cw?.notifyErrorBeforeClientAssigned(err);
        if (client) {
            client.release(err as Error);
        }
        // throw new DB2Error('Internal server error', 'DB2_001', 500, {
        //     err,
        //     error: 'Failed to connected to db. using backup server',
        // });
        throw err; // fix this with a proper error
    }
}
