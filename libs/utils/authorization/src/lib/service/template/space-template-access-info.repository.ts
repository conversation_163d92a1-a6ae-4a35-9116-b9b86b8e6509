import { Inject, Injectable } from '@nestjs/common';
import { ObjectType } from '@time-loop/ovm-object-version';

import { PublicTemplatesService, RowWithPermanentTemplateId } from '@clickup/templates/public-templates';
import type {
    DbLeafAcl,
    MetricsArgs,
    PermissionLevel,
    PermissionLevelByUser,
    TemplateDbAccessInfo,
    TemplateLeafAccessInfo,
} from '@clickup/utils/authorization/models';
import {
    AccessCheckStrategy,
    DD_DATABASE_METRICS,
    TemplateAccessCheckStrategy,
} from '@clickup/utils/authorization/models';

import { AccessInfoSimpleClient } from '../common/database-client';
import { AuthorizationFeatureFlagService } from '../common/feature-flags';
import { AuthorizationMetricsService } from '../common/metrics';
import { LeafAccessInfoRepository } from '../engine/leaf-access-info.repository';

/**
 * Service for fetching space template access info from the database.
 */
@Injectable()
export class SpaceTemplateAccessInfoRepository extends LeafAccessInfoRepository {
    constructor(
        protected readonly featureFlagService: AuthorizationFeatureFlagService,
        protected readonly metricsService: AuthorizationMetricsService,
        @Inject(PublicTemplatesService) private readonly publicTemplates: PublicTemplatesService
    ) {
        super(featureFlagService, metricsService);
    }

    public override get objectType(): ObjectType {
        return ObjectType.SPACE_TEMPLATE_ACCESS;
    }

    public async getAccessInfoForWorkspace(
        client: AccessInfoSimpleClient,
        workspaceId: number,
        objectIds: string[]
    ): Promise<Map<string, TemplateLeafAccessInfo>> {
        const results = new Map<string, TemplateLeafAccessInfo>();
        if (!objectIds?.length) {
            return results;
        }

        const aclsPromise = this.getSpaceTemplateAclsQuery(client, objectIds);

        for (const spaceTemplate of await this.getSpaceTemplatesQuery(client, workspaceId, objectIds)) {
            const userAcl: PermissionLevelByUser<PermissionLevel> = {};

            this.setCreatorInUserAclForPrivateTemplateStrategy(spaceTemplate, spaceTemplate.creator, userAcl);

            const allowedRoles = this.getAllowedRolesBasedOnTemplateStrategy(spaceTemplate);

            results.set(spaceTemplate.id, {
                key: {
                    object_type: this.objectType,
                    object_id: spaceTemplate.id,
                    workspace_id: workspaceId,
                },
                workspaceId,
                owner: spaceTemplate.owner,
                creator: spaceTemplate.creator,
                deleted: spaceTemplate.deleted === true,
                private: spaceTemplate.template_access_strategy === TemplateAccessCheckStrategy.Private,
                archived: false,
                parents: [],
                templateAccessCheckStrategy: spaceTemplate.template_access_strategy,
                templatePublicSharing: spaceTemplate.public_sharing === 'true' || spaceTemplate.public_sharing === true,
                templateApprovalStatus:
                    spaceTemplate.approval_status === 'true' || spaceTemplate.approval_status === true,
                userAcl,
                groupAcl: null,
                allowedRoles,
            });
        }

        super.setAcls(results, await aclsPromise);

        return results;
    }

    // Queries
    private async getSpaceTemplatesQuery(
        client: AccessInfoSimpleClient,
        workspaceId: number,
        spaceTemplateIds: string[]
    ): Promise<TemplateDbAccessInfo[]> {
        const args: MetricsArgs = {
            source: DD_DATABASE_METRICS,
            objectType: this.objectType,
            objectCount: spaceTemplateIds.length,
            kind: 'engine',
            query: 'spaceTemplates',
        };
        return this.metricsService.metricsScope(args, async () => {
            const query = `
                SELECT
                    projects.id,
                    projects.owner,
                    projects.creator,
                    projects.deleted,
                    projects.private,
                    projects.archived,
                    projects.permissions as template_access_strategy,
                    projects.public_sharing,
                    projects.permanent_template_id
                FROM
                    task_mgmt.projects
                WHERE
                    id = ANY($1) AND
                    template IS TRUE AND
                    team = $2
            `;

            const { rows } = await client.queryAsync<TemplateDbAccessInfo & RowWithPermanentTemplateId>(query, [
                spaceTemplateIds,
                workspaceId,
            ]);

            return this.publicTemplates.loadAndMergeApprovalStatusesForRowsWithPermanentTemplateIds(rows, 'project');
        });
    }

    private async getSpaceTemplateAclsQuery(
        client: AccessInfoSimpleClient,
        spaceTemplateIds: string[]
    ): Promise<DbLeafAcl[]> {
        const args: MetricsArgs = {
            source: DD_DATABASE_METRICS,
            objectType: this.objectType,
            objectCount: spaceTemplateIds.length,
            kind: 'engine',
            query: 'spaceTemplateAcls',
        };

        return this.metricsService.metricsScope(args, async () => {
            const query = `
                SELECT
                    project_id as object_id,
                    'user' as kind,
                    userid::text as shared_with,
                    5 as permission_level
                FROM task_mgmt.project_template_members
                WHERE project_id = ANY($1)
                    AND userid IS NOT NULL
                UNION
                SELECT
                    project_id as object_id,
                    'group' as kind,
                    group_id::text as shared_with,
                    5 as permission_level
                FROM task_mgmt.project_template_group_members
                WHERE project_id = ANY($1)
                    AND group_id IS NOT NULL
        `;

            const { rows } = await client.queryAsync<DbLeafAcl>(query, [spaceTemplateIds]);

            return rows;
        });
    }
}
