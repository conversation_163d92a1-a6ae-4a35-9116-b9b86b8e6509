// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`DocAccessInfoRepository getAccessInfoForWorkspace should return correct data 1`] = `
Map {
  "doc1" => Object {
    "archived": false,
    "creator": 1,
    "deleted": false,
    "groupAcl": Object {},
    "key": Object {
      "object_id": "doc1",
      "object_type": "doc",
      "workspace_id": 123,
    },
    "leafAccessCheckStrategy": 1,
    "lockedPermissions": undefined,
    "owner": 1,
    "parents": Array [
      Object {
        "object_id": "123",
        "object_type": "workspace",
        "workspace_id": 123,
      },
    ],
    "private": false,
    "protected": undefined,
    "underlyingViewId": "viewid",
    "userAcl": Object {},
    "viewParentId": 123,
    "viewParentType": 4,
    "viewType": 27,
    "viewVisibility": 1,
    "visibleTo": 456,
    "workspaceHasAccess": true,
    "workspaceId": 123,
  },
}
`;

exports[`DocAccessInfoRepository getAccessInfoForWorkspace should return correct data when there are ACLs 1`] = `
Map {
  "doc1" => Object {
    "archived": false,
    "creator": 1,
    "deleted": false,
    "groupAcl": Object {
      "group1": Object {
        "permissionLevel": 5,
      },
      "group2": Object {
        "permissionLevel": 5,
      },
    },
    "key": Object {
      "object_id": "doc1",
      "object_type": "doc",
      "workspace_id": 123,
    },
    "leafAccessCheckStrategy": 1,
    "lockedPermissions": undefined,
    "owner": null,
    "parents": Array [
      Object {
        "object_id": 123,
        "object_type": "space",
        "workspace_id": 123,
      },
    ],
    "private": false,
    "protected": undefined,
    "underlyingViewId": "viewid",
    "userAcl": Object {
      "1": Object {
        "permissionLevel": 5,
      },
      "2": Object {
        "permissionLevel": 5,
      },
    },
    "viewParentId": 123,
    "viewParentType": 4,
    "viewType": 27,
    "viewVisibility": 2,
    "visibleTo": 456,
    "workspaceHasAccess": false,
    "workspaceId": 123,
  },
}
`;
