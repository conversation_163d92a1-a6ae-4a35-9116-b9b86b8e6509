import { Inject, Injectable } from '@nestjs/common';
import { type ObjectKey, ObjectType } from '@time-loop/ovm-object-version';

import { ObjectKeyMap } from '@clickup/object-version-cache-common';
import {
    type AccessFilterRequestOptions,
    type AccessInfo,
    type UserWorkspaceAccessInfo,
    AccessCheckResult,
    AccessCheckStrategy,
    AccessFilterRequest,
    AccessInfoEntitlements,
    AccessInfoFilterRequest,
    AccessInfoObjects,
    AccessInfoRequest,
    AccessRequestOptions,
    appendFilterAccessTraceUser,
    AuthorizationTrace,
    AuthorizationTraceReason,
    availibleRolePermissionsForAllPlans,
    BasicGuestPermissions,
    CustomPermissionLevel,
    CustomPermissionLevelMap,
    CustomRolePermissions,
    DefaultPermissionsSupportType,
    FilterAccessTrace,
    HierarchyPermissionLevel,
    hierarchyPermissionLevelsSet,
    ObjectPermissions,
    Permission,
    PermissionConstants,
    PermissionLevel,
    PermissionLevelDefinition,
    PermissionLevelValue,
    PermissionsSupportType,
    PublicApiDevKeyPermission,
    RoleWithSubtypeId,
    ServiceStatus,
    TriggeredAccessCheckRule,
    UserAclType,
    UserId,
    UserRole,
    UserRoles,
    UserRoleSubType,
    ViewLeafAccessInfo,
    WorkspaceAccessInfo,
    WorkspaceId,
    WorkspaceRole,
    WorkspaceRolePermissionValue,
} from '@clickup/utils/authorization/models';

import {
    RecoverableException,
    UserNotFoundException,
    UserWorkspaceInviteException,
    WorkspaceArchivedException,
    WorkspaceNotFoundException,
} from '../common/exceptions';
import { AuthorizationFeatureFlagService } from '../common/feature-flags';
import { AuthorizationMetricsService } from '../common/metrics';
import { makeStringObjectKey } from '../common/object-key-utils';
import { TraceChanges } from '../common/trace-changes';
import {
    applyCustomPermissionLevelAggregates,
    canApplyDefaultPermissionLevel,
    getObjectSpaceHierarchyDistance,
    hasAccess,
    isChatView,
    isHierarchyAccessInfo,
    setWorkspaceRolePermissions,
    toPermissionLevelValue,
} from '../common/utils';
import { RoleService } from '../role/role.service';
import { getRoleKey, hasViewPermissionsEnabled, isExternalGuest } from '../role/role.utils';
import { ACCESS_CHECK_RULES, AccessCheckRules } from './access-check.rules';
import { AccessCheckRulesArgs } from './access-check-rules-args';
import { AuthorizationService } from './authorization.service';
import { FilterAccessRulesArgs } from './filter-access-rules-args';
import { PermissionConfigRepository } from './permission-config-repository';

/**
 * Result of a filter access rule.
 * The first element is a boolean indicating if the rule passed.
 * The second element is the reason for the result (can be undefined if the rule passed).
 */
export type FilterAccessRuleResult = [boolean, AuthorizationTraceReason | undefined];

/**
 * Base class for hierarchy access checks and filters.
 * Provides common protected methods for all access services.
 */
@Injectable()
export abstract class HierarchyAccessService {
    protected readonly tracePrefix: string = 'HIERARCHY';

    protected readonly rules: Map<ObjectType, AccessCheckRules>;

    protected readonly permissionLevelDefinitions: PermissionLevelDefinition[];

    protected readonly permissions: PermissionConstants;

    protected readonly defaultRolePermissions: Map<RoleWithSubtypeId, CustomRolePermissions>;

    constructor(
        protected readonly permissionConfigRepository: PermissionConfigRepository,
        protected readonly metricsService: AuthorizationMetricsService,
        protected readonly featureFlagService: AuthorizationFeatureFlagService,
        protected readonly roleService: RoleService,
        @Inject(ACCESS_CHECK_RULES)
        accessCheckRules: AccessCheckRules[]
    ) {
        this.rules = accessCheckRules.reduce((acc, cur) => {
            acc.set(cur.objectType, cur);
            return acc;
        }, new Map<ObjectType, AccessCheckRules>());
        this.permissions = this.permissionConfigRepository.getPermissionConstants();
        this.permissionLevelDefinitions = this.permissionConfigRepository.getPermissionLevels();
        this.defaultRolePermissions = this.permissionConfigRepository.getCalculatedDefaultRolePermissions();
    }

    /**
     * Calculate a user's permissions for a single access info object (hierarchy or leaf)
     */
    public calculateAccessInfoPermissions(
        authzUserId: number,
        accessInfo: AccessInfo,
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        defaultHierarchyRolePermissions: CustomRolePermissions | undefined,
        accessRequestOptions: AccessRequestOptions,
        prefetchedData: AccessInfoObjects,
        entitlements?: AccessInfoEntitlements,
        options?: {
            parentsInfo?: AccessInfo[];
            respectRoleInAcl?: boolean;
        },
        customPermissionLevels?: CustomPermissionLevelMap
    ): AccessCheckResult {
        let customPermissionLevel: CustomPermissionLevel | undefined;
        let permissionValues: Map<Permission, PermissionLevelValue | undefined>;
        let permissionLevel: UserAclType = this.getOwnerPermissions(
            authzUserId,
            userWorkspace,
            accessInfo,
            accessRequestOptions,
            prefetchedData,
            new ObjectKeyMap(options?.parentsInfo?.map(p => [p.key, p]) ?? [])
        );
        let reason: AuthorizationTraceReason;
        let applyDefaultHierarchyRolePermissions = false;

        if (permissionLevel == null) {
            // When looking at user and group permission levels, we want the effective
            // permissions that are closest in hierarchy distance, and for ties use most permissive
            permissionLevel = accessInfo.userAcl?.[authzUserId] as UserAclType;
            if (permissionLevel) {
                if ('hierarchyDistance' in permissionLevel && permissionLevel.hierarchyDistance > 0) {
                    reason = AuthorizationTraceReason.HasAccess_ParentIsSharedToUser;
                } else {
                    reason = AuthorizationTraceReason.HasAccess_ObjectIsSharedToUser;
                }
            }
            let isGroupLevel = false;

            for (const groupId of userWorkspace?.groupIds ?? []) {
                const groupLevel = accessInfo.groupAcl?.[groupId];
                if (groupLevel) {
                    if (permissionLevel === undefined) {
                        permissionLevel = groupLevel;
                        isGroupLevel = true;
                    } else if (
                        'hierarchyDistance' in permissionLevel &&
                        permissionLevel.hierarchyDistance != null &&
                        groupLevel.hierarchyDistance != null
                    ) {
                        if (groupLevel.hierarchyDistance < permissionLevel.hierarchyDistance) {
                            permissionLevel = groupLevel;
                            isGroupLevel = true;
                        } else if (isGroupLevel && groupLevel.permissionLevel > permissionLevel.permissionLevel) {
                            permissionLevel = groupLevel;
                        }
                    }

                    if (isGroupLevel) {
                        if ('hierarchyDistance' in permissionLevel && permissionLevel.hierarchyDistance > 0) {
                            reason = AuthorizationTraceReason.HasAccess_ParentIsSharedToTeam;
                        } else {
                            reason = AuthorizationTraceReason.HasAccess_ObjectIsSharedToTeam;
                        }
                    }
                }
            }
        } else {
            reason = AuthorizationTraceReason.HasAccess_ObjectOwnedByUser;
        }

        // Handle if permission level is a custom permission level ID
        if (
            permissionLevel &&
            'permissionLevel' in permissionLevel &&
            permissionLevel.permissionLevel &&
            !hierarchyPermissionLevelsSet.has(permissionLevel.permissionLevel)
        ) {
            customPermissionLevel = this.handleCustomPermissionLevel(
                permissionLevel,
                customPermissionLevels,
                accessInfo,
                entitlements
            );
        }

        // If we haven't yet found a permission level (via object ownership or ACL/sharing) we can apply
        // the default permission level that the object has specified, if available.
        if (
            (!permissionLevel || !('permissionLevel' in permissionLevel)) &&
            canApplyDefaultPermissionLevel(accessInfo, userWorkspace)
        ) {
            permissionLevel = accessInfo.defaultPermissionLevel;
            reason = AuthorizationTraceReason.HasAccess_ObjectDefaultPermissions;
        }

        // If we have a permission level, we can cap it based on the user's role for object types that support it.
        if (
            permissionLevel &&
            'permissionLevel' in permissionLevel &&
            permissionLevel.permissionLevel &&
            userWorkspace?.workspaceRole
        ) {
            permissionLevel.permissionLevel = this.permissionConfigRepository.capRolePermissionLevel({
                permissionLevel: permissionLevel.permissionLevel,
                objectType: accessInfo.key.object_type,
                role: userWorkspace.workspaceRole,
            });
        }

        // Handle protected objects.
        // If an object is protected the caller can request it to be readonly rather than no access.
        if (accessInfo.protected && accessRequestOptions?.allowProtected === true) {
            permissionLevel = {
                permissionLevel: HierarchyPermissionLevel.CAN_READ,
                hierarchyDistance: null,
            };
            reason = AuthorizationTraceReason.HasAcccess_ProtectedObjectAllowed;
        }

        // Extract the permission values for the permission level.
        // We have some special rules for workspace objects that we apply here,
        // where we map the users role to a hierarchy permission level for some object types.
        // We also determine if we are going to apply default hierarchy permissions via user role.
        let permissionsSupportType: PermissionsSupportType = DefaultPermissionsSupportType;
        if (permissionLevel) {
            let level: HierarchyPermissionLevel;
            if ('permissionLevel' in permissionLevel) {
                level = permissionLevel.permissionLevel;
            } else if ('role' in permissionLevel && permissionLevel.role != null) {
                const featureDisabled = this.featureFlagService.disableRoleObjectLevelPermissions(
                    accessInfo.key.workspace_id
                );
                if (options?.respectRoleInAcl && !featureDisabled) {
                    level = this.roleToHierarchyPermissionLevel(permissionLevel.role);
                } else {
                    applyDefaultHierarchyRolePermissions = true;
                }
            }

            ({ values: permissionValues, supportType: permissionsSupportType } = this.getPermissionValues(
                level,
                accessInfo.key.object_type,
                customPermissionLevel
            ));
        }

        // When custom permission level exists, we apply the aggregates of the cpl to the permission values map.
        if (customPermissionLevel != null && customPermissionLevel?.deleted !== true) {
            applyCustomPermissionLevelAggregates(customPermissionLevel, permissionValues);
        }

        // If we don't have permission values we still need to have a map of all permissions even if the values are undefined.
        if (!permissionValues) {
            permissionValues = new Map(
                [...this.permissionLevelDefinitions[0].permissionValues.keys()].map((k): [string, undefined] => [
                    k,
                    undefined,
                ])
            );
        }

        // Apply default hierarchy permissions via user role, if applicable.
        // This is mostly required for workspace objects, where we map the users role to a hierarchy permission level.
        if (applyDefaultHierarchyRolePermissions) {
            setWorkspaceRolePermissions(
                defaultHierarchyRolePermissions,
                this.permissionConfigRepository.getInverseWorkspaceRolePermissionsToCommonPermissionsMap(),
                (customPerm, perm) =>
                    permissionValues.get(perm) == null &&
                    (userWorkspace.rolePermissions?.[customPerm] == null ||
                        !(
                            entitlements.customRoles === true ||
                            availibleRolePermissionsForAllPlans.includes(customPerm)
                        )),
                permissionValues
            );
        }

        if (!reason && !permissionLevel && accessInfo.private) {
            reason = AuthorizationTraceReason.NoAccess_ObjectIsPrivate;
        }

        // Determine the effective permission level.
        // This is needed to filter out any "calculated" permission level applied via the user's role.
        const effectiveLevel =
            !permissionLevel ||
            ('role' in permissionLevel && permissionLevel?.role) ||
            !('permissionLevel' in permissionLevel)
                ? undefined
                : permissionLevel;

        // Calculate the sources of the permission level.
        // This is used to trace the permission level back to a parent object.
        const sources = new ObjectKeyMap([[accessInfo.key, effectiveLevel?.permissionLevel]]);

        if (accessInfo.scope?.length > 0 && effectiveLevel?.hierarchyDistance > 0) {
            for (let i = 1; i <= effectiveLevel.hierarchyDistance; i++) {
                // NOTE: scope is assumed to be in the correct ascending order.
                const parentKey = accessInfo.scope[i - 1];
                if (parentKey != null) {
                    sources.set(parentKey, effectiveLevel.permissionLevel);
                }
            }
        }

        return {
            permissions: permissionValues,
            level: effectiveLevel?.permissionLevel,
            sources,
            reason,
            hierarchyDistance: effectiveLevel?.hierarchyDistance,
            permissionsSupportType,
            customPermissionLevel,
        };
    }

    private roleToHierarchyPermissionLevel(role: UserRole): HierarchyPermissionLevel | undefined {
        switch (role) {
            case UserRole.OWNER:
                return HierarchyPermissionLevel.CAN_CREATE_AND_EDIT;
            case UserRole.ADMIN:
                return HierarchyPermissionLevel.CAN_CREATE_AND_EDIT;
            case UserRole.USER:
                return HierarchyPermissionLevel.CAN_EDIT;
            case UserRole.GUEST:
                return HierarchyPermissionLevel.CAN_READ;
            default:
                return undefined;
        }
    }

    protected userHasAccessToNonPrivateObject(
        accessInfo: AccessInfo,
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        options?: AccessFilterRequestOptions
    ) {
        return accessInfo.private !== true && this.canAccessNonPrivateObjects(userWorkspace, options);
    }

    protected canAccessNonPrivateObjects(
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        options?: AccessFilterRequestOptions
    ): boolean {
        return options?.guestsHavePublicAccess || userWorkspace?.workspaceRole !== UserRole.GUEST;
    }

    /**
     * Checks if the user has permission to use dev key authentication
     * @returns true if user has permission, false if not
     */
    protected assertAuthenticatedDevKey(user: UserWorkspaceAccessInfo, userId: UserId): FilterAccessRuleResult {
        const rolePermission = user.rolePermissions?.[PublicApiDevKeyPermission];

        if (rolePermission === WorkspaceRolePermissionValue.OFF) {
            return [false, AuthorizationTraceReason.NoAccess_DevKeyNotAllowed];
        }
        if (rolePermission !== WorkspaceRolePermissionValue.ON) {
            const defaultRolePermissions = this.roleService.getDefaultRolePermissions(user.workspaceRole);
            const defaultRolePermission = defaultRolePermissions.get(PublicApiDevKeyPermission);

            if (defaultRolePermission === PermissionLevelValue.NoAccess) {
                return [false, AuthorizationTraceReason.NoAccess_DevKeyNotAllowed_Default];
            }
        }

        return [true, undefined];
    }

    /**
     * Gets the requested workspace.
     * Throws WorkspaceNotFoundException when workspace doesn't exist
     */
    protected getWorkspaceAccessInfo(
        request: { prefetchedData: AccessInfoObjects },
        workspaceId: number,
        allowArchivedWorkspaces = false
    ): WorkspaceAccessInfo {
        const workspaceAccess = request.prefetchedData.workspaceObjects.get(workspaceId?.toString());
        if (workspaceAccess == null || workspaceAccess.plan == null) {
            throw new WorkspaceNotFoundException('Workspace was not found');
        }
        if (workspaceAccess.serviceStatus === ServiceStatus.Archived && !allowArchivedWorkspaces) {
            throw new WorkspaceArchivedException(workspaceId);
        }
        return workspaceAccess;
    }

    /**
     * For Hierarchy types that are not private(effective private) or owned by the user,
     * if the user has no explicit permission level on the resource we give the user
     * (so long as they're not a guest) full/max permissions on the resource type.
     */
    protected getOwnerPermissions(
        authzUserId: number,
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        accessInfo: AccessInfo,
        options: AccessFilterRequestOptions,
        prefetchedData: AccessInfoObjects,
        parentsInfo?: ObjectKeyMap<ObjectKey, AccessInfo>
    ): PermissionLevel | undefined {
        if (authzUserId === accessInfo.owner && this.isNonGuestChildHierarchy(accessInfo, userWorkspace, options)) {
            return {
                permissionLevel: HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                hierarchyDistance: 0,
                calculated: true,
            };
        }
        return undefined;
    }

    protected userHasAcl(
        authzUserId: number,
        user: UserWorkspaceAccessInfo,
        accessInfo: AccessInfo,
        workspaceAccessInfo: WorkspaceAccessInfo
    ): 'user' | 'group' | 'role' | undefined {
        const userAcl = accessInfo.userAcl?.[authzUserId];

        if (userAcl != null) {
            if (UserRoles.includes((userAcl as WorkspaceRole).role)) {
                return 'role';
            }
            if (
                hierarchyPermissionLevelsSet.has((userAcl as PermissionLevel).permissionLevel) &&
                this.assertGuestSpacePermissionLevel(
                    accessInfo.key,
                    user,
                    userAcl as PermissionLevel,
                    workspaceAccessInfo
                )
            ) {
                return 'user';
            }
        }

        let groupPermissionLevel: PermissionLevel;
        user.groupIds.some(groupId => {
            if (hierarchyPermissionLevelsSet.has(accessInfo.groupAcl?.[groupId]?.permissionLevel)) {
                groupPermissionLevel = accessInfo.groupAcl?.[groupId];
                return true;
            }
            return false;
        });

        if (
            groupPermissionLevel != null &&
            this.assertGuestSpacePermissionLevel(accessInfo.key, user, groupPermissionLevel, workspaceAccessInfo)
        ) {
            return 'group';
        }

        return undefined;
    }

    private isNonGuestChildHierarchy(
        accessInfo: AccessInfo,
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        options?: AccessFilterRequestOptions
    ): boolean {
        return (
            isHierarchyAccessInfo(accessInfo) &&
            accessInfo.key.object_type !== ObjectType.WORKSPACE &&
            this.canAccessNonPrivateObjects(userWorkspace, options)
        );
    }

    protected assertValidError(e: any, trace?: AuthorizationTrace): Error {
        if (e instanceof RecoverableException) return e;

        if (trace) {
            e.trace = trace;
        }

        throw e;
    }

    /**
     * Applies the default permission level to the access check result if no level is set.
     * Returns a boolean indicating if the default permissions were applied.
     */
    protected applyDefaultPermissions(
        userId: number,
        accessInfo: AccessInfo,
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        accessCheckResult: AccessCheckResult,
        prefetchedData: AccessInfoObjects,
        parentsInfo?: AccessInfo[]
    ): boolean {
        let result = false;
        const defaultPermissions = this.getDefaultPermissions(
            userId,
            accessInfo,
            userWorkspace,
            prefetchedData,
            parentsInfo
        );
        if (defaultPermissions) {
            accessCheckResult.level = defaultPermissions.permissionLevel;
            for (const [k, v] of this.getPermissionValues(accessCheckResult.level, accessInfo.key.object_type).values ??
                new Map()) {
                if (v !== undefined) {
                    accessCheckResult.permissions.set(k, v);
                    result = true;
                }
            }
        }
        return result;
    }

    /**
     * Returns the default permission level, if none is set
     */
    protected getDefaultPermissions(
        userId: number,
        accessInfo: AccessInfo,
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        prefetchedData: AccessInfoObjects,
        parentsInfo?: AccessInfo[]
    ): PermissionLevel {
        if (accessInfo.private !== true && this.isNonGuestChildHierarchy(accessInfo, userWorkspace)) {
            const objectDefaultPermissionLevel = this.getObjectDefaultPermissionLevel(
                userId,
                accessInfo,
                userWorkspace
            );
            return {
                permissionLevel: objectDefaultPermissionLevel ?? HierarchyPermissionLevel.CAN_CREATE_AND_EDIT,
                hierarchyDistance: 0,
            };
        }
        return undefined;
    }

    /**
     * Returns the default permission level set on the object
     */
    protected getObjectDefaultPermissionLevel(
        userId: number,
        accessInfo: AccessInfo,
        userWorkspace: UserWorkspaceAccessInfo
    ): HierarchyPermissionLevel | undefined {
        const isDefaultLocationPermissionsEnabled = this.featureFlagService.isDefaultLocationPermissionsEnabled(
            accessInfo.key.workspace_id
        );
        const isOwnerOfObject = accessInfo.owner === userId;
        return isDefaultLocationPermissionsEnabled &&
            !isOwnerOfObject &&
            canApplyDefaultPermissionLevel(accessInfo, userWorkspace)
            ? accessInfo.defaultPermissionLevel?.permissionLevel
            : undefined;
    }

    protected getPermissionValues(
        level: HierarchyPermissionLevel,
        objectType: ObjectType,
        customPermissionLevel?: CustomPermissionLevel
    ): { values: Map<string, PermissionLevelValue> | undefined; supportType: PermissionsSupportType } {
        let permissionValues = customPermissionLevel?.permissions
            ? (customPermissionLevel.permissions as Map<string, PermissionLevelValue>)
            : this.permissionLevelDefinitions.find(l => l.value === level)?.permissionValues;
        let permissionsSupportType: PermissionsSupportType = DefaultPermissionsSupportType;

        if (permissionValues != null) {
            // Get overrides for the current object type
            const objectLevelPermissionSettings =
                this.permissionConfigRepository.getObjectSpecificPermissionLevels(objectType);

            permissionsSupportType = objectLevelPermissionSettings.type;

            const objectPermissionValues =
                objectLevelPermissionSettings.permissionLevels?.get(level)?.permissionValues ?? new Map();

            if (permissionsSupportType === PermissionsSupportType.Partial) {
                permissionValues = new Map([...objectPermissionValues]);
            } else {
                // Copy so we don't mutate the originals
                permissionValues = new Map([...permissionValues, ...objectPermissionValues]);
            }

            // This is to keep the same logic as in legacy access checks
            // This is because manage_custom_fields exists in both role and hierarchy permissions
            // and role takes precedence in legacy.
            // *** This is technical debt and likely a bug!!! ***
            // Not adding this to AccessCheckRules because we would need to support calling rules
            // before merging the final result, and this also applies to all access checks.
            // See https://github.com/time-loop/clickup/blame/777db9ecf5c07b235e490d111f39dfccacbf89e5/src/utils/access2.js#L2494
            // Slack discussion: https://click-up.slack.com/archives/C02EEUBH2ET/p1674737537414059
            permissionValues?.set(this.permissions.manage_custom_fields, undefined);
        }

        return { values: permissionValues, supportType: permissionsSupportType };
    }

    /**
     * Resets all permissions to undefined
     */
    protected resetPermissions(accessCheckResult: AccessCheckResult) {
        accessCheckResult.level = undefined;
        for (const k of accessCheckResult.permissions.keys()) {
            accessCheckResult.permissions.set(k, undefined);
        }
    }

    protected buildObjectPermissions(
        authzUserId: number,
        request: AccessInfoRequest,
        requestedPermissions: Permission[],
        merged: AccessCheckResult,
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        objectAccessInfo: AccessInfo,
        workspaceAccessInfo: WorkspaceAccessInfo,
        trace: AuthorizationTrace,
        triggeredAccessCheckRules: Set<TriggeredAccessCheckRule>,
        parentsInfo?: AccessInfo[]
    ): ObjectPermissions {
        const { entitlements } = request.prefetchedData;
        const permissions = requestedPermissions.reduce((acc, permission) => {
            const permissionLevel = merged.permissions.get(permission);
            acc.set(
                permission,
                hasAccess(
                    authzUserId,
                    objectAccessInfo.owner,
                    objectAccessInfo.creator,
                    objectAccessInfo.key,
                    permissionLevel
                )
            );
            return acc;
        }, new Map<Permission, boolean>());

        const verboseLoggingEnabled =
            request.options?.verboseLoggingEnabled ?? this.featureFlagService.authzVerboseLogging(authzUserId) === true;

        if (verboseLoggingEnabled) {
            trace.append({
                code: `${this.tracePrefix}_FINAL_PERMISSIONS`,
                key: makeStringObjectKey(objectAccessInfo.key),
                role: userWorkspace?.workspaceRole,
                level: merged.level ?? 'undefined',
                data: {
                    permissions: Object.fromEntries(permissions),
                },
            });
        }

        const result: ObjectPermissions = {
            permissions,
            level: merged.level,
            defaultPermissionLevel: objectAccessInfo?.defaultPermissionLevel?.permissionLevel,
            role: userWorkspace?.workspaceRole,
            triggeredAccessCheckRules,
            trace,
            traceReason: trace.reason,
        };

        if (this.roleService.isLimitedMemberEnabledForWorkspace(workspaceAccessInfo)) {
            result.roleSubtype = userWorkspace?.workspaceRoleSubType;
        }

        result.roleKey = getRoleKey(result.role, result.roleSubtype);

        if (objectAccessInfo.deleted != null) {
            result.deleted = objectAccessInfo.deleted;
        }

        if (objectAccessInfo.private != null) {
            result.private = objectAccessInfo.private;
        }

        if (objectAccessInfo.creator != null) {
            result.creator = objectAccessInfo.creator;
        }

        if (objectAccessInfo.owner != null) {
            result.owner = objectAccessInfo.owner;
        }

        if (userWorkspace?.dateJoined != null) {
            result.dateJoined = userWorkspace.dateJoined;
        }

        if (
            entitlements?.customRoles === true &&
            (userWorkspace?.workspaceCustomRole != null || userWorkspace?.rolePermissions != null)
        ) {
            result.customRole = {
                id: userWorkspace.workspaceCustomRole,
                permissions: userWorkspace.rolePermissions,
            };
        }

        if (objectAccessInfo.scope) {
            result.scope = objectAccessInfo.scope;
        }

        if (this.canRequestAccess(result, objectAccessInfo.key, workspaceAccessInfo)) {
            result.canRequestAccess = true;
        }

        // TODO: remove plan, see https://staging.clickup.com/t/333/CLK-240293
        if (
            workspaceAccessInfo?.plan.effectiveId != null ||
            workspaceAccessInfo?.plan.workspaceId != null ||
            workspaceAccessInfo?.plan.trialId != null
        ) {
            result.plan = {};

            if (workspaceAccessInfo.plan.workspaceId != null) {
                result.plan.workspaceId = workspaceAccessInfo.plan.workspaceId;
            }

            if (workspaceAccessInfo.plan.effectiveId != null) {
                result.plan.effectiveId = workspaceAccessInfo.plan.effectiveId;
            }

            if (workspaceAccessInfo.plan.trialId != null) {
                result.plan.trialId = workspaceAccessInfo.plan.trialId;
            }
        }

        if (request.options?.includeFullParents && objectAccessInfo.scope?.length > 0) {
            result.parents ??= new ObjectKeyMap();

            for (const scopeParent of objectAccessInfo.scope) {
                const parentLevel = merged.sources?.get(scopeParent);
                result.parents.set(scopeParent, parentLevel);
                this.setParentCanRequestAccess(result, scopeParent, workspaceAccessInfo);
            }
        }

        return result;
    }

    protected canRequestAccess(
        result: ObjectPermissions,
        objectKey: ObjectKey,
        workspaceAccessInfo: WorkspaceAccessInfo | undefined
    ) {
        return (
            result.error == null &&
            (result.level ?? HierarchyPermissionLevel.CAN_READ) === HierarchyPermissionLevel.CAN_READ &&
            this.featureFlagService.requestAccess(objectKey, workspaceAccessInfo?.workspaceSettings)
        );
    }

    protected setParentCanRequestAccess(
        result: ObjectPermissions,
        parentKey: ObjectKey,
        workspaceAccessInfo: WorkspaceAccessInfo | undefined
    ) {
        if (this.canRequestAccess(result, parentKey, workspaceAccessInfo)) {
            result.parentsCanRequestAccess ??= new ObjectKeyMap();
            result.parentsCanRequestAccess.set(parentKey, true);
        }
    }

    /**
     * Gets the user for the requested workspace.
     * Throws UserNotFoundException when user doesn't exist in that workspace.
     * Throws UserWorkspaceInviteException when user has an outstanding invite and options.allowInvited is set to false.
     */
    protected getUserWorkspaceAccessInfo(
        request: AccessInfoRequest,
        authzUserId: UserId,
        workspaceId: WorkspaceId
    ): UserWorkspaceAccessInfo {
        const userWorkspace = request.prefetchedData.userWorkspaceObjects.get(workspaceId.toString())?.get(authzUserId);
        if (userWorkspace == null) {
            throw new UserNotFoundException('User is not part of workspace');
        }
        if (request.options?.allowInvited !== true && userWorkspace.hasInvite === true) {
            throw new UserWorkspaceInviteException('User must accept workspace invite');
        }

        return userWorkspace;
    }

    /**
     * If the user has no custom role permissions apply the default custom role permissions for their role.
     * We only want to apply the permissions that are mapped to hierarchy permissions,
     * for role permissions we apply a mask to set them to null
     */
    protected getDefaultHierarchyRolePermissions(
        userWorkspace?: UserWorkspaceAccessInfo
    ): CustomRolePermissions | undefined {
        if (
            userWorkspace != null &&
            this.defaultRolePermissions.has(
                `${userWorkspace.workspaceRole}:${userWorkspace.workspaceRoleSubType ?? UserRoleSubType.NONE}`
            )
        ) {
            return this.permissionConfigRepository.getDefaultHierarchyCustomRolePermissions(
                userWorkspace.workspaceRole,
                userWorkspace.workspaceRoleSubType
            );
        }
        return undefined;
    }

    /**
     * Merges an array of access check results according to the selected strategy
     */
    protected mergePermissionsWithStrategy(
        strategy: AccessCheckStrategy,
        resultMaps: AccessCheckResult[]
    ): AccessCheckResult {
        const results: AccessCheckResult = {
            permissions: new Map<Permission, PermissionLevelValue | undefined>(),
            sources: new ObjectKeyMap(),
        };

        for (const resultMap of resultMaps) {
            if (resultMap.sources) {
                for (const [k, v] of resultMap.sources) {
                    results.sources.set(k, v);
                }
            }

            if (resultMap.level !== undefined) {
                if (results.level === undefined || strategy === AccessCheckStrategy.Overwrite) {
                    results.level = resultMap.level;
                } else if (strategy === AccessCheckStrategy.LeastPermissive) {
                    results.level = Math.min(results.level, resultMap.level);
                } else if (strategy === AccessCheckStrategy.MostPermissive) {
                    results.level = Math.max(results.level, resultMap.level);
                }
            }

            for (const [key, value] of resultMap.permissions) {
                if (value !== undefined) {
                    const previousValue = results.permissions.get(key);

                    if (previousValue === undefined || strategy === AccessCheckStrategy.Overwrite) {
                        results.permissions.set(key, value);
                    } else if (strategy === AccessCheckStrategy.LeastPermissive) {
                        results.permissions.set(key, Math.min(value, previousValue));
                    } else if (strategy === AccessCheckStrategy.MostPermissive) {
                        results.permissions.set(key, Math.max(value, previousValue));
                    }
                }
            }

            if (resultMap.reason && !results.reason) {
                results.reason = resultMap.reason;
            }
        }
        return results;
    }

    /**
     * Merges an array of access check permission maps with the input list of requested permissions
     * and returns as a new map where all values are booleans.
     *
     * This is the final step called in the access check flow before creating the ObjectPermissions result.
     */
    protected mergePermissionsAndApplyRules(args: {
        authzUserId: number;
        objectAccessInfo: AccessInfo;
        workspaceAccessInfo: WorkspaceAccessInfo;
        requestedPermissions: Permission[];
        userWorkspace: UserWorkspaceAccessInfo;
        roleAccessCheckResult: AccessCheckResult;
        objectAccessCheckResult: AccessCheckResult;
        triggeredAccessCheckRules: Set<TriggeredAccessCheckRule>;
        mergedParentAccessCheckResults?: AccessCheckResult;
        parentsInfo?: AccessInfo[];
        parentChain?: ObjectKey[];
        trace: AuthorizationTrace;
        request: AccessInfoRequest;
    }) {
        const {
            authzUserId,
            objectAccessInfo,
            workspaceAccessInfo,
            requestedPermissions,
            userWorkspace,
            roleAccessCheckResult,
            objectAccessCheckResult,
            triggeredAccessCheckRules,
            mergedParentAccessCheckResults,
            parentsInfo = [],
            parentChain,
            trace,
            request,
        } = args;

        const { entitlements } = request.prefetchedData;
        const { options } = request;
        const verboseLoggingEnabled =
            options?.verboseLoggingEnabled ?? this.featureFlagService.authzVerboseLogging(authzUserId) === true;

        const stringKey = makeStringObjectKey(objectAccessInfo.key);
        const rules = this.rules.get(objectAccessInfo.key.object_type);

        // Step 1: merge all parent and object access checks.
        // NOTE: Order matters here, in the legacy checks the role values override
        //       the hierarchy object permissions so we do the same here.
        const objectAccessCheckResults = mergedParentAccessCheckResults
            ? [mergedParentAccessCheckResults, objectAccessCheckResult]
            : [objectAccessCheckResult];

        let merged = this.mergePermissionsWithStrategy(AccessCheckStrategy.Overwrite, objectAccessCheckResults);
        const traceChanges = verboseLoggingEnabled ? new TraceChanges() : undefined;
        trace.append({
            code: `${this.tracePrefix}_MERGE_PERMISSIONS`,
            key: stringKey,
            changes: traceChanges?.computeChanges(merged),
            role: userWorkspace?.workspaceRole,
            level: merged.level ?? 'undefined',
            data: {
                object_level: objectAccessCheckResult?.level ?? 'undefined',
                parents_level: mergedParentAccessCheckResults
                    ? mergedParentAccessCheckResults?.level ?? 'undefined'
                    : undefined,
                role_level: roleAccessCheckResult?.level,
            },
        });

        merged = this.mergeWithRolePermissions(merged, roleAccessCheckResult, args);
        trace.append({
            code: `${this.tracePrefix}_MERGE_WITH_ROLE_PERMISSIONS`,
            key: stringKey,
            changes: traceChanges?.computeChanges(merged),
            role: userWorkspace?.workspaceRole,
            level: merged.level ?? 'undefined',
            data: {},
        });

        // Step 2: apply individual guest permissions to results
        const guestPermissionsApplied = this.applyGuestPermissions(
            userWorkspace,
            objectAccessInfo,
            workspaceAccessInfo,
            entitlements,
            merged,
            roleAccessCheckResult
        );
        if (guestPermissionsApplied) {
            trace.append({
                code: `${this.tracePrefix}_GUEST_PERMISSIONS`,
                key: stringKey,
                reason: AuthorizationTraceReason.HasAccess_ObjectDefaultPermissions,
                changes: traceChanges?.computeChanges(merged),
                level: merged.level ?? 'undefined',
            });
        }

        // Step 3: apply any custom object rules that can remove access.
        const rulesResult = rules?.calculateAccessInfoPermissions(
            new AccessCheckRulesArgs(
                authzUserId,
                objectAccessInfo,
                workspaceAccessInfo,
                requestedPermissions,
                userWorkspace,
                entitlements,
                objectAccessCheckResult,
                merged,
                triggeredAccessCheckRules,
                options,
                parentsInfo,
                request.options?.originator
            )
        );
        if (rulesResult != null) {
            trace.append({
                code: `${this.tracePrefix}_CUSTOM_RULE_RESULTS`,
                key: stringKey,
                level: merged.level ?? 'undefined',
                changes: traceChanges?.computeChanges(merged),
                data: {
                    rulesResult,
                    triggeredAccessCheckRules: [...triggeredAccessCheckRules],
                },
            });
        }

        if (rulesResult !== false) {
            // Step 4: if custom rules don't block us from moving forward,
            // and if we still haven't gotten a hierarchy level then
            // we can try to apply the default level
            if (merged.level === undefined) {
                const defaultPermissionsApplied = this.applyDefaultPermissions(
                    authzUserId,
                    objectAccessInfo,
                    userWorkspace,
                    merged,
                    request.prefetchedData,
                    parentsInfo
                );
                if (defaultPermissionsApplied) {
                    trace.append({
                        code: `${this.tracePrefix}_DEFAULT_PERMISSIONS`,
                        key: stringKey,
                        level: merged.level ?? 'undefined',
                        reason: merged.level
                            ? AuthorizationTraceReason.HasAccess_ObjectDefaultPermissions
                            : AuthorizationTraceReason.NoAccess_NoObjectDefaultPermissions,
                        changes: traceChanges?.computeChanges(merged),
                    });
                }
            }

            // Step 5: apply role permissions and permissions that are potentially
            // overwritten by custom permission set
            merged = this.mergeWithRolePermissions(merged, roleAccessCheckResult, args);
            trace.append({
                code: `${this.tracePrefix}_CUSTOM_ROLE_PERMISSIONS`,
                key: stringKey,
                level: merged.level ?? 'undefined',
                reason: merged.reason,
                changes: traceChanges?.computeChanges(merged),
            });

            // Step 6: apply permissions requirements
            merged = this.checkPermissionsRequirements(merged, args);

            // Step 7: apply post-processing rules that modify individual permissions.
            // Don't apply these post-processing rules if we're checking a parent object,
            // we only want to apply to the final object actually being checked.
            if (rules != null && !parentChain?.length) {
                rules.modifyAccessInfoPermissions(
                    new AccessCheckRulesArgs(
                        authzUserId,
                        objectAccessInfo,
                        workspaceAccessInfo,
                        requestedPermissions,
                        userWorkspace,
                        entitlements,
                        objectAccessCheckResult,
                        merged,
                        triggeredAccessCheckRules,
                        options,
                        parentsInfo,
                        request.options?.originator
                    )
                );

                trace.append({
                    code: `${this.tracePrefix}_MODIFY_CUSTOM_RULE_RESULTS`,
                    key: stringKey,
                    level: merged.level ?? 'undefined',
                    changes: traceChanges?.computeChanges(merged),
                    data: {
                        triggeredAccessCheckRules: [...triggeredAccessCheckRules],
                    },
                });
            }
        } else {
            // Otherwise, remove all permissions if the boolean rules returned false
            this.resetPermissions(merged);
            trace.append({
                code: `${this.tracePrefix}_RESET_PERMISSIONS`,
                key: stringKey,
                level: merged.level ?? 'undefined',
                reason: AuthorizationTraceReason.NoAccess_CustomRule,
                changes: traceChanges?.computeChanges(merged),
                data: {
                    triggeredAccessCheckRules: [...triggeredAccessCheckRules],
                },
            });
        }

        trace.append({
            code: `${this.tracePrefix}_MERGED_PERMISSIONS`,
            key: stringKey,
            level: merged.level ?? 'undefined',
            changes: traceChanges?.computeChanges(merged),
            data: {
                triggeredAccessCheckRules: [...triggeredAccessCheckRules],
            },
        });

        return merged;
    }

    protected checkPermissionsRequirements(
        merged: AccessCheckResult,
        context: Parameters<typeof this.mergePermissionsAndApplyRules>[0]
    ): AccessCheckResult {
        const { entitlements: workspaceEntitlements } = context.request.prefetchedData;

        const permissionRequirementsMap = this.permissionConfigRepository.getPermissionRequirementsMap();

        for (const permission of merged.permissions.keys()) {
            const requirement = permissionRequirementsMap.get(permission);
            if (requirement) {
                if (requirement.entitlements) {
                    for (const requiredEntitlement of requirement.entitlements) {
                        if (
                            !workspaceEntitlements?.enabledEntitlements?.has(requiredEntitlement) ||
                            !workspaceEntitlements?.enabledEntitlements.get(requiredEntitlement)
                        ) {
                            merged.permissions.set(permission, PermissionLevelValue.NoAccess);
                            break;
                        }
                    }
                }
            }
        }

        return merged;
    }

    protected objectIsValidForAccessFilter(
        accessInfo: AccessInfo | null,
        request: AccessFilterRequest
    ): FilterAccessRuleResult {
        const allowDeleted = request.options?.allowDeleted === true;

        if (accessInfo?.key == null) {
            return [false, AuthorizationTraceReason.NoAccess_InvalidObject];
        }

        if (accessInfo.deleted && !allowDeleted) {
            return [false, AuthorizationTraceReason.NoAccess_ObjectIsDeleted];
        }

        return [true, undefined];
    }

    protected userIsValidForAccessFilter(
        userId: UserId,
        user: UserWorkspaceAccessInfo | null,
        request: AccessFilterRequest
    ): FilterAccessRuleResult {
        if (user == null) {
            return [false, AuthorizationTraceReason.NoAccess_InvalidUser];
        }

        if (user.dateJoined == null && request.options?.allowInvited !== true) {
            return [false, AuthorizationTraceReason.NoAccess_UserHasPendingWorkspaceInvite];
        }

        if (request.options?.devKeyAuthenticated) {
            const [isValid, reason] = this.assertAuthenticatedDevKey(user, userId);
            if (!isValid) {
                return [false, reason];
            }
        }

        return [true, undefined];
    }

    protected checkCustomRules(
        userId: UserId,
        user: UserWorkspaceAccessInfo,
        accessInfo: AccessInfo,
        request: AccessInfoFilterRequest
    ): FilterAccessRuleResult {
        const rules = this.rules.get(accessInfo.key.object_type);
        const customRulesResult = rules?.calculateFilterAccessPermissions(
            new FilterAccessRulesArgs(
                userId,
                user,
                accessInfo,
                request,
                this.getWorkspaceAccessInfo(request, accessInfo.key.workspace_id)
            )
        );
        if (customRulesResult === false) {
            return [false, AuthorizationTraceReason.NoAccess_CustomRule];
        }
        return [true, undefined];
    }

    protected checkUserHasAcl(
        userId: UserId,
        user: UserWorkspaceAccessInfo,
        accessInfo: AccessInfo,
        allowWorkspaceUserAcl: boolean,
        workspaceAccessInfo: WorkspaceAccessInfo
    ): FilterAccessRuleResult {
        switch (this.userHasAcl(userId, user, accessInfo, workspaceAccessInfo)) {
            case 'user':
                return [true, AuthorizationTraceReason.HasAccess_ObjectIsSharedToUser];
            case 'group':
                return [true, AuthorizationTraceReason.HasAccess_ObjectIsSharedToTeam];
            case 'role':
                if (allowWorkspaceUserAcl) {
                    return [true, AuthorizationTraceReason.HasAccess_UserBelongsToWorkspaceRole];
                }
                return [false, AuthorizationTraceReason.NoAccess_NoAccessToWorkspaceObjects];
            default:
                break;
        }
        return [false, undefined];
    }

    // Handle individual guest permissions.
    // This should be applied to the merged output of the core access check logic,
    // before any per-object rules are applied.
    //
    // Due to the way the access check currently work, we need to remove any permissions modified by this function
    // from the role access check result, as those will be re-applied later.  This is something we should revisit once
    // the core hierarchy and task legacy access checks (in src/utils/access) are removed and fully replaced with this library.
    private applyGuestPermissions(
        userWorkspace: UserWorkspaceAccessInfo | undefined,
        objectAccessInfo: AccessInfo,
        workspaceAccessInfo: WorkspaceAccessInfo | undefined,
        entitlements: AccessInfoEntitlements,
        mergedAccessCheckResult: AccessCheckResult,
        roleAccessCheckResult: AccessCheckResult
    ) {
        if (userWorkspace == null) {
            return false;
        }

        if (userWorkspace.workspaceRole === UserRole.GUEST) {
            // Guests get whatever basic permissions are set for them by admins
            for (const guestPermission of BasicGuestPermissions) {
                mergedAccessCheckResult.permissions.set(
                    guestPermission,
                    toPermissionLevelValue(userWorkspace.guestPermissions?.[guestPermission])
                );
                roleAccessCheckResult.permissions.delete(guestPermission);
            }

            // If tag management is disabled for guests, remove the relevant permissions
            if (!userWorkspace.guestPermissions?.can_edit_tags) {
                mergedAccessCheckResult.permissions.set(this.permissions.add_tags, PermissionLevelValue.NoAccess);
                roleAccessCheckResult.permissions.delete(this.permissions.add_tags);

                mergedAccessCheckResult.permissions.set(this.permissions.remove_tags, PermissionLevelValue.NoAccess);
                roleAccessCheckResult.permissions.delete(this.permissions.remove_tags);
            }

            // This is only applied to workspace level objects, since add_tags and remove_tags are hierarchy permissions.
            // For hierarchy level objects we rely on the permission level to determine access for tag actions.
            // This is a legacy behavior from teamAccess checks where undefined permission values is passing access checks.
            if (
                objectAccessInfo.key.object_type === ObjectType.WORKSPACE &&
                userWorkspace.guestPermissions?.can_edit_tags === true
            ) {
                mergedAccessCheckResult.permissions.set(this.permissions.add_tags, PermissionLevelValue.HasAccess);
                roleAccessCheckResult.permissions.delete(this.permissions.add_tags);

                mergedAccessCheckResult.permissions.set(this.permissions.remove_tags, PermissionLevelValue.HasAccess);
                roleAccessCheckResult.permissions.delete(this.permissions.remove_tags);
            }

            // View permissions are more complex as they need to take into account the individual permissions,
            // the custom role permission and the current hierarchy permission level.
            // For workspace level checks we use the default permission for the object type.
            const hasHierarchyPermission =
                objectAccessInfo.key.object_type === ObjectType.WORKSPACE
                    ? mergedAccessCheckResult.permissions.get(
                          AuthorizationService.defaultPermissionFor(objectAccessInfo.key.object_type)
                      )
                    : mergedAccessCheckResult.level >= HierarchyPermissionLevel.CAN_EDIT;

            const hasViewPermissions = hasViewPermissionsEnabled({
                role: userWorkspace.workspaceRole,
                roleSubtype: userWorkspace.workspaceRoleSubType,
                limitedMemberEnabled: this.roleService.isLimitedMemberEnabledForWorkspace(workspaceAccessInfo),
                hasIndividualPermissions: userWorkspace.guestPermissions?.can_create_views === true,
                hasCustomRoleEntitlement: entitlements.customRoles === true,
                customRolePermissionValue: userWorkspace.rolePermissions?.views,
            });

            const viewPermissions = this.permissionConfigRepository
                .getInverseWorkspaceRolePermissionsToCommonPermissionsMap()
                .get('views');

            for (const viewPermission of viewPermissions) {
                mergedAccessCheckResult.permissions.set(
                    viewPermission,
                    toPermissionLevelValue(hasHierarchyPermission && hasViewPermissions)
                );
                roleAccessCheckResult.permissions.delete(viewPermission);
            }
            return true;
        }

        // Non-guests get all basic guest permissions enabled
        for (const guestPermission of BasicGuestPermissions) {
            mergedAccessCheckResult.permissions.set(guestPermission, PermissionLevelValue.HasAccess);
            roleAccessCheckResult.permissions.delete(guestPermission);
        }
        return false;
    }

    protected handleCustomPermissionLevel(
        permissionLevel: PermissionLevel,
        customPermissionLevels: CustomPermissionLevelMap,
        accessInfo: AccessInfo,
        entitlements: AccessInfoEntitlements
    ): CustomPermissionLevel {
        const customPermissionLevel = customPermissionLevels?.get(permissionLevel.permissionLevel);

        // When custom permission level doesn't exist, is deleted, entitlement is false,
        // or feature flag is off, we fall back to base permission level
        if (
            !customPermissionLevel ||
            customPermissionLevel.deleted === true ||
            !entitlements?.customPermissionLevels ||
            !this.featureFlagService.isCustomPermissionLevelsEnabled(accessInfo.key.workspace_id)
        ) {
            // Fall back to base permission level
            if (customPermissionLevel?.basePermissionLevel) {
                permissionLevel.permissionLevel = customPermissionLevel.basePermissionLevel;
            } else {
                // If we get here for some reason an acl has a custom permission level that was deleted
                // and acls were not cleaned up properly
                permissionLevel.permissionLevel = HierarchyPermissionLevel.CAN_READ;
            }
            return undefined;
        }

        return customPermissionLevel;
    }

    /**
     * Merges an item and role permissions
     */
    protected mergeWithRolePermissions(
        resultMap: AccessCheckResult,
        roleMaps: AccessCheckResult,
        context: Parameters<typeof this.mergePermissionsAndApplyRules>[0]
    ): AccessCheckResult {
        if (!this.featureFlagService.useDefaultRolesPermissions(context.objectAccessInfo.key.workspace_id)) {
            return this.mergePermissionsWithStrategy(AccessCheckStrategy.Overwrite, [resultMap, roleMaps]);
        }

        const { objectAccessInfo, authzUserId, parentsInfo, request } = context;
        const rules = this.rules.get(objectAccessInfo.key.object_type);

        const rulesArgs = new AccessCheckRulesArgs(
            authzUserId,
            objectAccessInfo,
            context.workspaceAccessInfo,
            context.requestedPermissions,
            context.userWorkspace,
            context?.request?.prefetchedData?.entitlements,
            context.objectAccessCheckResult,
            resultMap,
            context.triggeredAccessCheckRules,
            request.options,
            parentsInfo,
            request.options?.originator
        );

        // Below logic is to make it compatible with legacy code, and to be  removed in future when we clear general logic how do we merge item and role.
        // Its full of hacks but this is just to reflect legacy logic and it covers a lot of different edge cases from legacy ,to resolve on buiness level of move to rules.

        const isItemIsChatView = isChatView((objectAccessInfo as ViewLeafAccessInfo).viewType);

        const type = objectAccessInfo.key.object_type;
        const { creator } = objectAccessInfo;
        const isCreator = creator === authzUserId;

        for (const [key, rolePermission] of roleMaps.permissions) {
            const hierarchyPermissionValue = resultMap.permissions?.get(key);
            if (rules?.mergeRolePermission) {
                const [applied, result] = rules.mergeRolePermission(
                    key,
                    hierarchyPermissionValue,
                    rolePermission,
                    rulesArgs
                );
                if (applied) {
                    if (result) {
                        resultMap.permissions.set(key, result);
                    }
                    continue;
                }
            }

            // special chat cases
            if (
                objectAccessInfo.key.object_type !== ObjectType.WORKSPACE &&
                ['chat_comment', 'comment'].includes(key) &&
                (hierarchyPermissionValue ?? PermissionLevelValue.NoAccess) === PermissionLevelValue.NoAccess
            ) {
                continue;
            }

            // case https://github.com/time-loop/clickup/blob/master/libs/utils/authorization/e2e/src/lib/authorization.chat-views.e2e.spec.ts#L142
            // this is case for member creator of chat view: it should not be able to delete comments having only "delete own permissions"
            if (
                isItemIsChatView &&
                key === 'can_delete_comments' &&
                [
                    PermissionLevelValue.HasAccessIfCreator,
                    PermissionLevelValue.HasAccessIfNotWorkspaceLevel,
                    PermissionLevelValue.HasAccessIfOwner,
                ].includes(rolePermission)
            ) {
                resultMap.permissions.set(key, PermissionLevelValue.NoAccess);
                continue;
            }

            // legacy behaviour
            if (rolePermission === PermissionLevelValue.NoAccess || rolePermission === PermissionLevelValue.HasAccess) {
                resultMap.permissions.set(key, rolePermission);
            }

            if (
                rolePermission === PermissionLevelValue.HasAccessIfNotWorkspaceLevel &&
                objectAccessInfo.key.object_type === ObjectType.WORKSPACE
            ) {
                resultMap.permissions.set(key, PermissionLevelValue.NoAccess);
            }

            // handling for create own permissions- looks too little but it covers all of our cases
            if ([ObjectType.FOLDER, ObjectType.LIST, ObjectType.PAGE].includes(type)) {
                if (key === 'delete') {
                    if (rolePermission === PermissionLevelValue.HasAccessIfCreator) {
                        resultMap.permissions.set(
                            key,
                            isCreator ? PermissionLevelValue.HasAccess : PermissionLevelValue.NoAccess
                        );
                    }
                }
            }
            // case when legacy viewAccess.js calls authz check for parent workspace for doc
            if (
                type === ObjectType.WORKSPACE &&
                request?.options?.originator?.key?.object_type === ObjectType.DOC &&
                key === 'delete' &&
                request?.options?.originator?.creator === authzUserId
            ) {
                resultMap.permissions.set(key, PermissionLevelValue.HasAccess);
            }
            if ([ObjectType.TASK].includes(type) && key === 'delete') {
                if (rolePermission === PermissionLevelValue.HasAccessIfCreator && !objectAccessInfo.deleted) {
                    resultMap.permissions.set(
                        key,
                        isCreator ? PermissionLevelValue.HasAccess : PermissionLevelValue.NoAccess
                    );
                }
            }
            if (
                // Logic from legacy: https://github.com/time-loop/clickup/blob/master/src/utils/access/tasksAccess.js#L879
                // Nasty as if custom Roles entitlement is not available then some permissions are disabled
                // But if entitlement is ON, then they are taken from role permissions which default is allows to do same
                // THis means that default members with Custom Roles On/OFF will behave differently
                type === ObjectType.TASK &&
                !context?.request?.prefetchedData?.entitlements?.customRoles &&
                ['can_convert_item', 'can_create_milestone'].includes(key) &&
                resultMap.level <= HierarchyPermissionLevel.CAN_COMMENT
            ) {
                resultMap.permissions.set(key, PermissionLevelValue.NoAccess);
            }
            if (
                // those are taken only when not set by hierarchy
                !resultMap.permissions?.has(key) &&
                ['can_edit_privacy', 'can_edit_project_settings', 'can_edit_space_settings'].includes(key)
            ) {
                resultMap.permissions.set(key, rolePermission);
            }
        }
        return resultMap;
    }

    protected appendValidFilterAccessTraceUser(
        trace: FilterAccessTrace | undefined,
        userId: number,
        reason: AuthorizationTraceReason
    ): FilterAccessRuleResult {
        appendFilterAccessTraceUser(trace, userId, reason);
        return [true, reason];
    }

    protected appendInvalidFilterAccessTraceUser(
        trace: FilterAccessTrace | undefined,
        userId: number,
        reason: AuthorizationTraceReason
    ): FilterAccessRuleResult {
        appendFilterAccessTraceUser(trace, userId, reason);
        return [false, reason];
    }

    private assertGuestSpacePermissionLevel(
        objectKey: ObjectKey,
        userWorkspace: UserWorkspaceAccessInfo,
        permissionLevel: PermissionLevel,
        workspaceAccessInfo: WorkspaceAccessInfo
    ): boolean {
        if (permissionLevel?.hierarchyDistance == null) {
            return true;
        }

        if (
            this.featureFlagService.preventExternalGuestSpaceAccess() &&
            isExternalGuest(userWorkspace?.workspaceRole, userWorkspace?.workspaceRoleSubType, {
                chargeForInternalGuests: workspaceAccessInfo?.plan?.chargeForInternalGuests,
                roleSubTypeConfig: this.featureFlagService.roleSubTypeConfig(workspaceAccessInfo?.workspaceId),
                chargeForLimitedMemberInGroups: this.featureFlagService.chargeForLimitedMemberInGroups(
                    workspaceAccessInfo?.workspaceId
                ),
            }) &&
            getObjectSpaceHierarchyDistance(objectKey.object_type) === permissionLevel?.hierarchyDistance
        ) {
            return false;
        }

        return true;
    }
}
