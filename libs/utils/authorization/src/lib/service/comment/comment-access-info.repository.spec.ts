import { ObjectVersionChangeEvent } from '@time-loop/ovm-object-version';

import { CommentDbUtils } from '@clickup/comment/db';
import { ConfigService } from '@clickup/utils/config';
import { DBClientReadOptions, DBClientWriteOptions } from '@clickup/utils/db';
import { DbQueryResult } from '@clickup-legacy/utils/interfaces/DbQueryResult';
import { SimpleClient, SimpleObjectVersionClient } from '@clickup-legacy/utils/interfaces/TransactionClient';

import { AccessInfoSimpleClient } from '../common/database-client';
import { AuthorizationFeatureFlagService } from '../common/feature-flags';
import { AuthorizationMetricsService } from '../common/metrics';
import { CommentAccessInfoRepository, CommentType } from './comment-access-info.repository';

function buildDbClient() {
    return {
        readAsyncFunction<T>(
            queryFunction: (simpleClient: SimpleClient) => Promise<T>,
            options?: DBClientReadOptions
        ): Promise<T> {
            return queryFunction(makeDbClient(null));
        },

        readAsync<T>(query: string, params?: any[], options?: DBClientReadOptions): Promise<DbQueryResult<T>> {
            return this.readAsyncFunction(simpleClient => simpleClient.queryAsync(query, params), options);
        },

        writeAsyncFunction<T>(
            queryFunction: (
                simpleClient: SimpleObjectVersionClient,
                changeEvents?: ObjectVersionChangeEvent[]
            ) => Promise<T>,
            options?: DBClientWriteOptions
        ): Promise<T> {
            return queryFunction(makeDbClient(null));
        },

        writeAsync<T>(
            query: string,
            params?: any[],
            changeEvents?: ObjectVersionChangeEvent[],
            options?: DBClientWriteOptions
        ): Promise<DbQueryResult<T>> {
            return this.writeAsyncFunction(
                simpleClient => simpleClient.queryAsync(query, params, changeEvents),
                options
            );
        },
    };
}

const dbClient = buildDbClient();
const commentsDbClient = buildDbClient();

const configService = {
    get: jest.fn().mockImplementation((key: string) => {
        if (key === 'db_minimums') {
            return {
                'task_mgmt.comments': '1000000000000',
                'task_mgmt.comment_ids': '1000000000000',
            };
        }
        return undefined;
    }),
} as unknown as ConfigService;

const commentDbUtils = {
    isCommentIdFromCommentsDb: jest.fn(),
};

const service = new CommentAccessInfoRepository(
    {
        shouldUseChatPermissions: jest.fn().mockReturnValue(true),
    } as unknown as AuthorizationFeatureFlagService,
    {
        metricsScope: async (_: any, fn: () => Promise<any>) => fn(),
    } as unknown as AuthorizationMetricsService,
    configService,
    dbClient,
    commentsDbClient,
    commentDbUtils as unknown as CommentDbUtils
);

function makeDbClient(type: CommentType) {
    return {
        queryAsync: async () => ({ rows: [{ type }] as any[] }),
    } as unknown as AccessInfoSimpleClient;
}

function entries(commentTypes: CommentType[]) {
    return Object.entries(CommentType).filter(([_, v]) => commentTypes.includes(Number(v))) as [string, CommentType][];
}

describe('CommentAccessInfoRepository', () => {
    it.each(
        entries([
            CommentType.COMMENT,
            CommentType.VIEW,
            CommentType.TASK,
            CommentType.SPACE,
            CommentType.FOLDER,
            CommentType.LIST,
            CommentType.DOC,
            CommentType.ATTACHMENT,
        ])
    )('Handles comment type [%s] (%s)', async (_, commentType) => {
        const spy = jest.spyOn(commentsDbClient, 'readAsyncFunction');
        commentDbUtils.isCommentIdFromCommentsDb.mockReturnValue(false);
        const result = await service.getAccessInfoForWorkspace(makeDbClient(commentType), 123, ['456']);
        expect(result.size).toBe(1);
        expect(spy).not.toBeCalled();
    });

    it('Empty results on unsupported comment type', async () => {
        commentDbUtils.isCommentIdFromCommentsDb.mockReturnValue(false);
        const result = await service.getAccessInfoForWorkspace(makeDbClient(null as CommentType), 123, ['456']);
        expect(result.size).toBe(0);
    });

    it('Handles query to new Aurora DB', async () => {
        const spy = jest.spyOn(commentsDbClient, 'readAsyncFunction');
        commentDbUtils.isCommentIdFromCommentsDb.mockReturnValue(true);
        await service.getAccessInfoForWorkspace(makeDbClient(CommentType.COMMENT), 123, ['456']);
        expect(spy).toBeCalled();
    });
});
