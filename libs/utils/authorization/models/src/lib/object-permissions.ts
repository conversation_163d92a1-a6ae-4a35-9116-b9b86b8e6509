import { ObjectKey, ObjectType } from '@time-loop/ovm-object-version';

import { ObjectKeyMap } from '@clickup/object-version-cache-common';
import { AttachmentType, FieldType, NamedAttachmentType, ViewVisibility } from '@clickup/utils/constants';

import { TriggeredAccessCheckRule } from './access-info';
import { AuthorizationTrace, AuthorizationTraceReason } from './authorization.trace';
import { CustomRoleId, WorkspaceId } from './ids';
import { HierarchyPermissionLevel, Permission } from './permission';
import { RoleKey, UserRole, UserRoleSubType, WorkspaceRolePermissions } from './role';
import { AccessCheckStrategy, TemplateAccessCheckStrategy } from './strategies';
import { ViewParentType, ViewType } from './views';

/**
 * Permission access check results for an object
 */
export interface ObjectPermissions {
    permissions: Map<Permission, boolean>;
    role?: UserRole;
    roleSubtype?: UserRoleSubType;
    roleKey?: RoleKey;
    customRole?: {
        id?: CustomRoleId;
        permissions?: WorkspaceRolePermissions;
    };
    level?: HierarchyPermissionLevel;
    defaultPermissionLevel?: HierarchyPermissionLevel;
    parents?: ObjectKeyMap<ObjectKey, HierarchyPermissionLevel>;
    parentAccessCheckStrategy?: AccessCheckStrategy;
    leafAccessCheckStrategy?: AccessCheckStrategy;
    templateAccessCheckStrategy?: TemplateAccessCheckStrategy;
    scope?: ObjectKey[];
    deleted?: boolean;
    private?: boolean;
    owner?: number;
    creator?: number;
    canRequestAccess?: boolean;
    parentsCanRequestAccess?: ObjectKeyMap<ObjectKey, boolean>;
    // TODO: remove plan, see https://staging.clickup.com/t/333/CLK-240293
    plan?: {
        workspaceId?: WorkspaceId;
        trialId?: number;
        effectiveId?: number;
    };
    //---------
    dateJoined?: number;
    // Field
    fieldType?: FieldType;
    // Attachment
    attachmentType?: AttachmentType;
    attachmentNamedType?: NamedAttachmentType;
    attachmentParentId?: string;
    attachmentParentObjectType?: ObjectType;
    // View
    underlyingViewId?: string;
    viewType?: ViewType;
    viewParentType?: ViewParentType;
    viewParentId?: string;
    viewParentObjectType?: ObjectType;
    viewVisibility?: ViewVisibility;
    visibleTo?: number;
    protected?: boolean;
    workspaceHasAccess?: boolean;
    // Misc
    error?: Error;
    triggeredAccessCheckRules?: Set<TriggeredAccessCheckRule>;
    trace?: AuthorizationTrace;
    traceReason?: AuthorizationTraceReason;
    legacy?: boolean;
}
