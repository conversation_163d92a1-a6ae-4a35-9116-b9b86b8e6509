import { CreateTableCommandInput } from '@aws-sdk/client-dynamodb';

// If updated here also update tools/localstack/user-service-dynamodb-bootstrap.sh
// CDK: user-service-cdk/src/access-requests-db.ts

export const tableNames = ['access-requests-test', 'access-requests-development'];

export function tableDefinition(tableName: string): CreateTableCommandInput {
    return {
        TableName: tableName,
        BillingMode: 'PAY_PER_REQUEST',
        AttributeDefinitions: [
            {
                AttributeName: 'id',
                AttributeType: 'S',
            },
            {
                AttributeName: 'objectKey',
                AttributeType: 'S',
            },
            {
                AttributeName: 'workspaceId',
                AttributeType: 'N',
            },
            {
                AttributeName: 'status',
                AttributeType: 'S',
            },
            {
                AttributeName: 'latestTimestamp',
                AttributeType: 'N',
            },
        ],
        KeySchema: [
            {
                AttributeName: 'id',
                KeyType: 'HASH',
            },
        ],
        GlobalSecondaryIndexes: [
            {
                IndexName: 'objectKey',
                KeySchema: [
                    {
                        AttributeName: 'objectKey',
                        KeyType: 'HASH',
                    },
                    {
                        AttributeName: 'id',
                        KeyType: 'RANGE',
                    },
                ],
                Projection: { ProjectionType: 'ALL' },
            },
            {
                IndexName: 'workspaceId',
                KeySchema: [
                    {
                        AttributeName: 'workspaceId',
                        KeyType: 'HASH',
                    },
                    {
                        AttributeName: 'id',
                        KeyType: 'RANGE',
                    },
                ],
                Projection: { ProjectionType: 'ALL' },
            },
            {
                IndexName: 'status',
                KeySchema: [
                    {
                        AttributeName: 'status',
                        KeyType: 'HASH',
                    },
                    {
                        AttributeName: 'latestTimestamp',
                        KeyType: 'RANGE',
                    },
                ],
                Projection: { ProjectionType: 'ALL' },
            },
        ],
    };
}
