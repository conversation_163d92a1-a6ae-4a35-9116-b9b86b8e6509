import { ObjectType } from '@time-loop/ovm-object-version';

import { TestDataObjectDefinition } from '../test-data.object.definition';
import { DefaultValuesFn, ObjectsFn } from '../test-data.types';

export class WorkspaceMembersDefinition extends TestDataObjectDefinition {
    public readonly table: string = 'task_mgmt.team_members';

    public readonly objects: ObjectsFn = workspace => workspace.members;

    public readonly defaultValues: DefaultValuesFn = (member, workspace, ids) => ({
        object_type: ObjectType.USER_ACCESS,
        name: member.user,
        userid: ids.get(member.user),
        team_id: workspace.id,
        invited_by: ids.get(member.invited_by_user),
        custom_role: ids.get(member.custom_role_name),
        manager: ids.get(member.manager),
        date_joined: 1679595234942, // otherwise member wont appear in the workspace
        // Need these two so we can have an ObjectKey created
        id: ids.get(member.user),
        workspace_id: workspace.id,
    });
}
