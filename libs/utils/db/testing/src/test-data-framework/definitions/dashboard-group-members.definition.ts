import { TestDataObjectDefinition } from '../test-data.object.definition';
import { DefaultValuesFn, ObjectsFn } from '../test-data.types';

export class DashboardGroupMembersDefinition extends TestDataObjectDefinition {
    public readonly table: string = 'task_mgmt.dashboard_group_members';

    public readonly objects: ObjectsFn = dashboard => dashboard.group_members;

    public readonly defaultValues: DefaultValuesFn = (member, dashboard, ids) => ({
        dashboard_id: dashboard.id,
        workspace_id: dashboard.workspace_id,
        group_id: ids.get(member.group),
    });
}
