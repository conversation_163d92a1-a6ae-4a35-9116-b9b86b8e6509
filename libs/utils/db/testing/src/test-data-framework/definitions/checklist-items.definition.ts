import { ObjectType } from '@time-loop/ovm-object-version';

import { TestDataObjectDefinition } from '../test-data.object.definition';
import { DefaultValuesFn, IdTypeMap, ObjectsFn } from '../test-data.types';

export class ChecklistItemsDefinition extends TestDataObjectDefinition {
    public readonly table: string = 'task_mgmt.checklist_items';

    public readonly idFields?: IdTypeMap = { id: 'uuid' };

    public readonly objects: ObjectsFn = checklist => checklist.checklist_items;

    public readonly defaultValues: DefaultValuesFn = (checklist_item, checklist, ids) => ({
        object_type: ObjectType.CHECKLIST_ITEM,
        workspace_id: checklist.workspace_id,
        deleted: checklist_item.deleted === true,
        creator: ids.get(checklist_item.creator),
        checklist_id: checklist.id,
    });
}
