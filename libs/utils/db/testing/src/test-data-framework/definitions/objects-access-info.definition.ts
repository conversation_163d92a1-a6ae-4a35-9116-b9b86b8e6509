import { TestDataObjectDefinition } from '../test-data.object.definition';
import { DefaultValuesFn, ObjectsFn } from '../test-data.types';

export class ObjectsAccessInfoDefinition extends TestDataObjectDefinition {
    public readonly table: string = 'task_mgmt.objects_access_info';

    public readonly objects: ObjectsFn = object => object.accessInfo;

    public readonly defaultValues: DefaultValuesFn = (accessInfo, object) => ({
        id: object.id,
        name: `${object.name}_access_info`,
        object_type: object.object_type,
        object_id: object.id,
        object_id_int: Number.isSafeInteger(Number(object.id)) ? Number(object.id) : null,
        workspace_id: object.workspace_id,
        default_permission_level: accessInfo.default_permission_level,
    });
}
