import type { DbQ<PERSON><PERSON>, DbQueryResult, QueryParams } from '@clickup/utils/db-types';

import * as db from '../db_legacy';

/**
 * Promise-based batch doc history aurora queries function
 */
export async function batchDocHistoryAuroraQueriesAsync(
    queries: { query: DbQuery; params?: QueryParams }[]
): Promise<DbQueryResult<any>[]> {
    return db.batchDocHistoryAuroraQueriesAsync(queries);
}

/**
 * Promise-based doc history Aurora read query
 */
export async function promiseDocHistoryAuroraReadQuery<T = any>(
    query: DbQuery,
    params?: QueryParams,
    options?: Record<string, any>
): Promise<DbQueryResult<T>> {
    return db.promiseDocHistoryAuroraReadQuery(query, params, options);
}

/**
 * Promise-based doc history Aurora write query
 */
export async function promiseDocHistoryAuroraWriteQuery<T = any>(
    query: Db<PERSON><PERSON><PERSON>,
    params?: QueryParams,
    options?: Record<string, any>
): Promise<DbQueryResult<T>> {
    return db.promiseDocHistoryAuroraWriteQuery(query, params, options);
}
