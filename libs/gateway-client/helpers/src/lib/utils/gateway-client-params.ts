export function isDate(v: string | null | undefined): boolean {
    if (v === null || v === undefined) {
        return false;
    }
    return !Number.isNaN(new Date(v).getTime());
}

// Pattern to match ${encodeURIComponent(String(...))} with potential method calls
const URL_TEMPLATE_REGEX = /\${encodeURIComponent\(String\((.*?)(?:\.(.*?)\(\))?\)\)}/g;

/**
 * Resolves a URL template by simplifying complex parameter patterns coming from the OpenAPI generator path.
 *
 * This function takes URL templates with patterns like:
 * ${encodeURIComponent(String(parameter))} or ${encodeURIComponent(String(parameter.methodCall()))}
 * and converts them to a simplified form: {parameter}
 *
 * @param urlTemplate - The URL template string to process
 * @returns A simplified URL template with clean parameter references
 *
 * @example
 * // Returns "/ui/v3/workspaces/{workspace_id}/forms/{form_id}"
 * resolveUrlTemplate('/ui/v3/workspaces/${encodeURIComponent(String(workspace_id))}/forms/${encodeURIComponent(String(form_id))}');
 *
 * @example
 * // Returns "/data/v3/workspaces/{workspace_id}/whiteboards/{whiteboard_id}/snapshots/{timestamp}"
 * resolveUrlTemplate('/data/v3/workspaces/${encodeURIComponent(String(workspace_id))}/whiteboards/${encodeURIComponent(String(whiteboard_id))}/snapshots/${encodeURIComponent(String(timestamp.toISOString()))}');
 */
export function resolveUrlTemplate(urlTemplate: string): string {
    // Replace all matches with simplified parameter references (without the $ sign)
    return urlTemplate.replace(URL_TEMPLATE_REGEX, (_, paramName) => {
        // If parameter contains a dot (indicating a method call), extract just the variable name
        const cleanParamName = paramName.split('.')[0];
        return `:${cleanParamName}`;
    });
}
