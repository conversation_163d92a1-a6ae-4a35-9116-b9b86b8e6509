/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ******************************** } from './********************************';


export interface InboxPushNotificationLog { 
    platform: ********************************;
    /**
     * The Datadog trace ID in the push notification payload
     */
    trace_id: string;
    /**
     * The uuid in the push notification payload
     */
    uuid: string;
    /**
     * The notification ID in the push notification payload
     */
    notification_id: string;
    /**
     * The message to log
     */
    msg: string;
    /**
     * The name of the metric to increment
     */
    metric: string;
    /**
     * The timestamp in milliseconds
     */
    timestamp: number;
    /**
     * The extra fields to log
     */
    props: object;
}



