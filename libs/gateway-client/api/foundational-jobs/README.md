# Foundational Jobs API

## Overview

The Foundational Jobs API is a NestJS module that provides a type-safe Foundational Jobs Client to make service-to-service calls through the API Gateway
that route to the corresponding Foundational Jobs API.
This client handles authentication, configuration, and provides a simple interface for making requests to the Foundational Jobs API endpoints.

## Quickstart

### Installation

To use the Foundational Jobs API client in your NestJS application, import the module from:

```typescript
import { FoundationalJobsApiModule, FoundationalJobsApi } from '@clickup/gateway-client/api/foundational-jobs';
```

### Basic Usage

Simply add the module to your NestJS module imports:

```typescript
@Module({
    imports: [
        FoundationalJobsApiModule,
        // other imports
    ],
    providers: [YourService],
})
export class YourModule {}
```

Then inject the API client in your service:

```typescript
@Injectable()
export class YourService {
    constructor(private readonly foundationalJobsApi: FoundationalJobsApi) {}
}
```

## Local Development

To test using the `FoundationalJobsApi` client locally,

1. Serve the gateway-service: `pnpm nx serve gateway-service`
2. Serve both services in the service to service request for the client: `pnpm nx serve {service}`
3. Execute the request locally, in the `gateway-service` logs, you will see the request routing through the gateway

## `FoundationalJobsApi` Reference

To see the available methods within `FoundationalJobsApi`, see the [FoundationalJobsApi Reference](./src/generated/api.reference.md).

## Configuration

The Foundational Jobs API client can be configured with various options to customize its behavior:

### Default Configuration

When using the module without explicit configuration, it uses sensible defaults:

-   Automatically determines the base URL based on the environment
-   Sets up proper headers for service identification

### Custom Configuration

#### Using register() method

For static configuration, use the `register()` method:

```typescript
@Module({
    imports: [
        FoundationalJobsApiModule.register({
            bearerToken: 'your-static-token',
            headers: {
                'X-Custom-Header': 'custom-value',
            },
            retryConfig: {
                retries: 3,
                retryDelay: axiosRetry.exponentialDelay,
            },
            timeout: 5000,
        }),
        // other imports
    ],
})
export class YourModule {}
```

#### Using registerAsync() method

For dynamic configuration that depends on other services, use the `registerAsync()` method:

```typescript
@Module({
    imports: [
        FoundationalJobsApiModule.registerAsync({
            imports: [UtilsConfigModule],
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => ({
                bearerToken: () => configService.get<string>('API_TOKEN'),
                retryConfig: {
                    retries: configService.get<number>('API_RETRY_COUNT'),
                    retryDelay: axiosRetry.exponentialDelay,
                    retryCondition: error =>
                        axiosRetry.isNetworkError(error) ||
                        error.response?.status === 429 ||
                        error.response?.status >= 500,
                },
                timeout: configService.get<number>('API_TIMEOUT'),
            }),
        }),
        // other imports
    ],
})
export class YourModule {}
```

#### Request-Level Configuration

In addition to module-level configuration, you can also pass configuration options for individual API requests:

```typescript
// Example of using request-level configuration
await foundationalJobsApi.someEndpoint({
    { /* your foundationalJobsApi.someEndpoint input parameters  */ },
    {
        // Clickup-specific configuration
        backendVariant: 'internal', // Target a specific backend variant for this request
        bearerToken: 'request-specific-token', // Override auth token for this request only
        // Axios Request configuration
        config: {
            headers: {
                'Authorization': 'Bearer custom-auth-token' // Alternative way to set auth
                'X-Custom-Header': 'value', // Add custom headers for this request
            }
        }
        retryConfig: { // Set axios-retry configuration
            retries: 2,
        }
    }
});
```

### Available Configuration Options

The Gateway client provides the following options at the - `Request-level`: per request configuration, configuration applied to specific requests - `Module-level`: during Module registration and applied to all requests

#### Configuration Options

-   `backendVariant` (Module-level, Request-level): Specifies a backend variant to target when making requests through the internal API Gateway
    -   When specified (e.g., `internal`), requests will be forwarded to that variant of the service (e.g., `internal-my-service` instead of `my-service`)
    -   This enables traffic segregation by allowing you to target separate instances of services for different purposes
-   `bearerToken` (Module-level, Request-level): Token used for authenticating requests
    -   Can be a string, a Promise<string>, or a function returning string/Promise<string>
-   `config` (Request-level) or `root-level attributes` (Module-level): HttpModuleOptions for axios request configuration
    -   `headers`: Custom headers to send with each request
    -   `maxRedirects`: Maximum number of redirects to follow
    -   `timeout`: Request timeout in milliseconds
    -   Other standard Axios request configuration options
-   `interceptors` (Module-level): Intercept the request and perform transformations before and after the request
-   `retryConfig` (Module-level, Request-level): Configuration for the axios-retry integration
    -   `retries`: Number of retry attempts
    -   `retryDelay`: Function to determine delay between retries
    -   `retryCondition`: Function to determine when to retry a request
    -   Other axios-retry configuration options

#### Request Configuration Priority

When multiple configuration sources are used, they are applied with the following precedence (highest to lowest):

1. Request-level options, for example: the `headers` object in the request config
2. Module-level configuration (from `register()` or `registerAsync()`)
3. Default configuration

This allows flexible control over authentication and routing on a per-request basis.

## More Information

For more detailed information on using Gateway API clients, please refer to the [Gateway Client documentation](https://app.clickup-stg.com/333/v/dc/ad-1649861/ad-4695945).
