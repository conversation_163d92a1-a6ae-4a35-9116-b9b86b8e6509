/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

# API Reference Documentation

This document provides a reference of all available API operations generated from the OpenAPI specification.

> **Note:** For a complete per-service API reference, visit the [Internal API Catalog in Backstage](https://backstage.clickup-internal.com/catalog/default/api/clickup-private-internal-api/definition).


## Available API Operations

| Class | Operation Signature | Description |
|-------|-----------|-------------|
| [PageExportsApi](./api/pageExportsApi.ts) | `getPageExportsWorkspaceAuthzToken(request: PageExportsApiGetPageExportsWorkspaceAuthzTokenRequest, options?: AxiosRequestConfig): Promise<void>` | Tokenize a workspace page exports Cloudfront URL |

