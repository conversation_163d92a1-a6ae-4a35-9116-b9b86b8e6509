/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UserAudit } from './userAudit';


export interface UserWorkspaceMembershipInviteDetails { 
    /**
     * True if the user still has a pending invitation to the workspace.
     */
    pending: boolean;
    /**
     * The UNIX timestamp when an invitation was accepted.
     */
    accepted_on: number;
    /**
     * True if the workspace invitation was sent.
     */
    sent: boolean;
    /**
     * True if the user was invited to the workspace via SCIM.
     */
    via_scim: boolean;
    /**
     * True if the invitation was sent.
     */
    first_reminder_sent: boolean;
    /**
     * The UNIX timestamp when an invitation was last sent on, if any.
     */
    last_sent_on: object;
    /**
     * Invitation audit data.
     */
    audit: UserAudit;
}

