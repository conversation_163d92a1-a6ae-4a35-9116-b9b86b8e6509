/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The versioned object\'s type.
 */
export enum UserObjectVersionVectorResponseObjectType {
    Attachment = 'attachment',
    AttachmentAccess = 'attachmentAccess',
    Approval = 'approval',
    BanWorkspace = 'banWorkspace',
    Checklist = 'checklist',
    ChecklistItem = 'checklistItem',
    ChecklistTemplateAccess = 'checklistTemplateAccess',
    Comment = 'comment',
    CommentsLastReadAt = 'commentsLastReadAt',
    CustomField = 'customField',
    CustomFieldAccess = 'customFieldAccess',
    CustomItem = 'customItem',
    CustomPermissionLevel = 'customPermissionLevel',
    Dashboard = 'dashboard',
    DashboardAccess = 'dashboardAccess',
    Doc = 'doc',
    DocAccess = 'docAccess',
    Folder = 'folder',
    FolderDescendantsSet = 'folderDescendantsSet',
    FolderTemplateAccess = 'folderTemplateAccess',
    Form = 'form',
    FormulaValue = 'formulaValue',
    FoundationalJob = 'foundationalJob',
    Goal = 'goal',
    GoalAccess = 'goalAccess',
    GoalFolder = 'goalFolder',
    GoalFolderAccess = 'goalFolderAccess',
    Hierarchy = 'hierarchy',
    List = 'list',
    ListDescendantsSet = 'listDescendantsSet',
    ListDescendantsPoints = 'listDescendantsPoints',
    ListDescendantsTimeEstimates = 'listDescendantsTimeEstimates',
    ListTemplateAccess = 'listTemplateAccess',
    Notepad = 'notepad',
    Page = 'page',
    PageAccess = 'pageAccess',
    Post = 'post',
    Reminder = 'reminder',
    ReminderAccess = 'reminderAccess',
    RolledUpFieldValue = 'rolledUpFieldValue',
    ScheduledComment = 'scheduledComment',
    Space = 'space',
    SpaceDescendantsSet = 'spaceDescendantsSet',
    SpaceTemplateAccess = 'spaceTemplateAccess',
    Task = 'task',
    TaskAccess = 'taskAccess',
    TaskHistory = 'taskHistory',
    TaskProperty = 'taskProperty',
    TaskTemplateAccess = 'taskTemplateAccess',
    Template = 'template',
    User = 'user',
    UserAccess = 'userAccess',
    UserGroup = 'userGroup',
    UserHierarchy = 'userHierarchy',
    UserPresence = 'userPresence',
    View = 'view',
    ViewAccess = 'viewAccess',
    ViewTemplateAccess = 'viewTemplateAccess',
    Whiteboard = 'whiteboard',
    WhiteboardAccess = 'whiteboardAccess',
    Widget = 'widget',
    Workspace = 'workspace',
    WorkspaceDescendantsSet = 'workspaceDescendantsSet',
    WorkscheduleWorkweekSchedule = 'workscheduleWorkweekSchedule',
    WorkscheduleScheduleExceptions = 'workscheduleScheduleExceptions'
}

