/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { UserObjectVersionVectorResponseObjectType } from './userObjectVersionVectorResponseObjectType';
import { UserObjectVersionResponse } from './userObjectVersionResponse';


export interface UserObjectVersionVectorResponse { 
    object_type: UserObjectVersionVectorResponseObjectType;
    /**
     * The object\'s id.
     */
    object_id: string;
    /**
     * The object\'s workspace id.
     */
    workspace_id: number;
    /**
     * The object\'s version vectors.
     */
    vector: Array<UserObjectVersionResponse>;
}



