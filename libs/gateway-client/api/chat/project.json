{"name": "gateway-client-typescript-nestjs-api-chat", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/gateway-client/api/chat/src", "projectType": "library", "tags": ["gateway-client"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/gateway-client/api/chat/jest.config.ts"}}}}