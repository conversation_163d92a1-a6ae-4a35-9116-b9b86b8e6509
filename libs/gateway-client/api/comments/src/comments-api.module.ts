// THIS FILE IS AUTO-GENERATED from the @clickup/nx-plugin-gateway:clients generator DO NOT EDIT
import { HttpModule, HttpService } from '@nestjs/axios';
import { DynamicModule, Module } from '@nestjs/common';
import axios from 'axios';
import axiosRetry from 'axios-retry';

import {
    GatewayClientAsyncModuleOptions,
    GatewayClientConfigurationFactoryModule,
    GatewayClientHttpConfigurationFactory,
    GatewayClientModuleOptions,
} from '@clickup/gateway-client/configuration';
import { UtilsConfigModule } from '@clickup/utils/config';
import { UtilsMetricsModule } from '@clickup/utils/metrics';

import { Configuration, CommentsApi } from './generated';

const GATEWAY_CLIENT_MODULE_OPTIONS = Symbol('GATEWAY_CLIENT_CONFIG_OPTIONS');

@Module({
    imports: [HttpModule, UtilsConfigModule, UtilsMetricsModule, GatewayClientConfigurationFactoryModule],
    providers: [
        {
            provide: Configuration,
            inject: [GatewayClientHttpConfigurationFactory],
            useFactory: (httpOptionsFactory: GatewayClientHttpConfigurationFactory) => {
                const httpOptions = httpOptionsFactory.createHttpOptions();
                const axiosInstance = axios.create(httpOptions);
                // default apply axiosRetry so we can configure per-request retries as a default feature
                axiosRetry(axiosInstance, {
                    retries: 0, // Default: no retries
                    retryDelay: axiosRetry.exponentialDelay,
                });
                const httpClient = new HttpService(axiosInstance);
                return new Configuration({
                    basePath: httpOptions.baseURL,
                    httpClient,
                });
            },
        },
        CommentsApi,
    ],
    exports: [CommentsApi],
})
export class CommentsApiModule {
    public static register(options: GatewayClientModuleOptions): DynamicModule {
        return {
            imports: [GatewayClientConfigurationFactoryModule],
            module: CommentsApiModule,
            providers: [
                {
                    provide: Configuration,
                    inject: [GatewayClientHttpConfigurationFactory],
                    useFactory: (httpOptionsFactory: GatewayClientHttpConfigurationFactory) =>
                        this.useConfigurationFactory(options, httpOptionsFactory),
                },
                CommentsApi,
            ],
            exports: [CommentsApi],
        };
    }

    public static registerAsync(asyncOptions: GatewayClientAsyncModuleOptions): DynamicModule {
        return {
            imports: [GatewayClientConfigurationFactoryModule, ...(asyncOptions.imports ?? [])],
            module: CommentsApiModule,
            providers: [
                ...(asyncOptions.extraProviders ?? []),
                {
                    provide: GATEWAY_CLIENT_MODULE_OPTIONS,
                    useFactory: asyncOptions.useFactory,
                    inject: asyncOptions.inject ?? [],
                },
                {
                    provide: Configuration,
                    inject: [GATEWAY_CLIENT_MODULE_OPTIONS, GatewayClientHttpConfigurationFactory],
                    useFactory: (
                        options: GatewayClientModuleOptions,
                        httpOptionsFactory: GatewayClientHttpConfigurationFactory
                    ) => this.useConfigurationFactory(options, httpOptionsFactory),
                },
            ],
        };
    }

    private static useConfigurationFactory(
        options: GatewayClientModuleOptions,
        httpOptionsFactory: GatewayClientHttpConfigurationFactory
    ) {
        const httpOptions = httpOptionsFactory.createHttpOptions(options);
        const axiosInstance = axios.create(httpOptions);
        if (options.retryConfig) {
            axiosRetry(axiosInstance, options.retryConfig);
        } else {
            // default apply axiosRetry so we can configure per-request retries as a default feature
            axiosRetry(axiosInstance, {
                retries: 0, // Default: no retries
                retryDelay: axiosRetry.exponentialDelay,
            });
        }
        if (options.interceptors) {
            options.interceptors?.request?.forEach(interceptor => {
                axiosInstance.interceptors.request.use(interceptor);
            });
        }
        const httpClient = new HttpService(axiosInstance);
        return new Configuration({
            accessToken: options?.bearerToken,
            basePath: httpOptions.baseURL,
            httpClient,
        });
    }
}
