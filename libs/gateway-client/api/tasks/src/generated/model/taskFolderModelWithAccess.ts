/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TaskHierarchyStatus } from './taskHierarchyStatus';
import { TaskFolderModelWithAccessCategoryType } from './taskFolderModelWithAccessCategoryType';
import { TaskPriority } from './taskPriority';
import { TaskFolderSprintSettings } from './taskFolderSprintSettings';


export interface TaskFolderModelWithAccess { 
    id: number;
    project_id: number;
    name: string;
    orderindex: number;
    boardindex: number;
    archived: boolean;
    deleted: boolean;
    date_deleted: number;
    import_id: string;
    import_uuid: string;
    importing: boolean;
    override_statuses: boolean;
    template: boolean;
    content: string;
    color: string;
    due_date: number;
    due_date_time: boolean;
    'private': boolean;
    owner: number;
    creator: number;
    hidden: boolean;
    deleted_by: number;
    team_id: number;
    permissions: number;
    public_sharing: boolean;
    template_field_ids: string;
    permanent_template_id: string;
    sprint: boolean;
    sprint_settings: TaskFolderSprintSettings;
    date_updated: number;
    date_created: number;
    status: TaskHierarchyStatus;
    priority: TaskPriority;
    workspace_id: number;
    assignee: number;
    start_date: number;
    start_date_time: boolean;
    parent_id?: number;
    parent_type?: number;
    subfolder: boolean;
    category_type?: TaskFolderModelWithAccessCategoryType | null;
    access: boolean;
}



