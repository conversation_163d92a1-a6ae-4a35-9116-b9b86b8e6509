/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ClipsPublicClipsUserRto } from './clipsPublicClipsUserRto';


export interface ClipsPublicTranscoderJobRto { 
    id: string;
    url: string;
    extension: string;
    title: string;
    created_date: string;
    user?: ClipsPublicClipsUserRto;
    dynamic_manifest_url?: string;
}

