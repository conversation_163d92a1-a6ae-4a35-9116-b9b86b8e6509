/**
 * ClickUp Private Internal API
 * This API is NOT exposed to the public internet and can only be accessed via service-to-service calls.
 *
 * The version of the OpenAPI document: version
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * The workspace role subtype being requested. Undefined if the user already belongs to the workspace.
 */
export enum UserAccessRequestResponseRoleSubtype {
    None = 'NONE',
    GuestExternalReadonly = 'GUEST_EXTERNAL_READONLY',
    GuestExternal = 'GUEST_EXTERNAL',
    GuestLimitedMemberReadonly = 'GUEST_LIMITED_MEMBER_READONLY',
    GuestLimitedMember = 'GUEST_LIMITED_MEMBER',
    _0 = '0',
    _1 = '1',
    _2 = '2',
    _3 = '3',
    _4 = '4'
}

