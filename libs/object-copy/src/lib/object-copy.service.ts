/* eslint-disable max-classes-per-file */
import { Injectable } from '@nestjs/common';
import { values } from 'lodash';

import { SdChangeDetectorService } from '@clickup/sd/change-detector';
import { AsyncStorage } from '@clickup/shared/utils-async-storage';
import type { CopyContext } from '@clickup/shared/utils-async-storage-types';

import { CopiedObjectReferenceRemapper, CopiedObjectRemappingContext } from './utils/copied-object-remapping';
import { CopyOperationType } from './utils/copy-operation-type';
import { CopyableObjectKey, ObjectType } from './utils/copyable-object-key';

export interface CopyOptions {
    userId: number;
    operationType?: CopyOperationType;
    sourceWorkspaceId?: number;
    sourceShardId?: string;
    targetWorkspaceId?: number;
}

export interface CopyOperationResult<T> {
    result: T;
    sourceObjectKey: CopyableObjectKey;
    copiedObjectKey: CopyableObjectKey;
    data?: any;
}

/**
 * Interface for an object copier which can copy objects of a specific type.
 */
export abstract class ObjectCopier<T, TInput, TCopyOptions extends CopyOptions> {
    /**
     * Types of objects that this copier can copy. Typically, this will be a single type and its complementing template
     * type.
     */
    abstract supportedTypes: ObjectType[];

    /**
     * Given an input object, copy it to a new object of the given type
     * @param input - Input object to copy
     * @param options - Copy options (e.g. target workspace ID, location, new object name, etc.)
     */
    abstract copyFromInput(input: TInput, options: TCopyOptions & CopyOptions): Promise<CopyOperationResult<T>>;

    /**
     * Returns the object with the given ID as a copy input
     * @param type - Type of the object
     * @param id - ID of the object
     * @param options - User request options
     */
    abstract getObjectCopyInputFromId(
        type: ObjectType,
        id: string,
        options: TCopyOptions & CopyOptions
    ): Promise<TInput>;
}

/**
 * Object copy service which handles delegating copy operations to registered copiers
 *
 * Each domain can register a copier which implements the ObjectCopier interface for one or more supported types.
 * When a copy operation is requested, the service will delegate to the appropriate copier based on the type of the
 * object being copied.
 */
@Injectable()
export class ObjectCopyService {
    private copiers: ObjectCopier<any, any, any>[] = [];

    private copierByType: Map<ObjectType, ObjectCopier<any, any, any>> = new Map();

    constructor(private sdChangeDetectorService: SdChangeDetectorService) {}

    registerCopier(copier: ObjectCopier<any, any, any>) {
        for (const type of copier.supportedTypes) {
            if (this.copierByType.has(type)) {
                throw new Error(`Copier already registered for type ${type}`);
            }
            this.copierByType.set(type, copier);
        }
        this.copiers.push(copier);
    }

    async copyObject<T>(type: ObjectType, input: any, options: any): Promise<T> {
        const copier = this.copierByType.get(type);
        if (!copier) {
            throw new Error(`No copier registered for type ${type}`);
        }
        const { result, sourceObjectKey, copiedObjectKey, data } = await copier.copyFromInput(input, options);
        this.currentCopyOperationContext?.reportObjectCopied(sourceObjectKey, copiedObjectKey, data);
        return result;
    }

    /**
     * Copy an object from the given ID
     * @param type - Type of the object
     * @param id - ID of the object
     * @param options - User request / copy options
     */
    async copyObjectFromId<T>(type: ObjectType, id: string, options: any): Promise<T> {
        const input = await this.getObjectCopyInputFromId(type, id, options);
        return this.copyObject(type, input, options);
    }

    /**
     * Returns the object with the given ID as a copy input
     * @param type - Type of the object
     * @param id - ID of the object
     * @param options - User request options
     */
    async getObjectCopyInputFromId<T>(type: ObjectType, id: string, options: any): Promise<T> {
        const copier = this.copierByType.get(type);
        if (!copier) {
            throw new Error(`No copier registered for type ${type}`);
        }
        return copier.getObjectCopyInputFromId(type, id, options);
    }

    /**
     * Called at the end of a copy operation, giving copiers an opportunity to remap any internal references to objects
     * that were copied as part of an operation.  We need to do this at the end of a copy operation so we can have a
     * complete mapping of source->destination IDs, since some internal references may point to objects that were also
     * copied as part of the operation (e.g. a document that references another document in the same folder that was
     * copied)
     * @param context - Context to get mapping of source->destination IDs and other info needed to remap references
     */
    async remapReferences(
        context: Pick<CopiedObjectRemappingContext, 'getIdMappingByType' | 'copiedObjectKeysByOriginalKey'>
    ): Promise<void> {
        const { userId, sourceWorkspaceId, targetWorkspaceId, operationType } = this.currentCopyOperationContext ?? {};
        const remappingContext: CopiedObjectRemappingContext = {
            userId,
            sourceWorkspaceId,
            targetWorkspaceId: targetWorkspaceId ?? sourceWorkspaceId,
            operationType: operationType as CopyOperationType,
            ...context,
        };
        for (const copier of this.copiers) {
            const referenceRemapper = copier as unknown as CopiedObjectReferenceRemapper;
            if (typeof referenceRemapper.remapReferences === 'function') {
                await referenceRemapper.remapReferences(remappingContext);
            }
        }

        await this.loadSdChangeDetectorService();
        values(context.getIdMappingByType(ObjectType.VIEW)).forEach(viewId =>
            this.sdChangeDetectorService.reportViewChanged(viewId)
        );
    }

    protected get currentCopyOperationContext() {
        return AsyncStorage.getInstance().getContext()?.copyContext as CopyContext;
    }

    /**
     * object-copy is used within NestJS services as well as outside within the monolith.
     * We rely on dependency injection when it is used within the NestJS services,
     * but can't do so when it is used outside of NestJS (e.g. in the monolith).
     * We use this method to dynamically import the SdChangeDetectorService and
     * instantiate it so we can use it outside of NestJS.
     */
    private async loadSdChangeDetectorService() {
        if (!this.sdChangeDetectorService) {
            const hooks = await import('@clickup-legacy/models/elastic/services/change-detector/hooks');
            this.sdChangeDetectorService = new SdChangeDetectorService(hooks);
        }
    }
}
