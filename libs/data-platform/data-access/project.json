{"name": "data-platform-data-access", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/data-platform/data-access/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/data-platform/data-access"], "options": {"jestConfig": "libs/data-platform/data-access/jest.config.ts"}}}, "tags": []}