import type { Pool } from 'pg';

import type { HostIdentifier } from './HostIdentifier';

/**
 * Note: The `query()` method is omitted from the IExtendedPool to enforce that all queries are made explicitly through
 * a client returned from the `connect()` method. This allows us to more easily wrap the client to intercept
 * queries as needed.
 */
export interface IExtendedPool extends Omit<Pool, 'query'> {
    readonly maxClients: number;
    readonly masterId: number;
    readonly hostIdentifier: HostIdentifier;
    readonly isModernShardPool: boolean;
    readonly dbPoolHostMetricLabel: string;

    /**
     * Returns the clients available. If the number goes negative, it means the pool is too saturated.
     */
    get calculatedAvailableClients(): number;

    /**
     * Returns the available clients able to be used. This number will not go below 0.
     */
    get availableClients(): number;

    /**
     * Returns the number of clients waiting for a client to be released.
     */
    get queuedClients(): number;
}
