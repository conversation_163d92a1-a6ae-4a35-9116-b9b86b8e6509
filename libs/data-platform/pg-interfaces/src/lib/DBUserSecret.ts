import type { DBSecret } from '@clickup/utils/config-types';

/**
 * This data structure is used to store database connection information as the value of an AWS secret manager (ASM)
 * secret. Not all fields are useful for application purposes as some of the values are used in order to
 * perform secret rotation
 *
 * @see ${@link DBSecret}
 */
export interface DBUserSecret
    extends Partial<Pick<DBSecret, 'host' | 'port' | 'username' | 'password' | 'dbname' | 'ssl'>> {
    /**
     * The name of the database engine, always 'postgres' and *NOT* used by the application and should be ignored.
     */
    engine?: string;

    /**
     * An AWS ARN for the master password into the database cluster.  This is *NOT* used by the application
     * and should be ignored.
     */
    masterarn?: string;

    /**
     * A list of roles use during secret rotation to assign the new username to.  This is *NOT* used by the application
     * and should be ignored.
     */
    roles?: string[];

    /**
     * A username prefix used during secret rotation to create new usernames.  This is *NOT* used by the application
     * and should be ignored.
     */
    username_prefix?: string;
}
