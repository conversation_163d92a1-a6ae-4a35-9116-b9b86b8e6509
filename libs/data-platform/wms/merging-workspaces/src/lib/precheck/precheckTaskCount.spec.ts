import type { IBasicDBClient } from '@clickup/data-platform/wms/basic-db-client';

import { PrecheckTaskCount } from './precheckTaskCount';

describe('precheckTaskCount', () => {
    const mockedBasicDBClient: IBasicDBClient = {
        read: jest.fn(),
        write: jest.fn(),
    } as unknown as IBasicDBClient;

    const precheck = new PrecheckTaskCount(mockedBasicDBClient);

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('precheckTaskCount', () => {
        it('should return small workspace', async () => {
            const mockData = [{ count: 10 }];

            (mockedBasicDBClient.read as jest.Mock).mockResolvedValueOnce(mockData);

            const result = await precheck.checkTaskCount(1);

            // Check same workspace
            expect(mockedBasicDBClient.read).toHaveBeenCalledWith(expect.any(String), [1]);
            expect(result).toEqual({ count: 10, size: 'small' });
        });

        it('should return medium workspace', async () => {
            const mockData = [{ count: 25000 }];

            (mockedBasicDBClient.read as jest.Mock).mockResolvedValueOnce(mockData);

            const result = await precheck.checkTaskCount(1);

            // Check same workspace
            expect(mockedBasicDBClient.read).toHaveBeenCalledWith(expect.any(String), [1]);
            expect(result).toEqual({ count: 25000, size: 'medium' });
        });

        it('should return large workspace', async () => {
            const mockData = [{ count: 50000 }];

            (mockedBasicDBClient.read as jest.Mock).mockResolvedValueOnce(mockData);

            const result = await precheck.checkTaskCount(1);

            // Check same workspace
            expect(mockedBasicDBClient.read).toHaveBeenCalledWith(expect.any(String), [1]);
            expect(result).toEqual({ count: 50000, size: 'large' });
        });

        it('should return xlarge workspace', async () => {
            const mockData = [{ count: 100000 }];

            (mockedBasicDBClient.read as jest.Mock).mockResolvedValueOnce(mockData);

            const result = await precheck.checkTaskCount(1);

            // Check same workspace
            expect(mockedBasicDBClient.read).toHaveBeenCalledWith(expect.any(String), [1]);
            expect(result).toEqual({ count: 100000, size: 'xlarge' });
        });

        it('should throw an error if no workspace count is returned', async () => {
            const mockData: number[] = [];

            (mockedBasicDBClient.read as jest.Mock).mockResolvedValueOnce(mockData);

            await expect(precheck.checkTaskCount(1)).rejects.toThrow('Returned no workspace count.');
        });
    });
});
