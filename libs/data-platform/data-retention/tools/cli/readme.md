# ClickUp Data Retention CLI

This CLI automates the scaffolding process for adding **Data Retention** support to any feature/entity inside the `data-retention` module.

---

## Usage

From the root of the project, run:

```
pnpm add-data-retention:cli
```

You will be prompted to provide:

-   Feature name (e.g. `chat`)
-   Entity name (e.g. `chat_comments`)
-   Namespace (from libs/temporal/shared-types/src/lib/constants.ts `Namespace` enum)
-   Action (e.g. `delete`, `archive`)
-   ClickUp Task ID

## What It Does

1. Creates files under: libs/data-platform/data-retention/src/lib/features/{feature}/{entity}/

    - `*-condition-mapper.ts`
    - `*-resource-discoverer.ts`
    - `*-<action>-provider.ts`
    - `*-retention-rule-precedence-provider.ts` (if feature is new)

2. Updates or creates:

    - `<feature>-retention.module.ts`
    - Enums in `retention-rule.enum.ts`
    - Namespace map in `constants.ts`
    - Feature module registry in `module-registry.ts`

3. Lints all generated files using ESLint.

4. Creates unit test for all generated files.

5. Commits changes to a new Git branch: {feature}\_data_retention
6. Optionally opens a PR via GitHub CLI.

## Tests

For every generated class, a corresponding `.spec.ts` file is scaffolded

You can write unit tests there as needed.

## Structure

All CLI code lives under: libs/data-platform/data-retention/tools/cli/src
