import type { Request, Response } from 'express';

import { AsyncStorage } from '@clickup/shared/utils-async-storage';
import { getLogger } from '@clickup/shared/utils-logging';
import { databaseConfig } from '@clickup-legacy/config/domains/databaseConfig';
import { CURRENT_SHARD_ID } from '@clickup-legacy/config/domains/shardingConfig';
import type { ShardIdConfig } from '@clickup-legacy/config/interfaces/ShardingConfig';
import { metricsClient } from '@clickup-legacy/metrics/metricsClient';
import type { WebhookIntegrationType } from '@clickup-legacy/models/integrations/interfaces';
import { ClickUpTracer } from '@clickup-legacy/utils/tracer';

import { MetricNames } from './MetricNames';
import { RequestState } from './RequestState';
import type { RerouteState } from './RerouteState';
import type {
    ShardRequestContext,
    ShardRerouteRequestContext,
    ShardWorkspaceResolutionRequestContext,
} from './ShardRequestContext';

const tracer = new ClickUpTracer();
const logger = getLogger('shardTracing.service');

const SKIP_ENDPOINTS_FOR_METRIC = ['/health', '/fullHealth'];

type ResolveWorkspaceShard = (workspaceId: string) => Promise<ShardIdConfig | undefined>;

export function transformShardRequestContextToTags(context: ShardRequestContext): Record<string, unknown> {
    let tags: Record<string, unknown> = {};

    if (context.state) {
        tags = {
            'sharding.state': context.state,
            'sharding.isGlobalEndpoint': context.state === RequestState.GlobalEndpoint,
        };
    }

    if (context.shardInfo) {
        tags = {
            ...tags,
            'sharding.hostShard': context.shardInfo.hostShard,
            'sharding.workspaceShard': context.shardInfo?.workspaceShard,
        };
    }

    if (context.request) {
        tags = {
            ...tags,
            'sharding.request.workspaceIdHeader': context.request.workspaceIdHeader,
            'sharding.request.requestedFrom': context.request.requestedFrom,
            'sharding.request.xForwardedFor': context.request.xForwardedFor,
            'sharding.request.userAgent': context.request.userAgent,
        };
    }

    return tags;
}

export function transformRerouteContextToTags(context: ShardRerouteRequestContext): Record<string, unknown> {
    const tags = {
        ...transformShardRequestContextToTags(context),
        'sharding.rerouteInfo.state': context.rerouteInfo.state,
        'sharding.rerouteInfo.appliedRule': context.rerouteInfo.appliedRule,
        'sharding.endpointType': context.endpointType,
    };

    if (!context.endpointType) {
        delete tags['sharding.endpointType'];
    }

    return tags;
}

export async function transformWorkspaceResolutionContextToTags(
    context: ShardWorkspaceResolutionRequestContext,
    resolveWorkspaceShardFn?: ResolveWorkspaceShard
): Promise<Record<string, unknown>> {
    const { verifiedWorkspaceId } = context.workspace;

    let expectedShard: ShardIdConfig;

    if (verifiedWorkspaceId) {
        expectedShard =
            getWorkspaceShardFromContext(verifiedWorkspaceId) ||
            (resolveWorkspaceShardFn ? await resolveWorkspaceShardFn(String(verifiedWorkspaceId)) : undefined);
        context.workspace.routedToCorrectShard = expectedShard === CURRENT_SHARD_ID;
        context.shardInfo = {
            hostShard: CURRENT_SHARD_ID,
            workspaceShard: expectedShard,
        };
    }

    const workspaceBdrSelector = Number(verifiedWorkspaceId) % (databaseConfig.pg_bouncer?.groups?.length || 1);
    return {
        ...transformShardRequestContextToTags(context),
        'sharding.workspace.verifiedWorkspaceId': verifiedWorkspaceId,
        'sharding.workspace.headerConflictDetected': context.workspace.headerConflictDetected,
        'sharding.workspace.multipleWorkspacesDetected': context.workspace.multipleWorkspacesDetected,
        'sharding.workspace.routedToCorrectShard': context.workspace.routedToCorrectShard,
        'sharding.workspace.bdrSelector': workspaceBdrSelector,
        'sharding.workspace.idWithBdrSelector': `${verifiedWorkspaceId}-${workspaceBdrSelector}`,
    };
}

export async function addTagsToActiveScopeAndRootSpan(
    context: ShardRequestContext,
    resolveWorkspaceShardFn?: ResolveWorkspaceShard
) {
    let tags: Record<string, unknown>;

    try {
        // don't include workspace information for global endpoints
        if (context.state !== RequestState.GlobalEndpoint && !isGlobalEndpoint() && 'workspace' in context) {
            tags = await transformWorkspaceResolutionContextToTags(context, resolveWorkspaceShardFn);
        } else if ('rerouteInfo' in context) {
            tags = transformRerouteContextToTags(context);
        } else {
            tags = transformShardRequestContextToTags(context);
        }

        tracer.addTagsToActiveScope(tags); // workspace_shard_proxy span
        tracer.addTagsToRootSpan(tags); // express.request span
        addTagsToAsyncStorage(tags);
    } catch (err) {
        logger.error({
            msg: 'Failed to add tags to active scope and root span',
            err,
        });
    }
}

function addTagsToAsyncStorage(tags: Record<string, any> = {}) {
    const asyncStorageContext = AsyncStorage.getInstance().getContext();
    if (!asyncStorageContext) {
        return;
    }

    if (!asyncStorageContext.shardRequestContext) {
        asyncStorageContext.shardRequestContext = {};
    }

    Object.assign(asyncStorageContext.shardRequestContext, tags);
}

function getShardingTagsFromAsyncStorage(filters: string[]): Record<string, string | boolean> {
    const asyncStorageContext = AsyncStorage.getInstance().getContext();

    return Object.entries(asyncStorageContext?.shardRequestContext || {}).reduce(
        (filteredTags: Record<string, string | boolean>, [tagName, tagValue]: [string, string | boolean]) => {
            if (!filters.includes(tagName)) {
                return filteredTags;
            }

            return Object.assign(filteredTags, { [tagName]: tagValue });
        },
        {}
    );
}

function shouldSkip(req: Request) {
    for (const skipSuffix of SKIP_ENDPOINTS_FOR_METRIC) {
        if (req?.url?.endsWith(skipSuffix)) {
            return true;
        }
    }
    return false;
}

export function reportShardRoutedRequest(req: Request, res: Response): void {
    if (shouldSkip(req)) {
        return;
    }

    createPostRequestHook(res, () => {
        const tags = getShardingTagsFromAsyncStorage([
            'sharding.state',
            'sharding.workspaceShard',
            'sharding.hostShard',
            'sharding.rerouteInfo.state',
            'sharding.rerouteInfo.appliedRule',
            'sharding.endpointType',
            'sharding.workspace.routedToCorrectShard',
            'sharding.workspace.multipleWorkspacesDetected',
            'sharding.workspace.headerConflictDetected',
        ]);

        if (tags['sharding.workspace.routedToCorrectShard'] === false) {
            logger.info({
                msg: 'Request routed to wrong shard',
                url: req.url,
                originalUrl: req.originalUrl,
                route: req.route,
                httpStatus: res.statusCode,
            });
        }

        metricsClient.increment(MetricNames.SHARD_ROUTED_REQUEST, 1, {
            shard_id: CURRENT_SHARD_ID,
            ...tags,
            'http.status_code': String(res.statusCode),
        });
    });
}

export function reportShardRoutedWebhook(rerouteState: RerouteState, webhookType: WebhookIntegrationType) {
    const tags = {
        shard_id: CURRENT_SHARD_ID,
        'sharding.state': RequestState.BroadcastRule,
        'sharding.endpointType': webhookType,
        'sharding.rerouteInfo.state': rerouteState,
    };
    metricsClient.increment(MetricNames.SHARD_ROUTED_REQUEST, 1, tags);
}

export function reportWorkspaceLocationRuleUsed(targetShard: ShardIdConfig): void {
    const tags = {
        shard_id: CURRENT_SHARD_ID,
        target_shard: targetShard,
    };
    metricsClient.increment(MetricNames.WORKSPACE_LOCATION_RULE_USED, 1, tags);
}

/**
 * Re-implemented here from src/utils/workspaceId/context.ts to avoid a circular dependency
 */
function isGlobalEndpoint() {
    return AsyncStorage.getInstance().getContext()?.isGlobalEndpoint;
}

function createPostRequestHook(res: Response, hook: (...args: unknown[]) => unknown): void {
    res.on('finish', function listener() {
        res.removeListener('finish', listener);
        hook();
    });
}

function getWorkspaceShardFromContext(workspaceId: number): ShardIdConfig | undefined {
    const context = AsyncStorage.getInstance().getContext();

    if (!workspaceId || context?.verifiedWorkspace?.untrustedId !== Number(workspaceId)) {
        return undefined;
    }

    return context.verifiedWorkspace.shardId as ShardIdConfig;
}
