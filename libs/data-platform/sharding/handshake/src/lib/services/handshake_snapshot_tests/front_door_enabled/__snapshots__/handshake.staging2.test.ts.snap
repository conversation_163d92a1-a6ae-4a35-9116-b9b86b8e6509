// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`US Staging - Handshake responses - front door enabled test response: From: staging (staging-us-west-2-1) - For Workspace in shard: g001 1`] = `
Object {
  "appEnvironment": Object {
    "apiUrl": "https://g001.clickup-stg.com/v1",
    "apiUrlAiService": "https://g001.clickup-stg.com/ai/v1",
    "apiUrlBase": "https://g001.clickup-stg.com",
    "apiUrlDoc": "https://g001.clickup-stg.com/docs/v1",
    "apiUrlV2": "https://g001.clickup-stg.com/v2",
    "attachmentUrl": "https://attch.clickup-stg.com/v1",
    "autoAuditlogServiceUrl": "https://g001.clickup-stg.com",
    "autoPaywallServiceUrl": "https://g001.clickup-stg.com",
    "autoWebhookServiceUrl": "https://g001.clickup-stg.com",
    "exportUrl": "https://g001.clickup-stg.com/v1",
    "formApiUrl": "https://form-submit.clickup-stg.com/v1",
    "formApiUrlBase": "https://form-submit.clickup-stg.com",
    "ganttExportUrl": "https://gantt-export-staging.clickup.com/gantt",
    "invoiceUrl": "https://cu-us-staging-staging-us-west-2-1-iv-interval.clickup-stg.com/v1",
    "oauthCallbackUrl": "https://app.clickup-stg.com",
    "publicDocsUrl": "https://doc.clickup-stg.com",
    "publicFormsUrl": "https://forms.clickup-stg.com",
    "publicShareUrl": "https://share.clickup-stg.com",
    "websocketUrl": "wss://ws.clickup-stg.com/ws",
  },
  "shardId": "g001",
  "workspaceId": "616",
  "workspaceUnderMaintenance": false,
}
`;
