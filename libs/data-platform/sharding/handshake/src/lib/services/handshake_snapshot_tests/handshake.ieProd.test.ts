import { ieProdEnvironment } from '@clickup-jest-legacy-config/environments';

import { ProductionWorkspaceShards } from '@clickup/data-platform/sharding/constants';

import { testHandshakeResponsesForShards } from './utils';

describe('IE Prod - Handshake responses', () => {
    beforeAll(async () => {
        await ieProdEnvironment.configureSettings();
    });

    testHandshakeResponsesForShards(ieProdEnvironment.name, ['g001', ...ProductionWorkspaceShards]);
});
