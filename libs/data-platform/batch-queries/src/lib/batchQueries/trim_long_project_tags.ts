/**
 * Trim project_tags with the names of over X characters.
 */

const maxTagLength = 2000;

export const selectQuery = `
SELECT project_id,
       name
FROM task_mgmt.project_tags
WHERE length(name) > ${maxTagLength}
LIMIT $1
`;

export const batchQuery = `
WITH long_tags as (
    ${selectQuery}
),
tags_to_update as (
    SELECT
        long_tags.project_id,
        long_tags.name,
        (duplicate_tags.project_id IS NOT NULL) as has_short_copy
    FROM long_tags
    LEFT JOIN task_mgmt.project_tags as duplicate_tags
        ON duplicate_tags.project_id = long_tags.project_id
        AND duplicate_tags.name = substring(long_tags.name, 0, ${maxTagLength + 1})
),
update_tags as (
    UPDATE task_mgmt.project_tags
        SET name = substring(project_tags.name, 0, ${maxTagLength + 1})
    FROM tags_to_update
    WHERE
        tags_to_update.project_id = project_tags.project_id
        AND tags_to_update.name = project_tags.name
        AND tags_to_update.has_short_copy = FALSE
)
DELETE FROM task_mgmt.project_tags
USING tags_to_update
WHERE tags_to_update.project_id = project_tags.project_id
    AND tags_to_update.name = project_tags.name
    AND tags_to_update.has_short_copy = TRUE
`;

export const collectionKey = 'trim_long_project_tags';

export const owner = 'fields';
