import { AxiosError, AxiosInstance, AxiosResponse } from 'axios';

import { Result, WorkspaceDeletionClient, WorkspaceDeletionStatus } from './deletion-client.interface';

export interface AcceptedOutput {
    ok?: boolean;
    error?: string;
}

export interface RequestWorkspaceDeletionInput {
    deleteAt: number;
    requester: string;
    reason: string;
}

export type RequestWorkspaceDeletionOutput = AcceptedOutput;

export interface GetWorkspaceDeletionStatusInput {
    workspaceId: string;
}

export interface GetWorkspaceDeletionStatusOutput {
    status?: WorkspaceDeletionStatus;
    error?: string;
}

export type BulkGetWorkspaceDeletionStatusInput = string[];
export type BulkGetWorkspaceDeletionStatusOutput = {
    error?: string;
    statuses?: Record<string, WorkspaceDeletionStatus>;
};

export interface CancelWorkspaceDeletionInput {
    workspaceId: string;
}

export type CancelWorkspaceDeletionOutput = AcceptedOutput;

export class HttpWorkspaceDeletionClient implements WorkspaceDeletionClient {
    constructor(private readonly deletionApiPrefix: string, private readonly httpClient: AxiosInstance) {}

    async requestWorkspaceDeletion(
        workspaceId: string | number,
        deleteAt: number,
        requester: string,
        reason: string
    ): Promise<Result<never, string>> {
        const input = {
            deleteAt,
            requester,
            reason,
        };

        try {
            const response = await this.httpClient.put<
                RequestWorkspaceDeletionOutput,
                AxiosResponse<RequestWorkspaceDeletionOutput>,
                RequestWorkspaceDeletionInput
            >(`${this.deletionApiPrefix}/${workspaceId}`, input);

            if (response.data.ok) {
                return { success: true };
            }

            // Check if non empty error message
            if (response.data.error) {
                return { success: false, error: response.data.error };
            }
        } catch (error: AxiosError | unknown) {
            if (error instanceof Error) {
                return { success: false, error: error.message };
            }
        }
        return { success: false, error: 'Unknown error' };
    }

    async getWorkspaceDeletionStatus(workspaceId: string | number): Promise<Result<WorkspaceDeletionStatus, string>> {
        try {
            const response = await this.httpClient.get<GetWorkspaceDeletionStatusOutput>(
                `${this.deletionApiPrefix}/${workspaceId}`
            );

            return { success: true, result: response.data.status };
        } catch (error: AxiosError | unknown) {
            if (error instanceof Error) {
                return { success: false, error: error.message };
            }
        }
        return { success: false, error: 'Unknown error' };
    }

    async getWorkspaceDeletionStatusBulk(
        workspaceIds: (string | number)[]
    ): Promise<Result<Record<string, WorkspaceDeletionStatus>, string>> {
        try {
            const response = await this.httpClient.post<
                BulkGetWorkspaceDeletionStatusOutput,
                AxiosResponse<BulkGetWorkspaceDeletionStatusOutput>,
                BulkGetWorkspaceDeletionStatusInput
            >(`${this.deletionApiPrefix}/bulk/status`, workspaceIds.map(String));

            // Check for errors
            if (response.data.error) {
                return { success: false, error: response.data.error };
            }
            // If no error validate the resposne
            if (response.data.statuses) {
                const result: Record<string, WorkspaceDeletionStatus> = {};

                for (const id of workspaceIds) {
                    if (response.data.statuses[id] === undefined) {
                        return { success: false, error: 'missing workspace ids' };
                    }
                    const status = response.data.statuses[id];
                    if (!Object.values(WorkspaceDeletionStatus).includes(status)) {
                        return { success: false, error: 'returned value not valid' };
                    }
                    result[id] = status;
                }

                return { success: true, result };
            }
            return { success: false, error: 'malformed output' };
        } catch (error: AxiosError | unknown) {
            if (error instanceof Error) {
                return { success: false, error: error.message };
            }
        }
        return { success: false, error: 'Unknown error' };
    }

    async cancelWorkspaceDeletion(workspaceId: string | number): Promise<Result<never, string>> {
        try {
            const response = await this.httpClient.delete<CancelWorkspaceDeletionOutput>(
                `${this.deletionApiPrefix}/${workspaceId}`
            );

            if (response.data.ok) {
                return { success: true };
            }

            // Check if non empty error message
            if (response.data.error) {
                return { success: false, error: response.data.error };
            }
        } catch (error: AxiosError | unknown) {
            if (error instanceof Error) {
                return { success: false, error: error.message };
            }
        }
        return { success: false, error: 'Unknown error' };
    }
}
