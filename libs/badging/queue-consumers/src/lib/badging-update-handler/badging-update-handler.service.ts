import { Injectable } from '@nestjs/common';
import type { Message } from 'aws-sdk/clients/sqs';
import promiseLimit from 'promise-limit';

import { ChatBadgingService } from '@clickup/badging/data-access';
import { FeatureFlag } from '@clickup/badging/feature-flags';
import { BadgingPushService } from '@clickup/badging/push';
import {
    BadgeComponent,
    BadgeDomain,
    BadgingUpdateRequest,
    CountUpdateType,
    UnreadRoomsPayloadRto,
    UnreadThreadsPayloadRto,
} from '@clickup/badging/shared-types';
import { tracer } from '@clickup/shared/utils-tracing';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';
import { SqsConsumerEventHandler, SqsMessageHandler } from '@clickup/utils/sqs';

const MAX_RETRIES = 5;

/**
 * SQS Message handler to handle badging updates
 */
@Injectable()
export class BadgingUpdateHandler {
    public static Name = 'BadgingUpdateHandler';

    constructor(
        private readonly chatBadgingService: ChatBadgingService,
        private readonly badgingPushService: BadgingPushService,
        private readonly logger: FormatLogger,
        private readonly metricsService: MetricsService,
        private readonly featureFlagService: FeatureFlagService
    ) {}

    @SqsMessageHandler(BadgingUpdateHandler.Name, false)
    async handleMessage(message: Message) {
        let encounteredError = false;
        const processingStartedTimestamp: number = Date.now();
        try {
            await this.handleBadgingUpdateRequest(message);
        } catch (e) {
            encounteredError = true;
            throw e;
        } finally {
            this.metricsService.client.increment('queue_consumers.badging_updates.received.count', 1);
            const receiveCount = +(message?.Attributes?.ApproximateReceiveCount ?? 1);
            if (!encounteredError || receiveCount >= MAX_RETRIES) {
                const metric = encounteredError
                    ? 'queue_consumers.badging_updates.processing_error.count'
                    : 'queue_consumers.badging_updates.processed_successfully.count';
                this.metricsService.client.increment(metric, 1, {
                    receiveCount: receiveCount.toString(),
                });
            }
            if (!encounteredError) {
                this.metricsService.client.distribution(
                    'inbox.badging_updates_message_in_queue.distribution',
                    processingStartedTimestamp - +(message.Attributes?.SentTimestamp ?? 0)
                );
            }
        }
    }

    async waitForTemporaryDelay(delayMs: number) {
        return new Promise(resolve => setTimeout(resolve, delayMs));
    }

    @tracer.Decorator('badging.badging_update_handler.handleBadgingUpdateRequest', {
        service: process.env.DD_SERVICE,
    })
    async handleBadgingUpdateRequest(message: Message) {
        const processingStartedTimestamp: number = Date.now();
        const ageOfMessage = processingStartedTimestamp - +(message.Attributes?.SentTimestamp ?? 0);

        const requestInput = BadgingUpdateRequest.fromJson(message.Body ?? '{}');
        tracer.addTagsToRootSpan({ workspace_id: requestInput.workspaceId, user_ids: requestInput.userIds });

        this.logger.debug(
            {
                message: 'Handling BadgingUpdateRequest message',
                request: JSON.stringify(requestInput),
            },
            BadgingUpdateHandler.Name
        );

        if (requestInput?.ttlMs != null && requestInput.ttlMs > 0 && requestInput.ttlMs < ageOfMessage) {
            this.logger.warn(
                {
                    message: "BadgingUpdateRequest message is expired - no-op'ing",
                    request: JSON.stringify(requestInput),
                },
                BadgingUpdateHandler.Name
            );
            return;
        }

        if (requestInput.badgeDomain === BadgeDomain.CHAT && requestInput.countUpdateType === CountUpdateType.REFRESH) {
            if (requestInput.workspaceId === undefined) {
                throw new Error('Workspace ID is required for chat badge refresh');
            }

            if (requestInput.userIds === undefined || requestInput.userIds.length === 0) {
                throw new Error('User IDs are required for chat badge refresh');
            }

            // TODO: This will be removed once badging-update refreshes are triggered via CLRA OVM consumer instead of from comment/CRUD
            await this.waitForTemporaryDelay(500);

            const roomsBadgeCountByUserIds = await this.chatBadgingService.fetchUnreadRoomsBadgeCounts({
                userIds: requestInput.userIds,
                workspaceId: requestInput.workspaceId,
            });

            const threadsBadgeCountByUserIds = await this.chatBadgingService.fetchUnreadThreadsBadgeCounts({
                userIds: requestInput.userIds,
                workspaceId: requestInput.workspaceId,
            });

            if (requestInput.badgeComponent === BadgeComponent.UNREAD_ROOMS) {
                await this.chatBadgingService.sendBadgeCountsUpdatedWSMessages(roomsBadgeCountByUserIds);
            } else if (requestInput.badgeComponent === BadgeComponent.UNREAD_THREADS) {
                await this.chatBadgingService.sendBadgeCountsUpdatedWSMessages(threadsBadgeCountByUserIds);
            }

            if (roomsBadgeCountByUserIds || threadsBadgeCountByUserIds) {
                await this.updateMobileBadgeCounts(
                    roomsBadgeCountByUserIds,
                    threadsBadgeCountByUserIds,
                    requestInput.workspaceId
                );
            }
        }

        // TODO: Should likely check the users preference of inbox, chat, or inbox+chat to avoid unnecessary work

        // if (requestInput.countUpdateType === CountUpdateType.SET && requestInput.badgeDomain === BadgeDomain.INBOX) {
        //     await this.handleUpdateInboxCounts(requestInput);
        // }

        const processingDuration: number = Date.now() - processingStartedTimestamp;
        this.metricsService.client.distribution('inbox.badging_updates_processing.distribution', processingDuration);

        const receiveCount = +(message?.Attributes?.ApproximateReceiveCount ?? 1);
        this.logger.debug(
            {
                message: 'Successfully handled BadgingUpdateRequest message: ',
                request: JSON.stringify(requestInput),
                receiveCount,
                messageId: message.MessageId,
            },
            BadgingUpdateHandler.Name
        );
    }

    private async updateMobileBadgeCounts(
        roomsBadgeCountByUserIds: Map<number, UnreadRoomsPayloadRto>,
        threadsBadgeCountByUserIds: Map<number, UnreadThreadsPayloadRto>,
        workspaceId: number
    ) {
        if (!this.featureFlagService.getBoolean(FeatureFlag.ShouldUpdateMobileBadgeCount)) {
            return;
        }

        const combinedBadges = new Map<number, number>();
        for (const [userId, roomsBadgeCount] of roomsBadgeCountByUserIds.entries()) {
            combinedBadges.set(userId, roomsBadgeCount.counts.badge_count);
        }
        for (const [userId, threadsBadgeCount] of threadsBadgeCountByUserIds.entries()) {
            const existingCount = combinedBadges.get(userId) || 0;
            combinedBadges.set(userId, existingCount + threadsBadgeCount.counts.badge_count);
        }

        const limit = promiseLimit(10);

        const updatePromises = Array.from(combinedBadges.entries()).map(([userId, badgeCount]) =>
            limit(() => this.badgingPushService.updateUsersMobileBadgeCount(userId, workspaceId, badgeCount))
        );

        const results = await Promise.allSettled(updatePromises);

        // Log any failures
        results.forEach((result, index) => {
            if (result.status === 'rejected') {
                const userId = Array.from(combinedBadges.keys())[index];
                this.logger.error(result.reason, BadgingUpdateHandler.Name, {
                    message: 'Failed to update mobile badge count',
                    userId,
                });
            }
        });
    }

    // async handleUpdateInboxCounts(request: BadgingUpdateRequest) {
    //     this.logger.info(`Updating inbox badge counts for all workspaces to ${JSON.stringify(request?.scopeValueMap)}`);
    // }

    @SqsConsumerEventHandler(BadgingUpdateHandler.Name, 'processing_error')
    handleProcessingError(error: Error, message?: Message) {
        const receiveCount = +(message?.Attributes?.ApproximateReceiveCount ?? 1);

        const logLevel = receiveCount === MAX_RETRIES ? 'error' : 'warn';
        this.logger[logLevel](error, BadgingUpdateHandler.Name, {
            message: error.message,
            request: message?.Body,
            receiveCount,
            messageId: message?.MessageId,
        });
    }

    @SqsConsumerEventHandler(BadgingUpdateHandler.Name, 'error')
    handleError(error: Error, message?: Message) {
        this.handleProcessingError(error, message);
    }
}
