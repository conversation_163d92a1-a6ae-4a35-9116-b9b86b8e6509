{"name": "websocket-domain", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/websocket/domain/src", "projectType": "library", "targets": {"lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/websocket/domain/jest.config.ts"}}}, "tags": []}