import { Test, TestingModule } from '@nestjs/testing';
import { ObjectType, ObjectVectorChangeEvent } from '@time-loop/ovm-object-version';

import { FeatureFlagService } from '@clickup/utils/feature-flag';
import {
    WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
    WebsocketSubscriptionRow,
    WebsocketSubscriptionsService,
} from '@clickup/websocket/subscriptions';

import { TestMessageOperator } from '../TestMessageOperator';
import { testEvents } from '../testObjectVectorChangeEvents';
import { SpaceUpdatedMessageOperator } from './SpaceUpdatedMessageOperator';

describe('SpaceUpdatedMessageOperator', () => {
    const subscription: WebsocketSubscriptionRow = {
        host: 'host',
        ip: '123-45-67-890',
        userid: 300001,
        version_two: true,
        ws_uuid: 'uuid-space',
    };

    const subscription2: WebsocketSubscriptionRow = {
        host: 'host2',
        ip: '123-45-67-891',
        userid: 300002,
        version_two: true,
        ws_uuid: 'uuid-space-users',
    };

    const subscription3: WebsocketSubscriptionRow = {
        host: 'host3',
        ip: '123-45-67-892',
        userid: 300003,
        version_two: true,
        ws_uuid: 'uuid-space-users',
    };

    const fakeSubsService = {
        getSpaceSubscriptions: jest.fn().mockResolvedValue([subscription]),
        getSpaceUserSubscriptions: jest.fn().mockResolvedValue([subscription2]),
        getTeamSubscriptions: jest.fn().mockResolvedValue([subscription3]),
    } as unknown as WebsocketSubscriptionsService;

    const fakeFeatureFlagService = {
        getBoolean: jest.fn(flagObj => {
            // Default: both flags false
            if (flagObj.flag === 'hierarchy-space-websockets-all-users') return false;
            if (flagObj.flag === 'hierarchy-websockets-use-flexible-subscriptions') return false;
            return false;
        }),
    } as unknown as FeatureFlagService;

    let testModule: TestingModule;
    let operator: SpaceUpdatedMessageOperator;

    beforeAll(async () => {
        testModule = await Test.createTestingModule({
            providers: [
                SpaceUpdatedMessageOperator,
                {
                    provide: WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
                    useValue: fakeSubsService,
                },
                {
                    provide: FeatureFlagService,
                    useValue: fakeFeatureFlagService,
                },
            ],
        }).compile();
    });

    beforeEach(() => {
        // Reset to default: both flags false
        fakeFeatureFlagService.getBoolean = jest.fn(flagObj => {
            if (flagObj.flag === 'hierarchy-space-websockets-all-users') return false;
            if (flagObj.flag === 'hierarchy-websockets-use-flexible-subscriptions') return false;
            return false;
        });
        operator = testModule.get(SpaceUpdatedMessageOperator);
    });

    describe('shouldTransform', () => {
        it('should return false for undefined or empty events', () => {
            expect(operator.shouldTransform(undefined)).toBe(false);
            expect(operator.shouldTransform(null)).toBe(false);
            expect(operator.shouldTransform({} as unknown as ObjectVectorChangeEvent)).toBe(false);
        });

        it('should only transform a space updated event', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            expect(transformableEvents.length).toEqual(2);
            expect(transformableEvents[0]).toMatchInlineSnapshot(`
                Object {
                  "date_created": 1695772261647,
                  "date_updated": 1695772261647,
                  "deleted": false,
                  "master_id": 0,
                  "object_id": "99990000019",
                  "object_type": "space",
                  "operation": "u",
                  "traceparent": "32541441-4108-416b-ab00-216e913bc456",
                  "vector": Array [
                    Object {
                      "deleted": false,
                      "master_id": 0,
                      "version": 1695772261647000,
                    },
                  ],
                  "version": 1695772261647000,
                  "workspace_id": 9999000005,
                }
            `);
        });
    });

    describe('transform', () => {
        it('should throw an exception when trying to transform an invalid event', () => {
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.SPACE);
            expect(invalidEvent).toBeDefined();

            expect(() => operator.transform(invalidEvent, 0)).toThrowErrorMatchingInlineSnapshot(
                `"Operator spaceUpdated cannot transform event"`
            );
        });

        it('should transform a space create vector event into a WS 2.0 enveloped message', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[1], 15);
            expect(message).toMatchInlineSnapshot(`
                Object {
                  "event": Object {
                    "date_created": 1696772261647,
                    "from_fanout": false,
                    "name": "spaceUpdated",
                    "operation": "u",
                    "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                    "version_change": Object {
                      "object_id": "99990000018",
                      "object_type": "space",
                      "vector": Array [
                        Object {
                          "deleted": false,
                          "master_id": 0,
                          "version": 1696772261647000,
                        },
                      ],
                      "workspace_id": 9999000005,
                    },
                    "version_event_data": Object {
                      "changes": Array [
                        Object {
                          "after": "#FF0000",
                          "field": "color",
                        },
                      ],
                    },
                  },
                  "msg": "spaceUpdated",
                  "ovm_consumer_received_time": 15,
                  "pipeline_context": Object {
                    "ovm_consumer_received_epoch_ms": 15,
                    "ovm_consumer_transformed_epoch_ms": 1697730034510,
                    "ovm_event_publish_time_ms": undefined,
                    "shadow_expired": undefined,
                    "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                  },
                  "schema": "websocket_ovm_message",
                  "version": "2.0.0",
                }
            `);
        });
    });

    describe('getSubscriptions', () => {
        it('should throw an exception when getting subscriptions for an invalid message', async () => {
            const testOperator = new TestMessageOperator(fakeSubsService);
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.SPACE);
            expect(invalidEvent).toBeDefined();

            const invalidMessage = testOperator.transform(invalidEvent, 0);

            await expect(operator.getSubscriptions(invalidMessage)).rejects.toThrowErrorMatchingInlineSnapshot(
                `"Attempting to get subscriptions for an invalid message testMessage"`
            );
        });

        it('should return the space subscriptions', async () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription]);
        });

        it('should return the space users subscriptions when the update is visible on the sidebar', async () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            // one of the test events has a color change
            const visibleUpdateEvent = transformableEvents.find(event =>
                event.data?.changes.some(c => c.field === 'color')
            );
            const message = operator.transform(visibleUpdateEvent, 0);

            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription2]);
        });

        it('should return the space user subscriptions when feature flag is on', async () => {
            fakeFeatureFlagService.getBoolean = jest.fn(flagObj => {
                if (flagObj.flag === 'hierarchy-space-websockets-all-users') return true;
                if (flagObj.flag === 'hierarchy-websockets-use-flexible-subscriptions') return false;
                return false;
            });
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription2]);
        });

        it('should return the space user subscriptions when privacy is changed', async () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const eventToTransform = structuredClone(transformableEvents[0]);
            eventToTransform.data = {};
            eventToTransform.data.changes = [
                {
                    field: 'private',
                    after: true,
                    before: false,
                },
            ];
            const message = operator.transform(eventToTransform, 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription2]);
        });

        it('should not return the space subscriptions when privacy is not changed', async () => {
            fakeFeatureFlagService.getBoolean = jest.fn(flagObj => {
                if (flagObj.flag === 'hierarchy-space-websockets-all-users') return false;
                if (flagObj.flag === 'hierarchy-websockets-use-flexible-subscriptions') return false;
                return false;
            });
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const eventToTransform = structuredClone(transformableEvents[0]);
            eventToTransform.data = {};
            eventToTransform.data.changes = [{ field: 'private', after: null, before: true }];
            const message = operator.transform(eventToTransform, 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription]);
        });
    });
});
