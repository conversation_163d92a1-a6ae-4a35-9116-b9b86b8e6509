import { Test, TestingModule } from '@nestjs/testing';
import { ObjectType, ObjectVectorChangeEvent } from '@time-loop/ovm-object-version';

import { FeatureFlagService } from '@clickup/utils/feature-flag';
import {
    WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
    WebsocketSubscriptionRow,
    WebsocketSubscriptionsService,
} from '@clickup/websocket/subscriptions';

import { TestMessageOperator } from '../TestMessageOperator';
import { testEvents } from '../testObjectVectorChangeEvents';
import { ListDescendantsPointsUpdatedMessageOperator } from './ListDescendantsPointsUpdatedMessageOperator';

describe('ListDescendantsPointsUpdatedMessageOperator', () => {
    const subscription: WebsocketSubscriptionRow = {
        host: 'any.domain.com',
        ip: '***********',
        userid: 300001,
        version_two: true,
        ws_uuid: '5d5efe64-ae91-4443-8e1c-c3228f638a16',
    };

    const fakeSubsService = {
        getListSubscriptions: jest.fn().mockResolvedValue([subscription]),
    } as unknown as WebsocketSubscriptionsService;

    const fakeFeatureFlagService = {
        getBoolean: jest.fn().mockReturnValue(false),
    } as unknown as FeatureFlagService;

    let testModule: TestingModule;
    let operator: ListDescendantsPointsUpdatedMessageOperator;

    beforeAll(async () => {
        testModule = await Test.createTestingModule({
            providers: [
                ListDescendantsPointsUpdatedMessageOperator,
                {
                    provide: WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
                    useValue: fakeSubsService,
                },
                {
                    provide: FeatureFlagService,
                    useValue: fakeFeatureFlagService,
                },
            ],
        }).compile();
    });

    beforeEach(() => {
        operator = testModule.get(ListDescendantsPointsUpdatedMessageOperator);
    });

    describe('shouldTransform', () => {
        it('should return false for undefined or empty events', () => {
            expect(operator.shouldTransform(undefined)).toBe(false);
            expect(operator.shouldTransform(null)).toBe(false);
            expect(operator.shouldTransform({} as unknown as ObjectVectorChangeEvent)).toBe(false);
        });

        it('should only transform a list descendants points update event', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            expect(transformableEvents.length).toEqual(1);
            expect(transformableEvents[0]).toMatchInlineSnapshot(`
                Object {
                  "date_created": 1703771140397,
                  "date_updated": 1703771140397,
                  "deleted": false,
                  "master_id": 0,
                  "object_id": "list-descendants-points-object-id",
                  "object_type": "listDescendantsPoints",
                  "operation": "u",
                  "traceparent": "94e03e69-f7d2-4d02-b2a8-c3c7427f3248",
                  "vector": Array [
                    Object {
                      "deleted": false,
                      "master_id": 0,
                      "version": 1703771140397000,
                    },
                  ],
                  "version": 1703771140397000,
                  "workspace_id": 9999000002,
                }
            `);
        });
    });

    describe('transform', () => {
        it('should throw an exception when trying to transform an invalid event', () => {
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.LIST_DESCENDANTS_POINTS);
            expect(invalidEvent).toBeDefined();

            expect(() => operator.transform(invalidEvent, 0)).toThrowErrorMatchingInlineSnapshot(
                `"Operator listDescendantsPointsUpdated cannot transform event"`
            );
        });

        it('should transform a list descendants set updated vector event into a WS 2.0 enveloped message', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 15);
            expect(message).toMatchInlineSnapshot(`
                Object {
                  "event": Object {
                    "date_created": 1703771140397,
                    "from_fanout": false,
                    "name": "listDescendantsPointsUpdated",
                    "operation": "u",
                    "trace_parent": "94e03e69-f7d2-4d02-b2a8-c3c7427f3248",
                    "version_change": Object {
                      "object_id": "list-descendants-points-object-id",
                      "object_type": "listDescendantsPoints",
                      "vector": Array [
                        Object {
                          "deleted": false,
                          "master_id": 0,
                          "version": 1703771140397000,
                        },
                      ],
                      "workspace_id": 9999000002,
                    },
                    "version_event_data": undefined,
                  },
                  "msg": "listDescendantsPointsUpdated",
                  "ovm_consumer_received_time": 15,
                  "pipeline_context": Object {
                    "ovm_consumer_received_epoch_ms": 15,
                    "ovm_consumer_transformed_epoch_ms": 1697730034510,
                    "ovm_event_publish_time_ms": undefined,
                    "shadow_expired": undefined,
                    "trace_parent": "94e03e69-f7d2-4d02-b2a8-c3c7427f3248",
                  },
                  "schema": "websocket_ovm_message",
                  "version": "2.0.0",
                }
            `);
        });
    });

    describe('getSubscriptions', () => {
        it('should throw an exception when getting subscriptions for an invalid message', async () => {
            const testOperator = new TestMessageOperator(fakeSubsService);
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.LIST_DESCENDANTS_SET);
            expect(invalidEvent).toBeDefined();
            const invalidMessage = testOperator.transform(invalidEvent, 0);

            await expect(operator.getSubscriptions(invalidMessage)).rejects.toThrowErrorMatchingInlineSnapshot(
                `"Attempting to get subscriptions for an invalid message testMessage"`
            );
        });

        it('should return the list subscriptions', async () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription]);
        });
    });
});
