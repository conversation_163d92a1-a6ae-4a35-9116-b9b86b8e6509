import { Test, TestingModule } from '@nestjs/testing';
import { ObjectType, ObjectVectorChangeEvent } from '@time-loop/ovm-object-version';

import { FeatureFlagService } from '@clickup/utils/feature-flag';
import {
    WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
    WebsocketSubscriptionRow,
    WebsocketSubscriptionsService,
} from '@clickup/websocket/subscriptions';

import { TestMessageOperator } from '../TestMessageOperator';
import { testEvents } from '../testObjectVectorChangeEvents';
import { ListDeletedMessageOperator } from './ListDeletedMessageOperator';

describe('ListDeletedMessageOperator', () => {
    const subscription: WebsocketSubscriptionRow = {
        host: 'host',
        ip: '123-45-67-890',
        userid: 300001,
        version_two: true,
        ws_uuid: 'uuid',
    };

    const subscription2: WebsocketSubscriptionRow = {
        host: 'host2',
        ip: '123-45-67-891',
        userid: 300002,
        version_two: true,
        ws_uuid: 'uuid2',
    };

    const fakeSubsService = {
        getListSubscriptions: jest.fn().mockResolvedValue([subscription]),
        getListUsersSubscriptions: jest.fn().mockResolvedValue([subscription2]),
    } as unknown as WebsocketSubscriptionsService;

    const fakeFeatureFlagService = {
        getBoolean: jest.fn(flagObj => {
            // Default: both flags false
            if (flagObj.flag === 'hierarchy-websockets-all-users') return false;
            if (flagObj.flag === 'hierarchy-websockets-use-flexible-subscriptions') return false;
            return false;
        }),
    } as unknown as FeatureFlagService;

    let testModule: TestingModule;
    let operator: ListDeletedMessageOperator;

    beforeAll(async () => {
        testModule = await Test.createTestingModule({
            providers: [
                ListDeletedMessageOperator,
                {
                    provide: WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
                    useValue: fakeSubsService,
                },
                {
                    provide: FeatureFlagService,
                    useValue: fakeFeatureFlagService,
                },
            ],
        }).compile();
    });

    beforeEach(() => {
        // Reset to default: both flags false
        fakeFeatureFlagService.getBoolean = jest.fn(flagObj => {
            if (flagObj.flag === 'hierarchy-websockets-all-users') return false;
            if (flagObj.flag === 'hierarchy-websockets-use-flexible-subscriptions') return false;
            return false;
        });
        operator = testModule.get(ListDeletedMessageOperator);
    });

    describe('shouldTransform', () => {
        it('should return false for undefined or empty events', () => {
            expect(operator.shouldTransform(undefined)).toBe(false);
            expect(operator.shouldTransform(null)).toBe(false);
            expect(operator.shouldTransform({} as unknown as ObjectVectorChangeEvent)).toBe(false);
        });

        it('should only transform a list delete event', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            expect(transformableEvents.length).toEqual(1);
            expect(transformableEvents[0]).toMatchInlineSnapshot(`
                Object {
                  "date_created": 1695772335240,
                  "date_updated": 1695772335240,
                  "deleted": true,
                  "master_id": 0,
                  "object_id": "999900000559",
                  "object_type": "list",
                  "operation": "d",
                  "traceparent": "32541441-4108-416b-ab00-216e913bc456",
                  "vector": Array [
                    Object {
                      "deleted": true,
                      "master_id": 0,
                      "version": 1695792335240000,
                    },
                  ],
                  "version": 1695792335240000,
                  "workspace_id": 9999000005,
                }
            `);
        });
    });

    describe('transform', () => {
        it('should throw an exception when trying to transform an invalid event', () => {
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.LIST);
            expect(invalidEvent).toBeDefined();

            expect(() => operator.transform(invalidEvent, 0)).toThrowErrorMatchingInlineSnapshot(
                `"Operator listDeleted cannot transform event"`
            );
        });

        it('should transform a list create vector event into a WS 2.0 enveloped message', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 15);
            expect(message).toMatchInlineSnapshot(`
                Object {
                  "event": Object {
                    "date_created": 1695772335240,
                    "from_fanout": false,
                    "name": "listDeleted",
                    "operation": "d",
                    "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                    "version_change": Object {
                      "object_id": "999900000559",
                      "object_type": "list",
                      "vector": Array [
                        Object {
                          "deleted": true,
                          "master_id": 0,
                          "version": 1695792335240000,
                        },
                      ],
                      "workspace_id": 9999000005,
                    },
                    "version_event_data": undefined,
                  },
                  "msg": "listDeleted",
                  "ovm_consumer_received_time": 15,
                  "pipeline_context": Object {
                    "ovm_consumer_received_epoch_ms": 15,
                    "ovm_consumer_transformed_epoch_ms": 1697730034510,
                    "ovm_event_publish_time_ms": undefined,
                    "shadow_expired": undefined,
                    "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                  },
                  "schema": "websocket_ovm_message",
                  "version": "2.0.0",
                }
            `);
        });
    });

    describe('getSubscriptions', () => {
        it('should throw an exception when getting subscriptions for an invalid message', async () => {
            const testOperator = new TestMessageOperator(fakeSubsService);
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.LIST);
            expect(invalidEvent).toBeDefined();

            const invalidMessage = testOperator.transform(invalidEvent, 0);

            await expect(operator.getSubscriptions(invalidMessage)).rejects.toThrowErrorMatchingInlineSnapshot(
                `"Attempting to get subscriptions for an invalid message testMessage"`
            );
        });

        it('should return the list subscriptions', async () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription]);
        });

        it('should return the list user subscriptions when feature flag is on', async () => {
            fakeFeatureFlagService.getBoolean = jest.fn(flagObj => {
                if (flagObj.flag === 'hierarchy-websockets-all-users') return true;
                if (flagObj.flag === 'hierarchy-websockets-use-flexible-subscriptions') return false;
                return false;
            });
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([subscription2]);
        });
    });
});
