import { Injectable } from '@nestjs/common';
import { OperationType } from '@time-loop/ovm-object-version';

import { ScheduledCommentMessageOperator } from './ScheduledCommentMessageOperator';

/**
 * This operator is responsible for processing a TASK or VIEW scheduled comment created event and generating a websocket message
 */
@Injectable()
export class ScheduledCommentCreatedMessageOperator extends ScheduledCommentMessageOperator {
    readonly messageName = 'scheduledCommentCreated';

    protected readonly targetEventType = OperationType.CREATE;
}
