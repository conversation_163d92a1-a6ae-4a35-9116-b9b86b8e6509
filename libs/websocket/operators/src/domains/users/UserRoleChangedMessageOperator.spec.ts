import { Test, TestingModule } from '@nestjs/testing';
import { ObjectType, ObjectVectorChangeEvent } from '@time-loop/ovm-object-version';

import {
    WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
    WebsocketSubscriptionRow,
    WebsocketSubscriptionsService,
} from '@clickup/websocket/subscriptions';

import { TestMessageOperator } from '../TestMessageOperator';
import { testEvents } from '../testObjectVectorChangeEvents';
import { UserRoleChangedMessageOperator } from './UserRoleChangedMessageOperator';

describe('UserRoleChangedMessageOperator', () => {
    const subscriptions: WebsocketSubscriptionRow[] = [
        {
            host: 'host',
            ip: '123-45-67-890',
            userid: 300001,
            version_two: true,
            ws_uuid: 'uuid-for-user-300001',
        },
        {
            host: 'host',
            ip: '123-45-67-999',
            userid: 300003,
            version_two: true,
            ws_uuid: 'uuid-for-user-300003',
        },
    ];

    const userSubcriptions: WebsocketSubscriptionRow[] = [
        {
            host: 'host',
            ip: '123-45-67-891',
            userid: 300002,
            version_two: true,
            ws_uuid: 'uuid-for-user-300002',
        },
    ];

    const fakeSubsService = {
        getTeamSubscriptions: jest.fn().mockResolvedValue(subscriptions),
        getUserSubscriptions: jest.fn().mockResolvedValue(userSubcriptions),
    } as unknown as WebsocketSubscriptionsService;

    let testModule: TestingModule;
    let operator: UserRoleChangedMessageOperator;

    beforeAll(async () => {
        testModule = await Test.createTestingModule({
            providers: [
                UserRoleChangedMessageOperator,
                {
                    provide: WEBSOCKET_SUBSCRIPTIONS_SERVICE_TOKEN,
                    useValue: fakeSubsService,
                },
            ],
        }).compile();
    });

    beforeEach(() => {
        operator = testModule.get(UserRoleChangedMessageOperator);
    });

    describe('shouldTransform', () => {
        it('should return false for undefined or empty events', () => {
            expect(operator.shouldTransform(undefined)).toBe(false);
            expect(operator.shouldTransform(null)).toBe(false);
            expect(operator.shouldTransform({} as unknown as ObjectVectorChangeEvent)).toBe(false);
        });

        it('should only transform a userAccess update event', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            expect(transformableEvents).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "changes": Array [
                        Object {
                          "after": 3,
                          "before": 4,
                          "field": "roleId",
                        },
                      ],
                    },
                    "date_created": 1695772261647,
                    "date_updated": 1695772261647,
                    "deleted": false,
                    "master_id": 0,
                    "object_id": "307136",
                    "object_type": "userAccess",
                    "operation": "u",
                    "traceparent": "32541441-4108-416b-ab00-216e913bc456",
                    "vector": Array [
                      Object {
                        "deleted": false,
                        "master_id": 0,
                        "version": 1695772261648000,
                      },
                    ],
                    "version": 1695772261648000,
                    "workspace_id": 9999000005,
                  },
                  Object {
                    "data": Object {
                      "changes": Array [
                        Object {
                          "after": 12345,
                          "before": null,
                          "field": "customRoleId",
                        },
                      ],
                    },
                    "date_created": 1695772261647,
                    "date_updated": 1695772261647,
                    "deleted": false,
                    "master_id": 0,
                    "object_id": "307136",
                    "object_type": "userAccess",
                    "operation": "u",
                    "traceparent": "32541441-4108-416b-ab00-216e913bc456",
                    "vector": Array [
                      Object {
                        "deleted": false,
                        "master_id": 0,
                        "version": 1695772261648000,
                      },
                    ],
                    "version": 1695772261648000,
                    "workspace_id": 9999000005,
                  },
                ]
            `);
        });
    });

    describe('transform', () => {
        it('should throw an exception when trying to transform an invalid event', () => {
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.USER_ACCESS);
            expect(invalidEvent).toBeDefined();

            expect(() => operator.transform(invalidEvent, 0)).toThrowErrorMatchingInlineSnapshot(
                `"Operator userRoleChanged cannot transform event"`
            );
        });

        it('should transform a userAccess role updated vector event into a WS 2.0 enveloped message', () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const messages = transformableEvents.map(e => operator.transform(e, 15));
            expect(messages).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "event": Object {
                      "date_created": 1695772261647,
                      "from_fanout": false,
                      "name": "userRoleChanged",
                      "operation": "u",
                      "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                      "version_change": Object {
                        "object_id": "307136",
                        "object_type": "userAccess",
                        "vector": Array [
                          Object {
                            "deleted": false,
                            "master_id": 0,
                            "version": 1695772261648000,
                          },
                        ],
                        "workspace_id": 9999000005,
                      },
                      "version_event_data": Object {
                        "changes": Array [
                          Object {
                            "after": 3,
                            "before": 4,
                            "field": "roleId",
                          },
                        ],
                      },
                    },
                    "msg": "userRoleChanged",
                    "ovm_consumer_received_time": 15,
                    "pipeline_context": Object {
                      "ovm_consumer_received_epoch_ms": 15,
                      "ovm_consumer_transformed_epoch_ms": 1697730034510,
                      "ovm_event_publish_time_ms": undefined,
                      "shadow_expired": undefined,
                      "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                    },
                    "schema": "websocket_ovm_message",
                    "version": "2.0.0",
                  },
                  Object {
                    "event": Object {
                      "date_created": 1695772261647,
                      "from_fanout": false,
                      "name": "userRoleChanged",
                      "operation": "u",
                      "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                      "version_change": Object {
                        "object_id": "307136",
                        "object_type": "userAccess",
                        "vector": Array [
                          Object {
                            "deleted": false,
                            "master_id": 0,
                            "version": 1695772261648000,
                          },
                        ],
                        "workspace_id": 9999000005,
                      },
                      "version_event_data": Object {
                        "changes": Array [
                          Object {
                            "after": 12345,
                            "before": null,
                            "field": "customRoleId",
                          },
                        ],
                      },
                    },
                    "msg": "userRoleChanged",
                    "ovm_consumer_received_time": 15,
                    "pipeline_context": Object {
                      "ovm_consumer_received_epoch_ms": 15,
                      "ovm_consumer_transformed_epoch_ms": 1697730034510,
                      "ovm_event_publish_time_ms": undefined,
                      "shadow_expired": undefined,
                      "trace_parent": "32541441-4108-416b-ab00-216e913bc456",
                    },
                    "schema": "websocket_ovm_message",
                    "version": "2.0.0",
                  },
                ]
            `);
        });
    });

    describe('getSubscriptions', () => {
        it('should throw an exception when getting subscriptions for an invalid message', async () => {
            const testOperator = new TestMessageOperator(fakeSubsService);
            const invalidEvent = testEvents.find(event => event.object_type !== ObjectType.USER_ACCESS);
            expect(invalidEvent).toBeDefined();

            const invalidMessage = testOperator.transform(invalidEvent, 0);

            await expect(operator.getSubscriptions(invalidMessage)).rejects.toThrowErrorMatchingInlineSnapshot(
                `"Attempting to get subscriptions for an invalid message testMessage"`
            );
        });

        it('should return the folder subscriptions', async () => {
            const transformableEvents = testEvents.filter(event => operator.shouldTransform(event));
            const message = operator.transform(transformableEvents[0], 0);
            const subs = await operator.getSubscriptions(message);
            expect(subs).toEqual([...subscriptions, ...userSubcriptions]);
        });
    });
});
