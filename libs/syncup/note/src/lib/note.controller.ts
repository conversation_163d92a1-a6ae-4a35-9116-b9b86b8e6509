import { BadRequestException, Body, Controller, Param, Post } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';

import { CreateNoteDto } from '@clickup/syncup/domain';
import { CurrentUserId } from '@clickup/utils/authentication';

import { SyncupNoteService } from './note.service';

@Controller('workspaces/:workspaceId/syncup/note')
export class SyncupNoteController {
    constructor(private readonly syncupNoteService: SyncupNoteService) {}

    @Post()
    @ApiOperation({
        summary: 'Create a syncup note with recording',
        description: 'Create a syncup note with recording',
    })
    async createNote(
        @Param('workspaceId') workspaceId: string,
        @CurrentUserId() userId: number,
        @Body() body: CreateNoteDto
    ): Promise<void> {
        const { recording_id: recordingId } = body;

        await this.syncupNoteService.createNote({
            userId,
            workspaceId,
            recordingId,
        });
    }
}
