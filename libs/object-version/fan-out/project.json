{"name": "object-version-fan-out", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/object-version/fan-out/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/object-version/fan-out/jest.config.ts"}}}}