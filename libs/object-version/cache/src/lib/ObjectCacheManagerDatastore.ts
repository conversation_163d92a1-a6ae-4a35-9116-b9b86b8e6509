import type { DynamoDB } from '@aws-sdk/client-dynamodb';
import { BatchGetItemCommand, PutItemCommand, QueryCommand } from '@aws-sdk/client-dynamodb';
import { marshall } from '@aws-sdk/util-dynamodb';
import type {
    IndexedObjectVersionRow,
    ObjectKey,
    ObjectType,
    ObjectVersionChangeEvent,
    ObjectVersionRow,
    ObjectVersionVector,
} from '@time-loop/ovm-object-version';
import config from 'config';

import type { ObjectVersionCacheManagerDatastoreConfig } from '@clickup/object-version-cache-config';
import { getLogger } from '@clickup/shared/utils-logging';
import { removedMasterIds } from '@clickup-legacy/config/domains/databaseConfig';
import { getBasicConfigTags, metricsClient } from '@clickup-legacy/metrics/metricsClient';
import { isLocal, isTest } from '@clickup-legacy/utils/environment';
import { ClickUpTracer } from '@clickup-legacy/utils/tracer';

const tracer = new ClickUpTracer();

enum MetricNames {
    BatchQueryChangeVersionsCounts = 'ocm.datastore.batchQueryChangeVersions.counts',
    BatchQueryChangeVersionsFailed = 'ocm.datastore.batchQueryChangeVersions.failed',
    QueryChangeVersionsCounts = 'ocm.datastore.queryChangeVersions.counts',
    QueryChangeVersionsFailed = 'ocm.datastore.queryChangeVersions.failed',
    AddObjectChangeEventsCounts = 'ocm.datastore.addObjectChangeEvents.counts',
    AddObjectChangeEventsFailed = 'ocm.datastore.addObjectChangeEvents.failed',
}

const logger = getLogger('ocm-datastore');

// Using keep-alive option since we will be handling events one-by-one

// Primary Key
export const PK = (key: ObjectKey) => `wsid=${key.workspace_id}#type=${key.object_type}#id=${key.object_id}`;
// Sort Key
export const SK = (masterId: number) => `mid=${masterId}`;

/**
 * @TODO(kcannon): extract this out of file when we start to move to having a NestJS Module.
 * Constructs the DynamoDB table name based on the CLICKUP_ENV + CU_REGION environment variables.
 *
 * Table names: https://github.com/time-loop/ovm-workers-cdk/blob/main/test/db/__snapshots__/object-change-versions.test.ts.snap
 */
export const getComputedDynamoDBTableName = (datastoreConfig?: ObjectVersionCacheManagerDatastoreConfig) => {
    const tablePrefix = datastoreConfig?.dynamoDbTablePrefix;
    const cuRegionOverride = datastoreConfig?.cuRegionOverride;

    const shouldAddTableNamePrefix = typeof tablePrefix === 'string' && tablePrefix.length > 0;
    const tableNamePrefix = `${shouldAddTableNamePrefix ? `${tablePrefix}-` : ``}`;
    const tableName = `${tableNamePrefix}object-change-versions`;

    if (isLocal || isTest) {
        return `${isLocal ? 'local' : 'test'}-${tableName}`;
    }

    const cu_region = (cuRegionOverride ?? process.env.CU_REGION)?.toLowerCase().replace('_', '-');
    const cu_env = process.env.CLICKUP_ENV?.toLowerCase();

    if (!cu_region || !cu_env) {
        logger.warn({ msg: 'ObjectChangeVersions table name not generated properly' });
        return `local-${tableName}`;
    }

    return `${cu_env}-${cu_region}-${tableName}`;
};

export class ObjectVersionCacheManagerDatastore {
    OBJECT_CHANGE_VERSION_TTL_MS = 1000 * 60 * 60 * 24 * this.datastoreConfig.changeVersionTTLDays;

    readonly computedTableName = getComputedDynamoDBTableName(this.datastoreConfig);

    readonly metricTags = {
        tableName: this.computedTableName,
        ...getBasicConfigTags(),
    };

    // @TODO: remove top level logger ref and call getLogger from within the class
    readonly logger = logger;

    constructor(
        private readonly dynamoDB: DynamoDB,
        private readonly datastoreConfig: ObjectVersionCacheManagerDatastoreConfig
    ) {
        this.logger.info({
            msg: 'ObjectVersionCacheManagerDatastore initialized',
            tableName: this.computedTableName,
        });
    }

    @tracer.Decorator('ObjectCacheManagerDatastore.addObjectChangeEvents')
    async addObjectChangeEvents(events: ObjectVersionChangeEvent[]): Promise<void> {
        tracer.addTagsToActiveScope({ 'events.length': events.length, tableName: this.computedTableName });

        try {
            metricsClient.increment(MetricNames.AddObjectChangeEventsCounts, events.length, this.metricTags);

            const resp = await Promise.allSettled(
                events.map(event =>
                    this.dynamoDB.send(
                        new PutItemCommand({
                            TableName: this.computedTableName,
                            Item: {
                                pk: { S: PK(event) },
                                sk: { S: SK(event.master_id) },
                                workspace_id: { N: `${event.workspace_id}` },
                                object_type: { S: event.object_type },
                                object_id: { S: event.object_id },
                                master_id: { N: `${event.master_id}` },
                                version: { N: `${event.version}` },
                                deleted: { BOOL: !!event.deleted },
                                operation: { S: event.operation },
                                data: { M: event.data ? marshall(event.data) : {} },
                                // epoch time in seconds of when the record should expire
                                ttl: { N: `${Math.floor((Date.now() + this.OBJECT_CHANGE_VERSION_TTL_MS) / 1000)}` },
                            },
                            ConditionExpression: '#currentVersion < :newVersion OR attribute_not_exists(pk)',
                            ExpressionAttributeNames: {
                                '#currentVersion': 'version',
                            },
                            ExpressionAttributeValues: {
                                ':newVersion': { N: `${event.version}` },
                            },
                        })
                    )
                )
            );

            resp.forEach(res => {
                if (res.status === 'rejected') {
                    // condition check failed means that a newer version is already in the table
                    if (res.reason.name !== 'ConditionalCheckFailedException') {
                        metricsClient.increment(MetricNames.AddObjectChangeEventsFailed, 1, {
                            reason: res.reason.name,
                            ...this.metricTags,
                        });
                    }
                }
            });
        } catch (err) {
            metricsClient.increment(MetricNames.AddObjectChangeEventsFailed, 1, this.metricTags);
            this.logger.error({
                msg: 'Failed to add object change events to DynamoDB',
                err,
            });
        }
    }

    @tracer.Decorator('ObjectCacheManagerDatastore.queryChangeVersions')
    async queryChangeVersions(keys: ObjectKey[]): Promise<IndexedObjectVersionRow[]> {
        tracer.addTagsToActiveScope({ 'keys.length': keys.length, tableName: this.computedTableName });

        try {
            metricsClient.increment(MetricNames.QueryChangeVersionsCounts, keys.length, this.metricTags);

            const partitionKeys = keys.map(key => PK(key));

            const dynamoQueryResult = await Promise.all(
                partitionKeys.map(key =>
                    this.dynamoDB.send(
                        new QueryCommand({
                            TableName: this.computedTableName,
                            ConsistentRead: true,
                            KeyConditionExpression: 'pk = :pk',
                            ExpressionAttributeValues: {
                                ':pk': { S: key },
                            },
                        })
                    )
                )
            );

            let index = 0;
            const excludedMasterIds = removedMasterIds();
            const results: IndexedObjectVersionRow[] = [];
            for (const queryResult of dynamoQueryResult) {
                for (const item of queryResult.Items) {
                    const master_id = parseInt(item.master_id.N, 10);
                    if (!excludedMasterIds.has(master_id)) {
                        results.push({
                            workspace_id: parseInt(item.workspace_id.N, 10),
                            object_type: item.object_type.S as ObjectType,
                            object_id: item.object_id.S,
                            master_id,
                            version: parseInt(item.version.N, 10),
                            deleted: item.deleted.BOOL,
                            index,
                        });
                    }
                }
                index++;
            }

            return results;
        } catch (err) {
            metricsClient.increment(MetricNames.QueryChangeVersionsFailed, 1, this.metricTags);
            this.logger.warn({
                msg: 'Failed to query change versions from DynamoDB',
                tableName: this.computedTableName,
                err,
            });
            return [];
        }
    }

    @tracer.Decorator('ObjectCacheManagerDatastore.getChangeVersions')
    async getChangeVersions(consumer: number, keys: ObjectKey[]): Promise<ObjectVersionVector[]> {
        tracer.addTagsToActiveScope({
            consumer,
            'keys.length': keys.length,
            tableName: this.computedTableName,
        });

        const rows = await this.queryChangeVersions(keys);
        const result = new Array<ObjectVersionVector>(keys.length);

        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            result[i] = {
                workspace_id: key.workspace_id,
                object_type: key.object_type,
                object_id: key.object_id,
                vector: [],
            };
        }

        for (let i = 0; i < rows.length; i++) {
            const row = rows[i];
            result[row.index].vector.push({
                master_id: row.master_id,
                version: row.version,
                deleted: row.deleted,
            });
        }

        return result;
    }

    @tracer.Decorator('ObjectCacheManagerDatastore.batchQueryChangeVersions')
    async batchQueryChangeVersions(vectors: ObjectVersionVector[]): Promise<ObjectVersionRow[]> {
        tracer.addTagsToActiveScope({ 'vectors.length': vectors.length, tableName: this.computedTableName });

        try {
            metricsClient.increment(MetricNames.BatchQueryChangeVersionsCounts, vectors.length, this.metricTags);

            const keyTuplesMap = new Map<string, [string, string]>();

            for (const vector of vectors) {
                for (const version of vector.vector) {
                    const key = `${PK(vector)}::${SK(version.master_id)}`;
                    keyTuplesMap.set(key, [PK(vector), SK(version.master_id)]);
                }
            }

            const sendBatchCommand = async (batchKeys: [string, string][]) => {
                const results: ObjectVersionRow[] = [];
                const params = {
                    RequestItems: {
                        [this.computedTableName]: {
                            Keys: batchKeys.map(keyTuple => ({
                                pk: { S: keyTuple[0] },
                                sk: { S: keyTuple[1] },
                            })),
                        },
                    },
                };

                const result = await this.dynamoDB.send(new BatchGetItemCommand(params));

                for (const item of result.Responses?.[this.computedTableName] ?? []) {
                    const master_id = parseInt(item.master_id.N, 10);
                    if (!removedMasterIds().has(master_id)) {
                        results.push({
                            workspace_id: parseInt(item.workspace_id.N, 10),
                            object_type: item.object_type.S as ObjectType,
                            object_id: item.object_id.S,
                            master_id,
                            version: parseInt(item.version.N, 10),
                            deleted: item.deleted.BOOL,
                        });
                    }
                }
                return results;
            };

            const keyTuples = Array.from(keyTuplesMap.values());
            const promises: Promise<ObjectVersionRow[]>[] = [];

            // DynamoDB allows for querying batches of 100 items at a time
            for (let i = 0; i < keyTuples.length; i += 100) {
                const batchKeys = keyTuples.slice(i, i + 100);
                promises.push(sendBatchCommand(batchKeys));
            }

            const versionRows: ObjectVersionRow[] = [];
            const settledResults = await Promise.allSettled(promises);

            for (const result of settledResults) {
                if (result.status === 'fulfilled') {
                    versionRows.push(...result.value);
                } else {
                    metricsClient.increment(MetricNames.BatchQueryChangeVersionsFailed, 1, this.metricTags);
                    this.logger.warn({
                        msg: 'Failed to run batch query for change versions from DynamoDB',
                        tableName: this.computedTableName,
                        err: result.reason,
                    });
                }
            }
            return versionRows;
        } catch (err) {
            this.logger.error({
                msg: 'Processing error during batchQueryChangeVersions',
                err,
            });
            return [];
        }
    }
}
