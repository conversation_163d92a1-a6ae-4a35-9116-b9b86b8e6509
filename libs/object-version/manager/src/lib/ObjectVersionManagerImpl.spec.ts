/* eslint-disable import/first */
import type { ObjectVersionManagerDatastore } from '@clickup/object-version-manager-datastore';

process.env.DD_SERVICE = 'test-service';

import type {
    IndexedObjectVersionRow,
    ObjectVersionChangeEvent,
    ObjectVersionUpdateRequest,
    ObjectVersionVector,
} from '@time-loop/ovm-object-version';
import { ObjectType, OperationType } from '@time-loop/ovm-object-version';
import { omit } from 'lodash';

// eslint-disable-next-line @nx/enforce-module-boundaries,@nx/workspace/enforce-module-boundaries
import type { ObjectVersionManager, ObjectVersionTransactionClient } from '@clickup/object-version-common';
import { ObjectVersionHelperService } from '@clickup/object-version-helpers';
import type { NotificationManager } from '@clickup/object-version-notification-manager';
import { ovmSchemaValidationConfig } from '@clickup/object-version-split-treatments';
import { enableAuditLogForUserActivity } from '@clickup-legacy/models/integrations/split/squadTreatments/userPlatformTreatments';
import { getCurrentCallContext } from '@clickup-legacy/utils/auth/getCurrentCallContext';
import * as errors from '@clickup-legacy/utils/errors';

import { hasEncryptedComment, ObjectVersionManagerImpl } from './ObjectVersionManagerImpl';
import { testRequests } from './test/testVersionRequests';

jest.mock('node-uuid', () => ({
    v4: jest.fn(() => 'abcdef01-2345-2abc-9876-123456fedcba'),
}));

jest.mock('@clickup/shared/utils-logging', () => {
    const testLogger = {
        info: jest.fn(),
        debug: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };

    return {
        getLogger: jest.fn(() => testLogger),
        testLogger,
    };
});

jest.mock('@clickup-legacy/utils/errors', () => {
    const logErrorFn = jest.fn();

    const mockError = jest.fn((msg, ECODE, statusCode) => ({
        msg,
        ECODE,
        statusCode,
        logError: jest.fn(() => logErrorFn),
    }));

    const mockBasicLogger = {
        info: jest.fn(),
        debug: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
    };

    class ClickUpErrorMock {
        static makeNamedError() {
            return mockError;
        }

        static getBasicLogger() {
            return mockBasicLogger;
        }
    }

    return {
        ClickUpError: ClickUpErrorMock,
        logErrorFn,
        mockError,
        mockBasicLogger,
    };
});

jest.mock('@clickup-legacy/utils/auth/getCurrentCallContext', () => ({
    getCurrentCallContext: jest.fn(),
}));

jest.mock('@clickup/object-version-cache', () => ({
    queryChangeVersions: jest.fn(),
}));

jest.mock('@clickup/object-version-split-treatments', () => ({
    ovmSchemaValidationConfig: jest.fn(),
}));

jest.mock('@clickup-legacy/models/integrations/split/squadTreatments/userPlatformTreatments', () => ({
    enableAuditLogForUserActivity: jest.fn(),
}));

const mockTransactionClient = {
    masterId: 0,
    done: jest.fn(),
    beginAsync: jest.fn(),
    commitAsync: jest.fn(),
    rollbackAsync: jest.fn(),
    queryAsync: jest.fn(),
    batchQueriesAsync: jest.fn(),
    recordEvents: jest.fn(),
    incrOvmUpsert: jest.fn(),
    getHostIdentifier: jest.fn(),
};

const clone: <T>(obj: T) => T = obj => JSON.parse(JSON.stringify(obj));

const ObjectVersionManagerDatastoreMock = jest.fn(() => ({
    addObjectChangeEvents: jest.fn(),
    getChangeVersions: jest.fn(),
    queryObjectVersionVectors: jest.fn(),
    addNewObjectVersions: jest.fn(),
    removeDeletedVersions: jest.fn(),
}));
const NotificationManagerMock = jest.fn(() => ({
    notify: jest.fn(),
}));

describe('ObjectVersionManagerImpl', () => {
    const objectVersionManagerDatastoreMock =
        new ObjectVersionManagerDatastoreMock() as unknown as ObjectVersionManagerDatastore;
    const objectVersionHelperServiceMock = new ObjectVersionHelperService(objectVersionManagerDatastoreMock);
    const notificationManagerMock =
        new NotificationManagerMock() as unknown as NotificationManager<ObjectVersionChangeEvent>;

    const masterId = 1;
    const objectId = 'object_id';
    const version = 1;
    const workspaceId = 1;

    const objectVersionRow = {
        workspace_id: workspaceId,
        object_type: ObjectType.TASK,
        object_id: objectId,
        master_id: masterId,
        version,
        index: 0,
    };

    describe('hasVersionVectors()', () => {
        const client = mockTransactionClient as unknown as ObjectVersionTransactionClient;
        let getObjectVectorSpy: jest.SpyInstance = jest.fn();

        let objectVersionManager: ObjectVersionManager;
        let objectVectorsRequest: ObjectVersionVector[];
        let objectVersionRows: IndexedObjectVersionRow[];

        beforeEach(() => {
            objectVersionManager = new ObjectVersionManagerImpl(
                objectVersionManagerDatastoreMock,
                objectVersionHelperServiceMock,
                notificationManagerMock
            );

            objectVectorsRequest = [
                {
                    workspace_id: workspaceId,
                    object_type: ObjectType.TASK,
                    object_id: objectId,
                    vector: [{ master_id: masterId, version }],
                },
            ];

            objectVersionRows = [objectVersionRow];

            getObjectVectorSpy = (
                objectVersionManagerDatastoreMock.queryObjectVersionVectors as jest.Mock
            ).mockResolvedValueOnce({
                rows: objectVersionRows,
            });
        });

        afterEach(() => {
            objectVersionManager = undefined as unknown as ObjectVersionManager;
            getObjectVectorSpy.mockRestore();
        });

        it('should call with correct parameters', async () => {
            await objectVersionManager.hasVersionVectors(client, objectVectorsRequest);

            expect(getObjectVectorSpy.mock.calls[0][0]).toBe(client);
            expect(getObjectVectorSpy.mock.calls[0][1]).toEqual([objectVectorsRequest[0]]);
        });

        it('should return an empty array when input version vectors are empty', async () => {
            const response = await objectVersionManager.hasVersionVectors(client, []);

            expect(response.length).toBe(0);
        });

        it('should return false when getObjectVersionVector() is success but object is not found', async () => {
            const objectVersionResponse: IndexedObjectVersionRow[] = [
                {
                    master_id: masterId,
                    object_id: objectId,
                    object_type: ObjectType.TASK,
                    version: 0, // setting version to a lower value to fail compare version call.
                    workspace_id: workspaceId,
                    index: 0,
                },
            ];

            getObjectVectorSpy.mockRestore();
            jest.spyOn(objectVersionManagerDatastoreMock, 'queryObjectVersionVectors').mockResolvedValueOnce({
                rows: objectVersionResponse,
            });

            const response = await objectVersionManager.hasVersionVectors(client, objectVectorsRequest);

            expect(response.length).toBe(1);
            expect(response[0]).toBe(false);
        });

        it('should return true when getObjectVersionVector() is success and object is found', async () => {
            const response = await objectVersionManager.hasVersionVectors(client, objectVectorsRequest);

            expect(response.length).toBe(1);
            expect(response[0]).toBe(true);
        });
    });

    describe('removeDeleted()', () => {
        let removeDeletedVersionsSpy: jest.SpyInstance = jest.fn();
        let objectVersionManager: ObjectVersionManager;

        beforeEach(() => {
            objectVersionManager = new ObjectVersionManagerImpl(
                objectVersionManagerDatastoreMock,
                objectVersionHelperServiceMock,
                notificationManagerMock
            );
        });

        afterEach(() => {
            removeDeletedVersionsSpy.mockRestore();
        });

        it('should throw error when an error is thrown in removeDeletedVersions()', async () => {
            removeDeletedVersionsSpy = jest
                .spyOn(objectVersionManagerDatastoreMock, 'removeDeletedVersions')
                .mockRejectedValue(new Error('error!'));

            try {
                await objectVersionManager.removeDeleted(1, 1, 1);
            } catch (error) {
                expect((error as Error).message).toBe('error!');
            }
        });

        it('should return 0 when no rows are deleted', async () => {
            removeDeletedVersionsSpy = jest
                .spyOn(objectVersionManagerDatastoreMock, 'removeDeletedVersions')
                .mockResolvedValueOnce(0);

            const result = await objectVersionManager.removeDeleted(1, 1, 1);

            expect(result).toBe(0);
        });

        it('should return 1 when 1 row is deleted', async () => {
            removeDeletedVersionsSpy = jest
                .spyOn(objectVersionManagerDatastoreMock, 'removeDeletedVersions')
                .mockResolvedValueOnce(1);

            const result = await objectVersionManager.removeDeleted(1, 1, 1);

            expect(result).toBe(1);
            expect(removeDeletedVersionsSpy).toHaveBeenCalledTimes(1);
        });

        it('should return the number of deleted rows when rows are deleted', async () => {
            removeDeletedVersionsSpy = jest
                .spyOn(objectVersionManagerDatastoreMock, 'removeDeletedVersions')
                .mockResolvedValueOnce(1)
                .mockResolvedValueOnce(2);

            const result = await objectVersionManager.removeDeleted(1, 1, 2);

            expect(result).toBe(3);
            expect(removeDeletedVersionsSpy).toHaveBeenCalledTimes(2);
        });
    });

    describe('updateVersions()', () => {
        const client = mockTransactionClient as unknown as ObjectVersionTransactionClient;
        const objectVersionManagerImpl = new ObjectVersionManagerImpl(
            objectVersionManagerDatastoreMock,
            objectVersionHelperServiceMock,
            notificationManagerMock
        );

        beforeEach(() => {
            (ovmSchemaValidationConfig as jest.Mock).mockReset();
            (ovmSchemaValidationConfig as jest.Mock).mockReturnValue({
                useValidation: true,
                maxStringLength: 1024,
                maxChatEventStringLength: 1024 * 3,
                maxChangesLength: 50,
                maxRelationshipsLength: 20,
                maxQueryLength: 1024,
                maxDataSize: 24000, // matching the topic max.message.size of 24KB (conservative since the topic's max is compressed)
            });
        });

        afterEach(() => {
            jest.clearAllMocks();
        });

        it('returns an empty array when versionRequests is undefined', async () => {
            const events = await objectVersionManagerImpl.updateVersions(client, null as any);

            expect(events.length).toBe(0);
        });

        it('returns an empty array when versionRequests is empty', async () => {
            const events = await objectVersionManagerImpl.updateVersions(client, []);

            expect(events.length).toBe(0);
        });

        it('returns an empty array when there are no valid versionRequests', async () => {
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: undefined as any,
                },
            ];

            const events = await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(events.length).toBe(0);
        });

        it('should process versions and clean unmarshalled relationships', async () => {
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'object_id',
                    object_type: ObjectType.ATTACHMENT,
                    workspace_id: 123,
                    data: {
                        relationships: undefined,
                    },
                } as ObjectVersionUpdateRequest,
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);
            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "context": Object {
                        "originating_service": "test-service",
                      },
                    },
                    "object_id": "object_id",
                    "object_type": "attachment",
                    "operation": "c",
                    "workspace_id": 123,
                  },
                ]
            `);
        });

        it('should process versions and clean unmarshalled CDC changes', async () => {
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'object_id',
                    object_type: ObjectType.ATTACHMENT,
                    workspace_id: 123,
                    data: {
                        changes: [
                            {
                                field: 'field',
                                before: undefined,
                                after: 'some value',
                                history_id: undefined,
                            },
                        ],
                    },
                } as ObjectVersionUpdateRequest,
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "changes": Array [
                        Object {
                          "after": "some value",
                          "field": "field",
                        },
                      ],
                      "context": Object {
                        "originating_service": "test-service",
                      },
                    },
                    "object_id": "object_id",
                    "object_type": "attachment",
                    "operation": "c",
                    "workspace_id": 123,
                  },
                ]
            `);
        });

        it('should process versions when optional context ws_key is not defined', async () => {
            const options: { ws_key?: string } = {};

            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'object_id',
                    object_type: ObjectType.ATTACHMENT,
                    workspace_id: 123,
                    data: {
                        context: { ws_key: options.ws_key },
                    },
                },
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "context": Object {
                        "originating_service": "test-service",
                      },
                    },
                    "object_id": "object_id",
                    "object_type": "attachment",
                    "operation": "c",
                    "workspace_id": 123,
                  },
                ]
            `);
        });

        it('should process versions when optional context ws_key is null (stripping the null value)', async () => {
            const versionRequests = [
                {
                    operation: OperationType.UPDATE,
                    object_id: 'object_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 123,
                    data: {
                        context: { ws_key: null as unknown as string },
                    },
                },
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "context": Object {
                        "originating_service": "test-service",
                      },
                    },
                    "object_id": "object_id",
                    "object_type": "task",
                    "operation": "u",
                    "workspace_id": 123,
                  },
                ]
            `);
        });

        it.each(testRequests)(
            'should not process requests which were reported as invalid - OVM_WS_003',
            async (request, isValid) => {
                const addNewObjectVersionsSpy: jest.SpyInstance = jest
                    .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                    .mockResolvedValue([]);

                await objectVersionManagerImpl.updateVersions(client, [request]);

                expect(addNewObjectVersionsSpy.mock.calls.length).toBe(isValid ? 1 : 0);
            }
        );

        it('should not process attachment versions longer than 128 characters', async () => {
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id:
                        'very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_attachment_id',
                    object_type: ObjectType.ATTACHMENT,
                    workspace_id: 123,
                },
            ];

            const events = await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(events.length).toBe(0);
        });

        it('should not process attachment versions longer than 128 characters even when ovm validation is OFF', async () => {
            (ovmSchemaValidationConfig as jest.Mock).mockReset();
            (ovmSchemaValidationConfig as jest.Mock).mockReturnValue({
                useValidation: false,
            });

            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id:
                        'very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_very_long_attachment_id',
                    object_type: ObjectType.ATTACHMENT,
                    workspace_id: 123,
                },
            ];

            const events = await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(events.length).toBe(0);
        });

        it('should not process the request if its size exceeds the max size configured in split', async () => {
            (ovmSchemaValidationConfig as jest.Mock).mockReset();
            (ovmSchemaValidationConfig as jest.Mock).mockReturnValue({
                useValidation: true,
                maxStringLength: 100,
                maxChangesLength: 10,
                maxRelationshipsLength: 10,
                maxQueryLength: 100,
                maxDataSize: 100, // data size should be well above that test value
            });

            const changes = [1, 2, 3, 4, 5].map(i => ({
                field: `field${i}`,
                before: `before${i}`,
                after: `after${i}`,
            }));

            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    data: {
                        changes,
                    },
                },
            ];

            const events = await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(events.length).toBe(0);
        });

        it('should not process the request for a non-chat message overly long content', async () => {
            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            const longString = 'X'.repeat(1024);
            const changes = [
                {
                    field: 'NOT_encrypted_comment',
                    before: '...',
                    after: `v1:ey....00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000${longString}`,
                },
            ];

            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: '9999999999',
                    object_type: ObjectType.COMMENT,
                    workspace_id: 333,
                    data: {
                        changes,
                    },
                },
            ];

            const events = await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(0);
        });

        it('should not process the request for a message with overly long content', async () => {
            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            const longString = 'X'.repeat(1024 * 5);
            const changes = [
                {
                    field: 'NOT_encrypted_comment',
                    before: '...',
                    after: `v1:ey....00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000${longString}`,
                },
            ];

            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: '9999999999',
                    object_type: ObjectType.COMMENT,
                    workspace_id: 333,
                    data: {
                        changes,
                    },
                },
            ];

            const events = await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(0);
        });

        it('should process the request for a chat message with encrypted_comment given its under the limit', async () => {
            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            const longString = 'X'.repeat(1024 * 2);
            const changes = [
                {
                    field: 'encrypted_comment',
                    before: '...',
                    after: `v1:ey....00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000${longString}`,
                },
            ];

            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: '9999999999',
                    object_type: ObjectType.COMMENT,
                    workspace_id: 333,
                    data: {
                        changes,
                    },
                },
            ];

            const events = await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toMatchObject([
                {
                    data: {
                        changes: [
                            {
                                after: expect.stringMatching(/^v1:/),
                                before: expect.any(String),
                                field: 'encrypted_comment',
                            },
                        ],
                        context: {
                            originating_service: 'test-service',
                        },
                    },
                    object_id: '9999999999',
                    object_type: 'comment',
                    operation: 'c',
                    workspace_id: 333,
                },
            ]);
        });

        it('only adds unique versions', async () => {
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                },
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                },
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toEqual([
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    data: {
                        context: { originating_service: 'test-service' },
                    },
                },
            ]);
        });

        it('does not overrides the new version', async () => {
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                },
                {
                    operation: OperationType.UPDATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                },
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toEqual([
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    data: {
                        context: { originating_service: 'test-service' },
                    },
                },
            ]);
        });

        it('overrides the new version', async () => {
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                },
                {
                    operation: OperationType.DELETE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                },
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toEqual([
                {
                    operation: OperationType.DELETE,
                    object_id: 'task_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    data: {
                        context: { originating_service: 'test-service' },
                    },
                },
            ]);
        });

        it('should include cdc data always - no more split flags', async () => {
            // Arrange: data
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id1',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    // note data omitted here: want to check handling of falsy
                },
                {
                    operation: OperationType.UPDATE,
                    object_id: 'task_id2',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    data: {
                        changes: [
                            {
                                history_id: 'history_id',
                                field: 'field',
                            },
                        ],
                    },
                },
            ];

            // Arrange: spies
            const addNewObjectVersionsSpy: jest.SpyInstance = jest.spyOn(
                objectVersionManagerDatastoreMock,
                'addNewObjectVersions'
            );

            // Act
            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            // Assert
            expect(addNewObjectVersionsSpy).toHaveBeenCalledTimes(1);
            expect(addNewObjectVersionsSpy).toHaveBeenCalledWith(
                client,
                [
                    {
                        operation: OperationType.CREATE,
                        object_id: 'task_id1',
                        object_type: ObjectType.TASK,
                        workspace_id: 1234,
                        data: {
                            context: { originating_service: 'test-service' },
                        },
                    },
                    {
                        operation: OperationType.UPDATE,
                        object_id: 'task_id2',
                        object_type: ObjectType.TASK,
                        workspace_id: 1234,
                        data: {
                            changes: [
                                {
                                    history_id: 'history_id',
                                    field: 'field',
                                },
                            ],
                            context: { originating_service: 'test-service' },
                        },
                    },
                ],
                'abcdef01-2345-2abc-9876-123456fedcba'
            );
            expect(versionRequests).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "context": Object {
                        "originating_service": "test-service",
                      },
                    },
                    "object_id": "task_id1",
                    "object_type": "task",
                    "operation": "c",
                    "workspace_id": 1234,
                  },
                  Object {
                    "data": Object {
                      "changes": Array [
                        Object {
                          "field": "field",
                          "history_id": "history_id",
                        },
                      ],
                      "context": Object {
                        "originating_service": "test-service",
                      },
                    },
                    "object_id": "task_id2",
                    "object_type": "task",
                    "operation": "u",
                    "workspace_id": 1234,
                  },
                ]
            `);
        });

        it('should add audit log context to version change event', async () => {
            // Arrange: data
            const versionRequests = [
                {
                    operation: OperationType.CREATE,
                    object_id: 'task_id1',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    // note data omitted here: want to check handling of falsy
                },
                {
                    operation: OperationType.UPDATE,
                    object_id: 'task_id2',
                    object_type: ObjectType.TASK,
                    workspace_id: 1234,
                    data: {
                        changes: [
                            {
                                history_id: 'history_id',
                                field: 'field',
                            },
                        ],
                        context: {
                            ws_key: 'test-key',
                        },
                    },
                },
            ];

            (enableAuditLogForUserActivity as jest.Mock).mockReset();
            (enableAuditLogForUserActivity as jest.Mock).mockReturnValue({
                add_audit_context_to_ovm: true,
            });

            const addNewObjectVersionsSpy: jest.SpyInstance = jest.spyOn(
                objectVersionManagerDatastoreMock,
                'addNewObjectVersions'
            );

            (getCurrentCallContext as jest.Mock).mockReturnValueOnce({
                userid: 123,
                ip: '***************',
            });

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy).toHaveBeenCalledTimes(1);
            expect(addNewObjectVersionsSpy).toHaveBeenCalledWith(
                client,
                expect.arrayContaining([
                    expect.objectContaining({
                        data: expect.objectContaining({
                            context: expect.objectContaining({
                                // (!) we expect the context to be added
                                audit_context: {
                                    userid: 123,
                                    ip: '***************',
                                },
                            }),
                        }),
                    }),
                    expect.objectContaining({
                        data: expect.objectContaining({
                            context: expect.objectContaining({
                                ws_key: 'test-key',
                                // (!) we expect the context to be merged
                                audit_context: {
                                    userid: 123,
                                    ip: '***************',
                                },
                            }),
                        }),
                    }),
                ]),
                'abcdef01-2345-2abc-9876-123456fedcba'
            );

            expect(versionRequests).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "context": Object {
                        "audit_context": Object {
                          "ip": "***************",
                          "userid": 123,
                        },
                        "originating_service": "test-service",
                      },
                    },
                    "object_id": "task_id1",
                    "object_type": "task",
                    "operation": "c",
                    "workspace_id": 1234,
                  },
                  Object {
                    "data": Object {
                      "changes": Array [
                        Object {
                          "field": "field",
                          "history_id": "history_id",
                        },
                      ],
                      "context": Object {
                        "audit_context": Object {
                          "ip": "***************",
                          "userid": 123,
                        },
                        "originating_service": "test-service",
                        "ws_key": "test-key",
                      },
                    },
                    "object_id": "task_id2",
                    "object_type": "task",
                    "operation": "u",
                    "workspace_id": 1234,
                  },
                ]
            `);
        });

        it('should process versions where originating_service defaults to value test-service (process.env.DD_SERVICE)', async () => {
            const versionRequests = [
                {
                    operation: OperationType.UPDATE,
                    object_id: 'object_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 123,
                    data: {
                        context: { ws_key: 'test-key' },
                    },
                },
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "context": Object {
                        "originating_service": "test-service",
                        "ws_key": "test-key",
                      },
                    },
                    "object_id": "object_id",
                    "object_type": "task",
                    "operation": "u",
                    "workspace_id": 123,
                  },
                ]
            `);
        });

        it('should process versions where default originating_service value is overriden by value set in context', async () => {
            const versionRequests = [
                {
                    operation: OperationType.UPDATE,
                    object_id: 'object_id',
                    object_type: ObjectType.TASK,
                    workspace_id: 123,
                    data: {
                        context: { ws_key: 'test-key', originating_service: 'some-service-name' },
                    },
                },
            ];

            const addNewObjectVersionsSpy: jest.SpyInstance = jest
                .spyOn(objectVersionManagerDatastoreMock, 'addNewObjectVersions')
                .mockResolvedValue([]);

            await objectVersionManagerImpl.updateVersions(client, versionRequests);

            expect(addNewObjectVersionsSpy.mock.calls.length).toBe(1);
            expect(addNewObjectVersionsSpy.mock.calls[0][1]).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "data": Object {
                      "context": Object {
                        "originating_service": "some-service-name",
                        "ws_key": "test-key",
                      },
                    },
                    "object_id": "object_id",
                    "object_type": "task",
                    "operation": "u",
                    "workspace_id": 123,
                  },
                ]
            `);
        });
    });

    describe('notifyChanges()', () => {
        const objectVersionManagerImpl = new ObjectVersionManagerImpl(
            objectVersionManagerDatastoreMock,
            objectVersionHelperServiceMock,
            notificationManagerMock
        );

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { mockBasicLogger } = errors as any;

        const versionChangeEvent: ObjectVersionChangeEvent = {
            ...omit(objectVersionRow, 'index'),
            operation: OperationType.UPDATE,
            traceparent: 'trace-parent',
            date_created: 123456789,
            date_updated: 123456789,
        };

        beforeEach(() => {
            (notificationManagerMock.notify as jest.Mock).mockClear();
            (mockBasicLogger.warn as jest.Mock).mockClear();
            jest.useFakeTimers({
                advanceTimers: true,
                now: new Date('2024-01-22 01:00:00 GMT-0700'),
            });
        });

        it('calls the notification manager to notify on events', async () => {
            await objectVersionManagerImpl.notifyChanges([versionChangeEvent]);

            expect(notificationManagerMock.notify).toHaveBeenCalledWith([versionChangeEvent]);
            expect(versionChangeEvent.event_publish_time).toBe(1705910400000);
        });

        it('successfully patches audit_context inside of notifyChanges', async () => {
            (enableAuditLogForUserActivity as jest.Mock).mockReset();
            (enableAuditLogForUserActivity as jest.Mock).mockReturnValue({
                add_audit_context_to_ovm: true,
            });
            (getCurrentCallContext as jest.Mock).mockReturnValueOnce({
                userid: 123,
                ip: '***************',
            });
            await objectVersionManagerImpl.notifyChanges([versionChangeEvent]);

            const cloneVersionChangeEvent = clone(versionChangeEvent);
            cloneVersionChangeEvent.data = {
                context: {
                    audit_context: {
                        userid: 123,
                        ip: '***************',
                    },
                },
            };

            expect(notificationManagerMock.notify).toHaveBeenCalledWith([cloneVersionChangeEvent]);
        });

        it('turns into a no-op if no events are being passed in', async () => {
            await objectVersionManagerImpl.notifyChanges(undefined as unknown as ObjectVersionChangeEvent[]);
            await objectVersionManagerImpl.notifyChanges([]);

            expect(notificationManagerMock.notify).not.toHaveBeenCalled();
        });

        it('Do not filter out duplicate events as part of the notification processing', async () => {
            const clone1 = clone(versionChangeEvent);
            const clone2 = clone(versionChangeEvent);

            await objectVersionManagerImpl.notifyChanges([versionChangeEvent, clone1, clone2]);

            expect(notificationManagerMock.notify).toHaveBeenCalledWith([versionChangeEvent, clone1, clone2]);
        });

        it('does filter out any event that does not pass validation', async () => {
            const clone1 = clone(versionChangeEvent);
            clone1.operation = 'invalid' as OperationType;

            await objectVersionManagerImpl.notifyChanges([versionChangeEvent, clone1]);

            expect(notificationManagerMock.notify).toHaveBeenCalledWith([versionChangeEvent]);
        });

        it('will warn of a failure to notify but not throw an exception when failing', async () => {
            (notificationManagerMock.notify as jest.Mock).mockRejectedValueOnce(new Error('test error'));

            await expect(objectVersionManagerImpl.notifyChanges([versionChangeEvent])).resolves.not.toThrow();

            expect(notificationManagerMock.notify).toHaveBeenCalled();
            expect((mockBasicLogger.warn as jest.Mock).mock.calls[0]).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "error": [Error: test error],
                    "msg": "OVM - Failed to notify Object Version Change Events",
                  },
                ]
            `);
        });
    });

    describe('hasEncryptedComment()', () => {
        const event = {
            operation: OperationType.CREATE,
            object_id: 'comment_id',
            object_type: ObjectType.COMMENT,
            workspace_id: 1234,
            data: {
                changes: [
                    {
                        field: 'encrypted_comment',
                        before: '...',
                        after: 'v1:ey....',
                    },
                ],
            },
            traceparent: 'trace-parent',
            date_created: 123456789,
            date_updated: 123456789,
            master_id: 123,
            version: 1,
        };
        it('should return true for encrypted comments', () => {
            const _hasEncryptedComment = hasEncryptedComment(event);
            expect(_hasEncryptedComment).toBe(true);
        });

        it('should return false for non-encrypted comments', () => {
            const nonEncryptedEvent = {
                ...event,
                data: {
                    changes: [
                        {
                            field: 'blah',
                            before: '...',
                            after: '...',
                        },
                    ],
                },
            };
            const _hasEncryptedComment = hasEncryptedComment(nonEncryptedEvent);
            expect(_hasEncryptedComment).toBe(false);
        });

        it('should return false for non-comment create events', () => {
            const nonCommentEvent = {
                ...event,
                object_type: ObjectType.TASK,
            };
            const _hasEncryptedComment = hasEncryptedComment(nonCommentEvent);
            expect(_hasEncryptedComment).toBe(false);
        });

        it('should return false for non-comment update events', () => {
            const nonCommentEvent = {
                ...event,
                operation: OperationType.UPDATE,
            };
            const _hasEncryptedComment = hasEncryptedComment(nonCommentEvent);
            expect(_hasEncryptedComment).toBe(false);
        });
    });
});
