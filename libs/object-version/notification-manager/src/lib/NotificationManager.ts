import type { EventMetadata } from '@time-loop/ovm-object-version';

import type { EventsListener } from '@clickup/object-version-common';

/**
 * A base implementation of a generic notification manager.
 */
export abstract class NotificationManager<T extends EventMetadata> {
    public abstract start(): void;

    public abstract stop(): Promise<void>;

    public abstract processEvents(events: T[]): Promise<void>;

    /**
     * Register a new listener to receive locally generated events.
     */
    public abstract registerListeners(...listeners: EventsListener<T>[]): void;

    /**
     * Register a new listener to receive events and process them synchronously.
     */
    public abstract registerImmediateListeners(...listeners: EventsListener<T>[]): void;

    /**
     * A simple pass through to all registered listeners
     */
    public abstract notify(events: T[]): Promise<void>;

    /**
     * A helper function used to process events for a given list of listeners
     */
    public static async fireListeners<T extends EventMetadata>(
        events: T[],
        listeners: EventsListener<T>[]
    ): Promise<void> {
        for (let i = 0; i < listeners.length; i++) {
            const results: boolean[] = await listeners[i].processEvents(events);
            if (results != null) {
                // we have individualized event propagation results
                const nextEventsToProcess: T[] = [];

                for (let j = 0; j < results.length; j++) {
                    if (results[j] === true) {
                        nextEventsToProcess.push(events[j]);
                    }
                }
                events = nextEventsToProcess;
            }
        }
    }
}
