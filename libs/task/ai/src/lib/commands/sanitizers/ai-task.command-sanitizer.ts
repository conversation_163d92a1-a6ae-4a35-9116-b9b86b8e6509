import { AiTaskCommand } from '../../ai-task.command';
import { AiTaskSource } from '../../ai-task.source';

export type AiTaskCommandSanitizerContext<Command extends AiTaskCommand> = Pick<Command, 'opts' | 'workspaceId'>;

/**
 * @description
 * Sanitization can happen for various reasons but the main goal
 *  is to ensure that the command config is in the valid state.
 *
 * For example:
 *  - AiTaskAssigneeCommand must be sanitized to ensure that the user id range is valid
 *      Users can leave or be removed from the workspace, so we need to additionally check if
 *       all user ids provided in the command are workspace members.
 */
export abstract class AiTaskCommandSanitizer<Command extends AiTaskCommand> {
    abstract source: AiTaskSource | null;

    abstract sanitize(context: AiTaskCommandSanitizerContext<Command>): Promise<AiTaskCommandSanitizerContext<Command>>;
}
