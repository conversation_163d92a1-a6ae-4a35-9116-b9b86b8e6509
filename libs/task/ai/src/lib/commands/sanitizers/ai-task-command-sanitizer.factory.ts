import { Inject, Injectable } from '@nestjs/common';

import { AiTaskCommand } from '../../ai-task.command';
import { AiTaskCommandSanitizer, AiTaskCommandSanitizerContext } from './ai-task.command-sanitizer';
import { AiTaskNoopCommandSanitizer } from './ai-task-noop.command-sanitizer';

export const AiTaskCommandSanitizerConfigToken = Symbol.for('AiTaskCommandSanitizerConfigToken');

/**
 * @description
 * Use AiTaskCommandSanitizerFactory to make sure the input
 *  of the Ai Task Property command is in the healthy state.
 *
 * @examples
 * - AiTaskAssignmentCommand containing user ids who no longer exist or belong in the workspace.
 */
@Injectable()
export class AiTaskCommandSanitizerFactory {
    constructor(
        @Inject(AiTaskCommandSanitizerConfigToken)
        private readonly aiTaskCommandSanitizerConfig: ReadonlyMap<string, AiTaskCommandSanitizer<AiTaskCommand>>,
        private readonly aiTaskNoopCommandSanitizer: AiTaskNoopCommandSanitizer
    ) {}

    /**
     * @description
     * Warning! This method mutates the state of the input!
     */
    async sanitize<Command extends AiTaskCommand>(source: string, input: AiTaskCommandSanitizerContext<Command>) {
        const sanitizer = this.aiTaskCommandSanitizerConfig.get(source) ?? this.aiTaskNoopCommandSanitizer;
        await sanitizer.sanitize(input);
    }
}
