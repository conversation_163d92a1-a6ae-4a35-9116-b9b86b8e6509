/* eslint-disable max-classes-per-file */
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

import { TaskOrderindexBasicType } from './task-orderindexes.types';

class TaskBasicOrderindex {
    @ApiProperty({ type: String, nullable: true })
    value: string | null;
}

class TaskFieldOrderindex {
    @ApiProperty({ type: String, nullable: true })
    fieldId: string | null;

    @ApiProperty({ type: String, nullable: true })
    value: string | null;
}

class TaskInboxOrderindex {
    @ApiProperty()
    userId: number;

    @ApiProperty({ type: String, nullable: true })
    value: string | null;
}

class TaskStatusOrderindex {
    @ApiProperty({ type: Number, nullable: true })
    listId: number | null;

    @ApiProperty({ type: String, nullable: true })
    value: string | null;
}

export class TaskOrderindexes {
    @ApiPropertyOptional()
    assignee?: TaskBasicOrderindex;

    @ApiPropertyOptional()
    custom_items?: TaskBasicOrderindex;

    @ApiPropertyOptional()
    due_date?: TaskBasicOrderindex;

    @ApiPropertyOptional()
    none?: TaskBasicOrderindex;

    @ApiPropertyOptional()
    priority?: TaskBasicOrderindex;

    @ApiPropertyOptional()
    subtask?: TaskBasicOrderindex;

    @ApiPropertyOptional()
    tag?: TaskBasicOrderindex;

    @ApiPropertyOptional({ type: [TaskFieldOrderindex] })
    field?: TaskFieldOrderindex[];

    @ApiPropertyOptional({ type: [TaskInboxOrderindex] })
    inbox?: TaskInboxOrderindex[];

    @ApiPropertyOptional({ type: [TaskStatusOrderindex] })
    status?: TaskStatusOrderindex[];
}
