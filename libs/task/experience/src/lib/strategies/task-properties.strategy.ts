import { ParentType } from '@clickup/field/domain-model';
import { ExtendedWorkspaceModel } from '@clickup/hierarchy/workspace';
import { tracer } from '@clickup/shared/utils-tracing';
import {
    BulkTaskExperienceRto,
    ExperienceFields,
    FieldsParam,
    isFieldRequested,
    TaskExperienceRto,
} from '@clickup/task/contracts';
import { TaskModel } from '@clickup/task/domain-model';
import {
    TaskPropertiesService,
    TaskProperty,
    TaskPropertyParent,
    TaskPropertyRto,
    TaskPropertySerializer,
    TaskPropertyWithLocations,
} from '@clickup/task/properties';
import { FeatureFlag } from '@clickup-legacy/models/integrations/split/feature-flag.enum';

import type { BulkTasksExperienceParams, TaskExperienceParams } from '../task-experience.service';
import { ExperienceFieldStrategy } from './experience-field.strategy';
import { StrategyDependencyBag } from './strategy-dependency-bag';

type SubcategoryId = number;

export class TaskPropertiesStrategy implements ExperienceFieldStrategy {
    constructor(
        private readonly taskPropertiesService: TaskPropertiesService,
        private readonly taskPropertySerializer: TaskPropertySerializer
    ) {}

    static getResolver(
        workspaceSettings: ExtendedWorkspaceModel | undefined
    ): (fields: FieldsParam, s: StrategyDependencyBag) => Promise<ExperienceFieldStrategy | undefined> {
        return async (fields: FieldsParam, s: StrategyDependencyBag) => {
            if (
                s.featureFlagService.getBoolean({
                    flag: FeatureFlag.TaskProperties,
                    target: String(workspaceSettings?.id),
                }) &&
                isFieldRequested(fields, ExperienceFields.TASK_PROPERTIES) &&
                isFieldRequested(fields, ExperienceFields.CORE)
            ) {
                return new TaskPropertiesStrategy(s.taskPropertiesService, s.taskPropertySerializer);
            }
            return undefined;
        };
    }

    private async getTaskProperties({
        parentIdRange,
        workspaceId,
    }: {
        parentIdRange: Set<SubcategoryId>;
        workspaceId: number;
    }): Promise<Map<SubcategoryId, TaskPropertyRto[]>> {
        const taskProperties = await this.taskPropertiesService.findTaskProperties({
            parentIds: Array.from(parentIdRange),
            parentType: ParentType.Subcategory,
            withLocations: true,
            workspaceId,
        });

        const taskPropertyMap: Map<SubcategoryId, TaskPropertyRto[]> = new Map();

        for await (const { parents, taskProperty } of this.iterateOverTaskPropertyParents(taskProperties)) {
            for (const { parentId } of parents) {
                if (!taskPropertyMap.has(parentId)) {
                    taskPropertyMap.set(parentId, []);
                }

                const taskPropertyRtos = taskPropertyMap.get(parentId);
                taskPropertyRtos.push(taskProperty);
            }
        }

        return taskPropertyMap;
    }

    /**
     * A Generator to make sure these iterations do not block the Event Loop
     */
    private *iterateOverTaskPropertyParents(
        taskProperties: TaskProperty[]
    ): Generator<{ parents: TaskPropertyParent[]; taskProperty: TaskPropertyRto }> {
        for (const taskProperty of taskProperties) {
            if (!(taskProperty instanceof TaskPropertyWithLocations)) {
                throw new Error('TaskProperty should have parent locations');
            }

            // We don't want to return parent locations in the response
            const taskPropertyWithoutParents = new TaskProperty(taskProperty);
            const taskPropertyRto = this.taskPropertySerializer.serialize(taskPropertyWithoutParents);

            yield { parents: taskProperty.parents, taskProperty: taskPropertyRto };
        }
    }

    @tracer.Decorator(`experience.strategy.taskPropertiesStrategy.executeForSingle`)
    async executeForSingle(params: TaskExperienceParams, rto: TaskExperienceRto): Promise<void> {
        const parentId = Number(rto.task.subcategory);
        if (Number.isNaN(parentId)) {
            return;
        }

        const taskPropertiesMap = await this.getTaskProperties({
            parentIdRange: new Set([parentId]),
            workspaceId: params.workspaceId,
        });

        rto.task_properties = taskPropertiesMap.get(parentId) ?? [];
    }

    @tracer.Decorator(`experience.strategy.taskPermissionsStrategy.executeForBulk`)
    async executeForBulk(
        params: BulkTasksExperienceParams,
        rto: BulkTaskExperienceRto,
        coreTasks: TaskModel[]
    ): Promise<void> {
        const parentIdRange = new Set<SubcategoryId>(
            coreTasks
                .map(task => task?.subcategory)
                .filter(Boolean)
                .map(Number)
        );

        if (!parentIdRange.size) {
            return;
        }

        const taskPropertiesMap: Map<SubcategoryId, TaskPropertyRto[]> = await this.getTaskProperties({
            parentIdRange,
            workspaceId: params.workspaceId,
        });

        for (const { value: taskExpRto } of rto.tasks) {
            const taskSubcategoryId = Number(taskExpRto?.task?.subcategory);
            if (!Number.isNaN(taskSubcategoryId)) {
                taskExpRto.task_properties = taskPropertiesMap.get(taskSubcategoryId) ?? [];
            }
        }
    }
}
