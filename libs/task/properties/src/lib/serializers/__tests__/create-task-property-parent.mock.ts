import { plainToClass } from 'class-transformer';

import { ParentType } from '@clickup/field/domain-model';

import { TaskPropertyParent } from '../../models';
import { createTaskPropertyMock } from './create-task-property.mock';

export const createTaskPropertyParentMock = (partial: Partial<TaskPropertyParent>): TaskPropertyParent =>
    plainToClass(TaskPropertyParent, {
        taskProperty: createTaskPropertyMock(),
        parentType: ParentType.Subcategory,
        parentId: 90000001,
        ...partial,
    });
