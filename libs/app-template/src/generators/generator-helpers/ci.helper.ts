// eslint-disable-next-line import/no-extraneous-dependencies
import chalk from 'chalk';

export function showLocalRunWarning() {
    if (process.env.CI || process.env.NODE_ENV === 'test') {
        return;
    }

    const border = '='.repeat(80);

    // eslint-disable-next-line no-console
    console.log(`
${border}
${chalk.red.bold('🚫 STOP! This generator should not be run locally!')}
${border}

${chalk.yellow('Why am I seeing this?')}
This generator is designed to run in CI environments only.

${chalk.green('What should I do instead?')}
Please use Backstage to create new services:
👉 ${chalk.blue.underline('https://backstage.clickup-internal.com/create/templates/default/new-backend-service')}

${border}
`);
}
