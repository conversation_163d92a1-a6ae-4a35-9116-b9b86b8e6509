package main

import (
	"context"
	"encoding/hex"
	"errors"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis_rate/v10"
	"lukechampine.com/blake3"
)

type Limiter interface {
	AllowN(ctx context.Context, key string, limit redis_rate.Limit, n int) (*redis_rate.Result, error)
}

type RateLimitRequestModifier struct {
	bucket        string
	cost          int
	endpoint      string
	key           []string
	limit         redis_rate.Limit
	limiter       Limiter
	skippedUserId string
}

var (
	errMissingRateLimitKey = errors.New("missing rate limit key")
	errUnknownRequestType  = errors.New("unknown request type")
)

func makeErrRateLimitReached(retryAfter time.Duration) *HTTPResponseError {
	return NewHTTPResponseError("Rate limit reached", "APP_003", 429, retryAfter)
}

func getUserId(req RequestWrapper) string {
	userIds := req.Headers()["ClickUp-User"]
	if len(userIds) != 1 {
		return ""
	}

	return userIds[0]
}

func getWorkspaceId(req RequestWrapper) string {
	params := req.Params()
	workspaceId := params["Workspace_id"]
	if len(workspaceId) == 0 {
		return ""
	}

	return workspaceId
}

func getAuthHeader(req RequestWrapper) string {
	authHeaders := req.Headers()["Authorization"]
	if len(authHeaders) != 1 {
		return ""
	}

	h := blake3.Sum512([]byte(authHeaders[0]))
	return hex.EncodeToString(h[:])
}

func getClientName(req RequestWrapper) string {
	clientName := req.Headers()["X-Cu-Client-Name"]
	if len(clientName) != 1 || clientName[0] == "" {
		return "unknown"
	}

	return clientName[0]
}

func modifyHeaders(rateResult *redis_rate.Result, headers map[string][]string) map[string][]string {
	newHeaders := make(map[string][]string, len(headers)+3)

	for k, v := range headers {
		newHeaders[k] = v
	}

	newHeaders["X-RateLimit-Limit"] = []string{strconv.Itoa(rateResult.Limit.Rate)}
	newHeaders["X-RateLimit-Remaining"] = []string{strconv.Itoa(rateResult.Remaining)}
	newHeaders["X-RateLimit-Reset"] = []string{strconv.Itoa(int(time.Now().Add(rateResult.ResetAfter).Unix()))}

	return newHeaders
}

func (m *RateLimitRequestModifier) getRateLimitKey(req RequestWrapper) string {
	rateLimitKeysLength := len(m.key)
	if len(m.bucket) != 0 {
		rateLimitKeysLength += 1
	}
	rateLimitKeys := make([]string, 0, rateLimitKeysLength)

	if len(m.bucket) != 0 {
		rateLimitKeys = append(rateLimitKeys, m.bucket)
	}

	for _, element := range m.key {
		switch element {
		case "authorization_header":
			rateLimitKeys = append(rateLimitKeys, "ah="+getAuthHeader(req))
		case "client_name":
			rateLimitKeys = append(rateLimitKeys, "cn="+getClientName(req))
		case "request_path":
			rateLimitKeys = append(rateLimitKeys, "e="+m.endpoint)
		case "user_id":
			rateLimitKeys = append(rateLimitKeys, "u="+getUserId(req))
		case "workspace_id":
			rateLimitKeys = append(rateLimitKeys, "w="+getWorkspaceId(req))
		}
	}

	return strings.Join(rateLimitKeys, ":")
}

func (m *RateLimitRequestModifier) ModifyRequest(input interface{}) (interface{}, error) {
	req, ok := input.(RequestWrapper)
	if !ok {
		return nil, errUnknownRequestType
	}

	userId := getUserId(req)
	if userId != "" && userId == m.skippedUserId {
		return input, nil
	}

	rateLimitKey := m.getRateLimitKey(req)
	if rateLimitKey == "" {
		return nil, errMissingRateLimitKey
	}

	res, err := m.limiter.AllowN(req.Context(), rateLimitKey, m.limit, m.cost)
	if err != nil {
		logger.Error(err)

		return input, nil
	}

	logger.Debug("User", userId, "has", res.Allowed, "req allowed,", res.Remaining, "remaining")

	if res.Allowed <= 0 {
		return nil, makeErrRateLimitReached(res.RetryAfter)
	}

	return requestWrapper{
		ctx:     req.Context(),
		body:    req.Body(),
		headers: modifyHeaders(res, req.Headers()),
		method:  req.Method(),
		params:  req.Params(),
		path:    req.Path(),
		query:   req.Query(),
		url:     req.URL(),
	}, nil
}
