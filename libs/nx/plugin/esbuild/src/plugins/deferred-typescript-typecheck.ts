import { TypecheckRunner } from '@jgoz/esbuild-plugin-typecheck';
import type { Plugin } from 'esbuild';

import { BuildEsbuildOptions } from '../utils/models';

export function deferredTypescriptTypecheck(options: Pick<BuildEsbuildOptions, 'watchMode' | 'tsconfigPath'>): Plugin {
    return {
        name: 'deferred-typescript-typecheck',
        setup(build) {
            let firstBuildEnded = false;
            const absWorkingDir = process.cwd();

            let runner: TypecheckRunner;

            build.onStart(() => {
                if (!runner) {
                    return;
                }
                runner.start();
            });

            build.onEnd(() => {
                if (firstBuildEnded && runner) {
                    return;
                }
                firstBuildEnded = true;

                runner = new TypecheckRunner({
                    absWorkingDir,
                    buildMode: 'write-output',
                    watch: options.watchMode,
                    configFile: options.tsconfigPath,
                    build: true,
                    compilerOptions: {
                        noEmit: true,
                    },
                });
                runner.start();
            });
        },
    };
}
