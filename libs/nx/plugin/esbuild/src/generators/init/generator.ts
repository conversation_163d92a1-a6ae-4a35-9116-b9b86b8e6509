import {
    formatFiles,
    readProjectConfiguration,
    TargetConfiguration,
    Tree,
    updateProjectConfiguration,
} from '@nx/devkit';
import type { EsBuildExecutorOptions } from '@nx/esbuild/src/executors/esbuild/schema';
import type { NodeExecutorOptions } from '@nx/js/src/executors/node/schema';
import * as path from 'path';

import type { BuildExecutorSchema } from '../../executors/build/schema';
import { INTERNAL_ESBUILD_TARGET_NAME } from '../../utils/constants';
import type { InitGeneratorSchema } from './schema';

export async function initGenerator(tree: Tree, options: InitGeneratorSchema) {
    const target = 'build';
    const serveTarget = 'serve';
    const projectConfiguration = readProjectConfiguration(tree, options.project);

    const previousBuildTarget: TargetConfiguration<BuildExecutorSchema> | undefined =
        projectConfiguration.targets?.[target];
    const prevBuildOptions = previousBuildTarget?.options;

    const previousServeTarget: TargetConfiguration<NodeExecutorOptions> | undefined =
        projectConfiguration.targets?.[serveTarget];
    const previousServeOptions = previousServeTarget?.options;

    if (previousBuildTarget?.executor === '@clickup/nx-plugin-esbuild:build') {
        throw new Error(
            `It looks like the target "build" in the project "${options.project}" is already configured to use esbuild.`
        );
    }

    tree.delete(path.join(projectConfiguration.root, 'webpack.config.js'));
    tree.delete(path.join(projectConfiguration.root, 'webpack.config.ts'));

    projectConfiguration.targets[target].options.webpackConfig = path.join(
        projectConfiguration.root,
        'webpack.config.ts'
    );

    const baseNxEsbuildTarget: TargetConfiguration<EsBuildExecutorOptions> = {
        executor: '@nx/esbuild:esbuild',
        outputs: ['{options.outputPath}'],
        options: {
            main: `${projectConfiguration.root}/src/${options.mainFile || 'main.ts'}`,
            outputPath: prevBuildOptions?.outputPath,
            outputFileName: options.outputFileName || 'main.js',
            tsConfig: prevBuildOptions?.tsConfig,
            assets: [],
        },
    };

    const clickupNxEsbuildTarget: TargetConfiguration<BuildExecutorSchema> = {
        ...previousBuildTarget,
        executor: '@clickup/nx-plugin-esbuild:build',
        outputs: ['{options.outputPath}'],
        defaultConfiguration: 'development',
        options: {
            ...baseNxEsbuildTarget.options,
            assets: [
                ...prevBuildOptions.assets,
                {
                    input: projectConfiguration.root,
                    glob: 'README.md',
                    output: '.',
                },
                {
                    input: `${projectConfiguration.root}/src/config`,
                    glob: '**/*',
                    output: './config',
                },
            ],
            fileReplacements: prevBuildOptions.fileReplacements,
            temporalWorkflowsFile: prevBuildOptions.temporalWorkflowsFile,
        },
    };

    delete projectConfiguration.targets['build-esbuild'];
    delete projectConfiguration.targets['serve-esbuild'];

    updateProjectConfiguration(tree, options.project, {
        ...projectConfiguration,
        targets: {
            ...projectConfiguration.targets,
            serve: {
                ...previousServeTarget,
                options: {
                    ...previousServeOptions,
                    watch: true,
                },
            },
            build: clickupNxEsbuildTarget,
            [INTERNAL_ESBUILD_TARGET_NAME]: baseNxEsbuildTarget,
        },
    });

    await formatFiles(tree);
}

export default initGenerator;
