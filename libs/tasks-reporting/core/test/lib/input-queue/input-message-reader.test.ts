import { Message } from '@aws-sdk/client-sqs';
import { mock, MockProxy } from 'jest-mock-extended';

import { InputMessageReader } from '../../../src/lib/input-queue/input-message-reader';
import { OvmUpdateProcessor } from '../../../src/lib/input-queue/ovm-update-processor';
import { TasksReportingMetricsReporter } from '../../../src/lib/metrics-reporter';
import { createUpdateEvent } from './utils';

describe('InputMessageReader test', () => {
    let testedObject: InputMessageReader;
    let ovmProcessor: MockProxy<OvmUpdateProcessor>;

    beforeEach(() => {
        ovmProcessor = mock<OvmUpdateProcessor>();
        testedObject = new InputMessageReader(ovmProcessor, mock<TasksReportingMetricsReporter>());
    });

    function toSQSMessage(Body: unknown) {
        return {
            Body,
        } as Message;
    }

    function eventsToSQSMessages(events: unknown[]) {
        return events.map(ev => toSQSMessage(JSON.stringify(ev)));
    }

    it('Calls OVM processor with parsed messages', async () => {
        const events = [createUpdateEvent(1, 't1'), createUpdateEvent(1, 't2')];
        await testedObject.handleMessageBatch(eventsToSQSMessages(events));
        expect(ovmProcessor.process).toHaveBeenCalledWith(events);
    });

    it('Throws if received message is not a valid JSON', async () => {
        const message = 'foobar';
        await expect(testedObject.handleMessageBatch([toSQSMessage(message)])).rejects.toThrowError(
            /^Unexpected token.*JSON/
        );
    });
});
