import { Injectable } from '@nestjs/common';

import { DBClient } from '@clickup/utils/db';
import { SimpleClient } from '@clickup-legacy/utils/interfaces/TransactionClient';

import { TaskDbSchema } from '../models/task.db.schema';
import { TaskSpaceDbSchema } from '../models/task-space.db.schema';
import { getSpacesForTasksQuery } from '../queries/get-spaces-for-tasks.query';
import { getTasksQuery } from '../queries/get-tasks.query';
import { updateTaskDatesQuery } from '../queries/update-task-dates.query';

@Injectable()
export class TaskRepository {
    constructor(private dbClient: DBClient) {}

    // A bit more work to do here - need to do some pre-processing of dates, instead of using this.dateToTimestamp
    private async updateTaskDates(updatedTasks: any[]) {
        await this.dbClient.writeAsyncFunction(async (writeClient: SimpleClient) => {
            for (const task of updatedTasks) {
                const { query, params } = updateTaskDatesQuery(task.id, task.start_date, task.end_date);
                await writeClient.queryAsync(query, params);
            }
        });
    }

    public async getTasks(taskIds: string[], masterId: number): Promise<TaskDbSchema[]> {
        const { query, params } = getTasksQuery(taskIds);
        const { rows } = await this.dbClient.writeAsyncFunction(
            async (writeClient: SimpleClient) => writeClient.queryAsync<any>(query, params),
            {
                clientOptions: {
                    masterId,
                },
            }
        );
        return rows;
    }

    public async getSpacesForTasks(taskIds: string[], simpleClient: SimpleClient): Promise<TaskSpaceDbSchema[]> {
        const { query, params } = getSpacesForTasksQuery(taskIds);
        const { rows } = await simpleClient.queryAsync<any>(query, params);
        return rows;
    }
}
