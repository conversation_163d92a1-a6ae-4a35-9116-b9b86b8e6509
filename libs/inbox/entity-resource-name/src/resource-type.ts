import { Logger } from '@nestjs/common';
import { ObjectType } from '@time-loop/ovm-object-version';

import { AttachmentParentType, EntityType, ViewParentType } from '@clickup/utils/constants';

/**
 * ResourceType is an enum that represents the different types of resources that are represented in the
 * inbox/notification system.  For the most part, these are the same as the ObjectType enum, but there are some
 * differences (e.g. all *_ACCESS ObjectTypes are mapped to the AccessRequest ResourceType).  A ResourceType need not
 * have a 1:1 mapping to an ObjectType, because inbox/notifications can also correspond to entities that are not
 * within the ClickUp domain (e.g. GitHub commits)
 */
export enum ResourceType {
    AccessRequest = 'access-request',
    Approval = 'approval',
    Attachment = 'attachment',
    Card = 'card',
    Category = 'category',
    ChatView = 'chat-view',
    ChecklistItem = 'checklist-item',
    Clip = 'clip',
    Comment = 'comment',
    CustomField = 'custom-field',
    Dashboard = 'dashboard',
    Doc = 'doc',
    GitHubCommit = 'github-commit',
    Page = 'page',
    Project = 'project',
    Reminder = 'reminder',
    Subcategory = 'subcategory',
    Tag = 'tag',
    Task = 'task',
    Timesheet = 'timesheet',
    User = 'user',
    UserGroup = 'user-group',
    View = 'view',
    Whiteboard = 'whiteboard',
    Workspace = 'workspace',
}

export function getResourceType(objectType: ObjectType) {
    switch (objectType) {
        case ObjectType.DASHBOARD_ACCESS:
        case ObjectType.DOC_ACCESS:
        case ObjectType.GOAL_ACCESS:
        case ObjectType.GOAL_FOLDER_ACCESS:
        case ObjectType.PAGE_ACCESS:
        case ObjectType.TASK_ACCESS:
        case ObjectType.VIEW_ACCESS:
        case ObjectType.USER_ACCESS:
        case ObjectType.WHITEBOARD_ACCESS:
            return ResourceType.AccessRequest;

        case ObjectType.ATTACHMENT:
            return ResourceType.Attachment;
        case ObjectType.APPROVAL:
            return ResourceType.Approval;
        case ObjectType.COMMENT:
            return ResourceType.Comment;
        case ObjectType.CUSTOM_FIELD:
            return ResourceType.CustomField;
        case ObjectType.DOC:
            return ResourceType.Doc;
        case ObjectType.FOLDER:
            return ResourceType.Category;
        case ObjectType.LIST:
            return ResourceType.Subcategory;
        case ObjectType.SPACE:
            return ResourceType.Project;
        case ObjectType.PAGE:
            return ResourceType.Page;
        case ObjectType.TASK:
            return ResourceType.Task;
        case ObjectType.USER:
            return ResourceType.User;
        case ObjectType.USER_GROUP:
            return ResourceType.UserGroup;
        case ObjectType.WIDGET:
            return ResourceType.Card;
        case ObjectType.WORKSPACE:
            return ResourceType.Workspace;

        /**
         * These are all treated as views currently from an inbox/notification perspective
         */
        case ObjectType.DASHBOARD:
        case ObjectType.FORM:
        case ObjectType.VIEW:
        case ObjectType.WHITEBOARD:
            return ResourceType.View;

        /**
         * Not supported (yet).  We should probably throw here but for backwards compatibility we'll just log a warning
         */
        case ObjectType.GOAL:
        case ObjectType.GOAL_FOLDER:
        case ObjectType.HIERARCHY:
        case ObjectType.POST:
        case ObjectType.TASK_HISTORY:
        default:
            Logger.warn(`Unsupported object type ${objectType}`);
            return objectType as unknown as ResourceType;
    }
}

/**
 * Maps a ResourceType to an ObjectType
 * @param resourceType The ResourceType to convert
 * @returns The corresponding ObjectType, or undefined if no direct mapping exists
 */
export function mapResourceTypeToObjectType(resourceType: ResourceType): ObjectType | undefined {
    switch (resourceType) {
        case ResourceType.AccessRequest:
            // Access request types don't have a 1:1 mapping back to object types
            return undefined;
        case ResourceType.Attachment:
            return ObjectType.ATTACHMENT;
        case ResourceType.Approval:
            return ObjectType.APPROVAL;
        case ResourceType.Comment:
            return ObjectType.COMMENT;
        case ResourceType.CustomField:
            return ObjectType.CUSTOM_FIELD;
        case ResourceType.Doc:
            return ObjectType.DOC;
        case ResourceType.Category:
            return ObjectType.FOLDER;
        case ResourceType.Subcategory:
            return ObjectType.LIST;
        case ResourceType.Project:
            return ObjectType.SPACE;
        case ResourceType.Page:
            return ObjectType.PAGE;
        case ResourceType.Task:
            return ObjectType.TASK;
        case ResourceType.User:
            return ObjectType.USER;
        case ResourceType.UserGroup:
            return ObjectType.USER_GROUP;
        case ResourceType.Workspace:
            return ObjectType.WORKSPACE;
        case ResourceType.View:
        case ResourceType.ChatView:
            return ObjectType.VIEW;
        case ResourceType.Whiteboard:
            return ObjectType.WHITEBOARD;
        case ResourceType.Clip:
            return ObjectType.ATTACHMENT;
        default:
            Logger.warn(`No direct ObjectType mapping for ResourceType ${resourceType}`);
            return undefined;
    }
}

/**
 * Maps a view parent type to a resource type
 * @param viewParentType
 */
export function getResourceTypeFromViewParentType(viewParentType: ViewParentType) {
    switch (viewParentType) {
        case ViewParentType.task:
            return ResourceType.Task;
        case ViewParentType.project:
            return ResourceType.Project;
        case ViewParentType.category:
            return ResourceType.Category;
        case ViewParentType.subcategory:
            return ResourceType.Subcategory;
        case ViewParentType.team:
        case ViewParentType.teamUnparented:
            return ResourceType.Workspace;
        case ViewParentType.document:
            return ResourceType.Doc;
        case ViewParentType.user_group:
            return ResourceType.UserGroup;
        case ViewParentType.view:
            return ResourceType.View;
        case ViewParentType.widget:
            return ResourceType.Card;
        case ViewParentType.shared:
        case ViewParentType.template:
        case ViewParentType.sharedTasks:
        case ViewParentType.dashboard:
        default:
            throw new Error(`Unknown resource type for view parent type: ${viewParentType}`);
    }
}

export function getResourceTypeFromAttachmentParentType(parentType: AttachmentParentType) {
    switch (parentType) {
        case AttachmentParentType.TASK:
            return ResourceType.Task;
        case AttachmentParentType.COMMENT:
        case AttachmentParentType.POST:
            return ResourceType.Comment;
        case AttachmentParentType.SPACE:
            return ResourceType.Project;
        case AttachmentParentType.FOLDER:
            return ResourceType.Category;
        case AttachmentParentType.LIST:
            return ResourceType.Subcategory;
        case AttachmentParentType.WORKSPACE:
            return ResourceType.Workspace;
        case AttachmentParentType.VIEW:
            return ResourceType.View;
        case AttachmentParentType.REMINDER:
            return ResourceType.Reminder;
        case AttachmentParentType.USER:
            return ResourceType.User;
        case AttachmentParentType.CUSTOM_FIELD:
            return ResourceType.CustomField;
        case AttachmentParentType.WIDGET:
            return ResourceType.Card;
        case AttachmentParentType.DOC:
            return ResourceType.Doc;
        case AttachmentParentType.GROUP:
            return ResourceType.UserGroup;
        case AttachmentParentType.ATTACHMENT:
            return ResourceType.Attachment;
        default:
            throw new Error(`Unknown resource type for attachment parent type: ${parentType}`);
    }
}

export function getEntityTypeFromResourceType(resourceType: ResourceType): EntityType | null {
    switch (resourceType) {
        case ResourceType.Approval:
            return EntityType.APPROVAL;
        case ResourceType.Attachment:
            return EntityType.ATTACHMENT;
        case ResourceType.Card:
            return EntityType.WIDGET;
        case ResourceType.Category:
            return EntityType.FOLDER;
        case ResourceType.ChatView:
            return EntityType.VIEW;
        case ResourceType.Clip:
            return EntityType.ATTACHMENT;
        case ResourceType.Comment:
            return EntityType.COMMENT;
        case ResourceType.CustomField:
            return EntityType.CUSTOM_FIELD;
        case ResourceType.Dashboard:
            return EntityType.DASHBOARD;
        case ResourceType.Doc:
            return EntityType.DOC;
        case ResourceType.Page:
            return EntityType.PAGE;
        case ResourceType.Project:
            return EntityType.SPACE;
        case ResourceType.Reminder:
            return EntityType.REMINDER;
        case ResourceType.Subcategory:
            return EntityType.LIST;
        case ResourceType.Task:
            return EntityType.TASK;
        case ResourceType.User:
            return EntityType.USER;
        case ResourceType.UserGroup:
            return EntityType.USER_GROUP;
        case ResourceType.View:
            return EntityType.VIEW;
        case ResourceType.Whiteboard:
            return EntityType.WHITEBOARD;
        case ResourceType.Workspace:
            return EntityType.WORKSPACE;

        // Following types don't have a clear mapping back to entity type
        case ResourceType.AccessRequest:
        case ResourceType.ChecklistItem:
        case ResourceType.GitHubCommit:
        case ResourceType.Tag:
        case ResourceType.Timesheet:
            return null;

        default:
            // If we reach here, it means a new ResourceType was added but not account for.
            throw new Error(
                `ResourceType ${resourceType} does not have a corresponding EntityType but was not expected to be null`
            );
    }
}
