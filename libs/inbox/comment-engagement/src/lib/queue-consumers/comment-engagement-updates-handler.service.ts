import { Injectable } from '@nestjs/common';
import { Message } from 'aws-sdk/clients/sqs';
import { deserialize } from 'class-transformer';

import { tracer } from '@clickup/shared/utils-tracing';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';
import { SqsConsumerEventHandler, SqsMessageHandler } from '@clickup/utils/sqs';
import {
    CommentEngagementUpdate,
    CommentEngagementUpdateTypes,
} from '@clickup-legacy/models/notifications/notificationUpdate/commentEngagementUpdate';

import { CommentEngagementDataService } from '../comment-engagement-data.service';

const MAX_RETRIES = 5;

/**
 * SQS Message handler to react to comment engagement updates
 */
@Injectable()
export class CommentEngagementUpdatesHandler {
    public static Name = 'CommentEngagementUpdatesHandler';

    constructor(
        private readonly commentEngagementDataService: CommentEngagementDataService,
        private readonly logger: FormatLogger,
        private readonly metricsService: MetricsService
    ) {}

    @SqsMessageHandler(CommentEngagementUpdatesHandler.Name, false)
    async handleMessage(message: Message) {
        let encounteredError = false;
        let updateType = 'unknown';
        try {
            const jsonObject = JSON.parse(message.Body);
            updateType = jsonObject.type;
            tracer.addTagsToRootSpan({ workspace: jsonObject.workspaceId });
            await this.handleCommentEngagementUpdate(message);
        } catch (e) {
            encounteredError = true;
            throw e;
        } finally {
            this.metricsService.client.increment('queue_consumers.comment_engagement_updates.received.count', 1, {
                type: updateType,
            });
            const receiveCount = +(message?.Attributes?.ApproximateReceiveCount ?? 1);
            if (!encounteredError || receiveCount >= MAX_RETRIES) {
                const metric = encounteredError
                    ? 'queue_consumers.comment_engagement_updates.processing_error.count'
                    : 'queue_consumers.comment_engagement_updates.processed_successfully.count';
                this.metricsService.client.increment(metric, 1, {
                    receiveCount: receiveCount.toString(),
                    type: updateType,
                });
            }
        }
    }

    async handleCommentEngagementUpdate(message: Message) {
        const processingStartedTimestamp: number = Date.now();
        const sentTimestampToProcessingStartedDuration: number =
            processingStartedTimestamp - +message.Attributes.SentTimestamp;
        this.metricsService.client.timing(
            'inbox.comment_engagement_updates_message_in_queue.duration',
            sentTimestampToProcessingStartedDuration
        );

        const requestInput = deserialize(CommentEngagementUpdate, message.Body);

        this.logger.debug(
            {
                message: 'Handling CommentEngagementUpdate message',
                request: JSON.stringify(requestInput),
                messageId: message.MessageId,
            },
            CommentEngagementUpdatesHandler.Name
        );

        if (requestInput.type === CommentEngagementUpdateTypes.CREATE) {
            await this.commentEngagementDataService.createCommentEngagements(requestInput);
        } else if (requestInput.type === CommentEngagementUpdateTypes.POTENTIAL_COMPLETE) {
            await this.commentEngagementDataService.tryCompleteCommentEngagement(requestInput);
        } else if (requestInput.type === CommentEngagementUpdateTypes.COMPLETE) {
            // This may be too generic/naïve - potentially need to move to a CHECK_FOR_COMPLETION type
            // Which would hit postgres to check some complex conditions
            // Remove gsi3 as engagement is no longer needed
            await this.commentEngagementDataService.completeCommentEngagement(
                requestInput.author,
                requestInput.workspaceId,
                requestInput.parentCommentResourceName,
                requestInput.commentEntityResourceName
            );
        } else {
            throw new Error(`Unknown CommentEngagementUpdate type: ${requestInput.type}`);
        }

        const processingDuration: number = Date.now() - processingStartedTimestamp;
        this.metricsService.client.timing('inbox.comment_engagement_updates_processing.duration', processingDuration, {
            type: requestInput.type,
        });

        const receiveCount = +(message?.Attributes?.ApproximateReceiveCount ?? 1);
        this.logger.debug(
            {
                message: 'Successfully handled CommentEngagementUpdate message: ',
                request: JSON.stringify(requestInput),
                receiveCount,
                messageId: message.MessageId,
            },
            CommentEngagementUpdatesHandler.Name
        );
    }

    @SqsConsumerEventHandler(CommentEngagementUpdatesHandler.Name, 'processing_error')
    handleProcessingError(error: Error, message?: Message) {
        const receiveCount = +(message?.Attributes?.ApproximateReceiveCount ?? 1);

        const logLevel = receiveCount === MAX_RETRIES ? 'error' : 'warn';
        this.logger[logLevel](error, CommentEngagementUpdatesHandler.Name, {
            message: error.message,
            request: message?.Body,
            receiveCount,
            messageId: message?.MessageId,
        });
    }

    @SqsConsumerEventHandler(CommentEngagementUpdatesHandler.Name, 'error')
    handleError(error: Error, message?: Message) {
        this.handleProcessingError(error, message);
    }
}
