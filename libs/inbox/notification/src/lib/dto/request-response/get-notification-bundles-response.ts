import { ApiExtraModels, ApiProperty, ApiPropertyOptional, refs } from '@nestjs/swagger';

import {
    CommentEntity,
    DocumentEntity,
    Entity,
    HierarchyItem,
    PageEntity,
    Subcategory,
    TaskEntity,
    UserDto,
} from '@clickup/inbox/shared-types';

import { NotificationBundleGroup, NotificationBundleGroupByDateTimeRange } from '../bundle';

const Entities = [CommentEntity, TaskEntity, PageEntity, DocumentEntity, Subcategory, HierarchyItem];
export type EntityTypes =
    | CommentEntity
    | TaskEntity
    | PageEntity
    | DocumentEntity
    | Subcategory
    | HierarchyItem
    | Entity;

@ApiExtraModels(...Entities)
export class GetNotificationBundlesResponse {
    /**
     * One or more groups of notifications (grouping will be based on the grouping type provide in the request,
     * e.g. by date)
     */
    @ApiProperty({
        type: [NotificationBundleGroupByDateTimeRange],
    })
    notificationBundleGroups: NotificationBundleGroup[];

    @ApiProperty({
        description: 'Mapping of entity resource names to resource/entity objects (e.g. tasks, comments, etc.)',
        type: Object,
        additionalProperties: {
            oneOf: refs(...Entities),
        },
    })
    resources: EntityTypes[];

    @ApiProperty()
    nextCursor?: string;

    @ApiPropertyOptional()
    users?: Record<string, UserDto>;
}
