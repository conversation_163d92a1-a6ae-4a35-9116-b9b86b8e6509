import { ApiProperty } from '@nestjs/swagger';
import { IsIn, IsNotEmpty, IsOptional } from 'class-validator';

export class PushNotificationLog {
    @ApiProperty({ description: 'The platform of the application', enum: ['ios', 'android', 'browser', 'desktop'] })
    @IsNotEmpty()
    @IsIn(['ios', 'android', 'browser', 'desktop'])
    platform: 'ios' | 'android' | 'browser' | 'desktop';

    @ApiProperty({ description: 'The Datadog trace ID in the push notification payload' })
    @IsOptional()
    trace_id?: string;

    @ApiProperty({ description: 'The uuid in the push notification payload' })
    @IsOptional()
    uuid?: string;

    @ApiProperty({ description: 'The notification ID in the push notification payload' })
    @IsOptional()
    notification_id?: string;

    @ApiProperty({ description: 'The message to log' })
    @IsOptional()
    msg?: string;

    @ApiProperty({ description: 'The name of the metric to increment' })
    @IsOptional()
    metric?: string;

    @ApiProperty({ description: 'The timestamp in milliseconds' })
    @IsOptional()
    timestamp?: number;

    @ApiProperty({ description: 'The extra fields to log' })
    @IsOptional()
    props?: Record<string, unknown>;
}
