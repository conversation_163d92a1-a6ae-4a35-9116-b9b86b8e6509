import { ClassConstructor, Type } from 'class-transformer';

import { AsyncCommand, AsyncCommandType } from '.';
import { ClearAllNotificationsCommand } from './clear-all-notifications-command';
import { UnsnoozeBundleCommand } from './unsnooze-bundle-command';

export type SupportedAsyncCommand = ClearAllNotificationsCommand | UnsnoozeBundleCommand;

export const AsyncCommandByType: Record<AsyncCommandType, ClassConstructor<SupportedAsyncCommand>> = {
    [AsyncCommandType.ClearAll]: ClearAllNotificationsCommand,
    [AsyncCommandType.UnsnoozeBundle]: UnsnoozeBundleCommand,
};

export class InboxAsyncCommand {
    userId: string;

    workspaceId: string;

    @Type(() => AsyncCommand, {
        discriminator: {
            property: 'commandType',
            subTypes: Object.entries(AsyncCommandByType).map(([key, value]) => ({ name: key, value })),
        },
    })
    command: SupportedAsyncCommand;

    static fromCommand(command: SupportedAsyncCommand) {
        const instance = new InboxAsyncCommand();
        instance.userId = command.userId;
        instance.workspaceId = command.workspaceId;
        instance.command = command;
        return instance;
    }
}
