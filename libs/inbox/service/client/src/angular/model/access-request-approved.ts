/**
 * Inbox Service
 * Handles notifications and notification bundles for Inbox 3.0
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface AccessRequestApproved { 
    id: string;
    type: AccessRequestApproved.Type;
    occurredAt: string;
    entityResourceName: string;
    rootEntityResourceName: string;
    actorId: string;
    automationTriggerId?: string;
    resourceToAccess: string;
}
export namespace AccessRequestApproved {
    export type Type = 'access_request_approved';
    export const Type = {
        AccessRequestApproved: 'access_request_approved' as Type
    };
}


