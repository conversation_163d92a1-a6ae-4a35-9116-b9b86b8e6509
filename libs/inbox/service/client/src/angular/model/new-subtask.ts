/**
 * Inbox Service
 * Handles notifications and notification bundles for Inbox 3.0
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface NewSubtask { 
    id: string;
    type: NewSubtask.Type;
    occurredAt: string;
    entityResourceName: string;
    rootEntityResourceName: string;
    actorId: string;
    automationTriggerId?: string;
    relatedTask: string;
}
export namespace NewSubtask {
    export type Type = 'new_subtask';
    export const Type = {
        NewSubtask: 'new_subtask' as Type
    };
}


