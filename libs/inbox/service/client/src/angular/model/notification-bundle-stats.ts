/**
 * Inbox Service
 * Handles notifications and notification bundles for Inbox 3.0
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface NotificationBundleStats { 
    activityCount: number;
    messagesCount: number;
    savedCount: number;
    reminderCount: number;
    mentionMessagesCount: number;
    assignedActivityCount: number;
    assignedMessagesCount: number;
    lastUpdatedAt: string;
    lastRefreshedAt: string;
    allWorkspaces: object;
}

