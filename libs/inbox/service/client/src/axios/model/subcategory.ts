/* tslint:disable */
/* eslint-disable */
/**
 * Inbox Service
 * Handles notifications and notification bundles for Inbox 3.0
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { Category } from './category';
// May contain unused imports in some cases
// @ts-ignore
import { Project } from './project';

/**
 * 
 * @export
 * @interface Subcategory
 */
export interface Subcategory {
    /**
     * 
     * @type {string}
     * @memberof Subcategory
     */
    'entityResourceName': string;
    /**
     * 
     * @type {string}
     * @memberof Subcategory
     */
    'id': string;
    /**
     * 
     * @type {string}
     * @memberof Subcategory
     */
    'type': SubcategoryTypeEnum;
    /**
     * 
     * @type {object}
     * @memberof Subcategory
     */
    'permissions'?: object;
    /**
     * 
     * @type {string}
     * @memberof Subcategory
     */
    'name'?: string;
    /**
     * 
     * @type {Category}
     * @memberof Subcategory
     */
    'category': Category;
    /**
     * 
     * @type {Project}
     * @memberof Subcategory
     */
    'project': Project;
}

export const SubcategoryTypeEnum = {
    Subcategory: 'subcategory'
} as const;

export type SubcategoryTypeEnum = typeof SubcategoryTypeEnum[keyof typeof SubcategoryTypeEnum];


