/* tslint:disable */
/* eslint-disable */
/**
 * Inbox Service
 * Handles notifications and notification bundles for Inbox 3.0
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { GetNotificationsInBundleResponseResourcesValue } from './get-notifications-in-bundle-response-resources-value';
// May contain unused imports in some cases
// @ts-ignore
import { Notification } from './notification';

/**
 * 
 * @export
 * @interface GetNotificationsInBundleResponse
 */
export interface GetNotificationsInBundleResponse {
    /**
     * 
     * @type {Array<Notification>}
     * @memberof GetNotificationsInBundleResponse
     */
    'notifications': Array<Notification>;
    /**
     * Mapping of entity resource names to resource/entity objects (e.g. tasks, comments, etc.)
     * @type {{ [key: string]: GetNotificationsInBundleResponseResourcesValue; }}
     * @memberof GetNotificationsInBundleResponse
     */
    'resources': { [key: string]: GetNotificationsInBundleResponseResourcesValue; };
    /**
     * 
     * @type {string}
     * @memberof GetNotificationsInBundleResponse
     */
    'nextCursor': string;
    /**
     * 
     * @type {object}
     * @memberof GetNotificationsInBundleResponse
     */
    'users'?: object;
}

