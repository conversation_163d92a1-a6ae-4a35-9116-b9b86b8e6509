/* eslint-disable max-classes-per-file */
import { InjectQueue, Processor } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Queue } from 'bullmq';

import { ScriptRegistry } from '@clickup/data-platform/scripts';
import {
    DEFAULT_SPLIT_SYNCHRONIZATION_DELAY_MILLIS,
    ScriptConfig,
    ScriptStatus,
    TrashScriptExecutionConfig,
} from '@clickup/trash/interfaces';
import { ConfigService } from '@clickup/utils/config';
import jsonParseSafe from '@clickup-legacy/models/sd/helpers/jsonParseSafe';

import { TrashDeletionQueueNames, TrashDeletionReasons } from './trash-deletion-constants';
import { TrashDeletionFeeder } from './trash-deletion-feeder';
import { TrashDeletionMessage } from './trash-deletion-message';
import { TrashDeletionProcessor } from './trash-deletion-processor';

type ScriptMessage = {
    key: string;
    item: any;
    args: any;
};

type ScriptState = {
    /** "resetting", "active"... */
    status: ScriptStatus;

    /** Cursor, if more work remaining. */
    cursor: any;

    /** Record time of last loop, for pacing. */
    updatedAt: number;

    /** Time of last transition between statuses */
    statusChangedAt: number | null;
};

/*
 * Each Script will implement 2 methods
 * - getPage: called from the feeder to select the next batch of work.
 * - processItem: called from the processor to execute the work.
 *
 * Tail the logs of your script here:
 *      https://app.datadoghq.com/logs/livetail?query=service%3Atrash_service%20%40script%3Amy-script&storage=driveline
 */
@Injectable()
export class TrashScriptExecutionFeeder extends TrashDeletionFeeder {
    constructor(
        @InjectQueue(TrashDeletionQueueNames.SCRIPTS)
        messageQueue: Queue<TrashDeletionMessage<ScriptMessage>>,
        private readonly scripts: ScriptRegistry,
        private readonly configService: ConfigService
    ) {
        super(messageQueue, 'scripts');
    }

    public override getIntervalConfigProvider(): () => TrashScriptExecutionConfig {
        return () =>
            this.featureFlagService.getObject<TrashScriptExecutionConfig>({
                flag: 'data-platform-script-execution-config',
            }) ?? { scripts: [] };
    }

    override async draftMessages(): Promise<TrashDeletionMessage<ScriptMessage>[]> {
        // TrashDeletionFeeder gives an invariant we are the only process running now.

        await this.syncSplitState();

        // Realistically, scripts should not take too long so `cfgs` would have
        // length of either 0 or 1
        const cfgs = this.getActiveScripts();
        const messages: ScriptMessage[] = [];

        for (const cfg of cfgs) {
            const { key } = cfg;
            try {
                const script = this.scripts.getScript(key);
                if (!script) {
                    this.logger.error({ message: `Script is undefined`, script: key });
                    continue;
                }

                const region = this.configService.get<string>('region');
                if (cfg.enabledRegions && !cfg.enabledRegions.includes(region)) {
                    continue;
                }

                const state = await this.getScriptState(script.key);
                if (!state) {
                    this.logger.error({ message: `Script is not initialized`, script: key });
                    continue;
                }
                if (state.status !== 'active') {
                    this.logger.info({ message: `Skipping ${state.status} script`, script: key });
                    continue;
                }

                if (cfg.checkIntervalMillis && Date.now() - state.updatedAt < cfg.checkIntervalMillis) {
                    continue;
                }

                let args: any;
                try {
                    args = script.parseArgs(cfg.args ?? {});
                } catch (err: unknown) {
                    this.logger.error({ message: 'Invalid args', script: key, err });
                    continue;
                }

                const { items, next } = await script.getPage(state.cursor, args);
                messages.push(...items.map(item => ({ key, item, args })));

                const isComplete = next === null;
                await this.updateScriptState(key, {
                    ...state,
                    status: isComplete ? 'complete' : 'active',
                    cursor: next,
                    updatedAt: Date.now(),
                });
                if (isComplete) {
                    await script.onEnd(args);
                }
            } catch (err: unknown) {
                this.logger.error({ message: 'Failed to draft messages', script: key, err });
            }
        }

        return messages.map(message => ({
            name: this.name,
            content: message,
            reason: TrashDeletionReasons.OPERATIONS,
            tags: { key: message.key },
        }));
    }

    private getActiveScripts(): ScriptConfig[] {
        const fullConfig = this.getIntervalConfigProvider()();
        return fullConfig.scripts.filter(cfg => cfg.status === 'active');
    }

    private async syncSplitState() {
        const fullConfig = this.getIntervalConfigProvider()();
        // Realistically, only 1 script would run at a time.
        for (const cfg of fullConfig.scripts) {
            const script = this.scripts.getScript(cfg.key);
            if (!script) {
                this.logger.error({
                    message: 'Unable to sync split state, script not found',
                    script: cfg.key,
                });
                continue;
            }

            let args: any;
            try {
                args = script.parseArgs(cfg.args ?? {});
            } catch (err: unknown) {
                this.logger.error({ message: 'Invalid args', script: cfg.key, err });
                continue;
            }

            if (cfg.status === 'resetting') {
                const current = await this.getScriptState(cfg.key);

                if (current?.status === 'resetting') {
                    this.logger.info({ message: 'Script already reset', script: cfg.key });
                    continue;
                }

                if (
                    current?.statusChangedAt &&
                    Date.now() - current.statusChangedAt <
                        (fullConfig.splitSynchronizationDelayMillis ?? DEFAULT_SPLIT_SYNCHRONIZATION_DELAY_MILLIS)
                ) {
                    this.logger.info({ message: 'Waiting for split to synchronize', script: cfg.key });
                    continue;
                }

                await this.updateScriptState(script.key, {
                    status: 'resetting',
                    cursor: script.initCursor(args),
                    updatedAt: Date.now(),
                    statusChangedAt: Date.now(),
                });
                this.logger.info({ message: 'Reset script', script: cfg.key });
            }

            if (cfg.status === 'active') {
                const current = await this.getScriptState(cfg.key);

                if (
                    current?.statusChangedAt &&
                    Date.now() - current.statusChangedAt <
                        (fullConfig.splitSynchronizationDelayMillis ?? DEFAULT_SPLIT_SYNCHRONIZATION_DELAY_MILLIS)
                ) {
                    this.logger.info({ message: 'Waiting for split to synchronize', script: cfg.key });
                    continue;
                }

                if (current?.status === 'resetting') {
                    await script.onStart(args);
                    await this.updateScriptState(cfg.key, {
                        ...current,
                        status: 'active',
                        updatedAt: Date.now(),
                        statusChangedAt: Date.now(),
                    });
                    this.logger.info({ message: 'Started script', script: cfg.key });
                }
            }
        }
    }

    private async getScriptState(key: string): Promise<ScriptState | null> {
        const str = await this.redisMultiClientService.intervalClient.get(this.getScriptStateRedisKey(key));
        if (!str) {
            return null;
        }

        return jsonParseSafe(str);
    }

    private async updateScriptState(key: string, state: ScriptState) {
        const str = JSON.stringify(state);
        await this.redisMultiClientService.intervalClient.set(this.getScriptStateRedisKey(key), str);
    }

    private getScriptStateRedisKey(key: string) {
        return `DP:SCRIPT-EXECUTION:${key}:state`;
    }
}

@Processor(TrashDeletionQueueNames.SCRIPTS, { concurrency: 1 })
export class TrashScriptExecutionProcessor extends TrashDeletionProcessor {
    constructor(private readonly scripts: ScriptRegistry) {
        super();
    }

    async execute(message: TrashDeletionMessage<ScriptMessage>) {
        const { key, item, args } = message.content;
        const script = this.scripts.getScript(key);
        if (!script) {
            this.logger.error({ message: `Script is undefined`, script: key });
            return;
        }

        try {
            await script.processItem(item, args);
        } catch (err: unknown) {
            this.logger.error({ message: 'Failed to execute page', err, script: key });
        }
    }
}
