export interface TemplatesCopiedObjectReferenceRemappingConfig {
    relationships?: boolean;

    /**
     * Custom fields.
     */
    widgetCustomFields?: boolean;
    customFieldsInCardViewColumns?: boolean;

    /**
     * Custom items (task types).
     */
    customItemsInDashboardFilters?: boolean;
    customItemsInCardFilters?: boolean;
    customItemsInCardViewFilters?: boolean;
    customItemsInCards?: boolean;

    /**
     * Statuses.
     */
    workInProgressStatuses?: boolean;

    /**
     * Cards.
     */
    dashboardPortfolioCards?: boolean;
    dashboardBookmarkCards?: boolean;
}
