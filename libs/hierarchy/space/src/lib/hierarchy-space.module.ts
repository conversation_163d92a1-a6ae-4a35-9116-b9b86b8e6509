import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { DynamicModule, Module } from '@nestjs/common';

import { UtilsAuthorizationModule } from '@clickup/utils/authorization';
import {
    AlwaysFallbackCacheClient,
    CacheClient,
    DummyOcmRedisClient,
    OcmRedisClient,
    UtilsCacheModule,
} from '@clickup/utils/cache';
import { UtilsCloudfrontModule } from '@clickup/utils/cloudfront';
import { UtilsConfigModule } from '@clickup/utils/config';
import { UtilsDbModule } from '@clickup/utils/db';
import { UtilsFeatureFlagModule } from '@clickup/utils/feature-flag';

import { HierarchySpaceRepository } from './dataAccess/hierarchy-space.repository';
import { HierarchySpaceService } from './hierarchy-space.service';
import { HierarchySpaceServiceImpl } from './hierarchy-space.service.impl';

@Module({
    imports: [
        AutomapperModule.forRoot({
            strategyInitializer: classes(),
        }),
        UtilsCacheModule,
        UtilsFeatureFlagModule,
        UtilsDbModule,
        UtilsConfigModule,
        UtilsAuthorizationModule,
        UtilsCloudfrontModule,
    ],
    controllers: [],
    providers: [
        { provide: 'USE_CACHE', useValue: true },
        HierarchySpaceRepository,
        {
            provide: HierarchySpaceService,
            useClass: HierarchySpaceServiceImpl,
        },
    ],
    exports: [HierarchySpaceService],
})
export class HierarchySpaceModule {
    static forTest(): DynamicModule {
        return {
            module: HierarchySpaceModule,
            imports: [UtilsCacheModule.forTest()],
            providers: [
                {
                    provide: CacheClient,
                    useClass: AlwaysFallbackCacheClient,
                },
                {
                    provide: OcmRedisClient,
                    useClass: DummyOcmRedisClient,
                },
            ],
        };
    }
}
