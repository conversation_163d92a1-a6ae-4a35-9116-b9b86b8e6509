/* tslint:disable */
/* eslint-disable */
/**
 * Hierarchy Service
 * Hierarchy Service API definition
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import { ObjectKeyDto } from './object-key-dto';
// May contain unused imports in some cases
// @ts-ignore
import { ObjectKeyWithLevelDto } from './object-key-with-level-dto';

/**
 * 
 * @export
 * @interface HierarchyParentsModel
 */
export interface HierarchyParentsModel {
    /**
     * 
     * @type {ObjectKeyDto}
     * @memberof HierarchyParentsModel
     */
    'id': ObjectKeyDto;
    /**
     * 
     * @type {Array<ObjectKeyWithLevelDto>}
     * @memberof HierarchyParentsModel
     */
    'parent_chain': Array<ObjectKeyWithLevelDto>;
}

