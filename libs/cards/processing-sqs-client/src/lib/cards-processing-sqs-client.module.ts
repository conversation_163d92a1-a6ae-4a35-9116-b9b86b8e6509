import { Modu<PERSON> } from '@nestjs/common';

import { CardsBlockingModule } from '@clickup/cards/blocking';
import { CardsCoreModule, WidgetRepository } from '@clickup/cards/core';
import { CardsFeatureFlagsModule } from '@clickup/cards/feature-flags';
import { CardsProcessingModule } from '@clickup/cards/processing';
import { UtilsMetricsModule } from '@clickup/utils/metrics';

import { CardsProcessingPrioritySQSConsumer } from './cards-processing-priority-sqs-consumer';
import { CardsProcessingSQSConsumer } from './cards-processing-sqs-consumer';
import { SQSMessageCardExtractor } from './sqs-message-card-extractor';

@Module({
    imports: [CardsBlockingModule, CardsCoreModule, CardsFeatureFlagsModule, CardsProcessingModule, UtilsMetricsModule],
    controllers: [],
    providers: [
        CardsProcessingSQSConsumer,
        CardsProcessingPrioritySQSConsumer,
        SQSMessageCardExtractor,
        WidgetRepository,
    ],
    exports: [CardsProcessingSQSConsumer],
})
export class CardsProcessingSqsClientModule {}
