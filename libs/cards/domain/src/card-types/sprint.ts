export type CardSprintMeasureUnit = 'points' | 'time_estimate';

export enum CardSprintIndicator {
    CURRENT_SPRINT = 'current_sprint',
    PREVIOUS_SPRINT = 'previous_sprint',
    SPECIFIC_SPRINT = 'specific_sprint',
}

export interface CardRange {
    min: number;
    max: number;
}

export type CardSprintId = number;

export interface CardSprint {
    id: number;
    name: string;
    index: number;
    iteration: number;
    startDate: number;
    endDate: number;
    inProgressDate?: number;
    doneDate?: number;
}

export type CardSpecificSprintSelection = {
    sprintIndicator: CardSprintIndicator.SPECIFIC_SPRINT;
    id: string;
};

export type CardCurrentOrPreviousSprintSelection = {
    sprintIndicator: CardSprintIndicator.CURRENT_SPRINT | CardSprintIndicator.PREVIOUS_SPRINT;
};

export type CardSprintSelection = CardSpecificSprintSelection | CardCurrentOrPreviousSprintSelection;
