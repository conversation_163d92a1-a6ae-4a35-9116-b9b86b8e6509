// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`CompletedQueryBuilder getReportQuery should generate correct query for basic parameters 1`] = `
"with
  \\"task_ids\\" as (
    select DISTINCT
      id,
      assignee
    from
      (
        (
          with
            \\"list_acls\\" as (
              select
                *
              from
                task_mgmt.get_hierarchy_object_acls (ARRAY['list'], $1, NULL) list_acls
              where
                jsonb_path_exists(list_acls.hierarchy_scopes, $2)
            ),
            \\"list_ids\\" as (
              select DISTINCT
                list_acls.object_id_int as id
              from
                \\"list_acls\\"
            )
          select
            maybe_assignee.userid as assignee,
            tasks.id
          from
            task_mgmt.tasks
            INNER JOIN list_ids ON tasks.subcategory = list_ids.id
            INNER JOIN list_acls ON list_acls.object_id_int = list_ids.id
            LEFT JOIN task_mgmt.object_acls task_acls ON tasks.id = task_acls.object_id
            AND task_acls.workspace_id = $3
            AND task_acls.object_type = 'task'
            INNER JOIN task_mgmt.statuses ON tasks.status = statuses.status
            AND statuses.workspace_id = $4
            LEFT OUTER JOIN task_mgmt.assignees maybe_assignee ON tasks.id = maybe_assignee.task_id
          where
            tasks.deleted IS NOT TRUE
            AND tasks.template IS NOT TRUE
            AND (
              task_acls.user_acl ? $5::text
              OR task_acls.group_acl ?| $6
              OR (
                COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
                AND (
                  (
                    list_acls.private IS NOT TRUE
                    AND $7 != 4
                  )
                  OR list_acls.user_acl ? $8::text
                  OR list_acls.group_acl ?| $9
                )
              )
            )
            AND (
              $10::boolean IS NULL
              OR tasks.archived = $11
            )
            and tasks.date_closed >= $12
            and tasks.date_closed <= $13
            and (statuses.type = $14)
        )
        union
        (
          with
            \\"list_acls\\" as (
              select
                *
              from
                task_mgmt.get_hierarchy_object_acls (ARRAY['list'], $15, NULL) list_acls
              where
                jsonb_path_exists(list_acls.hierarchy_scopes, $16)
            ),
            \\"list_ids\\" as (
              select DISTINCT
                list_acls.object_id_int as id
              from
                \\"list_acls\\"
            )
          select
            maybe_assignee.userid as assignee,
            tasks.id
          from
            task_mgmt.tasks
            INNER JOIN task_mgmt.task_subcategories ON task_subcategories.task_id = tasks.id
            AND tasks.subcategory != task_subcategories.subcategory
            INNER JOIN list_ids ON list_ids.id = task_subcategories.subcategory
            INNER JOIN list_acls ON list_acls.object_id_int = list_ids.id
            LEFT JOIN task_mgmt.object_acls task_acls ON tasks.id = task_acls.object_id
            AND task_acls.workspace_id = $17
            AND task_acls.object_type = 'task'
            INNER JOIN task_mgmt.statuses ON tasks.status = statuses.status
            AND statuses.workspace_id = $18
            LEFT OUTER JOIN task_mgmt.assignees maybe_assignee ON tasks.id = maybe_assignee.task_id
          where
            tasks.deleted IS NOT TRUE
            AND tasks.template IS NOT TRUE
            AND (
              task_acls.user_acl ? $19::text
              OR task_acls.group_acl ?| $20
              OR (
                COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
                AND (
                  (
                    list_acls.private IS NOT TRUE
                    AND $21 != 4
                  )
                  OR list_acls.user_acl ? $22::text
                  OR list_acls.group_acl ?| $23
                )
              )
            )
            AND (
              $24::boolean IS NULL
              OR tasks.archived = $25
            )
            and tasks.date_closed >= $26
            and tasks.date_closed <= $27
            and (statuses.type = $28)
        )
      ) as \\"task_ids_union\\"
  )
select
  \\"task_ids\\".\\"assignee\\" as \\"userid\\",
  count(*) AS completed_count
from
  \\"task_ids\\"
group by
  \\"task_ids\\".\\"assignee\\""
`;

exports[`CompletedQueryBuilder getReportQuery should generate correct query for basic parameters 2`] = `
Array [
  9000001,
  "$[*] ? ((@.object_type == \\"list\\" && @.object_id == \\"s1\\") || (@.object_type == \\"list\\" && @.object_id == \\"s2\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c1\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c2\\") || (@.object_type == \\"space\\" && @.object_id == \\"p1\\") || (@.object_type == \\"space\\" && @.object_id == \\"p2\\"))",
  9000001,
  9000001,
  101,
  Array [
    "a",
    "b",
  ],
  1,
  101,
  Array [
    "a",
    "b",
  ],
  false,
  false,
  100001,
  100002,
  "closed",
  9000001,
  "$[*] ? ((@.object_type == \\"list\\" && @.object_id == \\"s1\\") || (@.object_type == \\"list\\" && @.object_id == \\"s2\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c1\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c2\\") || (@.object_type == \\"space\\" && @.object_id == \\"p1\\") || (@.object_type == \\"space\\" && @.object_id == \\"p2\\"))",
  9000001,
  9000001,
  101,
  Array [
    "a",
    "b",
  ],
  1,
  101,
  Array [
    "a",
    "b",
  ],
  false,
  false,
  100001,
  100002,
  "closed",
]
`;

exports[`CompletedQueryBuilder getUserCompletedTasksQuery should generate correct query for basic parameters 1`] = `
"select distinct
  \\"id\\"
from
  (
    (
      with
        \\"list_acls\\" as (
          select
            *
          from
            task_mgmt.get_hierarchy_object_acls (ARRAY['list'], $1, NULL) list_acls
          where
            jsonb_path_exists(list_acls.hierarchy_scopes, $2)
        ),
        \\"list_ids\\" as (
          select DISTINCT
            list_acls.object_id_int as id
          from
            \\"list_acls\\"
        )
      select
        tasks.id
      from
        task_mgmt.tasks
        INNER JOIN list_ids ON tasks.subcategory = list_ids.id
        INNER JOIN list_acls ON list_acls.object_id_int = list_ids.id
        LEFT JOIN task_mgmt.object_acls task_acls ON tasks.id = task_acls.object_id
        AND task_acls.workspace_id = $3
        AND task_acls.object_type = 'task'
        INNER JOIN task_mgmt.assignees ON tasks.id = assignees.task_id
        INNER JOIN task_mgmt.statuses ON tasks.status = statuses.status
        AND statuses.workspace_id = $4
      where
        tasks.deleted IS NOT TRUE
        AND tasks.template IS NOT TRUE
        AND (
          task_acls.user_acl ? $5::text
          OR task_acls.group_acl ?| $6
          OR (
            COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
            AND (
              (
                list_acls.private IS NOT TRUE
                AND $7 != 4
              )
              OR list_acls.user_acl ? $8::text
              OR list_acls.group_acl ?| $9
            )
          )
        )
        AND (
          $10::boolean IS NULL
          OR tasks.archived = $11
        )
        and tasks.date_closed >= $12
        and tasks.date_closed <= $13
        and (statuses.type = $14)
        and assignees.userid = $15
    )
    union
    (
      with
        \\"list_acls\\" as (
          select
            *
          from
            task_mgmt.get_hierarchy_object_acls (ARRAY['list'], $16, NULL) list_acls
          where
            jsonb_path_exists(list_acls.hierarchy_scopes, $17)
        ),
        \\"list_ids\\" as (
          select DISTINCT
            list_acls.object_id_int as id
          from
            \\"list_acls\\"
        )
      select
        tasks.id
      from
        task_mgmt.tasks
        INNER JOIN task_mgmt.task_subcategories ON task_subcategories.task_id = tasks.id
        AND tasks.subcategory != task_subcategories.subcategory
        INNER JOIN list_ids ON list_ids.id = task_subcategories.subcategory
        INNER JOIN list_acls ON list_acls.object_id_int = list_ids.id
        LEFT JOIN task_mgmt.object_acls task_acls ON tasks.id = task_acls.object_id
        AND task_acls.workspace_id = $18
        AND task_acls.object_type = 'task'
        INNER JOIN task_mgmt.assignees ON tasks.id = assignees.task_id
        INNER JOIN task_mgmt.statuses ON tasks.status = statuses.status
        AND statuses.workspace_id = $19
      where
        tasks.deleted IS NOT TRUE
        AND tasks.template IS NOT TRUE
        AND (
          task_acls.user_acl ? $20::text
          OR task_acls.group_acl ?| $21
          OR (
            COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
            AND (
              (
                list_acls.private IS NOT TRUE
                AND $22 != 4
              )
              OR list_acls.user_acl ? $23::text
              OR list_acls.group_acl ?| $24
            )
          )
        )
        AND (
          $25::boolean IS NULL
          OR tasks.archived = $26
        )
        and tasks.date_closed >= $27
        and tasks.date_closed <= $28
        and (statuses.type = $29)
        and assignees.userid = $30
    )
  ) as \\"completed_tasks\\""
`;

exports[`CompletedQueryBuilder getUserCompletedTasksQuery should generate correct query for basic parameters 2`] = `
Array [
  9000001,
  "$[*] ? ((@.object_type == \\"list\\" && @.object_id == \\"s1\\") || (@.object_type == \\"list\\" && @.object_id == \\"s2\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c1\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c2\\") || (@.object_type == \\"space\\" && @.object_id == \\"p1\\") || (@.object_type == \\"space\\" && @.object_id == \\"p2\\"))",
  9000001,
  9000001,
  101,
  Array [
    "a",
    "b",
  ],
  1,
  101,
  Array [
    "a",
    "b",
  ],
  false,
  false,
  100001,
  100002,
  "closed",
  123,
  9000001,
  "$[*] ? ((@.object_type == \\"list\\" && @.object_id == \\"s1\\") || (@.object_type == \\"list\\" && @.object_id == \\"s2\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c1\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c2\\") || (@.object_type == \\"space\\" && @.object_id == \\"p1\\") || (@.object_type == \\"space\\" && @.object_id == \\"p2\\"))",
  9000001,
  9000001,
  101,
  Array [
    "a",
    "b",
  ],
  1,
  101,
  Array [
    "a",
    "b",
  ],
  false,
  false,
  100001,
  100002,
  "closed",
  123,
]
`;

exports[`CompletedQueryBuilder getUserCompletedTasksQuery should generate correct query for unassigned user 1`] = `
"select distinct
  \\"id\\"
from
  (
    (
      with
        \\"list_acls\\" as (
          select
            *
          from
            task_mgmt.get_hierarchy_object_acls (ARRAY['list'], $1, NULL) list_acls
          where
            jsonb_path_exists(list_acls.hierarchy_scopes, $2)
        ),
        \\"list_ids\\" as (
          select DISTINCT
            list_acls.object_id_int as id
          from
            \\"list_acls\\"
        )
      select
        tasks.id
      from
        task_mgmt.tasks
        INNER JOIN list_ids ON tasks.subcategory = list_ids.id
        INNER JOIN list_acls ON list_acls.object_id_int = list_ids.id
        LEFT JOIN task_mgmt.object_acls task_acls ON tasks.id = task_acls.object_id
        AND task_acls.workspace_id = $3
        AND task_acls.object_type = 'task'
        INNER JOIN task_mgmt.statuses ON tasks.status = statuses.status
        AND statuses.workspace_id = $4
      where
        tasks.deleted IS NOT TRUE
        AND tasks.template IS NOT TRUE
        AND (
          task_acls.user_acl ? $5::text
          OR task_acls.group_acl ?| $6
          OR (
            COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
            AND (
              (
                list_acls.private IS NOT TRUE
                AND $7 != 4
              )
              OR list_acls.user_acl ? $8::text
              OR list_acls.group_acl ?| $9
            )
          )
        )
        AND (
          $10::boolean IS NULL
          OR tasks.archived = $11
        )
        and tasks.date_closed >= $12
        and tasks.date_closed <= $13
        and (statuses.type = $14)
        and NOT EXISTS (
          SELECT
            1
          FROM
            task_mgmt.assignees
          WHERE
            tasks.id = assignees.task_id
        )
    )
    union
    (
      with
        \\"list_acls\\" as (
          select
            *
          from
            task_mgmt.get_hierarchy_object_acls (ARRAY['list'], $15, NULL) list_acls
          where
            jsonb_path_exists(list_acls.hierarchy_scopes, $16)
        ),
        \\"list_ids\\" as (
          select DISTINCT
            list_acls.object_id_int as id
          from
            \\"list_acls\\"
        )
      select
        tasks.id
      from
        task_mgmt.tasks
        INNER JOIN task_mgmt.task_subcategories ON task_subcategories.task_id = tasks.id
        AND tasks.subcategory != task_subcategories.subcategory
        INNER JOIN list_ids ON list_ids.id = task_subcategories.subcategory
        INNER JOIN list_acls ON list_acls.object_id_int = list_ids.id
        LEFT JOIN task_mgmt.object_acls task_acls ON tasks.id = task_acls.object_id
        AND task_acls.workspace_id = $17
        AND task_acls.object_type = 'task'
        INNER JOIN task_mgmt.statuses ON tasks.status = statuses.status
        AND statuses.workspace_id = $18
      where
        tasks.deleted IS NOT TRUE
        AND tasks.template IS NOT TRUE
        AND (
          task_acls.user_acl ? $19::text
          OR task_acls.group_acl ?| $20
          OR (
            COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
            AND (
              (
                list_acls.private IS NOT TRUE
                AND $21 != 4
              )
              OR list_acls.user_acl ? $22::text
              OR list_acls.group_acl ?| $23
            )
          )
        )
        AND (
          $24::boolean IS NULL
          OR tasks.archived = $25
        )
        and tasks.date_closed >= $26
        and tasks.date_closed <= $27
        and (statuses.type = $28)
        and NOT EXISTS (
          SELECT
            1
          FROM
            task_mgmt.assignees
          WHERE
            tasks.id = assignees.task_id
        )
    )
  ) as \\"completed_tasks\\""
`;

exports[`CompletedQueryBuilder getUserCompletedTasksQuery should generate correct query for unassigned user 2`] = `
Array [
  9000001,
  "$[*] ? ((@.object_type == \\"list\\" && @.object_id == \\"s1\\") || (@.object_type == \\"list\\" && @.object_id == \\"s2\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c1\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c2\\") || (@.object_type == \\"space\\" && @.object_id == \\"p1\\") || (@.object_type == \\"space\\" && @.object_id == \\"p2\\"))",
  9000001,
  9000001,
  101,
  Array [
    "a",
    "b",
  ],
  1,
  101,
  Array [
    "a",
    "b",
  ],
  false,
  false,
  100001,
  100002,
  "closed",
  9000001,
  "$[*] ? ((@.object_type == \\"list\\" && @.object_id == \\"s1\\") || (@.object_type == \\"list\\" && @.object_id == \\"s2\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c1\\") || (@.object_type == \\"folder\\" && @.object_id == \\"c2\\") || (@.object_type == \\"space\\" && @.object_id == \\"p1\\") || (@.object_type == \\"space\\" && @.object_id == \\"p2\\"))",
  9000001,
  9000001,
  101,
  Array [
    "a",
    "b",
  ],
  1,
  101,
  Array [
    "a",
    "b",
  ],
  false,
  false,
  100001,
  100002,
  "closed",
]
`;
