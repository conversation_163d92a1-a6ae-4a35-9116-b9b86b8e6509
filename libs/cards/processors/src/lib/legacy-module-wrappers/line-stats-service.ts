import { Injectable } from '@nestjs/common';

@Injectable()
export class LineWidgetStatsServiceLegacyModuleWrapper {
    constructor(
        public readonly mod: typeof import('@clickup-legacy/models/dashboard/widgets/services/LineWidgetStatsService')
    ) {}

    static register() {
        return {
            provide: LineWidgetStatsServiceLegacyModuleWrapper,
            useFactory: async () => {
                const mod = await import('@clickup-legacy/models/dashboard/widgets/services/LineWidgetStatsService');
                return new LineWidgetStatsServiceLegacyModuleWrapper(mod);
            },
        };
    }
}
