{"name": "whiteboards-v3-service-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/whiteboards-v3/service/client/src", "projectType": "library", "dependencies": {"ng-packagr": "^11.0.2", "reflect-metadata": "^0.1.3", "rxjs": "^6.6.0", "tsickle": "^0.39.1", "typescript": ">=4.0.0 <4.1.0", "zone.js": "^0.11.3"}, "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/whiteboards-v3/service/client"], "options": {"jestConfig": "libs/whiteboards-v3/service/client/jest.config.ts"}}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/whiteboards-v3/service/client", "tsConfig": "libs/whiteboards-v3/service/client/tsconfig.lib.json", "packageJson": "libs/whiteboards-v3/service/client/package.json", "main": "libs/whiteboards-v3/service/client/src/index.ts", "assets": ["libs/whiteboards-v3/service/client/*.md"]}}, "version": {"executor": "@jscutlery/semver:version"}, "publish": {"executor": "ngx-deploy-npm:deploy"}}, "tags": [], "namedInputs": {"projectSpecificFiles": ["{workspaceRoot}/tools/openapi-generator/swagger-files/whiteboards-v3-service-swagger.json"]}}