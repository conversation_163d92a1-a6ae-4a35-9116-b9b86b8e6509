import { afterLoadDocumentPayload, Extension, onChangePayload } from '@hocuspocus/server';
import { Injectable } from '@nestjs/common';

import { HealthEvaluator } from './health-evaluator';
import { HealthReporter } from './health-reporter/health-reporter';

@Injectable()
export class HealthcheckHpExtension implements Extension {
    constructor(private readonly healthEvaluator: HealthEvaluator, private readonly healthReporter: HealthReporter) {}

    afterLoadDocument(data: afterLoadDocumentPayload): Promise<any> {
        data.document.awareness.setLocalState({
            'db.attempts.count': 0,
            'db.success.count': 0,
            'db.failed.count': 0,
            'db.attempts.timestamp': null,
            'db.success.timestamp': null,
            'db.failed.timestamp': null,
            'changes.count': 0,
            'changes.timestamp': null,
        });
        return Promise.resolve();
    }

    onChange(data: onChangePayload): Promise<any> {
        const { awareness } = data.document;
        const localState = awareness.getLocalState();
        const healthReport = this.healthEvaluator.evaluateHealth(data.document);
        awareness.setLocalStateField('changes.timestamp', Date.now());
        awareness.setLocalStateField('changes.count', localState['changes.count'] + 1);
        this.healthReporter.reportHealth(healthReport, localState);
        return Promise.resolve();
    }
}
