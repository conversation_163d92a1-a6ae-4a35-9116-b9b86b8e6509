import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString, ValidateIf } from 'class-validator';

export class FormDisplayDto {
    @AutoMap()
    @ApiProperty()
    @IsBoolean()
    allow_multiple_submits!: boolean;

    @AutoMap()
    @ApiProperty()
    @IsOptional()
    @IsString()
    description?: string;

    @AutoMap()
    @ApiProperty()
    @IsBoolean()
    full_width!: boolean;

    @AutoMap()
    @ApiProperty()
    @IsBoolean()
    hide_branding!: boolean;

    @AutoMap()
    @ApiPropertyOptional({
        description: 'The original name of the form, set when created from a template.',
        deprecated: true,
    })
    @IsOptional()
    @IsString()
    name?: string;

    @AutoMap()
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    title?: string;

    @AutoMap()
    @ApiProperty()
    @IsString()
    theme!: string;

    // Fields that may only be present if the workspace has advanced_form_view enabled

    @AutoMap()
    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    add_captcha?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ nullable: true })
    @IsOptional()
    @IsString()
    @ValidateIf((object, value) => value !== null)
    avatar?: string | null;

    @AutoMap()
    @ApiPropertyOptional({ nullable: true })
    @IsOptional()
    @IsString()
    @ValidateIf((object, value) => value !== null)
    color?: string | null;

    @AutoMap()
    @ApiPropertyOptional()
    @IsOptional()
    @IsString()
    style?: string;

    @AutoMap()
    @ApiPropertyOptional({ nullable: true })
    @IsOptional()
    @IsString()
    background?: string | null;

    @AutoMap()
    @ApiPropertyOptional()
    @IsOptional()
    @IsBoolean()
    show_cover_header?: boolean;

    @AutoMap()
    @ApiPropertyOptional({ nullable: true })
    @IsOptional()
    @IsString()
    cover_image_url?: string | null;

    @AutoMap()
    @ApiPropertyOptional({ nullable: true })
    @IsOptional()
    @IsString()
    cover_image_color?: string | null;

    @AutoMap()
    @ApiPropertyOptional({ nullable: true })
    @IsOptional()
    @IsNumber()
    cover_position_x?: number | null;

    @AutoMap()
    @ApiPropertyOptional({ nullable: true })
    @IsOptional()
    @IsNumber()
    cover_position_y?: number | null;
}
