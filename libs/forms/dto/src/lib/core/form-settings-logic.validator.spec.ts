import { BadRequestException } from '@nestjs/common';

import { BUG_SUBMISSION_FORM } from '../testing/fixtures/bug-submission-form';
import { FormSettingsDto } from './form-settings.dto';
import { FormSettingsDtoLogicValidator } from './form-settings-logic.validator';

const MOCK_CONDITION = {
    expressions: [{ id: 'expression1', field: 'field1', operand: 'test', operator: 'equals' }],
    operator: 'AND',
};

describe('FormSettingsDtoLogicValidator', () => {
    let validator: FormSettingsDtoLogicValidator;

    beforeEach(() => {
        validator = new FormSettingsDtoLogicValidator();
    });

    it('should pass validation for a valid form settings', () => {
        const validFormSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [],
                },
                {
                    field: 'field2',
                    display_name: 'Field 2',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [
                        {
                            id: 'rule1',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field3', required: false }] },
                        },
                    ],
                },
            ],
            conditional_fields: [
                {
                    field: 'field3',
                    display_name: 'Field 3',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule1'],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(validFormSettings)).not.toThrow();
    });

    it('should throw an error for duplicate field IDs', () => {
        const invalidFormSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [],
                },
                {
                    field: 'field1',
                    display_name: 'Field 1 Duplicate',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(invalidFormSettings)).toThrow(BadRequestException);
        expect(() => validator.validate(invalidFormSettings)).toThrow(
            'Duplicate field IDs found in fields or conditional_fields'
        );
    });

    it('should throw an error for circular logic', () => {
        const circularFormSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [
                        {
                            id: 'rule1',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field2', required: false }] },
                        },
                    ],
                },
            ],
            conditional_fields: [
                {
                    field: 'field2',
                    display_name: 'Field 2',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule1', 'rule2'],
                    rules: [
                        {
                            id: 'rule2',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field2', required: false }] },
                        },
                    ],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(circularFormSettings)).toThrow(BadRequestException);
        expect(() => validator.validate(circularFormSettings)).toThrow('Circular logic detected');
    });

    it('should throw an error for exceeding nested logic depth', () => {
        const deeplyNestedFormSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [
                        {
                            id: 'rule1',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field2', required: false }] },
                        },
                    ],
                },
            ],
            conditional_fields: [
                {
                    field: 'field2',
                    display_name: 'Field 2',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule1'],
                    rules: [
                        {
                            id: 'rule2',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field3', required: false }] },
                        },
                    ],
                },
                {
                    field: 'field3',
                    display_name: 'Field 3',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule2'],
                    rules: [
                        {
                            id: 'rule3',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field4', required: false }] },
                        },
                    ],
                },
                {
                    field: 'field4',
                    display_name: 'Field 4',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule3'],
                    rules: [
                        {
                            id: 'rule4',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field5', required: false }] },
                        },
                    ],
                },
                {
                    field: 'field5',
                    display_name: 'Field 5',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule4'],
                    rules: [
                        {
                            id: 'rule5',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field6', required: false }] },
                        },
                    ],
                },
                {
                    field: 'field6',
                    display_name: 'Field 6',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule5'],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(deeplyNestedFormSettings)).toThrow(BadRequestException);
        expect(() => validator.validate(deeplyNestedFormSettings)).toThrow('Nested logic depth limit exceeded');
    });

    it('should throw an error for unreferenced conditional fields', () => {
        const unreferencedFieldsSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [],
                },
            ],
            conditional_fields: [
                {
                    field: 'field2',
                    display_name: 'Field 2',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['nonexistent_rule'],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(unreferencedFieldsSettings)).toThrow(BadRequestException);
        expect(() => validator.validate(unreferencedFieldsSettings)).toThrow(
            'Unreferenced conditional fields found: field2'
        );
    });

    it('should throw an error for non-conditional fields with dependencies', () => {
        const invalidDependenciesSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    dependencies: ['some_rule'],
                    rules: [],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(invalidDependenciesSettings)).toThrow(BadRequestException);
        expect(() => validator.validate(invalidDependenciesSettings)).toThrow(
            "Field 'field1' has dependencies, which are not supported"
        );
    });

    it('should throw an error for conditional fields with 0 dependencies', () => {
        const invalidConditionalFieldSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [
                        {
                            id: 'rule1',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field2', required: false }] },
                        },
                    ],
                },
            ],
            conditional_fields: [
                {
                    field: 'field2',
                    display_name: 'Field 2',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: [],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(invalidConditionalFieldSettings)).toThrow(BadRequestException);
        expect(() => validator.validate(invalidConditionalFieldSettings)).toThrow(
            "Conditional field 'field2' must have at least one rule dependency"
        );
    });

    it('should allow conditional fields to be referenced by multiple rules', () => {
        const validMultiReferenceSettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [
                        {
                            id: 'rule1',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field3', required: false }] },
                        },
                    ],
                },
                {
                    field: 'field2',
                    display_name: 'Field 2',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [
                        {
                            id: 'rule2',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field3', required: false }] },
                        },
                    ],
                },
            ],
            conditional_fields: [
                {
                    field: 'field3',
                    display_name: 'Field 3',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule1', 'rule2'],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(validMultiReferenceSettings)).not.toThrow();
    });

    it('should throw an error for conditional fields depending on non-referring rules', () => {
        const invalidRuleDependencySettings: FormSettingsDto = {
            fields: [
                {
                    field: 'field1',
                    display_name: 'Field 1',
                    required: false,
                    hidden: false,
                    append_description: false,
                    rules: [
                        {
                            id: 'rule1',
                            condition: MOCK_CONDITION,
                            action: { show: [{ field: 'field2', required: false }] },
                        },
                    ],
                },
            ],
            conditional_fields: [
                {
                    field: 'field2',
                    display_name: 'Field 2',
                    required: false,
                    hidden: true,
                    append_description: false,
                    dependencies: ['rule2'],
                },
            ],
        } as unknown as FormSettingsDto;

        expect(() => validator.validate(invalidRuleDependencySettings)).toThrow(BadRequestException);
        expect(() => validator.validate(invalidRuleDependencySettings)).toThrow(
            "Conditional field 'field2' must depend on the referring rule 'rule1'"
        );
    });

    it('should validate BUG_SUBMISSION_FORM fixture successfully', () => {
        expect(() => validator.validate(BUG_SUBMISSION_FORM.form_settings)).not.toThrow();
    });
});
