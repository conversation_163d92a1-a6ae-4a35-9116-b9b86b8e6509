import { AutoMap } from '@automapper/classes';
import { ApiPropertyOptional, refs } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsOptional, IsString, ValidateNested } from 'class-validator';

import { DropdownOption } from './dropdown-option.rto';
import { LabelOption } from './label-option.rto';

export class CustomFieldTypeConfig {
    @ApiPropertyOptional({ oneOf: refs(DropdownOption, LabelOption) })
    @IsArray()
    @IsOptional()
    @Type(() => Object)
    @ValidateNested({ each: true })
    @AutoMap(() => [Object])
    options?: (DropdownOption | LabelOption)[];

    @ApiPropertyOptional()
    @AutoMap()
    @IsString()
    @IsOptional()
    sorting?: string;

    // User custom fields

    @ApiPropertyOptional()
    @AutoMap()
    @IsBoolean()
    @IsOptional()
    single_user?: boolean;

    @ApiPropertyOptional()
    @AutoMap()
    @IsBoolean()
    @IsOptional()
    include_groups?: boolean;

    @ApiPropertyOptional()
    @AutoMap()
    @IsBoolean()
    @IsOptional()
    include_guests?: boolean;

    @ApiPropertyOptional()
    @AutoMap()
    @IsBoolean()
    @IsOptional()
    include_team_members?: boolean;

    // End User custom fields
}
