import { AutoMap } from '@automapper/classes';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsObject, IsOptional,IsString, IsUUID, Matches, ValidateNested } from 'class-validator';

import type { DefaultFieldValueRto } from './custom-field-default-value.rto';
import { CustomFieldTypeConfig } from './custom-field-type-config.rto';

export class CustomFieldRto {
    @ApiProperty()
    @IsString()
    @AutoMap()
    @Matches(/^cf_[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
    field!: string;

    @ApiProperty()
    @IsUUID()
    @AutoMap()
    id!: string;

    @ApiProperty()
    @IsString()
    @AutoMap()
    name!: string;

    @ApiProperty()
    @IsString()
    @AutoMap()
    team_id!: string;

    @ApiProperty()
    @IsString()
    @AutoMap()
    type!: string;

    @ApiProperty()
    @IsObject()
    @AutoMap(() => CustomFieldTypeConfig)
    @ValidateNested()
    @Type(() => CustomFieldTypeConfig)
    type_config!: CustomFieldTypeConfig;

    @ApiPropertyOptional()
    @IsObject()
    @AutoMap()
    @IsOptional()
    default_value?: DefaultFieldValueRto;
}