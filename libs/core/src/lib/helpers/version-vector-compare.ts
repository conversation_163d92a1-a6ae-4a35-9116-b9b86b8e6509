import { NotImplementedException } from '@nestjs/common';
import {
    type ObjectVersionVector,
    isVersionVectorEqual,
    isVersionVectorGreater,
    isVersionVectorGreaterOrEqual,
} from '@time-loop/ovm-object-version';

/**
 * Operation type when comparing two version vectors.
 * Generally applied from the API via a query parameter `?version-operator`,
 * see REST API guidelines https://staging.clickup.com/333/v/dc/ad-267369/ad-801613 for more details.
 */
export enum VersionCompareOperator {
    Equal = 'eq',
    Greater = 'gt',
    GreaterOrEqual = 'gte',
}

/**
 * Utility for comparing object version vectors.
 * @param providedVersionVector The version vector provided by the user
 * @param persistedVersionVector The version vector persisted in the database
 * @param operator The comparison operator to use. Defaults to VersionCompareOperator.Equal if not specified,.
 * @returns Results of the comparison operation
 */
export function compareVersionVectors(
    providedVersionVector: ObjectVersionVector,
    persistedVersionVector: ObjectVersionVector,
    operator: VersionCompareOperator | undefined = VersionCompareOperator.Equal
): boolean {
    switch (operator) {
        case VersionCompareOperator.Greater:
            return isVersionVectorGreater(persistedVersionVector, providedVersionVector);
        case VersionCompareOperator.GreaterOrEqual:
            return isVersionVectorGreaterOrEqual(persistedVersionVector, providedVersionVector);
        case VersionCompareOperator.Equal:
            return isVersionVectorEqual(persistedVersionVector, providedVersionVector);
        default:
            throw new NotImplementedException(`Unknown operator ${operator}`);
    }
}
