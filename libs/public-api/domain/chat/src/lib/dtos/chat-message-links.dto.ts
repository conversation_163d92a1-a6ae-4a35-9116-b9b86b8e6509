import { ApiProperty } from '@nestjs/swagger';

export const CHAT_MESSAGE_LINKED_FIELDS = ['reactions', 'replies', 'tagged_users'] as const;

type LinkedFields = typeof CHAT_MESSAGE_LINKED_FIELDS[number];

export class ChatMessageLinks2 implements Record<LinkedFields, string> {
    @ApiProperty({
        description: 'The link to the reactions of this message.',
        required: true,
    })
    reactions: string;

    @ApiProperty({
        description: 'The link to the replies of this message.',
        required: true,
    })
    replies: string;

    @ApiProperty({
        description: 'The link to the tagged users of this message.',
        required: true,
    })
    tagged_users: string;
}
