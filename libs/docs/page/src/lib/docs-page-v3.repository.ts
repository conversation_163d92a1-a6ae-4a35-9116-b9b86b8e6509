import type { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import {
    ObjectType,
    ObjectVersionChangeEvent,
    ObjectVersionUpdateRequest,
    ObjectVersionVector,
    OperationType,
} from '@time-loop/ovm-object-version';
import { Knex, knex } from 'knex';
import { QueryResultRow } from 'pg';

import { CreatePageOptions, EditPageOptions, PageV3, PageV3IdMetadata, PageV3Reference } from '@clickup/docs/domain';
import { DBClient } from '@clickup/utils/db';
import type { ObjectWithVersionUpdateEventProperty } from '@clickup/utils/db-ovm';
import type { DbQueryResult, QueryObject, QueryParams, SimpleClient } from '@clickup/utils/db-types';
import { DefaultMap } from '@clickup-legacy/utils/DefaultMap';

import { PageV3DbInsert } from './dto/page-v3-db-insert.dto';
import { PageV3DbRow, PageV3ReferenceDbRow } from './dto/page-v3-db-row.dto';
import { PageV3DbUpdate } from './dto/page-v3-db-update.dto';

type PageNode = PageV3Reference | PageV3;
const ROOT_PAGES_KEY = 'ROOT';

export interface SearchPageIdMetadatasResults {
    pageIdMetadatas: PageV3IdMetadata[];
    nextPageId: string;
}

@Injectable()
export class PageV3Repository {
    private readonly knexClient: Knex;

    constructor(private readonly dbClient: DBClient, @InjectMapper() private readonly mapper: Mapper) {
        this.knexClient = knex({ client: 'pg' });
    }

    async createPage(
        workspaceId: number,
        docId: string,
        pageId: string,
        userId: number,
        createPageOptions: CreatePageOptions
    ): Promise<string> {
        const orderIndex = await this.getNewPageOrderIndex(workspaceId, docId, createPageOptions.parentPageId);
        const now = Date.now();

        const pageV3DbInsert = this.getPageV3DbInsertFromCreatePageOptions(createPageOptions, {
            id: pageId,
            orderindex: orderIndex,
            team_id: workspaceId,
            view_id: docId,
            creator: userId,
            date_created: now,
            date_updated: now,
        });

        const queryObjects: QueryObject[] = [];

        const viewDocsInsertQueryBuilder = this.knexClient.withSchema('task_mgmt').into('view_docs');
        viewDocsInsertQueryBuilder.insert(pageV3DbInsert);
        const { sql: viewDocsQuery, bindings: viewDocsParams } = viewDocsInsertQueryBuilder.toSQL().toNative();

        queryObjects.push({ query: viewDocsQuery, params: viewDocsParams as QueryParams });

        const pageAuthorsInsertQueryBuilder = this.knexClient.withSchema('task_mgmt').into('page_authors');
        pageAuthorsInsertQueryBuilder.insert({
            page_id: pageId,
            user_id: userId,
            contributor: false,
            workspace_id: workspaceId,
        });
        const { sql: pageAuthorsQuery, bindings: pageAuthorsParams } = pageAuthorsInsertQueryBuilder.toSQL().toNative();

        queryObjects.push({ query: pageAuthorsQuery, params: pageAuthorsParams as QueryParams });

        const versionUpdates: ObjectVersionUpdateRequest[] = [
            {
                operation: OperationType.CREATE,
                object_type: ObjectType.PAGE,
                object_id: pageId,
                workspace_id: workspaceId,
            },
        ];

        await this.dbClient.writeAsyncFunction(
            (simpleClient: SimpleClient) =>
                Promise.all(queryObjects.map(qo => simpleClient.queryAsync(qo.query, qo.params))),
            { versionUpdates }
        );

        return pageId;
    }

    async editPage(
        workspaceId: number,
        docId: string,
        pageId: string,
        userId: number,
        editPageOptions: EditPageOptions
    ): Promise<ObjectVersionVector> {
        const now = Date.now();

        const pageDbUpdate = this.getPageV3DbUpdateFromEditPageOptions(editPageOptions, {
            date_edited: now,
            date_updated: now,
            edited_by: userId,
        });

        const queryBuilder = knex({ client: 'pg' }).withSchema('task_mgmt').from('view_docs');
        queryBuilder.where({ team_id: workspaceId, id: pageId, view_id: docId }).update(pageDbUpdate);
        const { sql: query, bindings: params } = queryBuilder.toSQL().toNative();

        const versionUpdates: ObjectVersionUpdateRequest[] = [
            {
                operation: OperationType.UPDATE,
                object_type: ObjectType.PAGE,
                object_id: pageId,
                workspace_id: workspaceId,
                data: {
                    // TODO: we are notifying on the changes as they relate to the updated db columns, do we want to
                    //  notifying on the names of our conceptual data model instead?
                    changes: Object.keys(pageDbUpdate).map(k => ({ field: k })),
                },
            },
        ];

        const queryResult = (await this.dbClient.writeAsyncFunction(
            (simpleClient: SimpleClient) => simpleClient.queryAsync(query, params as QueryParams),
            { versionUpdates }
        )) as ObjectWithVersionUpdateEventProperty<DbQueryResult<QueryResultRow>>;

        let versionChangeEvents: ObjectVersionChangeEvent[] = [];
        ({ __version_change_events: versionChangeEvents } = queryResult || {});

        const versionChangeEvent = versionChangeEvents?.find(event => event.object_id === pageId);

        let versionVector: ObjectVersionVector;

        if (versionChangeEvent) {
            const objectVersion = (({ master_id, version, deleted }) => ({
                master_id,
                version,
                deleted,
            }))(versionChangeEvent);

            versionVector = {
                object_id: pageId,
                object_type: ObjectType.PAGE,
                workspace_id: workspaceId,
                vector: [objectVersion],
            };
        }

        return versionVector;
    }

    async getPage(workspaceId: number, docId: string, pageId: string, skipCache = false): Promise<PageV3 | null> {
        const query = `
            SELECT vd.id, vd.view_id as doc_id, vd.team_id as workspace_id, vd.name, vd.date_created, vd.date_updated, 
                vd.content, vd.parent as parent_page_id, vd.creator as creator_id, vd.deleted, vd.deleted_by, 
                vd.date_deleted, vd.orderindex as order_index, vd.date_edited, vd.edited_by, vd.archived, 
                vd.archived_by, vd.date_archived, vd.subtitle as sub_title, vd.protected, vd.protected_note, 
                vd.protected_by,
                ARRAY_AGG(JSON_BUILD_OBJECT('user_id', pa.user_id, 'contributor', pa.contributor)) as authors,
                JSON_BUILD_OBJECT('value', vd.avatar_value, 'source', vd.avatar_source, 'color', vd.avatar_color) as 
                    avatar,
                JSON_BUILD_OBJECT('image_url', vd.cover_image_url, 'color', vd.cover_image_color, 'position', 
                    JSON_BUILD_OBJECT('x', vd.cover_position_x, 'y', vd.cover_position_y)) as cover,
                JSON_BUILD_OBJECT('font', pd.font, 'font_size', pd.font_size, 'line_height', pd.line_height, 
                    'paragraph_spacing', pd.paragraph_spacing, 'page_width', pd.page_width, 'show_cover_header', 
                    pd.show_cover_header, 'show_sub_title_header', pd.show_subtitle_header, 'show_author_header', 
                    pd.show_author_header, 'show_page_outline', pd.show_page_outline, 'show_sub_pages', 
                    pd.show_subpages, 'sub_page_size', pd.subpage_size, 'show_contributor_header', 
                    pd.show_contributor_header, 'show_title_icon_header', pd.show_title_icon_header, 
                    'show_date_header', pd.show_date_header) as presentation_details
            FROM task_mgmt.view_docs AS vd
            LEFT JOIN task_mgmt.page_authors AS pa ON pa.page_id=vd.id AND pa.workspace_id=vd.team_id
            LEFT JOIN task_mgmt.page_details AS pd ON pd.page_id=vd.id AND pd.workspace_id=vd.team_id
            WHERE vd.id=$1 AND vd.view_id=$2 AND vd.team_id=$3 AND vd.deleted IS NOT TRUE
            GROUP BY vd.id, pd.page_id
        `;
        const params = [pageId, docId, workspaceId];

        const { rows } = await this.dbClient.readAsync<PageV3DbRow>(query, params, { useReplica: !skipCache });

        if (rows.length) {
            return this.getPageV3FromPageV3DbRow(rows[0]);
        }

        return null;
    }

    async getPages(workspaceId: number, pageIds: string[], client?: SimpleClient): Promise<PageV3[]> {
        const query = `
            SELECT vd.id, vd.view_id as doc_id, vd.team_id as workspace_id, vd.name, vd.date_created, vd.date_updated, 
                vd.content, vd.parent as parent_page_id, vd.creator as creator_id, vd.deleted, vd.deleted_by, 
                vd.date_deleted, vd.orderindex as order_index, vd.date_edited, vd.edited_by, vd.archived, 
                vd.archived_by, vd.date_archived, vd.subtitle as sub_title, vd.protected, vd.protected_note, 
                vd.protected_by,
                ARRAY_AGG(JSON_BUILD_OBJECT('user_id', pa.user_id, 'contributor', pa.contributor)) as authors,
                JSON_BUILD_OBJECT('value', vd.avatar_value, 'source', vd.avatar_source, 'color', vd.avatar_color) as 
                    avatar,
                JSON_BUILD_OBJECT('image_url', vd.cover_image_url, 'color', vd.cover_image_color, 'position', 
                    JSON_BUILD_OBJECT('x', vd.cover_position_x, 'y', vd.cover_position_y)) as cover,
                JSON_BUILD_OBJECT('font', pd.font, 'font_size', pd.font_size, 'line_height', pd.line_height, 
                    'paragraph_spacing', pd.paragraph_spacing, 'page_width', pd.page_width, 'show_cover_header', 
                    pd.show_cover_header, 'show_sub_title_header', pd.show_subtitle_header, 'show_author_header', 
                    pd.show_author_header, 'show_page_outline', pd.show_page_outline, 'show_sub_pages', 
                    pd.show_subpages, 'sub_page_size', pd.subpage_size, 'show_contributor_header', 
                    pd.show_contributor_header, 'show_title_icon_header', pd.show_title_icon_header, 
                    'show_date_header', pd.show_date_header) as presentation_details
            FROM task_mgmt.view_docs AS vd
            LEFT JOIN task_mgmt.page_authors AS pa ON pa.page_id=vd.id AND pa.workspace_id=vd.team_id
            LEFT JOIN task_mgmt.page_details AS pd ON pd.page_id=vd.id AND pd.workspace_id=vd.team_id
            WHERE vd.id=ANY($1) AND vd.team_id=$2
            GROUP BY vd.id, pd.page_id
        `;
        const params = [pageIds, workspaceId];

        const { rows } = await (client
            ? client.queryAsync<PageV3DbRow>(query, params)
            : this.dbClient.readAsync<PageV3DbRow>(query, params));

        return rows.map(r => this.getPageV3FromPageV3DbRow(r));
    }

    /**
     * Returns page's content as a string (will be encrypted).
     *
     * @param workspaceId       WorkspaceId of the page.
     * @param docId             DocId of the page.
     * @param pageId            Id of the page.
     */
    async getPageContent(workspaceId: number, docId: string, pageId: string): Promise<string> {
        const query = `
            SELECT vd.content
            FROM task_mgmt.view_docs AS vd
            WHERE vd.id=$1 AND vd.view_id=$2 AND vd.team_id=$3
        `;
        const params = [pageId, docId, workspaceId];

        const { rows } = await this.dbClient.readAsyncFunction((simpleClient: SimpleClient) =>
            simpleClient.queryAsync<{ content: string }>(query, params)
        );

        if (rows.length) {
            return rows[0].content;
        }

        return null;
    }

    async getYdocContent(workspaceId: number, docId: string, pageId: string): Promise<Uint8Array> {
        const query = `
            SELECT vd.ydoc
            FROM task_mgmt.view_docs AS vd
            WHERE vd.id=$1 AND vd.view_id=$2 AND vd.team_id=$3
        `;
        const params = [pageId, docId, workspaceId];

        const { rows } = await this.dbClient.readAsyncFunction((simpleClient: SimpleClient) =>
            simpleClient.queryAsync<{ ydoc: Uint8Array }>(query, params)
        );

        if (rows.length) {
            return rows[0].ydoc;
        }

        return null;
    }

    async getDocPagesHierarchy(workspaceId: number, docId: string): Promise<Array<PageV3Reference>> {
        const query = `
            SELECT vd.id, vd.view_id as doc_id, vd.team_id as workspace_id, vd.name, vd.parent as parent_page_id,
            vd.orderindex as order_index
            FROM task_mgmt.view_docs AS vd
            WHERE vd.view_id=$1 AND vd.team_id=$2 AND vd.deleted IS NOT TRUE AND vd.archived IS NOT TRUE
            ORDER BY parent_page_id, order_index ASC
        `;
        const params = [docId, workspaceId];

        const { rows } = await this.dbClient.readAsyncFunction((simpleClient: SimpleClient) =>
            simpleClient.queryAsync<PageV3ReferenceDbRow>(query, params)
        );

        let listing: PageV3Reference[] = [];

        if (rows.length) {
            listing = PageV3Repository.assemblePageNodesTree<PageV3Reference>(
                rows.map(r => this.getPageV3ReferenceFromPageV3ReferenceDbRow(r))
            );
        }

        return listing;
    }

    async getDocPages(workspaceId: number, docId: string): Promise<Array<PageV3>> {
        const query = `
            SELECT vd.id, vd.view_id as doc_id, vd.team_id as workspace_id, vd.name, vd.date_created, vd.date_updated, 
                vd.content, vd.parent as parent_page_id, vd.creator as creator_id, vd.deleted, vd.deleted_by, 
                vd.date_deleted, vd.orderindex as order_index, vd.date_edited, vd.edited_by, vd.archived, 
                vd.archived_by, vd.date_archived, vd.subtitle as sub_title, vd.protected, vd.protected_note, 
                vd.protected_by,
                ARRAY_AGG(JSON_BUILD_OBJECT('user_id', pa.user_id, 'contributor', pa.contributor)) as authors,
                JSON_BUILD_OBJECT('value', vd.avatar_value, 'source', vd.avatar_source, 'color', vd.avatar_color) as 
                    avatar,
                JSON_BUILD_OBJECT('image_url', vd.cover_image_url, 'color', vd.cover_image_color, 'position', 
                    JSON_BUILD_OBJECT('x', vd.cover_position_x, 'y', vd.cover_position_y)) as cover,
                JSON_BUILD_OBJECT('font', pd.font, 'font_size', pd.font_size, 'line_height', pd.line_height, 
                    'paragraph_spacing', pd.paragraph_spacing, 'page_width', pd.page_width, 'show_cover_header', 
                    pd.show_cover_header, 'show_sub_title_header', pd.show_subtitle_header, 'show_author_header', 
                    pd.show_author_header, 'show_page_outline', pd.show_page_outline, 'show_sub_pages', 
                    pd.show_subpages, 'sub_page_size', pd.subpage_size, 'show_contributor_header', 
                    pd.show_contributor_header, 'show_title_icon_header', pd.show_title_icon_header, 
                    'show_date_header', pd.show_date_header) as presentation_details
            FROM task_mgmt.view_docs AS vd
            LEFT JOIN task_mgmt.page_authors AS pa ON pa.page_id=vd.id AND pa.workspace_id=vd.team_id
            LEFT JOIN task_mgmt.page_details AS pd ON pd.page_id=vd.id AND pd.workspace_id=vd.team_id
            WHERE vd.view_id=$1 AND vd.team_id=$2 AND vd.deleted IS NOT TRUE
            GROUP BY vd.id, pd.page_id
        `;
        const params = [docId, workspaceId];

        const { rows } = await this.dbClient.readAsyncFunction((simpleClient: SimpleClient) =>
            simpleClient.queryAsync<PageV3DbRow>(query, params)
        );

        let pages: PageV3[] = [];

        if (rows.length) {
            pages = PageV3Repository.assemblePageNodesTree<PageV3>(rows.map(r => this.getPageV3FromPageV3DbRow(r)));
        }

        return pages;
    }

    async searchPageIdMetadatas(
        workspaceId: number,
        limit: number,
        nextPageId?: string
    ): Promise<SearchPageIdMetadatasResults> {
        const params: QueryParams = [workspaceId, limit + 1];
        const query = `
            SELECT vd.id, vd.view_id as doc_id
            FROM task_mgmt.view_docs AS vd
            WHERE vd.team_id=$1 AND vd.deleted IS NOT TRUE AND vd.archived IS NOT TRUE 
              ${nextPageId != null ? `AND vd.id >= $${params.push(nextPageId)}` : ''}
            ORDER BY vd.id ASC LIMIT $2
        `;

        const { rows } = await this.dbClient.readAsync<{ doc_id: string; id: string }>(query, params);

        const pageIdMetadatas = rows.map(row => ({ docId: row.doc_id, id: row.id, workspaceId }));
        nextPageId = pageIdMetadatas.length > limit ? pageIdMetadatas.pop().id : null;

        return { pageIdMetadatas, nextPageId };
    }

    public async getNewPageOrderIndex(
        workspaceId: number,
        docId: string,
        parentPageId: string = null
    ): Promise<number> {
        let query = `
            SELECT coalesce(max(orderindex), 0) + 1 AS orderindex
            FROM task_mgmt.view_docs
            WHERE view_docs.team_id=$1 AND view_docs.view_id = $2
        `;
        const params = [workspaceId, docId];

        if (!parentPageId || parentPageId === 'none') {
            query += ` AND view_docs.parent IS NULL`;
        } else {
            query += ` AND view_docs.parent = $${params.push(parentPageId)}`;
        }

        const { rows } = await this.dbClient.readAsyncFunction((simpleClient: SimpleClient) =>
            simpleClient.queryAsync<{ orderindex: number }>(query, params)
        );

        return rows.length === 1 ? rows[0].orderindex : 0;
    }

    private getPageV3FromPageV3DbRow(row: PageV3DbRow): PageV3 {
        return this.mapper.map(row, PageV3DbRow, PageV3);
    }

    private getPageV3ReferenceFromPageV3ReferenceDbRow(row: PageV3ReferenceDbRow): PageV3Reference {
        return this.mapper.map(row, PageV3ReferenceDbRow, PageV3Reference);
    }

    private getPageV3DbInsertFromCreatePageOptions(
        createPageOptions: CreatePageOptions,
        pageV3DbInsertOverrides: Partial<PageV3DbInsert> = {}
    ): PageV3DbInsert {
        const pageV3DbInsert = this.mapper.map(createPageOptions, CreatePageOptions, PageV3DbInsert);
        return { ...pageV3DbInsert, ...pageV3DbInsertOverrides };
    }

    private getPageV3DbUpdateFromEditPageOptions(
        editPageOptions: EditPageOptions,
        pageV3DbUpdateOverrides: Partial<PageV3DbUpdate> = {}
    ): PageV3DbUpdate {
        const pageV3DbUpdate = this.mapper.map(editPageOptions, EditPageOptions, PageV3DbUpdate);
        return { ...pageV3DbUpdate, ...pageV3DbUpdateOverrides };
    }

    /**
     * Takes the given list of flat PageNode objects and returns a list
     * of the same objects, but with the pages attributes being properly
     * computed for each (i.e. page/subpage relationships computed and
     * pages/subpages nested appropriately. Each PageNode object in the
     * input list is expected to have an empty array for its pages attribute.
     * The operation is destructive in that it modifies the pages attribute for
     * each of the input PageNode objects when appropriate.
     *
     * For example, if we have an input list as follows:
     *
     * [
     *     { id: 'A', parentPageId: null, pages: [] },
     *     { id: 'B', parentPageId: null, pages: [] },
     *     { id: 'C', parentPageId: 'B', pages: [] },
     * ]
     *
     * The list returned by this function will look like this:
     *
     * [
     *     { id: 'A', parentPageId: null, pages: [] },
     *     { id: 'B', parentPageId: null, pages: [{ id: 'C', parentPageId: 'B', pages: [] }] },
     * ]
     *
     *
     * @param flatPageNodes
     */
    public static assemblePageNodesTree<T extends PageNode>(flatPageNodes: T[]): T[] {
        const parentIdToChildren = new DefaultMap<string, T[]>(() => []);

        for (const pageNode of flatPageNodes) {
            parentIdToChildren.get(pageNode.parentPageId ?? ROOT_PAGES_KEY).push(pageNode);
        }

        for (const pageNode of flatPageNodes) {
            const childPages = parentIdToChildren.get(pageNode.id);
            if (childPages.length) {
                pageNode.pages = childPages;
            }
        }

        return parentIdToChildren.get(ROOT_PAGES_KEY);
    }
}
