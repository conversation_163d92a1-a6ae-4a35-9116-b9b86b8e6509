import { createMock } from '@golevelup/ts-jest';
import { Test, TestingModule } from '@nestjs/testing';

import { PagePresentationDetails } from '@clickup/docs/domain';

import { PagePresentationDetailsRepository } from './docs-presentation-details.repository';
import { PagePresentationDetailsService } from './docs-presentation-details.service';

describe('PagePresentationDetailsService', () => {
    let pagePresentationDetailsService: PagePresentationDetailsService;
    let pageDetailsRepository: PagePresentationDetailsRepository;

    beforeAll(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                PagePresentationDetailsService,
                {
                    provide: PagePresentationDetailsRepository,
                    useValue: createMock<PagePresentationDetailsRepository>({
                        getPagesPresentationDetails: jest.fn(),
                    }),
                },
            ],
        })
            .useMocker(createMock)
            .compile();

        pagePresentationDetailsService = module.get<PagePresentationDetailsService>(PagePresentationDetailsService);
        pageDetailsRepository = module.get<PagePresentationDetailsRepository>(PagePresentationDetailsRepository);
    });

    describe('getPageDetailsMap', () => {
        it('should return a map of page details', async () => {
            const pageIds = ['1', '2', '3'];
            const pageDetails = [
                { page_id: '1' } as PagePresentationDetails,
                { page_id: '2' } as PagePresentationDetails,
                { page_id: '3' } as PagePresentationDetails,
            ];
            jest.spyOn(pageDetailsRepository, 'getPagesPresentationDetails').mockResolvedValue(pageDetails);

            const result = await pagePresentationDetailsService.getPageDetailsMap(pageIds);

            expect(result).toBeInstanceOf(Map);
            expect(result.size).toBe(pageDetails.length);
            expect(result.get(pageDetails[0].page_id)).toEqual(pageDetails[0]);
            expect(result.get(pageDetails[1].page_id)).toEqual(pageDetails[1]);
            expect(result.get(pageDetails[2].page_id)).toEqual(pageDetails[2]);
        });
    });
});
