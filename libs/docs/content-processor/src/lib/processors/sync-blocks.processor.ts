import { Message } from '@aws-sdk/client-sqs';
import { Injectable } from '@nestjs/common';
import { ObjectType, ObjectVectorChangeEvent } from '@time-loop/ovm-object-version';

import { RequestState } from '@clickup/data-platform/sharding/tracing';
import { Op, QuillOps } from '@clickup/docs/domain';
import { GenericEntityContentService } from '@clickup/docs/generic-content';
import { SyncBlockService } from '@clickup/docs/sync-blocks';
import { isSyncBlockOp } from '@clickup/quill';
import { AsyncStorage, initContext } from '@clickup/shared/utils-async-storage';
import { StatsDClient } from '@clickup/shared/utils-metrics';
import { tracer } from '@clickup/shared/utils-tracing';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';
import { SqsMessageHandler } from '@clickup/utils/sqs';

import { DOCS_UNSYNC_QUEUE_CONSUMER } from '../constants/queue-names';
import { SyncBlockProcessorMetricNames } from '../metric-names';
import { BaseProcessor } from './base.processor';

@Injectable()
export class SyncBlocksProcessor extends BaseProcessor {
    readonly name = 'SyncBlocksProcessor';

    private readonly metricsClient: StatsDClient;

    constructor(
        metricsService: MetricsService,
        logger: FormatLogger,
        private readonly syncBlockService: SyncBlockService,
        private readonly genericEntityService: GenericEntityContentService,
        private readonly featureFlagService: FeatureFlagService
    ) {
        super(metricsService, logger);
        this.metricsClient = metricsService.client;
    }

    override isValidOp(op: Op): boolean {
        return isSyncBlockOp(op);
    }

    async process(event: ObjectVectorChangeEvent, ops: Op[]): Promise<void> {
        const { object_id: objectId, workspace_id: workspaceId } = event;
        let { object_type: objectType } = event;
        const blocksFoundInContent = new Set<string>();
        ops.forEach(op => {
            blocksFoundInContent.add(op.insert['sync-block'].id);
        });
        const detachedBlocks: string[] = [];
        const reattachedBlocks: string[] = [];

        // caveat to handle since page and doc are equal but we track `doc` in the database
        if (objectType === ObjectType.PAGE) {
            objectType = ObjectType.DOC;
        }
        // search for sync blocks/references for this entity
        const { blocks: parentBlocks, references: referenceBlocks } =
            await this.syncBlockService.getBlockAndReferencesFromParent(objectId, objectType);

        // add to detached blocks if sync blocks not found in content
        for (const parentBlock of parentBlocks) {
            if (blocksFoundInContent.has(parentBlock.id)) {
                if (parentBlock.isDetached) {
                    reattachedBlocks.push(parentBlock.id);
                }
                blocksFoundInContent.delete(parentBlock.id);
            } else {
                detachedBlocks.push(parentBlock.id);
            }
        }

        if (
            detachedBlocks.length &&
            this.featureFlagService.getBoolean({
                flag: 'docs-should-detach-sync-blocks',
                target: String(workspaceId),
            })
        ) {
            await this.syncBlockService.updateBlocksDetached(detachedBlocks, true);
        }

        if (reattachedBlocks.length) {
            await this.syncBlockService.updateBlocksDetached(reattachedBlocks, false);
        }

        // remove sync block references
        for (const referenceBlock of referenceBlocks) {
            if (!blocksFoundInContent.has(referenceBlock.blockId)) {
                await this.syncBlockService.deleteReference(
                    referenceBlock.blockId,
                    referenceBlock.refId,
                    referenceBlock.refType
                );
            }
        }

        // create sync block references
        for (const blockId of blocksFoundInContent) {
            if (!referenceBlocks.find(referenceBlock => referenceBlock.blockId === blockId)) {
                await Promise.allSettled([
                    this.syncBlockService.createReference(blockId, objectId, objectType, workspaceId),
                ]);
            }
        }
    }

    @SqsMessageHandler(DOCS_UNSYNC_QUEUE_CONSUMER, false)
    @tracer.Decorator((ob: SyncBlocksProcessor, message: Message) => {
        const { blockId, userId, deletion } = JSON.parse(message.Body || '{}');
        return {
            name: `syncBlocksProcessor.processUnsyncMessage`,
            service: process.env.DD_SERVICE,
            tags: {
                'unsync.blockId': blockId,
                'unsync.userId': userId,
                'unsync.deletion': deletion ? 'true' : 'false',
            },
        };
    })
    async processUnsyncMessage(message: Message) {
        let userId!: number;
        let blockId!: string;
        let workspaceId!: number;
        let deletion!: boolean;
        let exclude_parent!: boolean;
        try {
            ({ userId, blockId, workspaceId, deletion, exclude_parent } = JSON.parse(message.Body || '{}'));
            if (!blockId) {
                return;
            }
            this.metricsClient.increment(SyncBlockProcessorMetricNames.SYNC_BLOCK_PROCESSOR_UNSYNC_PROCESS, 1);
            const asyncStorage = AsyncStorage.getInstance();
            await initContext(asyncStorage, async () => {
                const context = asyncStorage.getContext();
                context.unverifiedWorkspaceId = workspaceId;
                context.verifiedWorkspace = {
                    requestState: RequestState.WorkspaceResolved,
                    untrustedId: workspaceId,
                    debug: {
                        calls: [],
                        idConflictDetected: false,
                        headerConflictDetected: false,
                        shardConflictDetected: false,
                    },
                };
                await this.unsyncBlockAndReferences(userId, blockId, deletion, exclude_parent);
            });
            this.metricsClient.increment(SyncBlockProcessorMetricNames.SYNC_BLOCK_PROCESSOR_UNSYNC_SUCCESS, 1);
        } catch (err) {
            this.metricsClient.increment(SyncBlockProcessorMetricNames.SYNC_BLOCK_PROCESSOR_UNSYNC_ERROR, 1);
            this.logger.error({ msg: 'Error processing unsync message', err, blockId, userId, workspaceId, deletion });
        }
    }

    async unsyncBlockAndReferences(userId: number, blockId: string, deletion = false, exclude_parent = false) {
        const { block, references } = await this.syncBlockService.getBlockAndReferences(blockId);
        const { ops }: QuillOps = deletion ? { ops: [] } : JSON.parse(block.content);
        for (const reference of references) {
            const { refType, refId } = reference;
            // update reference entity with sync block content
            await this.updateWithSyncBlockContent(refId, refType, block.workspaceId, blockId, ops);
            await this.syncBlockService.deleteReference(blockId, refId, refType);
        }
        // update parent entity with sync block content
        if (!exclude_parent) {
            await this.updateWithSyncBlockContent(block.parentId, block.parentType, block.workspaceId, blockId, ops);
        }
        await this.syncBlockService.deleteBlock(blockId);
    }

    async updateWithSyncBlockContent(
        entityId: string,
        objectType: ObjectType,
        workspaceId: number,
        blockId: string,
        syncBlockContent: Op[]
    ) {
        const jsonContent = await this.genericEntityService.fetchContentByEntity({
            id: entityId,
            type: objectType,
            workspaceId,
        });
        if (!jsonContent) {
            return;
        }
        const newOps: QuillOps = { ops: [] };
        let referenceFound = false;
        // find and replace all sync block references
        jsonContent.ops.forEach(op => {
            if (op.insert?.['sync-block']?.id === blockId) {
                referenceFound = true;
                newOps.ops.push(...syncBlockContent);
            } else {
                newOps.ops.push(op);
            }
        });
        if (!referenceFound) {
            return;
        }
        this.metricsClient.increment(SyncBlockProcessorMetricNames.SYNC_BLOCK_PROCESSOR_UPDATE_ENTITY, 1, {
            objectId: entityId,
            objectType,
        });
        await this.genericEntityService.updateContentByEntity({
            id: entityId,
            type: objectType,
            workspaceId,
            content: JSON.stringify(newOps),
        });
    }
}
