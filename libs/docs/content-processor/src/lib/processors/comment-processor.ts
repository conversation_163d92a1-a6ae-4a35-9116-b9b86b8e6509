import { Inject, Injectable } from '@nestjs/common';
import { ObjectType, ObjectVectorChangeEvent, OperationType } from '@time-loop/ovm-object-version';

import { AttachmentService } from '@clickup/attachment/core';
import { CommentRepository } from '@clickup/comment/core';
import { isCommentIdFromCommentsDb } from '@clickup/comment/domain';
import { Blots, parseAttachmentIdsFromBlots } from '@clickup/quill';
import { DBClient, makeReadSimpleClient } from '@clickup/utils/db';
import { SimpleClient } from '@clickup/utils/db-types';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';
import { COMMENTS_DB_CLIENT } from '@clickup-legacy/utils/CommentsDBClient';

import { GenericOvmProcessor } from './generic-ovm.processor';

@Injectable()
export class CommentProcessor extends GenericOvmProcessor {
    private readonly simpleClient: SimpleClient;

    private readonly simpleCommentsDbClient: SimpleClient;

    constructor(
        private readonly attachmentService: AttachmentService,
        private readonly commentRepository: CommentRepository,
        dbClient: DBClient,
        @Inject(COMMENTS_DB_CLIENT) commentsDbClient: DBClient,
        metricsClientService: MetricsService,
        logger: FormatLogger
    ) {
        super(metricsClientService, logger);
        this.simpleClient = makeReadSimpleClient(dbClient);
        this.simpleCommentsDbClient = makeReadSimpleClient(commentsDbClient);
    }

    async process(event: ObjectVectorChangeEvent): Promise<void> {
        const { object_id: objectId, workspace_id: workspaceId } = event;
        const [commentSchema] = await this.commentRepository.getCommentsByIds(
            this.getSimpleClient(objectId),
            [objectId],
            workspaceId
        );

        if (!commentSchema) {
            return;
        }

        const blots = (
            typeof commentSchema.comment === 'string' ? JSON.parse(commentSchema.comment) : commentSchema.comment
        ) as Blots;
        const attachmentIds = parseAttachmentIdsFromBlots(blots);
        if (attachmentIds.length) {
            await this.attachmentService.setObjectAttachmentReferences(
                workspaceId,
                objectId,
                ObjectType.COMMENT,
                attachmentIds
            );
        }
    }

    private getSimpleClient(commentId: string): SimpleClient {
        return isCommentIdFromCommentsDb(commentId) ? this.simpleCommentsDbClient : this.simpleClient;
    }

    isValidEvent(event: ObjectVectorChangeEvent): boolean {
        return (
            event.object_type === ObjectType.COMMENT &&
            [OperationType.UPDATE, OperationType.CREATE].includes(event.operation)
        );
    }
}
