import { Injectable } from '@nestjs/common';
import { ObjectType, ObjectVectorChangeEvent } from '@time-loop/ovm-object-version';

import { Op } from '@clickup/docs/domain';
import { DocsListViewService } from '@clickup/docs/list-view';
import { isListViewOp } from '@clickup/quill';
import { StatsDClient } from '@clickup/shared/utils-metrics';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';

import { BaseProcessor } from './base.processor';

@Injectable()
export class ListViewProcessor extends BaseProcessor {
    readonly name = 'ListViewProcessor';

    private readonly metricsClient: StatsDClient;

    protected override get objectTypesToProcess() {
        return [ObjectType.PAGE];
    }

    constructor(
        metricsClientService: MetricsService,
        logger: FormatLogger,
        private readonly listViewService: DocsListViewService
    ) {
        super(metricsClientService, logger);
        this.metricsClient = metricsClientService.client;
    }

    /**
     * Validate if the event should be processed by this processor.
     * @param event
     */
    override isValidEvent(event: ObjectVectorChangeEvent): boolean {
        return super.isValidEvent(event);
    }

    override isValidOp(op: Op): boolean {
        return isListViewOp(op);
    }

    async process(event: ObjectVectorChangeEvent, ops: Op[]): Promise<void> {
        const listViewsFound = new Set<string>();

        ops.forEach(op => {
            const viewId = op.insert?.listview?.viewId || op.insert?.embedview?.viewId;
            listViewsFound.add(viewId);
        });

        await this.listViewService.upsertListViews(event.object_id, Array.from(listViewsFound), event.workspace_id);
    }
}
