{"extends": ["../../../.eslintrc.json"], "ignorePatterns": ["!**/*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {}}, {"files": ["*.ts", "*.tsx"], "rules": {"@typescript-eslint/consistent-type-imports": ["error", {"prefer": "type-imports"}]}}, {"files": ["*.js", "*.jsx"], "rules": {}}, {"files": ["*.json"], "parser": "jsonc-eslint-parser", "rules": {"@nx/dependency-checks": ["error", {"ignoredFiles": ["{projectRoot}/eslint.config.{js,cjs,mjs}"]}]}}]}