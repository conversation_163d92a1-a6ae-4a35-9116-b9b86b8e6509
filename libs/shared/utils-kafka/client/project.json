{"name": "shared-utils-kafka-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/utils-kafka/client/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/utils-kafka/client/jest.config.ts"}}}}