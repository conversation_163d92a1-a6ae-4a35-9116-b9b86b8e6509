/* eslint-disable no-console */
/* eslint-disable global-require */
/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * Unit test utils for testing kafka client
 *
 * @group unit/utils/kafka/utils
 */

import type { EachBatchPayload, EachMessagePayload, KafkaMessage } from 'kafkajs';

import type {
    BatchMessageProcessor,
    BatchMessageProcessorResult,
    KafkaConsumerProps,
    KafkaProducerProps,
    MessageProcessor,
} from '@clickup/shared/utils-kafka/client-common';
import { JSONValueDecoder, StringValueDecoder } from '@clickup/shared/utils-kafka/client-common';

import { KafkaConsumerImpl, KafkaProducerImpl, RetryableError } from './kafka-client';
import {
    connectMock,
    consumerMock,
    disconnectMock,
    producerMock,
    receivePayload,
    sendMock,
    subscribeMock,
} from './mocks';

const CLIENT_ID = 'test_client_id';
const GROUP_ID = 'test_group_id';
export const TOPIC = 'test-topic';
const MESSAGE1 = { value: 'test-value-1' };
const MESSAGE2 = { value: 'test-value-2' };
const MESSAGES = [MESSAGE1, MESSAGE2];
const MAX_BYTES = 24000; // Cannot import from config because that would mark it read-only... and unfortunately some of the tests require read-write for configOverride
export const producerProps: KafkaProducerProps = {
    clientId: CLIENT_ID,
};
export const consumerProps: KafkaConsumerProps = {
    clientId: CLIENT_ID,
    groupId: GROUP_ID,
    maxBytes: MAX_BYTES,
};

const stringValues: string[] = ['{"x":1, "y":2}', '{"x":2, "y":5}', '{"x":100, "y":15}'];

export const RECEIVE_MESSAGES: KafkaMessage[] = [
    {
        key: Buffer.from('task-1'),
        value: Buffer.from(stringValues[0]),
        timestamp: 'mock-time',
        size: 123,
        attributes: 1,
        offset: 'mock-offset-1',
    },
    {
        key: Buffer.from('task-1'),
        value: Buffer.from(stringValues[1]),
        timestamp: 'mock-time',
        size: 123,
        attributes: 1,
        offset: 'mock-offset-2',
    },
    {
        key: Buffer.from('task-3'),
        value: Buffer.from(stringValues[2]),
        timestamp: 'mock-time',
        size: 123,
        attributes: 1,
        offset: 'mock-offset-3',
    },
];

export function mockConfigs(testConfigs: TestConfigsHolder): TestConfigs {
    const overrideConfig = testConfigs();
    if (overrideConfig.configOverride) {
        const config = require('config');
        overrideConfig.configOverride(config);
    }
    return overrideConfig;
}

export interface TestConfigs {
    configOverride: TestConfigsOverride;
}

type TestConfigsHolder = () => TestConfigs;

// -- sending functions
export async function oneMessageSender(producer: KafkaProducerImpl): Promise<void> {
    await producer.send(TOPIC, MESSAGE1);
    expect(sendMock).toHaveBeenCalledWith({
        compression: 2,
        messages: [MESSAGE1],
        topic: TOPIC,
    });
}

export async function multipleMessagesSender(producer: KafkaProducerImpl): Promise<void> {
    await producer.send(TOPIC, MESSAGES);
    expect(sendMock).toHaveBeenCalledWith({
        compression: 2,
        messages: MESSAGES,
        topic: TOPIC,
    });
}

// -- consume functions

// consume strings
export const eachMessageStringProcessor: MessageProcessor<string> = async (value: string): Promise<void> => {
    receivePayload(value);
    // console.log(`- ${value}`);
};

export async function eachMessageStringConsumer(consumer: KafkaConsumerImpl): Promise<void> {
    await consumer.subscribe<string>({ topic: TOPIC }, eachMessageStringProcessor, StringValueDecoder);
    expect(receivePayload).toHaveBeenCalledTimes(3);
    expect(receivePayload).toHaveBeenCalledWith(stringValues[0]);
    expect(receivePayload).toHaveBeenCalledWith(stringValues[1]);
    expect(receivePayload).toHaveBeenCalledWith(stringValues[2]);
}

// consume structured data
export interface Coordinate {
    x: number;
    y: number;
}

export const eachMessageProcessorTwoArgs: MessageProcessor<Coordinate> = async (
    value: Coordinate,
    payload: Omit<EachMessagePayload, 'heartbeat'>
): Promise<void> => {
    const { topic, partition, message } = payload;
    const prefix = `${topic}[${partition} | ${message.offset}] / ${message.timestamp}`;
    // console.log(`- ${prefix} ${message.key}#(${value.x}, ${value.y})`);
    receivePayload(`${prefix} ${message.key}#(${value.x}, ${value.y})`);
};

export async function eachMessageJSONConsumer(consumer: KafkaConsumerImpl): Promise<void> {
    await consumer.subscribe<Coordinate>({ topic: TOPIC }, eachMessageProcessorTwoArgs, JSONValueDecoder);
    expect(receivePayload).toHaveBeenCalledTimes(3);
    expect(receivePayload).toHaveBeenCalledWith('test-topic[1 | mock-offset-1] / mock-time task-1#(1, 2)');
    expect(receivePayload).toHaveBeenCalledWith('test-topic[1 | mock-offset-2] / mock-time task-1#(2, 5)');
    expect(receivePayload).toHaveBeenCalledWith('test-topic[1 | mock-offset-3] / mock-time task-3#(100, 15)');
}

// consume in batches
export const batchMessageProcessor: BatchMessageProcessor<Coordinate> = async (
    values: Coordinate[],
    payload: EachBatchPayload
): Promise<void> => {
    const { topic, partition } = payload.batch;
    const coordinates = values.map(c => `(${c.x}, ${c.y})`).join(' ');
    // console.log(`- ${topic}[${partition}] | ${coordinates}`);
    receivePayload(`${topic}[${partition}] | ${coordinates}`);
};

export async function batchMessageJSONConsumer(consumer: KafkaConsumerImpl): Promise<void> {
    await consumer.subscribeBatch<Coordinate>({ topic: TOPIC }, batchMessageProcessor);
    expect(receivePayload).toHaveBeenCalledTimes(1);
    expect(receivePayload).toHaveBeenCalledWith('test-topic[1] | (1, 2) (2, 5) (100, 15)');
}

export function buildBatchWithResult(result: BatchMessageProcessorResult): TestMessageReceiver {
    return async (consumer: KafkaConsumerImpl): Promise<void> => {
        await consumer.subscribeBatch<Coordinate>(
            { topic: TOPIC },
            async (values: Coordinate[]): Promise<BatchMessageProcessorResult> => {
                const coordinates = values.map(c => `(${c.x}, ${c.y})`);

                if (result.messageProcessedUpToIndex >= 0) {
                    receivePayload(coordinates.slice(0, result.messageProcessedUpToIndex + 1).join(' '));
                } else if (result.messageProcessedUpToIndex !== 0) {
                    receivePayload(coordinates.join(' '));
                }

                return result;
            }
        );
    };
}

// consume with error
export const retryableErrorProcessor: BatchMessageProcessor<Coordinate> = async (
    values: Coordinate[]
): Promise<void> => {
    throw new RetryableError('retryable error');
};

type TestConfigsOverride = (config: any) => void;
type TestMessageSender = (client: KafkaProducerImpl) => Promise<void>;
type TestMessageReceiver = (client: KafkaConsumerImpl) => Promise<void>;

/**
 * Tests the lifecycle for a Kafka producer client such as configuration, connecting to server, sending messages,
 * and disconnecting from server.
 *
 * @param props
 * @param testMessageSender
 */
export async function testProducer(props: KafkaProducerProps, testMessageSender: TestMessageSender): Promise<void> {
    const producer = await KafkaProducerImpl.getKafkaProducer(props);
    await testMessageSender(producer);
    await producer.disconnect({ disableGracefulShutdown: true });

    expect(producerMock).toHaveBeenCalled();
    expect(connectMock).toHaveBeenCalled();
    expect(disconnectMock).toHaveBeenCalled();
}

/**
 * Tests the lifecycle for a Kafka consumer client such as configuration, connecting to server, subscribing to a topic,
 * receiving messages, and disconnecting from server.
 *
 * @param props
 * @param testMessageReceiver
 */
export async function testConsumer(props: KafkaConsumerProps, testMessageReceiver: TestMessageReceiver): Promise<void> {
    const consumer = await KafkaConsumerImpl.getKafkaConsumer(props);
    await testMessageReceiver(consumer);
    await consumer.disconnect();

    expect(consumerMock).toHaveBeenCalledWith({
        ...props,
        allowAutoTopicCreation: false,
        eachBatchAutoResolve: false,
        maxWaitTimeInMs: 1000,
        errorRetryDelayMaxMs: 10000,
        errorRetryDelayMinMs: 200,
        errorRetryMax: 10,
        processTimeoutMs: 10000,
        logLevel: 'INFO',
        maxBytes: MAX_BYTES,
    });
    expect(connectMock).toHaveBeenCalled();
    expect(subscribeMock).toHaveBeenCalledWith({ topic: TOPIC, fromBeginning: false });
    expect(disconnectMock).toHaveBeenCalled();
}
