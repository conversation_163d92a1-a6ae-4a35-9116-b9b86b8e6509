import { Injectable } from '@nestjs/common';

import { GroupService, UserEmailContext, UserService, WorkspaceId } from '@clickup/user/core';
import { TypedLoggerWithContext } from '@clickup/utils/typed-logger';

import { EmailRecipient } from './clickup-page-exports-response';

const LOGGER = TypedLoggerWithContext.create('EmailResolverService');

@Injectable()
export class EmailAddressResolverService {
    constructor(private readonly userService: UserService, private readonly groupService: GroupService) {}

    async resolve(workspaceId: WorkspaceId, recipients: EmailRecipient[] | undefined): Promise<UserEmailContext[]> {
        const memberIds = (recipients || [])
            .filter(recipient => recipient.type === 'memberId')
            .map(recipient => recipient.value);

        const users = memberIds.length ? await this.userService.getUsers({ ids: memberIds.map(Number) }) : [];

        return Promise.all(
            (recipients || []).map(async (recipient): Promise<UserEmailContext[]> => {
                if (recipient.type === 'email') {
                    return [
                        {
                            address: recipient.value,
                            isBounced: false,
                            isVerified: true,
                        },
                    ];
                }
                if (recipient.type === 'memberId') {
                    const user = users.find(u => u.id === Number(recipient.value));
                    if (user) {
                        return [
                            {
                                userId: user.id,
                                address: user.email.address,
                                isBounced: false,
                                isVerified: true,
                            },
                        ];
                    }
                    LOGGER.warn(`User ${recipient.value} not found`);
                }
                if (recipient.type === 'groupId') {
                    return this.groupService.listGroupUsers(workspaceId, recipient.value).then(workspaceUsers =>
                        workspaceUsers.map(({ user }) => ({
                            userId: user.id,
                            address: user.email.address,
                            isBounced: false,
                            isVerified: true,
                        }))
                    );
                }
                return [];
            })
        ).then(results => results.flat());
    }
}
