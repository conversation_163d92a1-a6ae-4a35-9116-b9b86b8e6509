import { Inject, Injectable } from '@nestjs/common';

import { FeatureFlagService } from '@clickup/utils/feature-flag';

interface ClickupPageExportsFeatureFlagConfig {
    maxAttachmentFileSizeMb: number;
}
@Injectable()
export class ClickupPageExportsFeatureService {
    public static readonly DEFAULT: ClickupPageExportsFeatureFlagConfig = {
        // Maximum file size in MB for an attachment.
        maxAttachmentFileSizeMb: 10,
    };

    constructor(@Inject(FeatureFlagService) private readonly featureFlagService: FeatureFlagService) {}

    getConfiguration(workspaceId: string): ClickupPageExportsFeatureFlagConfig {
        const featureFlagConfig = this.featureFlagService.getObject<ClickupPageExportsFeatureFlagConfig>({
            flag: 'cards_clickup_page_exports_config',
            target: workspaceId,
        });
        return featureFlagConfig || ClickupPageExportsFeatureService.DEFAULT;
    }

    humanReadableAttachments(workspaceId: string): boolean {
        const featureFlagConfig = this.featureFlagService.getBoolean({
            flag: 'cards-dashboard-exports-human-readable-attachments',
            target: workspaceId,
        });
        return featureFlagConfig || false;
    }
}
