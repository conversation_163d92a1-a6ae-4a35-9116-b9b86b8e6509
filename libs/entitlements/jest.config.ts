/* eslint-disable */
export const e2eRegexp = '\\.e2e\\.(spec|test)\\.ts$';

export default {
    displayName: 'entitlements',
    preset: '../../jest.preset.js',
    testEnvironment: 'node',
    transform: {
        '^.+\\.[tj]s$': [
            'ts-jest',
            {
                tsconfig: '<rootDir>/tsconfig.spec.json',
            },
        ],
    },
    moduleFileExtensions: ['ts', 'js', 'html'],
    coverageDirectory: '../../coverage/libs/entitlements',
    testPathIgnorePatterns: ['/node_modules/', e2eRegexp],
};
