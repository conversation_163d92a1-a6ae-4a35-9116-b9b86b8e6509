// Abstract class instead of an interface to allow for Nest injections
export abstract class RateLimiterGcra {
    /**
     * This function calculates how many requests can be performed given the rate limit
     * according to GCRA algorithm. You should calculate the requestTimeMs from desired requests per second
     * value.
     * The limiter should be statful and should limit the request number according to its internal state.
     * Two consecutive calls with the same parameters should result in the fist call getting processed
     * correctly, and the second one should always return 0 (fully allocated).
     * @param key Redis key to use
     * @param currentTimeMs Current timestamp
     * @param requestTimeMs How much time the requests should have allocated, derivative of requests per second
     * @param numberOfRequests How many requests want to be submitted
     * @param burstSize Bucket capacity
     * @returns {Promise<{allocatedRequests: number, bucketEmptyAt: number}>} Object containing number of requests that can be submitted and timestamp when bucket will be empty
     */
    abstract rateLimit(
        key: string,
        currentTimeMs: number,
        requestTimeMs: number,
        numberOfRequests: number,
        burstSize?: number
    ): Promise<{ allocatedRequests: number; bucketEmptyAt: number }>;
}
