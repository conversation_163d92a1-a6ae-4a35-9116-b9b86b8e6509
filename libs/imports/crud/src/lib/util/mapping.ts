import moment from 'moment';

import {
    ImportStatus,
    ImportStatusFinished,
    ImportStatusFinishedValues,
    ImportStatusInProgressValues,
    TaskImportDto,
    TaskImportRow,
    TaskImportRto,
} from '@clickup/imports/types';

export function mapTaskImportRowToTaskImportDto(taskImportRow: TaskImportRow): TaskImportDto {
    const importDto: Partial<TaskImportDto> = {
        import_id: taskImportRow.import_id,
        user_id: taskImportRow.userid,
        workspace_id: taskImportRow.team_id,
        created_date: new Date(Number(taskImportRow.started)),
        updated_date: new Date(Number(taskImportRow.last_update)),
        complete_tasks: taskImportRow.complete_tasks,
        total_tasks: taskImportRow.total_tasks,
        error: taskImportRow.error,
        warnings: taskImportRow.warnings,
        source: taskImportRow.source,
        import_version: taskImportRow.import_version,
    };

    const fifteenMinutesAgo = moment().subtract(15, 'minutes');
    const lastUpdateAt = moment(new Date(Number(taskImportRow.last_update)));

    if (
        Object.values(ImportStatusFinishedValues).includes(taskImportRow.import_step as unknown as ImportStatusFinished)
    ) {
        importDto.status = taskImportRow.import_step as unknown as ImportStatus;
    } else if (taskImportRow.import_step === 'complete') {
        importDto.status = ImportStatusFinishedValues.Completed;
    } else if (taskImportRow.node_working === null || fifteenMinutesAgo.isAfter(lastUpdateAt)) {
        importDto.status = ImportStatusInProgressValues.Queued as unknown as ImportStatus;
    } else {
        importDto.status = ImportStatusInProgressValues.Pending as unknown as ImportStatus;
    }

    if (importDto.error) {
        delete importDto.error.err;
        delete importDto.error.stack;
        delete importDto.error.code;
    }

    if (importDto.warnings && importDto.warnings.length === 0) {
        importDto.warnings = null;
    }

    return importDto as TaskImportDto;
}

export function mapTaskImportRowToTaskImportRto(taskImportRow: TaskImportRow): TaskImportRto {
    const taskImportDto = mapTaskImportRowToTaskImportDto(taskImportRow);
    delete taskImportDto.input_data;

    return taskImportDto as TaskImportRto;
}
