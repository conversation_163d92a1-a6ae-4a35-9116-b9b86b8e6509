import { Connection } from '@typedorm/core';

import { WhiteboardsDynamicConfigurationServiceDummy } from '@clickup/whiteboards/dynamic-config';

import { WhiteboardObjectUpdater } from '../../../../../../src/lib/state/dynamodb/whiteboard/whiteboard-objects/whiteboard-object-updater';

const whiteboardContext = { workspaceId: 123, whiteboardId: 'test', userId: 456 };
const UNEXPECTED_ERROR = new Error('Unexpected error');
describe('WhiteboardObjectUpdater', () => {
    let testedObject: WhiteboardObjectUpdater;
    let updateFunctionMock: jest.Mock;

    beforeEach(() => {
        jest.resetAllMocks();
        updateFunctionMock = jest.fn();
        testedObject = new WhiteboardObjectUpdater(
            {
                entityManager: { update: updateFunctionMock },
            } as unknown as Connection,
            new WhiteboardsDynamicConfigurationServiceDummy()
        );
    });

    it('should re-throw unknown exception', async () => {
        updateFunctionMock.mockRejectedValue(UNEXPECTED_ERROR);

        await expect(testedObject.bulkUpdate(whiteboardContext, [{}])).rejects.toThrowErrorMatchingSnapshot();
    });
});
