import { OPERATION_PAGE } from '@clickup/security-scanner/common';

import { PhishingAnalysisResult, ViewsAnalyzerCommandMessage } from './views-analyzer-command-message';

export class PagesAnalyzerCommandMessage extends ViewsAnalyzerCommandMessage {
    constructor(
        scannerCorrelationId: string,
        viewId: string,
        teamId: number,
        userId: number,
        publicUrl: string,
        crmUrl: string,
        internalUrl: string,
        phishingAnalysis?: PhishingAnalysisResult
    ) {
        super(
            OPERATION_PAGE,
            scannerCorrelationId,
            viewId,
            teamId,
            userId,
            publicUrl,
            crmUrl,
            internalUrl,
            phishingAnalysis
        );
    }
}
