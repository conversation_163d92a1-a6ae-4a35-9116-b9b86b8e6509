import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';

import { UserWorkspaceTokenScopeModule } from '@clickup/user/workspace-token-scope';
import { JwtDecodeMiddleware, UtilsAuthenticationModule } from '@clickup/utils/authentication';
import { AuthorizeWorkspaceUserGuard, UtilsAuthorizationModule } from '@clickup/utils/authorization';
import { UtilsConfigModule } from '@clickup/utils/config';
import { UtilsDbModule } from '@clickup/utils/db';
import { UtilsLoggingModule } from '@clickup/utils/logging';

import { ClickUpSecurityModule } from './click-up-security-module';

@Module({
    imports: [
        UtilsDbModule,
        UtilsAuthenticationModule,
        UtilsAuthorizationModule,
        UtilsConfigModule,
        UtilsLoggingModule,
        UserWorkspaceTokenScopeModule,
    ],
    providers: [
        {
            provide: APP_GUARD,
            useClass: AuthorizeWorkspaceUserGuard,
        },
    ],
})
export class NestSecurityModule extends ClickUpSecurityModule implements NestModule {
    configure(consumer: MiddlewareConsumer): any {
        // JwtDecodeMiddleware parses and decodes the JWT and adds it to the
        // current request context if successful.
        // If it fails to handle the JWT the request continues, we rely on
        // AuthorizeWorkspaceUserGuard to authorize the current user's decoded JWT.
        consumer.apply(JwtDecodeMiddleware).exclude('(.*)/health').forRoutes('*');
    }
}
