import 'reflect-metadata';

import { type MiddlewareConsumer } from '@nestjs/common';

import { BasePublicApiModule, checkControllers } from './base-public-api.module';

export abstract class BaseGatewayPublicApiModule extends BasePublicApiModule {
    async configure(consumer: MiddlewareConsumer) {
        const controllers = Reflect.getMetadata('controllers', this.constructor);

        checkControllers(controllers);

        const [{ PublicAPIAuthenticationMiddleware }, { limiter: publicApiLimiter }] = await Promise.all([
            import('@clickup/utils/authentication'),
            import('@clickup-legacy/utils/ratelimit'),
        ]);

        consumer.apply(publicApiLimiter, PublicAPIAuthenticationMiddleware).forRoutes(...controllers);
    }
}
