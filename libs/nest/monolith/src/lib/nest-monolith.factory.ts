import { NestFactory } from '@nestjs/core';

import { getLogger } from '@clickup/shared/utils-logging';

import { ClickUpMonolithModule } from './clickup-monolith.module';
import { CreateNestMonolithApplicationContextOptions } from './models';

const logger = getLogger('nest-monolith-factory');

type NestApplicationContextOptions = Parameters<typeof NestFactory.createApplicationContext>[1];

export class ClickUpNestMonolithFactory {
    static async create(options?: CreateNestMonolithApplicationContextOptions) {
        const appModule = options?.appModuleFn ? await options.appModuleFn() : undefined;
        if (!appModule) {
            logger.debug({ msg: `No optional app module provided` });
        } else {
            logger.debug({ msg: `optional app module provided ${appModule?.name}` });
        }
        try {
            const nestApplicationOptions: NestApplicationContextOptions = {
                bufferLogs: true,
            };
            const appContext = await NestFactory.createApplicationContext(
                ClickUpMonolithModule.forRoot({ appModule }),
                nestApplicationOptions
            );

            const nestDefaultLogger = getLogger('nest-monolith');

            appContext.useLogger(nestDefaultLogger);
            appContext.flushLogs();

            return appContext;
        } catch (err) {
            logger.error({
                msg: 'Failed to bootstrap the nest application context',
                err,
            });
            throw err;
        }
    }
}
