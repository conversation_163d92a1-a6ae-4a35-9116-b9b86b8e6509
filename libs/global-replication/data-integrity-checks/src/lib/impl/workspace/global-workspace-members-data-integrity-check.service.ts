import { Inject, Injectable } from '@nestjs/common';

import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';

import type { DataIntegrityCheckJobConfig } from '../../data-integrity-check-job.interface';
import { DataIntegrityCheckJobRunner } from '../data-integrity-check-job-runner';
import { DEFAULT_JOB_CONFIG } from '../defaults.config';
import { GlobalWorkspaceMembersDataIntegrityCheckJob } from './global-workspace-members-data-integrity-check-job.service';

@Injectable()
export class GlobalWorkspaceMembersDataIntegrityCheck {
    private runner: DataIntegrityCheckJobRunner<unknown>;

    constructor(
        job: GlobalWorkspaceMembersDataIntegrityCheckJob,
        logger: FormatLogger,
        metricsService: MetricsService,
        @Inject(DEFAULT_JOB_CONFIG) conf: DataIntegrityCheckJobConfig
    ) {
        this.runner = new DataIntegrityCheckJobRunner(job, conf, logger, metricsService);
    }

    start(): void {
        this.runner.start();
    }

    setConfig(conf: DataIntegrityCheckJobConfig): void {
        this.runner.setConfig(conf);
    }
}
