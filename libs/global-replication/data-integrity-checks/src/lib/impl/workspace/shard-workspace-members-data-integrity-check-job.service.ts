import { Inject, Injectable } from '@nestjs/common';

import { InjectableRedisConnectionClient } from '@clickup/redis';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { FormatLogger } from '@clickup/utils/logging';
import { MetricsService } from '@clickup/utils/metrics';

import type { DataIntegrityCheckJobConfig } from '../../data-integrity-check-job.interface';
import { DEFAULT_JOB_CONFIG } from '../defaults.config';
import {
    Batch,
    WorkspaceMembersDataIntegrityCheckJob,
    WorkspaceMembershipKey,
} from './workspace-members-data-integrity-check.job';
import {
    WorkspaceMembersDataIntegrityCheckRepository,
    WorkspaceMembershipDataRecord,
} from './workspace-members-data-integrity-check.repository';

@Injectable()
export class ShardWorkspaceMembersDataIntegrityCheckJob extends WorkspaceMembersDataIntegrityCheckJob {
    constructor(
        @Inject(DEFAULT_JOB_CONFIG) conf: DataIntegrityCheckJobConfig,
        repository: WorkspaceMembersDataIntegrityCheckRepository,
        logger: FormatLogger,
        metricsService: MetricsService,
        featureFlagService: FeatureFlagService,
        redis: InjectableRedisConnectionClient
    ) {
        super(conf, repository, logger, metricsService, featureFlagService, redis, false);
    }

    getName() {
        return 'ShardWorkspaceMembersDataIntegrityCheckJob';
    }

    async fetchNextBatch(): Promise<Array<WorkspaceMembershipKey>> {
        // If specific primary keys are provided, return them directly
        if (this.conf.specificPrimaryKeys && this.conf.specificPrimaryKeys.length > 0) {
            this.logger.info({
                message: `Using specific primary keys for data integrity check: ${this.getName()}`,
                jobName: this.getName(),
                specificPrimaryKeys: this.conf.specificPrimaryKeys,
            });

            // Convert string primary keys to WorkspaceMembershipKey objects
            // Assuming format is "team_id:userid"
            const validKeys: WorkspaceMembershipKey[] = [];

            for (const key of this.conf.specificPrimaryKeys) {
                const parts = key.split(':');
                if (parts.length === 2 && parts[0] && parts[1]) {
                    validKeys.push({ team_id: parts[0], userid: parts[1] });
                } else {
                    this.logger.warn({
                        message: `Invalid primary key format for workspace member: ${key}. Expected format is "team_id:userid"`,
                        jobName: this.getName(),
                    });
                }
            }

            return validKeys;
        }

        return this.repository.fetchNextBatch(this.state.lastId, this.conf.batchSize, false);
    }

    async fetchReferenceRecords(batch: Batch): Promise<Map<string, WorkspaceMembershipDataRecord>> {
        return this.repository.fetchWorkspaceMembersFromLocal(batch);
    }

    async fetchCandidateRecords(batch: Batch): Promise<Map<string, WorkspaceMembershipDataRecord>> {
        return this.repository.fetchWorkspaceMembersFromGlobal(batch);
    }
}
