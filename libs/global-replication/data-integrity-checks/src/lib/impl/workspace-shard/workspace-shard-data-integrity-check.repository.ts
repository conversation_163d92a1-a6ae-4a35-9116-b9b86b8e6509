import { Inject, Injectable } from '@nestjs/common';
import { Cluster, Redis } from 'ioredis';

import { RedisMultiClientService } from '@clickup/data-platform/redis-multi-client';
import {
    buildShardCacheKeyFromWorkspaceId,
    parseCachedValue,
    WorkspaceDataLocation,
} from '@clickup/data-platform/workspace-data-location';
import { ConfigService } from '@clickup/utils/config';
import { DBClient } from '@clickup/utils/db';
import { WorkspaceServiceStatus } from '@clickup-legacy/libs/common/types';
import { GLOBAL_DATA_CLIENT } from '@clickup-legacy/utils/GlobalDataClient';

import {
    fetchNextBatchQuery,
    fetchWorkspaceShardCountsQuery,
    fetchWorkspaceShardQuery,
} from './workspace-shard-data-integrity-check.queries';

export type WorkspaceShardRow = { workspace_id: string; shard_id: string; microshard_id: number } & Record<
    string,
    unknown
>;

export type CachedWorkspaceShards = {
    primary: Record<string, WorkspaceDataLocation | null>;
    secondary: Record<string, WorkspaceDataLocation | null>;
    interval: Record<string, WorkspaceDataLocation | null>;
};

@Injectable()
export class WorkspaceShardDataIntegrityCheckRepository {
    constructor(
        private dbClient: DBClient,
        @Inject(GLOBAL_DATA_CLIENT) private globalDataClient: DBClient,
        private redisMultiClientService: RedisMultiClientService,
        private configService: ConfigService
    ) {}

    async fetchWorkspacesFromGlobal(ids: Array<string>): Promise<Array<WorkspaceShardRow>> {
        const result = await this.globalDataClient.readAsync<WorkspaceShardRow>(fetchWorkspaceShardQuery(true), [
            ids,
            WorkspaceServiceStatus.Archived,
        ]);
        return result.rows;
    }

    async fetchWorkspacesFromLocal(ids: Array<string>): Promise<Array<WorkspaceShardRow>> {
        const result = await this.dbClient.readAsync<WorkspaceShardRow>(fetchWorkspaceShardQuery(false), [
            ids,
            WorkspaceServiceStatus.Archived,
        ]);
        return result.rows;
    }

    async fetchCountsFromGlobal(): Promise<number> {
        const currentShardId = this.configService.get<string>('sharding.shard_id');
        const result = await this.globalDataClient.readAsync<{ count: number }>(fetchWorkspaceShardCountsQuery(), [
            currentShardId,
        ]);
        return result.rows[0]?.count ?? 0;
    }

    async fetchCountsFromLocal(): Promise<number> {
        const currentShardId = this.configService.get<string>('sharding.shard_id');
        const result = await this.dbClient.readAsync<{ count: number }>(fetchWorkspaceShardCountsQuery(), [
            currentShardId,
        ]);
        return result.rows[0]?.count ?? 0;
    }

    async fetchNextBatch(lastId: string, currentShardId: string, batchSize: number): Promise<Array<string>> {
        const result = await this.globalDataClient.readAsync<{ workspace_id: string }>(fetchNextBatchQuery(), [
            lastId,
            currentShardId,
            batchSize,
        ]);

        if (result.rows.length === 0) {
            return [];
        }

        return result.rows.map(row => row.workspace_id);
    }

    async fetchWorkspacesFromRedis(ids: Array<string>): Promise<CachedWorkspaceShards> {
        const primary = await this.bulkGetCachedShardFromWorkspaceId(ids, this.redisMultiClientService.primaryClient);
        const secondary = await this.bulkGetCachedShardFromWorkspaceId(
            ids,
            this.redisMultiClientService.secondaryClient
        );
        const interval = await this.bulkGetCachedShardFromWorkspaceId(ids, this.redisMultiClientService.intervalClient);

        return {
            primary,
            secondary,
            interval,
        };
    }

    private async bulkGetCachedShardFromWorkspaceId(
        workspaceIds: string[],
        redis: Redis | Cluster
    ): Promise<Record<string, WorkspaceDataLocation | null>> {
        const keys = workspaceIds.map(workspaceId => buildShardCacheKeyFromWorkspaceId(workspaceId));

        const cachedValues = (await redis.mget(keys)) as Array<string | null>;

        return cachedValues.reduce(
            (collection, value, index) => ({
                ...collection,
                [workspaceIds[index] as string]: parseCachedValue(value) ?? null,
            }),
            {}
        );
    }
}
