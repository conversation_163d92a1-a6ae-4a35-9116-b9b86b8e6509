import { Inject, Injectable } from '@nestjs/common';
import type { ObjectVersionChangeEvent } from '@time-loop/ovm-object-version';

import { versionChangeEventToVersionVector } from '@clickup/global-replication/common';
import { FormatLogger } from '@clickup/utils/logging';
import { type ReplicatedWorkspace, WorkspaceReplicationService } from '@clickup/workspace';

import {
    FailedToFetchWorkspacesException,
    FailedToUpsertWorkspacesException,
    WorkspaceDataReplicatorErrors,
} from './exceptions';

@Injectable()
export class WorkspaceEventsProcessor {
    constructor(
        @Inject(WorkspaceReplicationService)
        private readonly workspaceService: WorkspaceReplicationService,
        @Inject(FormatLogger)
        private readonly logger: FormatLogger
    ) {}

    public async handleEvents(workspaceEvents: ObjectVersionChangeEvent[]) {
        if (workspaceEvents.length) {
            const maybeWorkspaces = await this.getWorkspaces(workspaceEvents);

            if (!maybeWorkspaces.length || maybeWorkspaces.some(u => u === null || u === undefined)) {
                this.logger.error({
                    message: 'Could not find workspaces for replication',
                    ECODE: WorkspaceDataReplicatorErrors.WorkspacesNotFound,
                    workspaceEvents,
                    maybeWorkspaces,
                });
            }
            // we're now certain that all workspaces are not null or undefined
            const workspaces = maybeWorkspaces.filter(x => x) as ReplicatedWorkspace[];

            await this.upsertWorkspaces(workspaces);
        }
    }

    private async getWorkspaces(events: ObjectVersionChangeEvent[]): Promise<Array<ReplicatedWorkspace | null>> {
        try {
            return await this.workspaceService.getWorkspacesByVectors(events.map(versionChangeEventToVersionVector));
        } catch (err) {
            this.logger.error({
                message: 'Failed to fetch all workspaces for replication',
                ECODE: WorkspaceDataReplicatorErrors.FailedToFetchWorkspaces,
                events,
                err,
            });
            throw new FailedToFetchWorkspacesException();
        }
    }

    private async upsertWorkspaces(workspaces: ReplicatedWorkspace[]) {
        try {
            await this.workspaceService.upsertWorkspaces(workspaces);
        } catch (err) {
            this.logger.error({
                message: 'Failed to upsert workspaces',
                ECODE: WorkspaceDataReplicatorErrors.FailedToUpsertWorkspaces,
                workspaces: workspaces.map(workspace => workspace.id),
                err,
            });
            throw new FailedToUpsertWorkspacesException();
        }
    }
}
