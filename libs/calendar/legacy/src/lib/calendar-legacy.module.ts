import { Module } from '@nestjs/common';

import { UtilsConfigModule } from '@clickup/utils/config';
import { UtilsDbModule } from '@clickup/utils/db';

import { CalendarLegacyRepository } from './calendar-legacy.repository';
import { CalendarLegacyService } from './calendar-legacy.service';

@Module({
    controllers: [],
    imports: [UtilsConfigModule, UtilsDbModule],
    providers: [CalendarLegacyService, CalendarLegacyRepository],
    exports: [CalendarLegacyService],
})
export class CalendarLegacyModule {}
