export function validateAndSanitizeTitle(title: string) {
    let result = title;

    // Replace multiple slashes with single slash.
    result = (result || '').replace(/\/\/+/g, '/');

    result = result.replace('advertisement', 'ad');
    result = result.replace('Advertisement', 'Ad');
    const lowerTitle = result.toLowerCase();
    if (lowerTitle.startsWith('ads_') || lowerTitle.startsWith('ad_')) {
        result = `_${result}`;
    }

    return result;
}
