import { DynamicModule, Module } from '@nestjs/common';
import fs from 'fs';
import path from 'path';

import { AttachmentModule } from '@clickup/attachment/core';
import { AttachmentTokenModule } from '@clickup/attachment/token';
import { CloudFrontSigningService } from '@clickup/aws/cloud-front';
import { ENFORCE_ASYNC_CONTEXT, UtilsAuthorizationModule } from '@clickup/utils/authorization';
import { ConfigService, UtilsConfigModule } from '@clickup/utils/config';
import { UtilsFeatureFlagModule } from '@clickup/utils/feature-flag';
import { UtilsLoggingModule } from '@clickup/utils/logging';

import { AttachmentUrlService } from './attachment-url.service';

@Module({
    imports: [AttachmentTokenModule, UtilsConfigModule, UtilsFeatureFlagModule, UtilsLoggingModule],
    providers: [
        {
            provide: CloudFrontSigningService,
            useFactory: (configService: ConfigService) => {
                const cloudfrontAccessKey = configService.get<string>('aws.cloudfront_access_key');
                const cloudfrontPrivateKey = configService.getEnvChecks().isTest
                    ? fs.readFileSync(path.resolve(process.cwd(), 'certs/cloudfront_private_key.pem')).toString('ascii')
                    : Buffer.from(configService.get<string>('aws.cloudfront_pem'), 'base64').toString('ascii');
                const defaultDurationInSeconds =
                    configService.get<number>('attachments.default_attachment_expire') / 1000;
                return new CloudFrontSigningService(
                    cloudfrontAccessKey,
                    cloudfrontPrivateKey,
                    defaultDurationInSeconds
                );
            },
            inject: [ConfigService],
        },
        AttachmentUrlService,
    ],
    exports: [AttachmentUrlService],
})
export class AttachmentUrlModule {
    static forMonolith(): DynamicModule {
        return {
            module: AttachmentUrlModule,
            imports: [UtilsAuthorizationModule.forMonolith(), AttachmentModule.forMonolith()],
            controllers: [],
            providers: [
                {
                    provide: ENFORCE_ASYNC_CONTEXT,
                    useValue: false,
                },
            ],
        };
    }

    static forRoot(): DynamicModule {
        return {
            module: AttachmentUrlModule,
            imports: [UtilsAuthorizationModule, AttachmentModule.forRoot()],
            controllers: [],
            providers: [],
        };
    }
}
