# Data Retention Temporal Workflows

A Temporal-based workflow implementation for executing data retention policies across ClickUp features.

## 📋 Table of Contents

-   [Overview](#overview)
-   [Architecture](#architecture)
-   [Module Structure](#module-structure)
-   [Component Relationships](#component-relationships)
-   [Workflow Implementation](#workflow-implementation)
-   [Dynamic Workflow Creation](#dynamic-workflow-creation)
-   [Integration Guide](#integration-guide)
-   [Usage Examples](#usage-examples)
-   [Best Practices](#best-practices)

## 🔍 Overview

The Data Retention Temporal Workflows module provides a robust, scalable implementation of data retention policies using Temporal workflows. It leverages the core data retention system to discover and process resources according to configured retention rules.

This module serves as the execution layer for the data retention system, providing:

-   Feature-specific workflow implementations
-   Scalable, fault-tolerant execution of retention rules
-   Activity-based processing of retention tasks
-   Automatic retry and error handling

## 🏗️ Architecture

The Data Retention Temporal Workflows module follows a workflow-based architecture designed for reliability and scalability:

```mermaid
sequenceDiagram
    participant Client
    participant ParentWorkflow as Parent Workflow
    participant ChildWorkflow as Child Workflow
    participant Activities
    participant DataRetentionProcessor

    Client->>ParentWorkflow: start(feature)

    ParentWorkflow->>Activities: getWorkspacesWithRules(feature)
    Activities-->>ParentWorkflow: workspaceIds[]

    loop For each workspaceId
        ParentWorkflow->>ChildWorkflow: executeChild(workspaceId, feature)

        ChildWorkflow->>Activities: processRules(workspaceId, feature)
        Activities->>DataRetentionProcessor: processRules(workspaceId, feature)
        DataRetentionProcessor-->>Activities: rulesWithExclusions[]
        Activities-->>ChildWorkflow: rulesWithExclusions[]

        loop For each rule
            ChildWorkflow->>Activities: discoverResources(workspaceId, rule, exclusions, batchSize, cursor)
            Activities->>DataRetentionProcessor: discoverResources(...)
            DataRetentionProcessor-->>Activities: {resources[], hasMore, nextCursor}
            Activities-->>ChildWorkflow: {resources[], hasMore, nextCursor}

            alt resources.length > 0
                ChildWorkflow->>Activities: executeAction(workspaceId, rule, resources)
                Activities->>DataRetentionProcessor: executeAction(...)
                DataRetentionProcessor-->>Activities: actionResult
                Activities-->>ChildWorkflow: actionResult
            end
        end
    end

    ChildWorkflow-->>ParentWorkflow: Complete
    ParentWorkflow-->>Client: Complete
```

### Key Components

1. **DataRetentionWorkflowsModule**: Main module that registers workflows and activities
2. **ActivitiesService**: Implements activities that interact with the data retention system
3. **DataRetentionWorkflowsRegistry**: Manages feature-specific workflow implementations
4. **Workflows**:
    - **Parent Workflow**: Gets workspaces with rules and spawns child workflows for each workspace
    - **Child Workflow**: Discovers and processes resources for a specific workspace and feature

## 📦 Module Structure

### DataRetentionWorkflowsModule

The main entry point that registers workflows and activities:

```typescript
@Module({})
export class DataRetentionWorkflowsModule {
    static forRoot(): DynamicModule {
        const featureWorkflowClasses = Object.values(DataRetentionWorkflowsRegistry.getFeatureWorkflows());

        return {
            module: DataRetentionWorkflowsModule,
            imports: [UtilsConfigModule, UtilsLoggingModule, DataRetentionModule.forRoot()],
            providers: [ActivitiesService, ...featureWorkflowClasses, DataRetentionWorkflowsRegistry],
            exports: [ActivitiesService, ...featureWorkflowClasses, DataRetentionWorkflowsRegistry],
        };
    }

    static forFeature(feature: Feature): DynamicModule {
        const workflowClass = DataRetentionWorkflowsRegistry.getWorkflowClassForFeature(feature);

        if (!workflowClass) {
            throw new Error(`No workflow class found for feature: ${feature}`);
        }

        return {
            module: DataRetentionWorkflowsModule,
            imports: [UtilsConfigModule, UtilsLoggingModule, DataRetentionModule.forFeature([feature])],
            providers: [ActivitiesService, workflowClass, DataRetentionWorkflowsRegistry],
            exports: [ActivitiesService, workflowClass, DataRetentionWorkflowsRegistry],
        };
    }
}
```

### DataRetentionWorkflowsRegistry

Registry that manages feature-specific workflow implementations:

```typescript
@Injectable()
export class DataRetentionWorkflowsRegistry {
    static getFeatureWorkflows(): FeatureWorkflowMap {
        return FEATURE_WORKFLOWS;
    }

    static getWorkflowClassForFeature(feature: Feature): IFeatureWorkflowClass {
        return FEATURE_WORKFLOWS[feature];
    }

    static getTaskQueueForFeature(feature: Feature): string {
        return getTaskQueueForFeature(feature);
    }

    static getAllTaskQueues(): string[] {
        return Object.values(Feature).map(getTaskQueueForFeature);
    }
}
```

### ActivitiesService

Service that implements activities for interacting with the data retention system:

```typescript
@Injectable()
export class ActivitiesService implements IActivities {
    constructor(
        private readonly logger: FormatLogger,
        private readonly processorService: DataRetentionProcessorService
    ) {}

    async getWorkspacesWithRules(feature: Feature): Promise<number[]> {
        // Implementation details...
    }

    async processRules(workspaceId: number, feature: Feature): Promise<RetentionRuleWithExclusions[]> {
        // Implementation details...
    }

    async discoverResources(
        workspaceId: number,
        rule: RetentionRuleEntity,
        exclusions: ExclusionMap,
        batchSize: number,
        cursor?: string
    ): Promise<DiscoveryResult> {
        // Implementation details...
    }

    async executeAction(workspaceId: number, rule: RetentionRuleEntity, resources: unknown[]): Promise<ActionResult> {
        // Implementation details...
    }
}
```

## 🔄 Component Relationships

The following diagram shows how the components in the data retention system work together:

```
+----------------------------------+
|        Application Module        |
+----------------------------------+
              |
              | imports
              v
+----------------------------------+
|   DataRetentionWorkflowsModule   |
+----------------------------------+
        |             |
        | provides    | registers
        v             v
+----------------+  +-------------------------+
| ActivitiesService | DataRetentionWorkflowsRegistry |
+----------------+  +-------------------------+
        |                      |
        | uses                 | creates
        v                      v
+----------------------------------+  +----------------------------------+
| DataRetentionProcessorService   |  |    Feature Workflow Classes      |
+----------------------------------+  +----------------------------------+
        |                                        |
        | uses                                   | register with
        v                                        v
+----------------------------------+  +----------------------------------+
|      Provider Registry           |  |       Temporal Service           |
+----------------------------------+  +----------------------------------+
        |                                        |
        | manages                                | executes
        v                                        v
+----------------------------------+  +----------------------------------+
|       Feature Providers          |  |     Workflow Implementations     |
| (Mappers, Discoverers, Actions)  |  |    (Parent and Child Workflows)  |
+----------------------------------+  +----------------------------------+
```

This architecture allows feature teams to focus on implementing the necessary providers in the data-retention library, while the workflow infrastructure is automatically created and managed by the system.

## ⚙️ Workflow Implementation

The module implements two types of workflows:

### Parent Workflow

The parent workflow (`retentionParentWorkflow`) is responsible for:

-   Getting workspaces with rules for a specific feature using the `getWorkspacesWithRules` activity
-   Spawning child workflows for each workspace
-   Monitoring the progress of child workflows

```typescript
export async function retentionParentWorkflow(args: { feature: Feature }) {
    log.info(`Starting retention parent workflow for feature ${args.feature}`);

    // Process rules to get workspaces with rules
    log.info(`Processing rules to find workspaces with rules for feature ${args.feature}`);
    const workspaceIds = await activities.getWorkspacesWithRules(args.feature);

    log.info(
        `Found ${workspaceIds.length} workspaces with rules for feature ${args.feature}: ${workspaceIds.join(', ')}`
    );

    if (workspaceIds.length === 0) {
        log.info(`No workspaces found with rules for feature ${args.feature}, workflow completed`);
        return;
    }

    const taskQueue = getTaskQueueForFeature(args.feature);
    log.info(`Using task queue ${taskQueue} for feature ${args.feature}`);

    log.info(`Starting child workflows for ${workspaceIds.length} workspaces`);

    await Promise.all(
        workspaceIds.map(workspaceId => {
            const workflowId = `${WorkflowType.DataRetentionChild}-${args.feature}-ws-${workspaceId}-${Date.now()}`;
            log.info(`Starting child workflow for workspace ${workspaceId}, workflowId: ${workflowId}`);

            return executeChild(retentionChildWorkflow, {
                args: [{ workspaceId, feature: args.feature }],
                workflowId,
                taskQueue,
            });
        })
    );

    log.info(`All child workflows completed for feature ${args.feature}`);
}
```

### Child Workflow

The child workflow (`retentionChildWorkflow`) is responsible for:

-   Processing rules for a specific workspace and feature using the `processRules` activity
-   Iterating through each rule and its exclusions
-   Discovering resources that match each rule using the `discoverResources` activity
-   Executing actions on discovered resources using the `executeAction` activity
-   Handling pagination and batching of resources

```typescript
export async function retentionChildWorkflow(args: { workspaceId: number; feature: Feature }) {
    log.info(`Starting retention child workflow for workspace ${args.workspaceId}, feature ${args.feature}`);

    const taskQueue = getTaskQueueForFeature(args.feature);
    const activities = proxyActivities<IActivities>({
        startToCloseTimeout: '30 minutes',
        taskQueue,
    });

    // Process rules and get rules with exclusions
    log.info(`Processing rules for workspace ${args.workspaceId}, feature ${args.feature}`);
    const rulesWithExclusions = await activities.processRules(args.workspaceId, args.feature);

    if (rulesWithExclusions.length === 0) {
        log.info(`No rules found for workspace ${args.workspaceId}, feature ${args.feature}`);
        return;
    }

    log.info(`Found ${rulesWithExclusions.length} rules for workspace ${args.workspaceId}, feature ${args.feature}`);

    for (const { rule, exclusions } of rulesWithExclusions) {
        log.info(`Processing rule ${rule.ruleId} for workspace ${args.workspaceId}, feature ${args.feature}`);

        let hasMore = true;
        let nextCursor: string | undefined;
        const batchSize = 1000;
        let totalProcessed = 0;
        let batchCount = 0;

        while (hasMore) {
            batchCount++;
            log.info(
                `Discovering resources for rule ${rule.ruleId}, batch ${batchCount}, cursor: ${nextCursor || 'initial'}`
            );

            const batch: DiscoveryResult = await activities.discoverResources(
                args.workspaceId,
                rule,
                exclusions,
                batchSize,
                nextCursor
            );

            log.info(
                `Discovered ${batch.resources.length} resources for rule ${rule.ruleId}, batch ${batchCount}, hasMore: ${batch.hasMore}`
            );

            if (batch.resources.length > 0) {
                log.info(
                    `Executing action for rule ${rule.ruleId}, batch ${batchCount}, resources: ${batch.resources.length}`
                );

                const result = await activities.executeAction(args.workspaceId, rule, batch.resources);

                totalProcessed += result.successCount;

                log.info(
                    `Action executed for rule ${rule.ruleId}, batch ${batchCount}, success: ${result.successCount}, failure: ${result.failureCount}`
                );

                if (result.failureCount > 0 && result.errorMessage) {
                    log.warn(`Errors during action execution for rule ${rule.ruleId}: ${result.errorMessage}`);
                }
            }

            hasMore = batch.hasMore;
            nextCursor = batch.cursor;

            if (!hasMore) {
                log.info(`Completed processing rule ${rule.ruleId}, total processed: ${totalProcessed}`);
                break;
            }
        }
    }

    log.info(`Completed retention child workflow for workspace ${args.workspaceId}, feature ${args.feature}`);
}
```

## 🔄 Dynamic Workflow Creation

A key feature of this module is the automatic creation of workflow classes for each feature defined in the `Feature` enum. This means that feature teams don't need to write any workflow code - they just need to add their feature to the enum and implement the necessary providers.

### How It Works

1. The `DataRetentionWorkflowsRegistry` automatically creates workflow classes for each feature in the `Feature` enum:

```typescript
function createFeatureWorkflowClass(feature: Feature): IFeatureWorkflowClass {
    const taskQueue = getTaskQueueForFeature(feature);
    const namespace = getNamespaceForFeature(feature);

    @Injectable()
    @TemporalWorkflows({
        namespace,
        taskQueue,
        workflowTypes: {
            [WorkflowType.DataRetentionParent]: {
                migratable: true,
            },
            [WorkflowType.DataRetentionChild]: {
                migratable: true,
            },
        },
    })
    class FeatureDataRetentionWorkflows implements IFeatureWorkflowClass {
        static readonly feature = feature;
        static readonly taskQueue = taskQueue;
    }

    Object.defineProperty(FeatureDataRetentionWorkflows, 'name', {
        value: `${feature}DataRetentionWorkflows`,
    });

    return FeatureDataRetentionWorkflows;
}

const FEATURE_WORKFLOWS: FeatureWorkflowMap = Object.values(Feature).reduce((acc, feature) => {
    acc[feature] = createFeatureWorkflowClass(feature);
    return acc;
}, {} as FeatureWorkflowMap);
```

2. Each feature gets:

    - A dedicated workflow class with a unique name
    - A feature-specific task queue
    - Registration of both parent and child workflow types

3. Benefits for feature teams:
    - No need to write workflow code
    - Automatic integration with the data retention system
    - Feature-specific isolation for better scalability and monitoring
    - Consistent implementation across all features

This dynamic workflow creation approach significantly reduces the effort required to add data retention capabilities to new features, allowing feature teams to focus on implementing the necessary providers (condition mappers, resource discoverers, action providers, and precedence providers) rather than worrying about workflow implementation details.

## 🔧 Integration Guide

### Adding a New Feature

To add a new feature to the data retention workflows, follow these steps:

1. **Add the feature to the Feature enum**

    First, ensure the feature is added to the Feature enum in the data-retention module:

    ```typescript
    export enum Feature {
        CHAT = 'chat',
        YOUR_FEATURE = 'your_feature', // Add your feature here
    }
    ```

2. **Implement the required providers in the data-retention library**

    For your feature to work with the data retention system, you need to implement several providers in the data-retention library:

    #### a. Condition Mapper

    Maps retention rule conditions to SQL clauses:

    ```typescript
    @Injectable()
    @ConditionMapper(Feature.YOUR_FEATURE, EntityType.YOUR_ENTITY)
    export class YourEntityConditionMapper implements IQueryClauseMapper {
        constructor(private readonly logger: FormatLogger) {}

        getFeature(): Feature {
            return Feature.YOUR_FEATURE;
        }

        getEntityType(): EntityType {
            return EntityType.YOUR_ENTITY;
        }

        mapConditions(conditions: RetentionRuleCondition[]): string {
            // Implement condition mapping logic
            // Return a SQL WHERE clause
            return '';
        }

        buildExclusionClause(exclusions: ExclusionMap): string {
            // Implement exclusion clause logic
            return '';
        }

        buildScopeClause(scopeType: string, scopeId: string): string {
            // Implement scope clause logic
            return '';
        }
    }
    ```

    #### b. Resource Discoverer

    Discovers resources that match retention rules:

    ```typescript
    @Injectable()
    @ResourceDiscoverer(Feature.YOUR_FEATURE, EntityType.YOUR_ENTITY)
    export class YourEntityResourceDiscoverer implements IResourceDiscoverer {
        constructor(private readonly logger: FormatLogger, private readonly dbService: DbService) {}

        getFeature(): Feature {
            return Feature.YOUR_FEATURE;
        }

        getEntityType(): EntityType {
            return EntityType.YOUR_ENTITY;
        }

        async discoverResources(
            rule: RetentionRuleEntity,
            conditionClause: string,
            exclusionClause: string,
            scopeClause: string,
            batchSize: number,
            cursor?: string
        ): Promise<DiscoveryResult> {
            // Implement resource discovery logic
            // Return discovered resources with pagination info
            return {
                resources: [],
                hasMore: false,
                cursor: undefined,
            };
        }
    }
    ```

    #### c. Action Provider

    Executes actions on discovered resources:

    ```typescript
    @Injectable()
    @ActionProvider(Feature.YOUR_FEATURE, EntityType.YOUR_ENTITY, ActionType.DELETE)
    export class YourEntityDeleteProvider implements IActionProvider {
        constructor(private readonly logger: FormatLogger, private readonly dbService: DbService) {}

        getFeature(): Feature {
            return Feature.YOUR_FEATURE;
        }

        getEntityType(): EntityType {
            return EntityType.YOUR_ENTITY;
        }

        getActionType(): ActionType {
            return ActionType.DELETE;
        }

        async executeAction(rule: RetentionRuleEntity, resources: unknown[]): Promise<ActionResult> {
            // Implement action execution logic
            // Return results of the action
            return {
                successCount: 0,
                failureCount: 0,
                errorMessage: undefined,
            };
        }
    }
    ```

    #### d. Precedence Provider

    Determines rule precedence and builds exclusions:

    ```typescript
    @Injectable()
    @PrecedenceProvider(Feature.YOUR_FEATURE)
    export class YourFeatureRetentionRulePrecedenceProvider implements IPrecedenceProvider {
        constructor(private readonly logger: FormatLogger) {}

        getFeature(): Feature {
            return Feature.YOUR_FEATURE;
        }

        sortRulesByPrecedence(rules: RetentionRuleEntity[]): RetentionRuleEntity[] {
            // Implement rule sorting logic
            // Return rules sorted by precedence
            return rules;
        }

        buildExclusionMap(sortedRules: RetentionRuleEntity[]): Record<string, ExclusionMap> {
            // Implement exclusion map building logic
            // Return exclusion map for each rule
            return {};
        }
    }
    ```

3. **Import the DataRetentionWorkflowsModule**

    Import the module in your application:

    ```typescript
    import { Module } from '@nestjs/common';
    import { DataRetentionWorkflowsModule } from '@clickup/temporal/workflows/data-platform/data-retention';
    import { Feature } from '@clickup/data-platform/data-retention';

    @Module({
        imports: [DataRetentionWorkflowsModule.forFeature(Feature.YOUR_FEATURE)],
    })
    export class AppModule {}
    ```

4. **Start the Temporal worker**

    Start a Temporal worker for your feature:

    ```typescript
    import { NestFactory } from '@nestjs/core';
    import { AppModule } from './app.module';
    import { DataRetentionWorkflowsRegistry } from '@clickup/temporal/workflows/data-platform/data-retention';
    import { Feature } from '@clickup/data-platform/data-retention';
    import { TemporalWorkerService } from '@clickup/temporal/worker';

    async function bootstrap() {
        const app = await NestFactory.create(AppModule);
        const workerService = app.get(TemporalWorkerService);

        await workerService.startWorker({
            taskQueue: DataRetentionWorkflowsRegistry.getTaskQueueForFeature(Feature.YOUR_FEATURE),
        });

        await app.listen(3000);
    }
    bootstrap();
    ```

## 🚀 Usage Examples

### Starting a Workflow

```typescript
import { Injectable } from '@nestjs/common';
import { TemporalClient } from '@clickup/temporal/client';
import { Feature } from '@clickup/data-platform/data-retention';
import { WorkflowType } from '@clickup/temporal/shared-types';
import { DataRetentionWorkflowsRegistry } from '@clickup/temporal/workflows/data-platform/data-retention';

@Injectable()
export class DataRetentionService {
    constructor(private readonly temporalClient: TemporalClient) {}

    async startRetentionWorkflow(feature: Feature): Promise<string> {
        const taskQueue = DataRetentionWorkflowsRegistry.getTaskQueueForFeature(feature);
        const namespace = 'default'; // Or your specific namespace

        const workflowId = `data-retention-${feature}-${Date.now()}`;

        await this.temporalClient.startWorkflow({
            workflowType: WorkflowType.DataRetentionParent,
            workflowId,
            taskQueue,
            namespace,
            args: [{ feature }],
        });

        return workflowId;
    }
}
```

## 🏆 Best Practices

### Workflow Design

-   **Idempotency**: Ensure all activities are idempotent to handle retries gracefully
-   **Error Handling**: Implement proper error handling in activities to avoid workflow failures
-   **Logging**: Use detailed logging to track workflow progress and diagnose issues

### Performance Considerations

-   **Batch Size**: Configure appropriate batch sizes for resource discovery to balance memory usage and performance
-   **Concurrency**: Limit the number of concurrent child workflows to avoid overwhelming the system
-   **Timeouts**: Set appropriate timeouts for activities based on expected execution time

### Monitoring and Maintenance

-   **Workflow Visibility**: Use the Temporal UI to monitor workflow execution and diagnose issues
-   **Metrics**: Track key metrics such as workflow execution time, activity success rate, and resource processing rate
-   **Alerting**: Set up alerts for workflow failures and unexpected results

### Running Unit Tests

Run `nx test data-retention-temporal-workflows` to execute the unit tests via [Jest](https://jestjs.io).
