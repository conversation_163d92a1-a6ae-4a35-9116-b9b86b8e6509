import { condition, defineSignal, log, proxyActivities, setHandler, sleep } from '@temporalio/workflow';

import type { BillingTrialActivitiesService } from '../lib/trial-activities.service';

export const { resetCounterEntitlementLimit } = proxyActivities<BillingTrialActivitiesService>({
    startToCloseTimeout: '10s',
    retry: {
        initialInterval: '5s',
        backoffCoefficient: 2,
        maximumInterval: '60s',
        maximumAttempts: 20,
    },
});

type FinishEarlyArgs = {
    version?: '1.0';
    executePostProcessing?: boolean;
};

export const trialFinishEarly = defineSignal<[FinishEarlyArgs]>('trialFinishEarly');

// TODO: Migrate trial db manipulation to this workflow, only counter entitlement limit are handled here now
export async function billingFinishTrial(args: {
    trialEnd: number;
    workspaceId: string;
    version: '1.0';
}): Promise<{ message: string }> {
    if (args.version === '1.0') {
        const { trialEnd, workspaceId } = args;
        const nowTimestamp = Date.now();
        const target = new Date(trialEnd).getTime();
        const delay = target - nowTimestamp > 0 ? target - nowTimestamp : 0;

        let finishedEarly = false;
        let executePostProcessing = false;

        setHandler(trialFinishEarly, (finishEarlyArgs: FinishEarlyArgs) => {
            if (finishEarlyArgs.version !== '1.0') {
                throw new Error('Unsupported version');
            }

            finishedEarly = true;
            executePostProcessing = finishEarlyArgs.executePostProcessing ?? false;
        });

        await Promise.race([sleep(delay), condition(() => finishedEarly)]);

        if (!finishedEarly || executePostProcessing) {
            try {
                await resetCounterEntitlementLimit(workspaceId);
            } catch (err) {
                log.error('Failed to reset counter entitlement limit', {
                    workspaceId,
                    err,
                });
            }
        }

        return {
            message: 'Trial finished',
        };
    }

    throw new Error('Unsupported version');
}
