import { Test, TestingModule } from '@nestjs/testing';
import axios, { AxiosInstance } from 'axios';
import fs from 'fs';
import { Readable } from 'stream';

import { AttachmentService, AttachmentSource, AttachmentType } from '@clickup/attachment/core';
import { DailyCoService, DailyMeetingDto } from '@clickup/chat/daily-co';
import { RecordingsService } from '@clickup/clips/recording';
import { AttachmentsApi } from '@clickup/gateway-client/api/attachments';
import { CommentsApi, CommentsControllerCreateCommentEntityTypePath } from '@clickup/gateway-client/api/comments';
import { DocsApi, DocsCreateDocOptionsDtoVisibilityOneOf } from '@clickup/gateway-client/api/docs';
import { SdApiService } from '@clickup/sd/api';
import { MeetingNotes, MeetingNotesTopic, MeetingNotesTopicBadgeCategory } from '@clickup/syncup/domain';
import { SyncupParticipationService } from '@clickup/syncup/participation';
import { SyncupChannelType } from '@clickup/syncup/state-management';
import { WorkspaceInfoService } from '@clickup/syncup/workspace-info';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import { MONOLITH_HTTP_CLIENT } from '@clickup/utils/http-client';
import { FormatLogger } from '@clickup/utils/logging';

import { ActivitiesService } from './activities.service';
import { BuildSyncupNoteJobArgs } from './build-syncup-note.job-args';
import { convertMeetingNotesToQuillDoc } from './quill-doc/helper';

jest.mock('axios');
jest.mock('fs', () => ({
    ...jest.requireActual('fs'),
    writeFileSync: jest.fn(),
    statSync: jest.fn(),
    promises: {
        unlink: jest.fn().mockResolvedValueOnce(undefined),
        readFile: jest.fn(),
    },
}));

jest.mock('@clickup/utils/authentication', () => ({
    ...jest.requireActual('@clickup/utils/authentication'),
    getJwtToken: jest.fn().mockImplementation((userId: number) => `mock-jwt-token-${userId}`),
}));

describe('ActivitiesService', () => {
    let activitiesService: ActivitiesService;
    let dailyCoService: DailyCoService;
    let syncupParticipationService: SyncupParticipationService;
    let workspaceInfoService: WorkspaceInfoService;
    let attachmentService: AttachmentService;
    let attachmentsApi: AttachmentsApi;
    let docsApi: DocsApi;
    let commentsApi: CommentsApi;
    let sdApiService: SdApiService;
    let recordingsService: RecordingsService;
    let monolithClient: AxiosInstance;
    let logger: FormatLogger;
    let featureFlagService: FeatureFlagService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                ActivitiesService,
                {
                    provide: DailyCoService,
                    useValue: {
                        getTranscriptAccessLink: jest.fn(),
                        fetchDailyMeeting: jest.fn(),
                        fetchDailyMeetingParticipants: jest.fn(),
                    },
                },
                {
                    provide: SyncupParticipationService,
                    useValue: {
                        getTranscriptOwner: jest.fn(),
                        getSyncupRecordingId: jest.fn(),
                        getSyncupMeetingInfo: jest.fn(),
                        getSyncupChannelInfo: jest.fn(),
                    },
                },
                {
                    provide: WorkspaceInfoService,
                    useValue: {
                        getWorkspaceInfo: jest.fn(),
                    },
                },
                {
                    provide: AttachmentService,
                    useValue: {
                        createAttachment: jest.fn(),
                        deleteAttachments: jest.fn(),
                        getAttachments: jest.fn(),
                    },
                },
                {
                    provide: AttachmentsApi,
                    useValue: {
                        getWorkspaceObjectAttachments: jest.fn(),
                    },
                },
                {
                    provide: DocsApi,
                    useValue: {
                        createDoc: jest.fn(),
                        editDoc: jest.fn(),
                        createPage: jest.fn(),
                    },
                },
                {
                    provide: CommentsApi,
                    useValue: {
                        createComment: jest.fn(),
                    },
                },
                {
                    provide: SdApiService,
                    useValue: {
                        sdGenerateMeetingSummary: jest.fn(),
                    },
                },
                {
                    provide: RecordingsService,
                    useValue: {
                        getSyncupRecordingAttachmentId: jest.fn(),
                    },
                },
                {
                    provide: MONOLITH_HTTP_CLIENT,
                    useValue: {
                        post: jest.fn(),
                        put: jest.fn(),
                        delete: jest.fn(),
                    },
                },
                {
                    provide: FormatLogger,
                    useValue: {
                        error: jest.fn(),
                        info: jest.fn(),
                    },
                },
                {
                    provide: FeatureFlagService,
                    useValue: {
                        getObject: jest.fn(),
                    },
                },
            ],
        }).compile();

        activitiesService = module.get<ActivitiesService>(ActivitiesService);
        dailyCoService = module.get<DailyCoService>(DailyCoService);
        syncupParticipationService = module.get<SyncupParticipationService>(SyncupParticipationService);
        workspaceInfoService = module.get<WorkspaceInfoService>(WorkspaceInfoService);
        attachmentService = module.get<AttachmentService>(AttachmentService);
        attachmentsApi = module.get<AttachmentsApi>(AttachmentsApi);
        docsApi = module.get<DocsApi>(DocsApi);
        commentsApi = module.get<CommentsApi>(CommentsApi);
        sdApiService = module.get<SdApiService>(SdApiService);
        recordingsService = module.get<RecordingsService>(RecordingsService);
        monolithClient = module.get<AxiosInstance>(MONOLITH_HTTP_CLIENT);
        logger = module.get<FormatLogger>(FormatLogger);
        featureFlagService = module.get<FeatureFlagService>(FeatureFlagService);
    });

    describe('downloadTranscript', () => {
        it('should download transcript successfully', async () => {
            const transcriptId = 'transcript-123';
            const mockTranscriptContent = 'mock transcript content';
            const mockTranscriptAccessLink = 'https://example.com/transcript';

            (dailyCoService.getTranscriptAccessLink as jest.Mock).mockResolvedValue(mockTranscriptAccessLink);
            (axios as jest.MockedFunction<typeof axios>).mockResolvedValue({
                data: Readable.from([mockTranscriptContent]),
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {},
            });

            const result = await activitiesService.downloadTranscript(transcriptId);

            expect(dailyCoService.getTranscriptAccessLink).toHaveBeenCalledWith(transcriptId);
            expect(axios).toHaveBeenCalledWith({
                url: mockTranscriptAccessLink,
                method: 'GET',
                responseType: 'stream',
            });
            expect(result).toBe(mockTranscriptContent);
        });

        it('should handle download errors', async () => {
            const transcriptId = 'transcript-123';
            const error = new Error('Download failed');

            (dailyCoService.getTranscriptAccessLink as jest.Mock).mockResolvedValue('https://example.com/transcript');
            (axios as jest.MockedFunction<typeof axios>).mockRejectedValue(error);

            await expect(activitiesService.downloadTranscript(transcriptId)).rejects.toThrow('Download failed');
        });
    });

    describe('getTranscriptOwner', () => {
        it('should get transcript owner successfully', async () => {
            const transcriptId = 'transcript-123';
            const expectedOwnerId = 456;

            (syncupParticipationService.getTranscriptOwner as jest.Mock).mockResolvedValue(expectedOwnerId);

            const result = await activitiesService.getTranscriptOwner(transcriptId);

            expect(syncupParticipationService.getTranscriptOwner).toHaveBeenCalledWith(transcriptId);
            expect(result).toBe(expectedOwnerId);
        });
    });

    describe('getDailyMeetingParticipants', () => {
        it('should get daily meeting participants successfully', async () => {
            const mtgSessionId = 'meeting-123';
            const dailyParticipants = [
                { user_name: '123', id: 123, join_time: 123456789, duration: 60, participant_id: '123' },
                { user_name: '456', id: 456, join_time: 123456789, duration: 60, participant_id: '456' },
            ];

            (dailyCoService.fetchDailyMeetingParticipants as jest.Mock).mockResolvedValue(dailyParticipants);

            (workspaceInfoService.getWorkspaceInfo as jest.Mock).mockResolvedValue({
                members: [{ user: { id: 123, username: 'John Doe' } }, { user: { id: 456, username: 'Jane Smith' } }],
            });

            const result = await activitiesService.getDailyMeetingParticipants({
                workspaceId: 'workspace-123',
                ownerId: 456,
                mtgSessionId,
            });

            expect(dailyCoService.fetchDailyMeetingParticipants).toHaveBeenCalledWith(mtgSessionId);
            expect(result).toEqual([
                { id: 123, name: 'John Doe' },
                { id: 456, name: 'Jane Smith' },
            ]);
        });
    });

    describe('convertToAICompatibleTranscript', () => {
        it('should convert transcript to AI compatible format', async () => {
            const options: BuildSyncupNoteJobArgs = {
                workspaceId: 'workspace-123',
                transcriptId: 'transcript-123',
                mtgSessionId: 'meeting-123',
            };
            const rawTranscriptData = `WEBVTT
transcript:0
00:00:09.581 --> 00:00:12.601
<v>123:</v>Essence

transcript:1
00:00:16.588 --> 00:00:18.308
<v>123:</v>the step flies

transcript:2
00:00:21.320 --> 00:00:26.040
<v>456:</v>send information`;
            const ownerId = 456;

            const syncupParticipants = [
                { id: 123, name: 'John Doe' },
                { id: 456, name: 'Jane Smith' },
            ];

            const result = await activitiesService.convertToAICompatibleTranscript(
                options,
                rawTranscriptData,
                ownerId,
                syncupParticipants
            );

            expect(result).toEqual({
                transcriptFilePath: expect.stringContaining('meeting-transcript-transcript-123.txt'),
                transcriptContent: 'John Doe: Essence the step flies\nJane Smith: send information',
            });
            expect(fs.writeFileSync).toHaveBeenCalledWith(
                '/tmp/meeting-transcript-transcript-123.txt',
                'John Doe: Essence the step flies\nJane Smith: send information'
            );
        });
    });

    describe('createTranscriptAttachment', () => {
        it('should create transcript attachment successfully', async () => {
            const options = {
                userId: 456,
                workspaceId: 'workspace-123',
                transcriptId: 'transcript-123',
                transcriptFilePath: '/tmp/transcript.txt',
            };
            const mockAttachmentId = 'attachment-123';

            (fs.statSync as jest.Mock).mockReturnValue({ size: 100 });
            (attachmentService.createAttachment as jest.Mock).mockResolvedValue(mockAttachmentId);

            const result = await activitiesService.createTranscriptAttachment(options);

            expect(attachmentService.createAttachment).toHaveBeenCalledWith({
                userId: options.userId,
                workspaceId: Number(options.workspaceId),
                type: AttachmentType.TEAM,
                permanent: true,
                template: false,
                parent: options.workspaceId,
                source: AttachmentSource.SYNCUP,
                hidden: false,
                title: `Syncup Transcript - ${options.transcriptId}.txt`,
                path: options.transcriptFilePath,
                mimeType: 'txt',
                size: 100,
            });
            expect(result).toBe(mockAttachmentId);
            expect(fs.promises.unlink).toHaveBeenCalledWith(options.transcriptFilePath);
        });

        it('should handle file deletion errors', async () => {
            const options = {
                userId: 456,
                workspaceId: 'workspace-123',
                transcriptId: 'transcript-123',
                transcriptFilePath: '/tmp/transcript.txt',
            };
            const mockAttachmentId = 'attachment-123';
            const deleteError = new Error('Delete failed');

            (fs.statSync as jest.Mock).mockReturnValue({ size: 100 });
            (attachmentService.createAttachment as jest.Mock).mockResolvedValue(mockAttachmentId);
            (fs.promises.unlink as jest.Mock).mockRejectedValue(deleteError);

            const result = await activitiesService.createTranscriptAttachment(options);

            expect(result).toBe(mockAttachmentId);
            expect(logger.error).toHaveBeenCalledWith({
                msg: `Failed to delete transcript for workspace ${options.workspaceId}`,
                err: deleteError,
            });
        });
    });

    describe('getAIMeetingSummary', () => {
        it('should get AI meeting summary successfully', async () => {
            const options = {
                workspaceId: 'workspace-123',
                attachmentId: 'attachment-123',
                ownerId: 456,
            };
            const mockMeetingNotes: MeetingNotes = {
                summary: 'Meeting summary',
                topics: [
                    {
                        topic: 'Topic 1',
                        bullet_points: ['Point 1'],
                        badge_category: MeetingNotesTopicBadgeCategory.Update,
                    },
                ],
                topicsHeader: 'Topics',
                name: 'Meeting Name',
            };

            (sdApiService.sdGenerateMeetingSummary as jest.Mock).mockResolvedValue(mockMeetingNotes);

            const result = await activitiesService.getAIMeetingSummary(options);

            expect(sdApiService.sdGenerateMeetingSummary).toHaveBeenCalledWith({
                workspaceId: options.workspaceId,
                attachmentId: options.attachmentId,
                jwtToken: `mock-jwt-token-${options.ownerId}`,
                timeoutMs: 600000,
            });
            expect(result).toEqual(mockMeetingNotes);
        });
    });

    describe('createMeetingDoc', () => {
        type docTestOptions = {
            name: string;
            meetingInfo: DailyMeetingDto | null;
            channelType: SyncupChannelType;
            channelName: string;
            useAIGeneratedTitle: boolean;
            expectedDocName: string;
        };

        it.each([
            {
                name: 'No meeting info',
                meetingInfo: null,
                channelType: SyncupChannelType.CHANNEL,
                channelName: 'Channel X',
                useAIGeneratedTitle: false,
                expectedDocName: 'Syncup Call',
            },
            {
                name: 'Channel meeting info',
                meetingInfo: {
                    id: 'meeting-123',
                    room: 'room-123',
                    start_time: 1718006400,
                    duration: 1000,
                    ongoing: false,
                    max_participants: 10,
                    participants: [],
                },
                channelType: SyncupChannelType.CHANNEL,
                channelName: 'Channel X',
                useAIGeneratedTitle: false,
                expectedDocName: 'Channel X',
            },
            {
                name: 'DM meeting info',
                meetingInfo: {
                    id: 'meeting-123',
                    room: 'room-123',
                    start_time: 1718006400,
                    duration: 1000,
                    ongoing: false,
                    max_participants: 10,
                    participants: [],
                },
                channelType: SyncupChannelType.DM,
                channelName: 'DM X',
                useAIGeneratedTitle: false,
                expectedDocName: 'DM X',
            },
            {
                name: 'Group DM meeting info',
                meetingInfo: {
                    id: 'meeting-123',
                    room: 'room-123',
                    start_time: 1718006400,
                    duration: 1000,
                    ongoing: false,
                    max_participants: 10,
                    participants: [],
                },
                channelType: SyncupChannelType.GROUP_DM,
                channelName: 'Group DM X',
                useAIGeneratedTitle: false,
                expectedDocName: 'SyncUp in private group',
            },
            {
                name: 'Use AI generated title',
                meetingInfo: {
                    id: 'meeting-123',
                    room: 'room-123',
                    start_time: 1718006400,
                    duration: 1000,
                    ongoing: false,
                    max_participants: 10,
                    participants: [],
                },
                channelType: SyncupChannelType.CHANNEL,
                channelName: 'Channel X',
                useAIGeneratedTitle: true,
                expectedDocName: 'AI Generated Meeting Name',
            },
        ])('should create meeting doc successfully for case: $name', async (testOptions: docTestOptions) => {
            const options = {
                workspaceId: 'workspace-123',
                meetingNotes: {
                    summary: 'meeting summary',
                    topics: [] as MeetingNotesTopic[],
                    topicsHeader: 'Topics',
                    name: 'AI Generated Meeting Name',
                },
                ownerId: 456,
                participants: [
                    { id: 123, name: 'John Doe' },
                    { id: 456, name: 'Jane Smith' },
                ],
                transcriptContent: 'John Doe: transcript content 1\nJane Smith: transcript content 2',
                recordingAttachmentId: 'attachment-123',
                mtgSessionId: 'meeting-123',
            };

            (featureFlagService.getObject as jest.Mock).mockResolvedValue({
                useAIGeneratedTitle: testOptions.useAIGeneratedTitle,
            });

            (docsApi.createDoc as jest.Mock).mockResolvedValue({
                status: 200,
                data: { id: 'doc-123' },
            });
            (docsApi.editDoc as jest.Mock).mockResolvedValue({
                status: 200,
                data: { id: 'doc-123' },
            });

            (docsApi.createPage as jest.Mock).mockResolvedValue({
                status: 200,
                data: { id: 'page-123' },
            });
            (attachmentsApi.getWorkspaceObjectAttachments as jest.Mock).mockResolvedValue({
                status: 200,
                data: {
                    attachments: [
                        {
                            id: 'attachment-123',
                            title: 'Recording v1',
                            url: 'https://example.com/recording',
                        },
                    ],
                },
            });
            (recordingsService.getSyncupRecordingAttachmentId as jest.Mock).mockResolvedValue('attachment-123');
            (dailyCoService.fetchDailyMeeting as jest.Mock).mockResolvedValue(testOptions.meetingInfo);
            (syncupParticipationService.getSyncupMeetingInfo as jest.Mock).mockResolvedValue({
                channel_id: 'channel-123',
            });
            (syncupParticipationService.getSyncupChannelInfo as jest.Mock).mockResolvedValue({
                channel_type: testOptions.channelType,
                channel_name: testOptions.channelName,
            });

            (monolithClient.post as jest.Mock).mockResolvedValue({
                status: 200,
                data: null,
            });

            (monolithClient.put as jest.Mock).mockResolvedValue({
                status: 200,
                data: null,
            });

            const result = await activitiesService.createMeetingDoc(options);

            expect(docsApi.createDoc).toHaveBeenCalledWith(
                {
                    workspaceId: Number(options.workspaceId),
                    docsCreateDocOptionsDto: {
                        parent: {
                            id: options.workspaceId,
                            type: 12,
                        },
                        visibility: DocsCreateDocOptionsDtoVisibilityOneOf.Private,
                        create_page: false,
                        name: expect.stringContaining(testOptions.expectedDocName),
                    },
                },
                { config: { headers: { Authorization: `Bearer mock-jwt-token-${options.ownerId}` } } }
            );

            expect(docsApi.editDoc).toHaveBeenCalledWith(
                {
                    workspaceId: Number(options.workspaceId),
                    docId: 'doc-123',
                    docsEditDocOptionsDto: {
                        type: 3,
                    },
                },
                { config: { headers: { Authorization: `Bearer mock-jwt-token-${options.ownerId}` } } }
            );

            expect(monolithClient.post).toHaveBeenCalledWith(
                `/v1/view/doc-123/members`,
                {
                    members: [123, 456],
                    permission_level: 5,
                },
                { headers: { Authorization: `Bearer mock-jwt-token-${options.ownerId}` } }
            );

            expect(monolithClient.put).toHaveBeenCalledWith(
                `/v1/view/doc-123/page/page-123`,
                {
                    followers: { add: [123, 456] },
                },
                { headers: { Authorization: `Bearer mock-jwt-token-${options.ownerId}` } }
            );

            expect(docsApi.createPage).toHaveBeenCalledTimes(2);

            expect(docsApi.createPage).toHaveBeenNthCalledWith(
                1,
                {
                    workspaceId: Number(options.workspaceId),
                    docId: 'doc-123',
                    docsCreatePageOptionsDto: {
                        name: expect.stringContaining(testOptions.expectedDocName),
                        content: JSON.stringify({
                            ops: [
                                { insert: 'Attendees: ', attributes: { bold: true } },
                                { insert: 'John Doe, Jane Smith' },
                                { insert: '\n' },
                                {
                                    attributes: { 'data-size': 'large' },
                                    insert: {
                                        frame: {
                                            id: 'https://example.com/recording',
                                            src: 'https://example.com/recording',
                                            url: 'https://example.com/recording',
                                            service: 'clickup_video',
                                            source: 8,
                                            title: 'Recording v1',
                                        },
                                    },
                                },
                                { insert: '\n' },
                                { insert: 'meeting summary\n' },
                            ],
                        }),
                    },
                },
                { config: { headers: { Authorization: `Bearer mock-jwt-token-${options.ownerId}` } } }
            );

            expect(docsApi.createPage).toHaveBeenNthCalledWith(
                2,
                {
                    workspaceId: Number(options.workspaceId),
                    docId: 'doc-123',
                    docsCreatePageOptionsDto: {
                        name: 'Meeting Transcript',
                        content: JSON.stringify({
                            ops: [
                                { insert: 'John Doe', attributes: { bold: true } },
                                { insert: ': transcript content 1\n' },
                                { insert: 'Jane Smith', attributes: { bold: true } },
                                { insert: ': transcript content 2\n' },
                            ],
                        }),
                    },
                },
                { config: { headers: { Authorization: `Bearer mock-jwt-token-${options.ownerId}` } } }
            );

            expect(result).toEqual({
                docPageId: 'page-123',
                title: expect.stringContaining(testOptions.expectedDocName),
            });
        });

        it('should handle doc creation failure and delete the doc', async () => {
            const options = {
                workspaceId: 'workspace-123',
                meetingNotes: {
                    summary: 'Meeting summary',
                    topics: [] as MeetingNotesTopic[],
                    topicsHeader: 'Topics',
                    name: 'Meeting Name',
                },
                ownerId: 456,
                participants: [
                    { id: 123, name: 'John Doe' },
                    { id: 456, name: 'Jane Smith' },
                ],
                transcriptContent: 'John Doe: transcript content 1\nJane Smith: transcript content 2',
                recordingAttachmentId: 'attachment-123',
                mtgSessionId: 'meeting-123',
            };

            (docsApi.createDoc as jest.Mock).mockResolvedValue({
                status: 200,
                data: { id: 'doc-123' },
            });

            (docsApi.editDoc as jest.Mock).mockResolvedValue({
                status: 400,
                data: null,
            });

            await expect(activitiesService.createMeetingDoc(options)).rejects.toThrow('Failed to edit meeting doc');

            expect(monolithClient.delete).toHaveBeenCalledWith(`/viz/v1/view/doc-123`, {
                headers: { Authorization: `Bearer mock-jwt-token-${options.ownerId}` },
            });
        });
    });

    describe('sendNoteCreatedComment', () => {
        it('should send note created comment successfully', async () => {
            const options = {
                userId: 456,
                title: 'Meeting Name',
                docPageId: 'page-123',
                workspaceId: 'workspace-123',
                participants: [
                    { id: 456, name: 'Jane Smith' },
                    { id: 123, name: 'John Doe' },
                ],
            };

            await activitiesService.sendNoteCreatedComment(options);

            expect(commentsApi.createComment).toHaveBeenCalledWith(
                {
                    workspaceId: Number(options.workspaceId),
                    entityType: CommentsControllerCreateCommentEntityTypePath.Doc,
                    entityId: options.docPageId,
                    body: {
                        comment: [
                            {
                                type: 'tag',
                                user: { id: 456 },
                                text: '@Jane Smith',
                                attributes: {},
                            },
                            {
                                text: ' ',
                                attributes: {},
                            },
                            {
                                type: 'tag',
                                user: { id: 123 },
                                text: '@John Doe',
                                attributes: {},
                            },
                            {
                                text: ' ',
                                attributes: {},
                            },
                            {
                                attributes: {},
                                text: 'Meeting note for',
                            },
                            {
                                attributes: { bold: true },
                                text: ' Meeting Name',
                            },
                            {
                                attributes: {},
                                text: ' has been created.',
                            },
                        ],
                    },
                },
                {
                    config: {
                        headers: {
                            Authorization: expect.stringContaining('Bearer'),
                        },
                    },
                }
            );
        });

        it('should send note created comment with watchers tag when participants are more than 5', async () => {
            const options = {
                userId: 456,
                title: 'Meeting Name',
                docPageId: 'page-123',
                workspaceId: 'workspace-123',
                participants: [
                    { id: 456, name: 'Jane Smith' },
                    { id: 123, name: 'John A' },
                    { id: 789, name: 'Jim Beam' },
                    { id: 101, name: 'John B' },
                    { id: 102, name: 'John C' },
                    { id: 103, name: 'John D' },
                    { id: 104, name: 'John E' },
                    { id: 105, name: 'John F' },
                    { id: 106, name: 'John G' },
                ],
            };

            await activitiesService.sendNoteCreatedComment(options);

            expect(commentsApi.createComment).toHaveBeenCalledWith(
                {
                    workspaceId: Number(options.workspaceId),
                    entityType: CommentsControllerCreateCommentEntityTypePath.Doc,
                    entityId: options.docPageId,
                    body: {
                        comment: [
                            {
                                type: 'followers_tag',
                                text: '@everyone',
                                attributes: {},
                            },
                            {
                                text: ' ',
                                attributes: {},
                            },
                            {
                                attributes: {},
                                text: 'Meeting note for',
                            },
                            {
                                attributes: { bold: true },
                                text: ' Meeting Name',
                            },
                            {
                                attributes: {},
                                text: ' has been created.',
                            },
                        ],
                    },
                },
                {
                    config: {
                        headers: {
                            Authorization: expect.stringContaining('Bearer'),
                        },
                    },
                }
            );
        });
    });

    describe('deleteTranscriptAttachment', () => {
        it('should delete transcript attachment successfully', async () => {
            const options = {
                attachmentId: 'attachment-123',
                workspaceId: 'workspace-123',
                ownerId: 456,
            };

            await activitiesService.deleteTranscriptAttachment(options);

            expect(attachmentService.deleteAttachments).toHaveBeenCalledWith([options.attachmentId], {
                workspaceId: Number(options.workspaceId),
                userId: options.ownerId,
            });
        });
    });

    describe('getSyncupRecordingId', () => {
        it('should get syncup recording id successfully', async () => {
            const transcriptId = 'transcript-123';
            const expectedRecordingId = 'recording-123';

            (syncupParticipationService.getSyncupRecordingId as jest.Mock).mockResolvedValue(expectedRecordingId);

            const result = await activitiesService.getSyncupRecordingId(transcriptId);

            expect(syncupParticipationService.getSyncupRecordingId).toHaveBeenCalledWith(transcriptId);
            expect(result).toBe(expectedRecordingId);
        });

        it('should handle syncup recording id failure', async () => {
            const transcriptId = 'transcript-123';
            const error = new Error('Get syncup recording id failed');

            (syncupParticipationService.getSyncupRecordingId as jest.Mock).mockRejectedValue(error);

            await expect(activitiesService.getSyncupRecordingId(transcriptId)).rejects.toThrow(
                'Get syncup recording id failed'
            );
        });
    });

    describe('getSyncupRecordingAttachmentId', () => {
        it('should get syncup recording attachment id successfully', async () => {
            const recordingId = 'recording-123';
            const expectedAttachmentId = 'attachment-123';

            (recordingsService.getSyncupRecordingAttachmentId as jest.Mock).mockResolvedValue(expectedAttachmentId);

            const result = await activitiesService.getSyncupRecordingAttachmentId(recordingId);

            expect(recordingsService.getSyncupRecordingAttachmentId).toHaveBeenCalledWith(recordingId);
            expect(result).toBe(expectedAttachmentId);
        });
    });
});
