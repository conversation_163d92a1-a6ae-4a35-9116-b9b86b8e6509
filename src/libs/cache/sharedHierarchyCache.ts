import { client as redis } from '@clickup-legacy/utils/redis';
import { ClickUpError } from '@clickup-legacy/utils/errors';

const SharedLogError = ClickUpError.getIsolatedErrorHandler('sharedHierarchy');

const oneHour = 1 * 60 * 60;

const CATEGORY_SHARED_IDS_REDIS_PREFIX = `CATEGORY_SHARED_IDS`;
const CATEGORY_SHARED_IDS_KEYS_PREFIX = `CAT_SHARED_IDS_KEYS`;
const SUBCATEGORY_SHARED_IDS_REDIS_PREFIX = `SUBCATEGORY_SHARED_IDS`;
const SUBCATEGORY_SHARED_IDS_KEYS_PREFIX = `SUBCAT_SHARED_IDS_KEYS`;
const TASKS_SHARED_COUNT_PREFIX = `TASKS_SHARED_COUNT`;
const TASKS_SHARED_COUNT_KEYS_PREFIX = `TASKS_SHARED_COUNT_KEYS`;

/**
 * Returns a random integer expiration time between 0 and oneHour
 */
function getRandomExpiration(): number {
    return Math.ceil(Math.random() * oneHour);
}

export function invalidateCachedCategoryIdsForUser(workspaceId: number, userid: number) {
    redis.srem(
        `${CATEGORY_SHARED_IDS_KEYS_PREFIX}:${workspaceId}`,
        `${CATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`,
        err => {
            if (err) {
                SharedLogError(err);
            }
            redis.unlink(`${CATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`, err1 => {
                if (err1) {
                    SharedLogError(err1);
                }
            });
        }
    );
}

export function getCachedSharedCategoryIds(userid: number, workspaceId: number, options: any, cb: any) {
    redis.get(`${CATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`, (err, result) => {
        if (err) {
            cb(err);
        } else if (result) {
            cb(null, JSON.parse(result));
        } else {
            cb();
        }
    });
}

export function cacheSharedCategoryIds(userid: number, workspaceId: number, category_ids: any) {
    const rand_exp = getRandomExpiration();

    redis.setex(
        `${CATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`,
        oneHour + rand_exp,
        `${JSON.stringify(category_ids)}`,
        err => {
            if (err) {
                SharedLogError(err);
            }
        }
    );
    redis.sadd(
        `${CATEGORY_SHARED_IDS_KEYS_PREFIX}:${workspaceId}`,
        `${CATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`,
        err => {
            if (err) {
                SharedLogError(err);
            }
        }
    );
}

export function getCachedSharedSubcategoryIds(userid: number, workspaceId: number, options: any, cb: any) {
    redis.get(`${SUBCATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`, (err, result) => {
        if (err) {
            cb(err);
        } else if (result) {
            cb(null, JSON.parse(result));
        } else {
            cb();
        }
    });
}

export function cacheSharedSubcategoryIds(userid: number, workspaceId: number, category_ids: any) {
    const rand_exp = getRandomExpiration();
    const key = `${SUBCATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`;

    redis.setex(key, oneHour + rand_exp, `${JSON.stringify(category_ids)}`, err => {
        if (err) {
            SharedLogError(err);
        }
    });
    redis.sadd(`${SUBCATEGORY_SHARED_IDS_KEYS_PREFIX}:${workspaceId}`, key, err => {
        if (err) {
            SharedLogError(err);
        }
    });
}

export function invalidateCachedSubcategoryIdsForUser(workspaceId: number, userid: number) {
    const key = `${SUBCATEGORY_SHARED_IDS_KEYS_PREFIX}:${workspaceId}`;
    const id = `${SUBCATEGORY_SHARED_IDS_REDIS_PREFIX}:${workspaceId}:${userid}`;
    redis.srem(key, id, err => {
        if (err) {
            SharedLogError(err);
        }
        redis.unlink(id, err1 => {
            if (err1) {
                SharedLogError(err1);
            }
        });
    });
}

export function getCachedSharedTaskCount(userid: number, workspaceId: number, options: any, cb: any) {
    let options_key = '';
    options_key += `${options.comments ? '1' : '0'}`;
    options_key += `${options.subtasks ? '1' : '0'}`;
    options_key += `${options.checklists ? '1' : '0'}`;
    options_key += `${options.include_subtasks ? '1' : '0'}`;
    options_key += `${options.all_statuses ? '1' : '0'}`;
    options_key += `:${options.assignees ? options.assignees.join(',') : '0'}`;
    options_key += `:${options.statuses ? options.statuses.join(',') : '0'}`;

    redis.get(`${TASKS_SHARED_COUNT_PREFIX}:${workspaceId}:${userid}:${options_key}`, (err, result) => {
        if (err) {
            cb(err);
        } else if (result) {
            cb(null, result);
        } else {
            cb();
        }
    });
}

export function cacheSharedTasksCount(userid: number, workspaceId: number, options: any, count: any) {
    const rand_exp = getRandomExpiration();

    let options_key = '';
    options_key += `${options.comments ? '1' : '0'}`;
    options_key += `${options.subtasks ? '1' : '0'}`;
    options_key += `${options.checklists ? '1' : '0'}`;
    options_key += `${options.include_subtasks ? '1' : '0'}`;
    options_key += `${options.all_statuses ? '1' : '0'}`;
    options_key += `:${options.assignees ? options.assignees.join(',') : '0'}`;
    options_key += `:${options.statuses ? options.statuses.join(',') : '0'}`;

    redis.setex(
        `${TASKS_SHARED_COUNT_PREFIX}:${workspaceId}:${userid}:${options_key}`,
        oneHour * 2 + rand_exp,
        count,
        err => {
            if (err) {
                SharedLogError(err);
            }
        }
    );
    redis.sadd(
        `${TASKS_SHARED_COUNT_KEYS_PREFIX}:${workspaceId}`,
        `${TASKS_SHARED_COUNT_PREFIX}:${workspaceId}:${userid}:${options_key}`,
        err => {
            if (err) {
                SharedLogError(err);
            }
        }
    );
}

export function invalidateCachedSharedTasksCountForUser(workspaceId: number, userid: number) {
    const key = `${TASKS_SHARED_COUNT_KEYS_PREFIX}:${workspaceId}`;
    const id = `${TASKS_SHARED_COUNT_PREFIX}:${workspaceId}:${userid}`;
    redis.srem(key, id, err => {
        if (err) {
            SharedLogError(err);
        }
        redis.unlink(id, err1 => {
            if (err1) {
                SharedLogError(err1);
            }
        });
    });
}
