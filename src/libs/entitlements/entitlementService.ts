import { memoize } from 'lodash';
import pLimit from 'p-limit';
import pRetry from 'p-retry';

import {
    Jobs<PERSON><PERSON>,
    JobSchedulerRunWorkflowNamespacePath,
    JobSchedulerRunWorkflowRequestWorkflowType,
} from '@clickup/gateway-client/api/jobs';
import type { MonolithPaywallClient } from '@clickup/paywall/service/types';
import { coerceToPlanIdsSafe } from '@clickup-legacy/libs/billing/plan/utils';
import { RedisClientImpl } from '@clickup/object-version-cache';

import { SendIterableEmailNotificationTemporalWorkflowArgs } from '@clickup/billing/iterable-types';
import { setImmediate } from 'async';
import { retry } from '@clickup-legacy/libs/common/utils/retry';
import { ConfigService } from '@clickup/utils/config-types';
import { type PlanId, PlanIds, supportedPlanIds } from '@clickup/billing/types';
import type { SimpleClient } from '@clickup/utils/db-types';
import type { TeamId } from '../common/types';
import {
    AddonInfo,
    BillingUsageServiceCounterType,
    CheckEntitlementOptionsByName,
    CheckEntitlementsResult,
    EntitlementCheck,
    EntitlementDefinition,
    EntitlementDefinitionByName,
    EntitlementDefinitionConfig,
    EntitlementDefinitionCounter,
    EntitlementDefinitionFlag,
    EntitlementInfo,
    EntitlementInfoByName,
    EntitlementName,
    EntitlementNameFlagEnum,
    EntitlementNameCounter,
    EntitlementNameExcluding,
    EntitlementNameExcludingLimitWithOptions,
    EntitlementNameFlag,
    EntitlementNameLimit,
    EntitlementNameReadOnlyCount,
    EntitlementNameWithOptions,
    EntitlementsPlansResult,
    EntitlementType,
    GetEntitlementOptions,
    GetEntitlementOptionsByName,
    GetEntitlementsOptions,
    GetEntitlementsPlanResult,
    GetEntitlementsPlansResult,
    GetEntitlementsResult,
    GetPlanEntitlementsOptions,
    IncrementEntitlementOptions,
    isEntitlementDefinitionCounter,
    isEntitlementDefinitionFlag,
    isEntitlementDefinitionLimit,
    isEntitlementDefinitionReadOnlyCountBdr,
    isEntitlementInfoCounter,
    isEntitlementInfoFlag,
    isEntitlementInfoLimit,
    isEntitlementNameCounter,
    isIncrementEntitlementOptions,
    LimitWithInfinity,
    PlanAndLimits,
    SkuTrialDefinitionConfig,
    EntitlementDefinitionLimit,
    isEntitlementName,
} from './types';
import { ClickUpError } from '../../utils/errors';
import { generateEntitlementDefinitions } from './helpers/entitlementDefinitions';
import { StatsDClient } from '../../metrics/StatsDClient';
import { endTimer, startTimer } from '../../metrics/timing';
import { EntitlementRepository } from './entitlementRepository';
import { UpdateRuleResponse } from '../paywall/clients/PaywallSimpleClient';
import { SkuEntitlementsConfigMap, SkuTrialId } from './skuTrial/types/skuTrialId';
import { SkuTrialAccessService } from './skuTrial/skuTrialAccessService';
import { SegmentEvent } from '../integrations/segment/enums/eventEnums';
import { FakeUserId } from './consts';
import { SplitTreatment } from '../../models/integrations/split/splitTreatment';
import { RuleLimitTransformer } from '../paywall/transformers/rule-limit.transformer';
import type { EntitlementsClient } from './entitlementClient';
import { BillingShouldIncludeLimitOverridesConfig } from '../../models/integrations/split/interfaces/crmBillingTreatments';

export const PaywallError = ClickUpError.makeNamedError('paywall');

const METRICS_CACHE_PREFIX = 'entitlements.service.cache';

export const logger = ClickUpError.getBasicLogger('EntitlementService');

const EntitlementsClientConstructor = memoize(async () => (await import('./entitlementClient')).EntitlementsClient);

const bulkGetShardFromWorkspaceIdMemoized = memoize(
    async () => (await import('@clickup/data-platform/sharding/workspace-assignment')).bulkGetShardFromWorkspaceId
);

export class EntitlementService {
    private entitlementDefinitions: EntitlementDefinitionConfig;

    private ruleLimitTransformer: RuleLimitTransformer = new RuleLimitTransformer();

    private clients: Record<string, EntitlementsClient> = {};

    private _isCacheEnabled = true;

    // this will be much less effective when autoscaling kicks in, but at least it'll somewhat work
    private static fallbackCacheVersion = `NO-VERSION-${new Date().toISOString().replace(/[^\d]/g, '').slice(0, 10)}`;

    /** a map of entitlement name to a list of entitlements names that depend on it
     *
     * e.g. PaidAggregatedAutoAi -> AggregatedAutoAi -> TaskAutomatedAiUsages
     *
     * this is used to invalidate entitlements when their dependencies change */
    private reverseEntitlementDependencies: Partial<Record<EntitlementName, EntitlementName[]>> = {};

    constructor(
        private readonly configService: ConfigService,
        private readonly entitlementRepository: EntitlementRepository,
        private readonly skuTrialAccessService: SkuTrialAccessService,
        private readonly metricsClient: StatsDClient,
        private readonly shouldTrackUsageLimitHit: (teamId: TeamId) => boolean,
        private readonly removingGlobalReplicationConfig: () => {
            enable_routing?: boolean;
            enable_logging?: boolean;
            throw_on_wrong_shard?: boolean;
        },
        private readonly trackTypedEvent: (entitlement: string, userId: number, props: object) => void,
        private readonly billingShouldGetAllEntitlementsAlwaysFullData: SplitTreatment,
        private readonly billingShouldIncludeLimitOverrides: (
            teamId: TeamId
        ) => BillingShouldIncludeLimitOverridesConfig,
        private readonly getJobsApi: () => JobsApi,
        private readonly shouldSendEntitlementLifetimeUsageEmails: SplitTreatment,
        private readonly billingShouldUseEntitlementsCache: SplitTreatment,
        private readonly paywallClient?: MonolithPaywallClient,
        private readonly connectedSearchTrialIsOn?: SplitTreatment,
        private readonly redisCache?: RedisClientImpl,
        private readonly cacheTTLs: {
            noWsEntitlements: number;
            hasWsEntitlements: number;
        } = {
            noWsEntitlements: 60 * 60, // default 1 hour
            hasWsEntitlements: 60 * 3, // default 3 minutes
        }
    ) {
        this.entitlementDefinitions = generateEntitlementDefinitions(configService);
        this.reverseEntitlementDependencies = this.buildReverseEntitlementDependencies(this.entitlementDefinitions);
    }

    private buildReverseEntitlementDependencies(
        entitlementDefinitions: EntitlementDefinitionConfig
    ): Record<EntitlementName, EntitlementName[]> {
        return Object.values(entitlementDefinitions).reduce((acc, definition) => {
            // Note we don't handle the queries here - this is strictly done outside of workspace context;
            // hence it's important to have some static limits defined even if there's a query.

            // Handle limitsProxy dependencies
            if ('limitsProxy' in definition && definition.limitsProxy) {
                Object.values(definition.limitsProxy).forEach(dependency => {
                    const key = dependency as EntitlementName;
                    const existingDeps = new Set(acc[key] ?? []);
                    existingDeps.add(definition.name as EntitlementName);
                    acc[key] = Array.from(existingDeps);
                });
            }

            // Handle incrementFactors dependencies
            if ('incrementFactors' in definition && definition.incrementFactors) {
                Object.keys(definition.incrementFactors).forEach(dependency => {
                    const key = dependency as EntitlementName;
                    const existingDeps = new Set(acc[key] ?? []);
                    existingDeps.add(definition.name as EntitlementName);
                    acc[key] = Array.from(existingDeps);
                });
            }

            // Handle checks dependencies
            if ('checks' in definition && definition.checks) {
                const addChecks = (checks: (EntitlementName | EntitlementCheck)[]) => {
                    checks.forEach(check => {
                        if (typeof check === 'string') {
                            const key = check as EntitlementName;
                            const existingDeps = new Set(acc[key] ?? []);
                            existingDeps.add(definition.name as EntitlementName);
                            acc[key] = Array.from(existingDeps);
                        } else {
                            addChecks(check.checks);
                        }
                    });
                };
                addChecks(definition.checks.checks);
            }

            return acc;
        }, {} as Record<EntitlementName, EntitlementName[]>);
    }

    public getReverseEntitlementDependencies(): Readonly<Partial<Record<EntitlementName, EntitlementName[]>>> {
        return Object.freeze(this.reverseEntitlementDependencies);
    }

    public getReverseEntitlementDependenciesFor(entitlementName: EntitlementName): Readonly<EntitlementName[]> {
        if (entitlementName in this.reverseEntitlementDependencies) {
            return Object.freeze(
                (this.reverseEntitlementDependencies as Record<EntitlementName, EntitlementName[]>)[entitlementName]
            );
        }
        return Object.freeze([]);
    }

    private async getClient(shardId: string) {
        if (!this.clients[shardId]) {
            const ClientConstructor = await EntitlementsClientConstructor();
            const host = this.configService.get<string>(`sharding.routes.${shardId}`);

            this.debugLog({
                msg: 'creating entitlements client',
                host,
                shardId,
            });

            this.clients[shardId] = new ClientConstructor(host, this.configService);
        }
        return this.clients[shardId];
    }

    private async wrongShardGuard(teamIds: TeamId[]) {
        const currentShardId = this.configService.get<string>('sharding.shard_id');
        const { throw_on_wrong_shard, enable_logging } = this.removingGlobalReplicationConfig() ?? {};

        if (enable_logging || throw_on_wrong_shard) {
            const shardMapping = await (await bulkGetShardFromWorkspaceIdMemoized())(teamIds.map(String));

            const wrongShardTeams = teamIds.filter(teamId => {
                const shardId = shardMapping[String(teamId)];
                return shardId !== currentShardId;
            });

            if (wrongShardTeams.length > 0) {
                this.debugLog({
                    msg: 'Entitlements operation attempted on wrong shard',
                    wrongShardTeamIds: wrongShardTeams,
                    currentShardId,
                    shardMapping,
                    stack: new Error().stack,
                });

                if (throw_on_wrong_shard) {
                    throw new PaywallError('Operation attempted on wrong shard', 'PAYWALL_029', 400);
                }
            }
        }
    }

    /**
     * This method handles enttilements routing returning groups of requests to be submitted
     * together and to which shard.
     * @param teamIds
     * @param entitlements
     */
    private async handleRouting(
        teamIds: TeamId[],
        entitlements: EntitlementName[] | undefined
    ): Promise<
        Array<{
            shardId: string;
            entitlements: typeof entitlements;
            teamIds: typeof teamIds;
        }>
    > {
        const { enable_routing, enable_logging } = this.removingGlobalReplicationConfig() ?? {};

        const routingDisabledResult = [
            {
                shardId: this.configService.get<string>('sharding.shard_id'),
                entitlements,
                teamIds,
            },
        ];

        // Base negative case, if routing and logging are disabled everything should be routed to current shard
        if (!(enable_routing || enable_logging)) {
            return routingDisabledResult;
        }

        // If routing is enabled initiate the groups
        const entitlementGroups: Array<{
            shardId: string;
            entitlements: typeof entitlements;
            teamIds: typeof teamIds;
        }> = [];

        // Get full entitlement definitions and separate them into ones requiring workspace data
        // and ones that don't
        const groupedEntitlements = entitlements
            .map(entitlement => this.getDefinition(entitlement))
            .reduce(
                (acc, entitlement) => {
                    if (entitlement.requiresWorkspaceData) {
                        acc.requiringWorkspaceData.push(entitlement.name);
                    } else {
                        acc.global.push(entitlement.name);
                    }
                    return acc;
                },
                { requiringWorkspaceData: [] as typeof entitlements, global: [] as typeof entitlements }
            );

        // All global entitlements go to current shard
        // This means we will have to entries for current shard - that's by design, they should be separated
        // One group will include all teams, despite their shard assignment - they'll use the global queries
        // The other will have only local workspaces
        if (groupedEntitlements.global.length > 0) {
            entitlementGroups.push({
                shardId: this.configService.get<string>('sharding.shard_id'),
                entitlements: groupedEntitlements.global,
                teamIds,
            });
        }

        const wrongShardEntitlements = [];

        // After global entitlements handle workspace entitlements
        // This requires resolving workspace shard handshake
        if (groupedEntitlements.requiringWorkspaceData.length > 0) {
            const shardMapping = await (await bulkGetShardFromWorkspaceIdMemoized())(teamIds.map(String));

            const teamsByShard: Record<string, TeamId[]> = teamIds.reduce((acc, teamId) => {
                const shardId = shardMapping[String(teamId)];
                if (!acc[shardId]) {
                    acc[shardId] = [];
                }
                acc[shardId].push(teamId);
                return acc;
            }, {} as Record<string, TeamId[]>);

            for (const [shardId, teams] of Object.entries(teamsByShard)) {
                const entitlementGroup = {
                    shardId,
                    entitlements: groupedEntitlements.requiringWorkspaceData,
                    teamIds: teams,
                };
                entitlementGroups.push(entitlementGroup);

                if (shardId !== this.configService.get<string>('sharding.shard_id')) {
                    wrongShardEntitlements.push(entitlementGroup);
                }
            }
        }

        // If logging is enabled, log the routing info
        if (wrongShardEntitlements.length > 0) {
            this.debugLog({
                msg: 'Entitlements checked on wrong shard',
                wrongShardEntitlements,
                stack: new Error().stack,
            });
        }

        return enable_routing ? entitlementGroups : routingDisabledResult;
    }

    // /////
    // cache

    public setCacheEnabled(enabled: boolean) {
        this._isCacheEnabled = enabled;
    }

    public isCacheEnabled(teamId?: TeamId) {
        return this._isCacheEnabled && this.isRedisCacheValid(teamId);
    }

    private isRedisCacheValid(teamId?: TeamId) {
        return this.redisCache && this.billingShouldUseEntitlementsCache(teamId);
    }

    private getCacheKey(
        type: string,
        options: {
            teamId?: TeamId;
            extra?: string;
            options?: Record<string, unknown> & { invalidateCache?: boolean; doNotCache?: boolean };
        }
    ) {
        // redis key friendly serialization of options
        const serializedOptions = options.options
            ? Object.entries(options.options)
                  .filter(
                      ([key, value]) =>
                          key !== 'invalidateCache' && key !== 'doNotCache' && key !== 'throwOnDeny' && value != null
                  )
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([key, value]) => `${key}-${String(value).replace(/[^a-zA-Z0-9-_]/g, '_')}`)
                  .join('-')
            : '';
        return `${this.getCacheKeyPrefix(type, options)}${options.extra ? '' : ':-'}${
            serializedOptions ? `:${serializedOptions}` : ''
        }`;
    }

    private _getCacheKeyPrefix(type: string) {
        return `entitlements:${process.env.VERSION || EntitlementService.fallbackCacheVersion}:${type}`;
    }

    /** useful for mass unlink */
    private getCacheKeyPrefix(
        type: string,
        options: {
            teamId?: TeamId;
            extra?: string;
            options?: Record<string, unknown> & { invalidateCache?: boolean; doNotCache?: boolean };
        }
    ) {
        return `${this._getCacheKeyPrefix(type)}:${options.teamId ? `${options.teamId}` : 'no-team'}${
            options.extra ? `:${options.extra}` : ''
        }`;
    }

    /** Invalidate the whole cache - intended to be used in testing. Slow! */
    async invalidateCacheFull() {
        if (!this.isRedisCacheValid()) {
            return;
        }
        await this.redisCache.batchUnlinkByPrefix([
            this._getCacheKeyPrefix('plan'),
            this._getCacheKeyPrefix('plans'),
            this._getCacheKeyPrefix('info'),
        ]);
    }

    /**
     * Invalidate the cache for a workspace. Use e.g. when a workspace changes plan or addons.
     *
     * This is a slow operation!
     *
     * @param teamId The team ID to invalidate the cache for.
     */
    async invalidateCacheForTeam(teamId: TeamId) {
        // TODO this can be smarter - e.g. only invalidate entitlements which can change?
        if (!teamId || !this.isRedisCacheValid(teamId)) {
            return;
        }
        this.metricsClient.increment(`${METRICS_CACHE_PREFIX}.invalidate.team`, 1);

        // specifcally do not check if cache is enabled for this team - invalidating should always invalidate
        const keys = [this.getCacheKeyPrefix('plan', { teamId }), this.getCacheKeyPrefix('info', { teamId })];

        // retry invalidation attempts - they are more important than get/set
        await pRetry(() => this.redisCache.batchUnlinkByPrefix(keys), {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            retries: 5,
            factor: 2,
            onFailedAttempt: err => {
                logger.warn({ msg: 'redis cache invalidate error for team', err, keys });
            },
        });
    }

    async invalidateCacheForTeamEntitlement(teamId: TeamId, entitlement: EntitlementName): Promise<void>;

    async invalidateCacheForTeamEntitlement(teamId: TeamId, entitlements: Readonly<EntitlementName[]>): Promise<void>;

    async invalidateCacheForTeamEntitlement(
        teamId: TeamId,
        entitlement: EntitlementName | Readonly<EntitlementName[]>
    ): Promise<void> {
        const startTime = Date.now();
        try {
            if (!this.isRedisCacheValid(teamId)) {
                return;
            }
            // specifcally do not check if cache is enabled for this team - invalidating should always invalidate
            // retry invalidation attempts - they are more important than get/set
            const keys =
                typeof entitlement === 'string'
                    ? [this.getCacheKey('info', { teamId, extra: `${entitlement}` })]
                    : entitlement.map(e => this.getCacheKey('info', { teamId, extra: `${e}` }));

            if (typeof entitlement === 'string') {
                this.metricsClient.increment(`${METRICS_CACHE_PREFIX}.invalidate.entitlement`, 1, { entitlement });
            } else {
                entitlement.forEach(e => {
                    this.metricsClient.increment(`${METRICS_CACHE_PREFIX}.invalidate.entitlement`, 1, {
                        entitlement: e,
                    });
                });
            }

            // this is the slow operation we want to remove. With the new cache key strategy, we can use batchUnlink
            await pRetry(() => this.redisCache.batchUnlink(keys), {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                retries: 3,
                factor: 2,
                onFailedAttempt: err => {
                    logger.warn({ msg: 'redis cache invalidate error for entitlement', err, keys });
                },
            });
        } finally {
            this.metricsClient.timing('entitlements.invalidateCacheForTeamEntitlement.timing', Date.now() - startTime);
        }
    }

    private serializeOptions(
        options?: Record<string, unknown> & { invalidateCache?: boolean; doNotCache?: boolean }
    ): string {
        if (!options) {
            return 'no-options';
        }
        const serialized = Object.entries(options)
            .filter(
                ([key, value]) =>
                    key !== 'invalidateCache' && key !== 'doNotCache' && key !== 'throwOnDeny' && value != null
            )
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => `${key}-${String(value).replace(/[^a-zA-Z0-9-_]/g, '_')}`)
            .join('-');
        return serialized || 'no-options';
    }

    /** get entitlement info from cache.
     *
     * due to cache invalidation necessities we need the key to be 100% predictable regardless of options used.
     * the solution here is to store options as an index in the cache value; this complicated fetching and setting, but is much faster that SCAN.
     *
     * @param metricName - the metric name to use for metrics
     * @param cacheKey - the cache key to use
     * @param metricTags - the metric tags to use
     * @param options - the options to use
     * @param teamId - the team ID to use
     */
    private async handleEntitlementInfoCacheGet<T>(
        metricName: 'entitlement',
        cacheKey: string,
        metricTags: Record<string, string | undefined>,
        options?: { invalidateCache?: boolean; doNotCache?: boolean },
        teamId?: TeamId
    ): Promise<T | undefined> {
        if (this.isCacheEnabled(teamId) && !options?.invalidateCache) {
            try {
                const cachedResult = await this.redisCache.batchMget([cacheKey]);
                if (cachedResult && cachedResult[0]) {
                    const allOptionsResult = JSON.parse(cachedResult[0]);
                    const serializedOptions = this.serializeOptions(options);
                    const result = allOptionsResult[serializedOptions];

                    if (result) {
                        this.metricsClient.increment(`${METRICS_CACHE_PREFIX}.hit.${metricName}`, 1, metricTags);
                        return result as T;
                    }
                }
            } catch (e: unknown) {
                logger.warn({ msg: 'redis cache get error', err: e, cacheKey });
            }
        }

        if (this.isRedisCacheValid(teamId)) {
            if (options?.invalidateCache) {
                await pRetry(() => this.redisCache.batchUnlink([cacheKey]), {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    retries: 3,
                    factor: 2,
                    onFailedAttempt: err => {
                        logger.warn({ msg: 'redis cache invalidate on get error', err, cacheKey });
                    },
                });
            }
            this.metricsClient.increment(`${METRICS_CACHE_PREFIX}.miss.${metricName}`, 1, metricTags);
        }

        return undefined;
    }

    private async handleEntitlementInfoCacheSet<T>(
        cacheKey: string | undefined,
        value: T,
        options: { doNotCache?: boolean } & Record<string, unknown>,
        teamId?: TeamId
    ): Promise<void> {
        if (this.isCacheEnabled(teamId) && cacheKey && !options?.doNotCache) {
            const ttl = this.getCacheTTL(teamId);
            try {
                // NOTE: This read-modify-write is not atomic and can lead to a race condition
                // where concurrent requests for the same entitlement but different options might overwrite each other.
                // Given the current redis client capabilities, this is a known trade-off for performance.
                const existing = await this.redisCache.batchMget([cacheKey]);
                const allOptionsResult = existing && existing[0] ? JSON.parse(existing[0]) : {};

                const serializedOptions = this.serializeOptions(options);
                allOptionsResult[serializedOptions] = value;

                await this.redisCache.batchSetex([cacheKey], [JSON.stringify(allOptionsResult)], ttl);
            } catch (e: unknown) {
                logger.warn({ msg: 'redis cache set error', err: e, cacheKey });
            }
        }
    }

    private async handleCacheGet<T>(
        metricName: 'plan' | 'plans' | 'entitlement',
        cacheKey: string,
        metricTags: Record<string, string | undefined>,
        options?: { invalidateCache?: boolean },
        teamId?: TeamId
    ): Promise<T | undefined> {
        if (this.isCacheEnabled(teamId) && !options?.invalidateCache) {
            try {
                const cachedResult = await this.redisCache.batchMget([cacheKey]);
                if (cachedResult && cachedResult[0]) {
                    this.metricsClient.increment(`${METRICS_CACHE_PREFIX}.hit.${metricName}`, 1, metricTags);
                    return JSON.parse(cachedResult[0]) as T;
                }
            } catch (e: unknown) {
                logger.warn({ msg: 'redis cache get error', err: e, cacheKey });
            }
        }

        if (this.isRedisCacheValid()) {
            if (options?.invalidateCache) {
                await pRetry(() => this.redisCache.batchUnlink([cacheKey]), {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    retries: 3,
                    factor: 2,
                    onFailedAttempt: err => {
                        logger.warn({ msg: 'redis cache invalidate on get error', err, cacheKey });
                    },
                });
            }
            this.metricsClient.increment(`${METRICS_CACHE_PREFIX}.miss.${metricName}`, 1, metricTags);
        }

        return undefined;
    }

    private async handleCacheSet<T>(
        cacheKey: string | undefined,
        value: T,
        options: { doNotCache?: boolean },
        teamId?: TeamId
    ): Promise<void> {
        if (this.isCacheEnabled(teamId) && cacheKey && !options?.doNotCache) {
            const ttl = this.getCacheTTL(teamId);
            try {
                await this.redisCache.batchSetex([cacheKey], [JSON.stringify(value)], ttl);
            } catch (e: unknown) {
                logger.warn({ msg: 'redis cache set error', err: e, cacheKey });
            }
        }
    }

    private getCacheTTL(teamId?: TeamId): number {
        return teamId
            ? this.configService.get<number>('entitlements.cacheTTLs.hasWs') ?? this.cacheTTLs.hasWsEntitlements
            : this.configService.get<number>('entitlements.cacheTTLs.noWs') ?? this.cacheTTLs.noWsEntitlements;
    }

    // ///////////////////
    // entitlements proper

    async getEntitlementForTeams<T extends EntitlementName>(
        teamIds: TeamId[],
        entitlement: T,
        options?: GetEntitlementOptionsByName[T],
        client?: SimpleClient
    ): Promise<({ teamId: TeamId } & EntitlementInfoByName[T])[]> {
        if (!teamIds?.length) {
            return [];
        }

        const currentShardId = this.configService.get<string>('sharding.shard_id');

        const entitlementGroups = await this.handleRouting(teamIds, [entitlement]);

        this.debugLog({
            msg: 'getEntitlementForTeams',
            teamIds,
            entitlementGroups,
            entitlement,
            options,
        });

        const callback = async () => {
            const results: ({ teamId: TeamId } & EntitlementInfoByName[T])[] = [];

            for (const group of entitlementGroups) {
                // Handle teams on current shard
                if (group.shardId === currentShardId) {
                    for (const teamId of group.teamIds) {
                        const { definition, planAndLimits } = await this.getTeamEntitlementDefinition(
                            teamId,
                            entitlement,
                            null,
                            // includeLimitOverrides is always true as planAndLimits are required here
                            { ...options, includeLimitOverrides: true },
                            client
                        );
                        const entitlementInfo = (await this.parseEntitlementInfoWithEntitledQueries({
                            teamId,
                            planId: this.getActualPlanId(definition, planAndLimits, options),
                            client,
                            definition,
                            usage: planAndLimits?.[`${entitlement}_usage`] ?? 0,
                            limitOverride: null,
                            options,
                        })) as EntitlementInfoByName[T];
                        results.push({
                            teamId,
                            ...entitlementInfo,
                        });
                    }
                } else {
                    // Handle teams on other shards
                    const { shardId } = group;
                    const teamsInOtherShard = group.teamIds;

                    if (shardId === undefined) {
                        logger.error({
                            msg: 'Invalid workspace shard',
                            ECODE: 'PAYWALL_028',
                            teamIds: teamsInOtherShard,
                        });
                        throw new PaywallError('Invalid workspace shard', 'PAYWALL_028', 500);
                    }

                    // Track number of teams routed to this shard
                    this.metricsClient.increment(
                        'entitlements.routedEntitlementChecks.count',
                        teamsInOtherShard.length,
                        { shardId }
                    );

                    const entitlementsClient = await this.getClient(shardId);
                    const shardResults = await entitlementsClient.getEntitlementForTeams(
                        teamsInOtherShard.map(String),
                        entitlement,
                        options
                    );

                    results.push(...shardResults);
                }
            }

            return results;
        };

        return this.withMetrics('getEntitlementForTeams', callback, { entitlement });
    }

    /** get entitlements for multiple plans without workspace context. useful for e.g. the landing page */
    async getPlansEntitlements(
        planIds: PlanId[],
        entitlementsWithOption?: EntitlementNameWithOptions[],
        options?: GetPlanEntitlementsOptions
    ): Promise<GetEntitlementsPlansResult> {
        const cacheKey = this.getPlansEntitlementsCacheKey(
            'plans',
            planIds,
            undefined,
            entitlementsWithOption,
            options
        );
        const cachedResult = await this.handleCacheGet<GetEntitlementsPlansResult>(
            'plans',
            cacheKey,
            {
                planIds: planIds.join(','),
            },
            options
        );
        if (cachedResult) {
            return cachedResult;
        }

        const callback = async () => {
            const entitlementsResultPerPlan = await Promise.all(
                planIds.map(async planId => ({
                    planId,
                    // teamId is undefined here
                    ...(await this.getPlanEntitlements(planId, undefined, entitlementsWithOption, options)),
                }))
            );
            const entitlementNames = this.getEntitlementsNamesFromTypes(entitlementsWithOption, options?.types);

            const result = entitlementNames.reduce<GetEntitlementsPlansResult>((acc, entitlementName) => {
                acc[entitlementName] = entitlementsResultPerPlan.reduce<EntitlementsPlansResult>(
                    (plansAcc, entitlementsResult, index) => {
                        const planEntitlement: EntitlementsPlansResult[keyof EntitlementsPlansResult] = {
                            planId: entitlementsResult.planId,
                            nextAvailable: [],
                            ...entitlementsResult[entitlementName],
                        };

                        const currEntitlement = entitlementsResultPerPlan[index][entitlementName];

                        for (let i = index + 1; i < entitlementsResultPerPlan.length; i++) {
                            const nextEntitlement = entitlementsResultPerPlan[i][entitlementName];
                            const nextPlanId = entitlementsResultPerPlan[i].planId;

                            const { nextAvailable, nextUpgrade, topUpgrade } = this.getPlanUpgradePath(
                                entitlementName,
                                currEntitlement,
                                nextEntitlement,
                                nextPlanId
                            );

                            if (nextAvailable) {
                                planEntitlement.nextAvailable.push(nextAvailable);
                            }

                            if (!planEntitlement.nextUpgrade && nextUpgrade) {
                                planEntitlement.nextUpgrade = nextUpgrade;
                            }

                            if (!planEntitlement.topUpgrade && topUpgrade) {
                                planEntitlement.topUpgrade = topUpgrade;
                            }
                        }

                        plansAcc[entitlementsResult.planId] = planEntitlement;

                        return plansAcc;
                    },
                    {}
                );
                return acc;
            }, {});

            await this.handleCacheSet(cacheKey, result, options ?? {}, undefined);

            return result;
        };

        return this.withMetrics('getPlansEntitlements', callback);
    }

    private getPlansEntitlementsCacheKey(
        type: 'plan' | 'plans',
        planIds: PlanId[],
        teamId?: TeamId,
        entitlementsWithOption?: EntitlementNameWithOptions[],
        options?: GetPlanEntitlementsOptions
    ) {
        return this.getCacheKey(type, {
            teamId,
            extra: `${planIds.join(',')}${
                entitlementsWithOption?.length > 0
                    ? `:${entitlementsWithOption
                          ?.map(e =>
                              typeof e === 'string'
                                  ? e
                                  : `${e.entitlement}${e.entitlementOverride ? `-EO${e.entitlementOverride}` : ''}${
                                        e.skipStatus ? '-skip' : ''
                                    }${e.skipUsage ? '-skipUsage' : ''}`
                          )
                          .join(',')
                          .replace(':', '_')}`
                    : ''
            }`,
            options,
        });
    }

    async setPaywallEntitlements(planId: PlanId, teamId: TeamId): Promise<void> {
        const paywallEntitlements: EntitlementName[] = [
            EntitlementName.AutomationActions,
            EntitlementName.CustomFieldManualAiUsages,
            EntitlementName.CustomFieldAutomatedAiUsages,
            EntitlementName.PaidAggregatedAutoAi,
            EntitlementName.FreeAggregatedAutoAi,
            EntitlementName.AggregatedAutoAi,
            EntitlementName.TaskManualAiUsages,
            EntitlementName.TaskAutomatedAiUsages,
            EntitlementName.CardsAutomatedAiUsages,
            EntitlementName.AutomatedAiAgentUsages,
            EntitlementName.ImageGeneration,
        ];

        await this.setEntitlements(planId, teamId, paywallEntitlements);
    }

    async setEntitlements(
        planId: PlanId,
        teamId: TeamId,
        entitlementsWithOption?: EntitlementNameWithOptions[]
    ): Promise<void> {
        await this.wrongShardGuard([teamId]);

        const entitlements = await this.getPlanEntitlements(planId, teamId, entitlementsWithOption, {
            invalidateCache: true,
            doNotCache: true,
        });
        const promises = [];
        for (const key of Object.keys(entitlements)) {
            const entitlementName = key as EntitlementName;
            const entitlement = entitlements[entitlementName as EntitlementName];
            const definition = this.getDefinition(entitlementName);
            if (definition.paywallService) {
                promises.push(this.paywallClient.updateRule(String(teamId), entitlementName, entitlement, false));
            }
            // XXX should we reset team_limits here? they're supposed to be lifetime
        }

        return Promise.all(promises) as unknown as void;
    }

    async applyAddonToAutomationActionsRule(
        planId: PlanId,
        teamId: TeamId,
        addonInfo: AddonInfo
    ): Promise<UpdateRuleResponse> {
        await this.wrongShardGuard([teamId]);
        if (addonInfo.actions) {
            const entitlements = await this.getPlanEntitlements(planId, teamId, [EntitlementName.AutomationActions], {
                invalidateCache: true,
                doNotCache: true,
            });
            const entitlement = entitlements[EntitlementName.AutomationActions];
            await this.invalidateCacheForTeamEntitlement(teamId, EntitlementName.AutomationActions);
            return this.paywallClient.updateRule(
                String(teamId),
                EntitlementName.AutomationActions,
                {
                    ...entitlement,
                    limit: +entitlement.limit + addonInfo.actions,
                },
                true
            );
        }
        throw new PaywallError('No addon info provided', 'PAYWALL_002', 400);
    }

    async removeAddonFromAutomationActionsRule(planId: PlanId, teamId: TeamId): Promise<UpdateRuleResponse> {
        await this.wrongShardGuard([teamId]);
        const entitlements = await this.getPlanEntitlements(planId, teamId, [EntitlementName.AutomationActions]);
        const entitlement = entitlements[EntitlementName.AutomationActions];
        await this.invalidateCacheForTeamEntitlement(teamId, EntitlementName.AutomationActions);
        return this.paywallClient.updateRule(
            String(teamId),
            EntitlementName.AutomationActions,
            {
                ...entitlement,
                limit: +entitlement.limit,
            },
            true
        );
    }

    async getPlanEntitlements(
        planId: PlanId,
        teamId?: TeamId,
        entitlementsWithOption?: EntitlementNameWithOptions[],
        options?: GetPlanEntitlementsOptions
    ): Promise<GetEntitlementsPlanResult> {
        const cacheKey = this.getPlansEntitlementsCacheKey('plan', [planId], teamId, entitlementsWithOption, options);
        const cachedResult = await this.handleCacheGet<GetEntitlementsPlanResult>(
            'plan',
            cacheKey,
            { planId: String(planId) },
            options,
            teamId
        );
        if (cachedResult) {
            return cachedResult;
        }

        const callback = async () => {
            const entitlements = this.getEntitlementsNameOfTypes(entitlementsWithOption, options?.types);
            const definitionsAndOptions = entitlements.map(entitlement => {
                const { entitlement: entitlementName, ...entitlementOptions } =
                    typeof entitlement === 'string' ? { entitlement } : entitlement;
                const definition = this.getDefinition(entitlementName, entitlementOptions);
                return { definition, entitlementOptions };
            });
            const entitlementsResult = await definitionsAndOptions.reduce<Promise<GetEntitlementsResult>>(
                async (accPromise, { definition, entitlementOptions }) => {
                    const acc = await accPromise;
                    // possibly no team context here, so no entitled queries, limit queries, etc.
                    const entitlementInfo = await this.parseEntitlementInfo(
                        Number(planId),
                        definition,
                        0,
                        null,
                        teamId ?? undefined,
                        entitlementOptions
                    );

                    acc[definition.name] = {
                        entitled: entitlementInfo.entitled,
                        type: entitlementInfo.type,
                        ...(!isEntitlementInfoFlag(entitlementInfo) && { limit: entitlementInfo.limit }),
                    } as never;

                    return acc;
                },
                Promise.resolve({})
            );

            await this.handleCacheSet(cacheKey, entitlementsResult, options ?? {}, teamId);

            return entitlementsResult;
        };

        return this.withMetrics('getPlanEntitlements', callback);
    }

    async checkEntitlements(
        teamId: TeamId,
        entitlementsWithOption?: EntitlementNameExcludingLimitWithOptions[],
        options?: GetEntitlementsOptions,
        client?: SimpleClient
    ): Promise<CheckEntitlementsResult> {
        const callback = async () => {
            const entitlementsResult = await this.getEntitlements(teamId, entitlementsWithOption, options, client);

            return Object.entries(entitlementsResult).reduce<CheckEntitlementsResult>(
                (acc, [entitlement, { entitled }]) => {
                    acc[entitlement as EntitlementName] = entitled;
                    return acc;
                },
                {}
            );
        };

        return this.withMetrics('checkEntitlements', callback);
    }

    async getEntitlements(
        teamId: TeamId,
        entitlementsWithOption?: EntitlementNameWithOptions[],
        options?: GetEntitlementsOptions,
        client?: SimpleClient
    ): Promise<GetEntitlementsResult> {
        const callback = async () => {
            const entitlements = this.getEntitlementsNameOfTypes(entitlementsWithOption, options?.types);

            const routingGroups = await this.handleRouting(
                [teamId],
                entitlements?.map(ent => (typeof ent === 'string' ? ent : ent.entitlement))
            );

            this.debugLog({
                msg: 'getEntitlements routing groups',
                routingGroups,
                teamId,
                entitlementsWithOption,
            });

            const results = await Promise.all(
                routingGroups.map(async group => {
                    const routedEntitlements =
                        entitlements?.filter(ent =>
                            typeof ent === 'string'
                                ? group.entitlements.includes(ent)
                                : group.entitlements.includes(ent.entitlement)
                        ) ?? undefined;
                    if (group.shardId === this.configService.get<string>('sharding.shard_id')) {
                        return this.getEntitlementsInfo(teamId, routedEntitlements, options, client);
                    }
                    this.metricsClient.increment('entitlements.routedEntitlementChecks.count', 1, {
                        shardId: group.shardId,
                    });
                    return (await this.getClient(group.shardId)).getEntitlements(
                        String(teamId),
                        routedEntitlements,
                        options
                    );
                })
            );

            return results.reduce((acc, entitlement) => {
                Object.assign(acc, entitlement);
                return acc;
            }, {});
        };

        return this.withMetrics('getEntitlements', callback);
    }

    async getEntitlement<T extends EntitlementName>(
        teamId: TeamId,
        entitlement: T,
        options?: GetEntitlementOptionsByName[T],
        client?: SimpleClient
    ): Promise<EntitlementInfoByName[T]> {
        const callback = async () => {
            const [routingInfo] = await this.handleRouting([teamId], [entitlement]);

            this.debugLog({
                msg: 'getEntitlement routing info',
                routingInfo,
                teamId,
                entitlement,
            });

            if (routingInfo.shardId === this.configService.get<string>('sharding.shard_id')) {
                return this.getEntitlementInfo(teamId, entitlement, options, client);
            }
            this.metricsClient.increment('entitlements.routedEntitlementChecks.count', 1, {
                shardId: routingInfo.shardId,
            });
            return (await this.getClient(routingInfo.shardId)).getEntitlement(String(teamId), entitlement, options);
        };

        return this.withMetrics('getEntitlement', callback, { entitlement });
    }

    private async traverseEntitlementChecks<T>(
        check: EntitlementCheck | EntitlementName,
        leafFn: (entitlement: EntitlementName) => Promise<T>,
        predFn: (result: T) => Promise<boolean>
    ): Promise<T | undefined> {
        if (typeof check === 'string') {
            // actually, EntitlementName
            return leafFn(check);
        }

        const compound = check as EntitlementCheck;

        // any: break on first true
        if (compound.type === 'anyOf') {
            for (const innerCheck of compound.checks) {
                const innerResult = await this.traverseEntitlementChecks(innerCheck, leafFn, predFn);
                if (innerResult !== undefined && (await predFn(innerResult))) {
                    return innerResult;
                }
            }
            return undefined;
        }

        // all: break on first false
        if (compound.type === 'allOf') {
            let innerResult;
            for (const innerCheck of compound.checks) {
                innerResult = await this.traverseEntitlementChecks(innerCheck, leafFn, predFn);
                if (innerResult === undefined || !(await predFn(innerResult))) {
                    return undefined;
                }
            }
            return innerResult;
        }

        // shouldn't ever happen
        logger.error({ msg: 'Unknown entitlement check type in traverseEntitlementChecks', check });
        return undefined;
    }

    private async checkDependentEntitlements<T extends EntitlementNameExcluding<EntitlementNameLimit>>(
        teamId: TeamId,
        entitlement: T,
        planAndLimits?: PlanAndLimits,
        options?: CheckEntitlementOptionsByName[T],
        client?: SimpleClient
    ): Promise<{ entitled: boolean; entitlementInfo?: EntitlementInfoByName[T] }> {
        const includeLimitOverrides =
            (!!options && 'includeLimitOverrides' in options && options?.includeLimitOverrides) ||
            this.billingShouldIncludeLimitOverrides(teamId).dependantEntitlements;

        const { definition } = await this.getTeamEntitlementDefinition(
            teamId,
            entitlement,
            planAndLimits,
            { ...options, includeLimitOverrides },
            client
        );

        if (!definition.checks) {
            return { entitled: true };
        }

        const result = await this.traverseEntitlementChecks(
            definition.checks,
            async checkEntitlement => {
                const info = await this.getEntitlementInfo(teamId, checkEntitlement, options, client);
                return {
                    entitled: info.entitled,
                    entitlementInfo: info as EntitlementInfoByName[T],
                };
            },
            async innerResult => innerResult.entitled
        );

        return result ?? { entitled: false };
    }

    async checkEntitlement<T extends EntitlementNameExcluding<EntitlementNameLimit>>(
        teamId: TeamId,
        entitlement: T,
        options?: CheckEntitlementOptionsByName[T],
        client?: SimpleClient
    ): Promise<boolean> {
        const increment = isIncrementEntitlementOptions(options) ? options.increment : undefined;

        const callback = async (): Promise<{ entitled: boolean; entitlementInfo?: EntitlementInfoByName[T] }> => {
            // checks dependent entitlements in .entitled
            const entitlementInfo = await this.getEntitlement(
                teamId,
                entitlement,
                {
                    ...(options ?? {}),
                    ...(increment && { doNotCache: true, invalidateCache: true }),
                },
                client
            );

            if (!entitlementInfo.entitled) {
                // short circuit - no need to check usage service
                return { entitled: false, entitlementInfo };
            }

            const { definition } = await this.getTeamEntitlementDefinition(
                teamId,
                entitlement,
                null,
                // limit overrides are not needed here as paywallService is only checked here
                { ...options, includeLimitOverrides: false },
                client
            );

            if (definition.paywallService) {
                if (!this.paywallClient) {
                    throw new PaywallError('Paywall client not initialized', 'PAYWALL_026', 500);
                }
                const isPaywallExceeded = await this.paywallClient.checkPaywallLimit(
                    String(teamId),
                    entitlement,
                    increment
                );

                // can omit entitlementInfo.entitled check since we already checked it above
                return { entitled: !isPaywallExceeded, entitlementInfo };
            }

            if (isEntitlementInfoLimit(entitlementInfo)) {
                throw new PaywallError(
                    'This function is not supported for limits, try checkEntitlementLimit',
                    'PAYWALL_006',
                    400
                );
            }

            return { entitled: entitlementInfo.entitled, entitlementInfo };
        };

        const { entitled, entitlementInfo } = await this.withMetrics('checkEntitlement', callback, { entitlement });

        if (!entitled && options?.throwOnDeny) {
            const includeLimitOverrides =
                (!!options && 'includeLimitOverrides' in options && options?.includeLimitOverrides) ||
                this.billingShouldIncludeLimitOverrides(teamId).checkEntitlement;

            const { definition } = await this.getTeamEntitlementDefinition(
                teamId,
                entitlement,
                null,
                { ...options, includeLimitOverrides },
                client
            );
            throw new PaywallError(
                this.getErrorMessage(definition, entitlementInfo),
                definition.customError?.code ?? 'PAYWALL_004',
                definition.customError?.statusCode ?? 403,
                { skipLogging: true }
            );
        }

        if (entitlementInfo && this.shouldTrackUsageLimitHit(teamId)) {
            this.trackUsageLimitHit<T>(teamId, entitlement, entitlementInfo);
        }

        return entitled;
    }

    private async checkDependentEntitlementLimits<T extends EntitlementNameLimit>(
        teamId: TeamId,
        entitlement: T,
        planAndLimits?: PlanAndLimits,
        options?: CheckEntitlementOptionsByName[T],
        client?: SimpleClient
    ): Promise<{ entitled: boolean; entitlementInfo?: EntitlementInfoByName[T] }> {
        const includeLimitOverrides =
            (!!options && 'includeLimitOverrides' in options && options?.includeLimitOverrides) ||
            this.billingShouldIncludeLimitOverrides(teamId).dependantEntitlements;

        const { definition } = await this.getTeamEntitlementDefinition(
            teamId,
            entitlement,
            planAndLimits,
            { ...options, includeLimitOverrides },
            client
        );
        if (!definition.checks) {
            return { entitled: true };
        }

        const result = await this.traverseEntitlementChecks(
            definition.checks,
            async checkEntitlement => {
                const info = await this.getEntitlement(teamId, checkEntitlement, options, client);
                return {
                    entitled: info.entitled,
                    entitlementInfo: info as EntitlementInfoByName[T],
                };
            },
            async innerResult => (await innerResult).entitled
        );

        return result ?? { entitled: false };
    }

    async checkEntitlementLimit<T extends EntitlementNameLimit>(
        teamId: TeamId,
        entitlement: T,
        usage: number,
        options?: CheckEntitlementOptionsByName[T],
        client?: SimpleClient
    ): Promise<boolean> {
        const entitlementInfo = await this.withMetrics(
            'checkEntitlementLimit',
            async () => {
                const innerInfo = await this.getEntitlement(teamId, entitlement, options, client);

                if (!isEntitlementInfoLimit(innerInfo)) {
                    throw new PaywallError(
                        'This function is only supported for limits, try checkEntitlement',
                        'PAYWALL_006',
                        400
                    );
                }

                const { entitled } = await this.checkDependentEntitlementLimits(
                    teamId,
                    entitlement,
                    null,
                    options,
                    client
                );
                if (!entitled) {
                    return { ...innerInfo, entitled: false };
                }

                return {
                    ...innerInfo,
                    entitled: innerInfo.entitled && Number(innerInfo.limit) >= usage,
                };
            },
            { entitlement }
        );

        if (!entitlementInfo.entitled && options?.throwOnDeny) {
            const includeLimitOverrides =
                (!!options && 'includeLimitOverrides' in options && options?.includeLimitOverrides) ||
                this.billingShouldIncludeLimitOverrides(teamId).checkEntitlementLimit;

            const { definition } = await this.getTeamEntitlementDefinition(
                teamId,
                entitlement,
                null,
                { ...options, includeLimitOverrides },
                client
            );

            throw new PaywallError(
                this.getErrorMessage(definition, entitlementInfo),
                definition.customError?.code ?? 'PAYWALL_004',
                definition.customError?.statusCode ?? 403,
                { skipLogging: true }
            );
        }

        return entitlementInfo.entitled;
    }

    static definitionHasDependentEntitlements(definition: EntitlementDefinitionCounter): boolean {
        return (
            EntitlementService.definitionHasIncrementFactors(definition) ||
            EntitlementService.definitionHasLimitsProxy(definition) ||
            EntitlementService.definitionHasChecks(definition)
        );
    }

    static definitionHasIncrementFactors(definition: EntitlementDefinitionCounter): boolean {
        return (
            ('incrementFactors' in definition && Object.keys(definition.incrementFactors).length > 0) ||
            'incrementFactorsQuery' in definition
        );
    }

    static definitionHasLimitsProxy(definition: EntitlementDefinition | EntitlementDefinitionCounter): boolean {
        return (
            ('limitsProxy' in definition && definition.limitsProxy && Object.keys(definition.limitsProxy).length > 0) ||
            ('limitsProxyQuery' in definition && !!definition.limitsProxyQuery)
        );
    }

    static definitionHasChecks(definition: EntitlementDefinition | EntitlementDefinitionCounter): boolean {
        return 'checks' in definition && definition.checks && definition.checks.checks.length > 0;
    }

    private async getActiveEntitlementPlanIdForTeam(teamId: TeamId, client?: SimpleClient): Promise<PlanId> {
        // probably overkill to use getCurrentPlanInfo here
        const planInfo = await this.entitlementRepository.getCurrentPlanInfo(teamId, client);
        return planInfo.trialPlanId ?? planInfo.planId;
    }

    private async getDependentEntitlementFromProxyQuery(
        definition: EntitlementDefinitionCounter,
        teamId: TeamId,
        client?: SimpleClient
    ): Promise<EntitlementNameCounter | undefined> {
        const dependentEntitlement = await this.entitlementRepository.runLimitsProxyQuery({
            teamId,
            query: definition.limitsProxyQuery,
            wsShardClient: client,
        });

        if (!dependentEntitlement) {
            logger.error({
                msg: 'No dependent entitlement found for limits proxy query',
                teamId,
                entitlement: definition.name,
            });
            logger.debug({
                msg: 'Limits proxy query result',
                limitsProxyQuery: definition.limitsProxyQuery,
                dependentEntitlement,
            });
            return undefined;
        }

        return dependentEntitlement;
    }

    /** note currentPlanId is required */
    private async incrementDependentEntitlementLimitsProxy(
        teamId: TeamId,
        currentPlanId: PlanId,
        definition: EntitlementDefinitionCounter,
        options?: IncrementEntitlementOptions,
        client?: SimpleClient
    ): Promise<unknown> {
        const hasStaticProxy = definition.limitsProxy && Object.keys(definition.limitsProxy).length > 0;
        const hasDynamicProxy = !!definition.limitsProxyQuery;

        let dependentEntitlement: EntitlementNameCounter | undefined;

        if (hasStaticProxy) {
            dependentEntitlement = definition.limitsProxy[coerceToPlanIdsSafe(currentPlanId)] as EntitlementNameCounter;
        } else if (hasDynamicProxy) {
            // XXX if we have more entitlements which use this feature heavily, we'll have a performance problem
            dependentEntitlement = await this.getDependentEntitlementFromProxyQuery(definition, teamId, client);
        } else {
            return Promise.resolve();
        }

        if (!dependentEntitlement) {
            return Promise.resolve();
        }

        // NOTE this might not be needed: incrementEntitlement will clear the cache - but better safe than sorry
        await this.invalidateCacheForTeamEntitlement(teamId, definition.name);

        const dependentDefinition = this.getDefinition(dependentEntitlement);

        if (dependentDefinition.type !== EntitlementType.Counter) {
            return Promise.resolve();
        }

        const incrementFactors = await this.getIncrementFactors(definition, teamId);

        const factor = (incrementFactors as Record<string, number>)?.[dependentEntitlement] ?? 1;
        return this.incrementEntitlement(
            teamId,
            dependentEntitlement,
            { increment: (options?.increment ?? 1) * factor },
            client
        );
    }

    private async incrementDependentEntitlementIncrementFactors(
        teamId: TeamId,
        definition: EntitlementDefinitionCounter,
        options?: IncrementEntitlementOptions,
        client?: SimpleClient
    ): Promise<unknown> {
        if (!EntitlementService.definitionHasIncrementFactors(definition)) {
            return Promise.resolve();
        }

        const incrementFactors = await this.getIncrementFactors(definition, teamId);

        return Promise.all(
            (Object.keys(incrementFactors) as Array<EntitlementNameCounter>).map(
                async (dependentEntitlement: EntitlementNameCounter) => {
                    const factor = (incrementFactors as Record<string, number>)?.[dependentEntitlement];
                    if (factor === undefined) {
                        return Promise.resolve();
                    }
                    return this.incrementEntitlement(
                        teamId,
                        dependentEntitlement,
                        { increment: (options?.increment ?? 1) * factor },
                        client
                    );
                }
            )
        );
    }

    /** low level function for unconditional increment; @see checkEntitlement with `increment` option for conditional
     *
     * increments dependent entitlements
     */
    async incrementEntitlement(
        teamId: TeamId,
        entitlement: EntitlementNameCounter,
        options?: IncrementEntitlementOptions,
        client?: SimpleClient
    ): Promise<void> {
        const callback = async () => {
            this.validateTeamId(teamId);

            // If the increment value is explicitly 0, then noop
            if (options?.increment === 0) {
                return;
            }

            const [routingInfo] = await this.handleRouting([teamId], [entitlement]);

            if (routingInfo.shardId !== this.configService.get<string>('sharding.shard_id')) {
                logger.warn({
                    msg: 'incrementing entitlement on a different shard',
                    entitlement,
                    teamId,
                    shardId: routingInfo.shardId,
                });
                this.metricsClient.increment('entitlements.routedEntitlementChecks.count', 1, {
                    shardId: routingInfo.shardId,
                });
                const entitlementsClient = await this.getClient(routingInfo.shardId);
                await entitlementsClient.incrementEntitlement(String(teamId), entitlement, options);
                return;
            }

            const definition = this.getDefinition(entitlement, { teamId });

            if (!isEntitlementDefinitionCounter(definition)) {
                throw new PaywallError('counter expected in increment', 'PAYWALL_006', 400);
            }

            // Invalidate the entitlement being incremented and any parents
            // XXX this should be possible to do only at the leaf, but it fails tests when done this way
            await this.invalidateCacheForTeamEntitlement(teamId, [
                entitlement,
                ...this.getReverseEntitlementDependenciesFor(entitlement),
            ]);

            if (EntitlementService.definitionHasDependentEntitlements(definition)) {
                // most entitlements don't have dependent entitlements - avoid extra DB calls!
                const currentPlanId =
                    options?.currentPlanId ?? (await this.getActiveEntitlementPlanIdForTeam(teamId, client));

                // if the entitlement has limitsProxy, it will be incremented ONLY by the proxy
                // otherwise, it will be incremented by the incrementFactors
                if (EntitlementService.definitionHasLimitsProxy(definition)) {
                    await this.incrementDependentEntitlementLimitsProxy(
                        teamId,
                        currentPlanId,
                        definition,
                        options,
                        client
                    );
                } else if (EntitlementService.definitionHasIncrementFactors(definition)) {
                    await this.incrementDependentEntitlementIncrementFactors(teamId, definition, options, client);
                }
            }

            if (definition.paywallService) {
                if (!this.paywallClient) {
                    throw new PaywallError('Paywall client not initialized', 'PAYWALL_026', 500);
                }
                if (definition.usageServiceCounterType === BillingUsageServiceCounterType.CounterOnly) {
                    const entitlementInfo = await this.getEntitlement(
                        teamId,
                        entitlement,
                        { skipUsage: true, invalidateCache: true, doNotCache: true },
                        client
                    );
                    await this.paywallClient.incrementRule(String(teamId), entitlement, options?.increment ?? 1, {
                        newLimit: +entitlementInfo.limit,
                        clearExceeded: true,
                    });
                } else if (definition.usageServiceCounterType === BillingUsageServiceCounterType.CounterAndLimit) {
                    await this.paywallClient.incrementRule(String(teamId), entitlement, options?.increment ?? 1);
                }
                return;
            }

            const entitlementInfo = await this.getEntitlement(
                teamId,
                entitlement,
                { skipUsage: true, invalidateCache: true, doNotCache: true },
                client
            );

            if (
                options?.skipOnInfiniteLimit &&
                isEntitlementInfoCounter(entitlementInfo) &&
                entitlementInfo.limit === 'Infinity'
            ) {
                return;
            }

            if (definition.teamLimitColumn) {
                const { preUpdateValue, postUpdateValue } = await this.entitlementRepository.incrementLimits(
                    teamId,
                    definition,
                    options?.increment ?? 1,
                    client
                );

                await this.scheduleCounterUsageEmailIfNecessary(
                    teamId,
                    definition,
                    preUpdateValue,
                    postUpdateValue,
                    entitlementInfo.limit,
                    client
                );
            }
        };

        return this.withMetrics('incrementEntitlement', callback, { entitlement });
    }

    public async deleteEntitlementRuleAndCounter(teamId: TeamId, entitlement: EntitlementNameCounter) {
        this.validateTeamId(teamId);
        const definition = this.getDefinition(entitlement, { teamId });
        if (!isEntitlementDefinitionCounter(definition)) {
            logger.info({ msg: 'no counter definition for entitlement', entitlement });
            return;
        }
        if (!definition.paywallService) {
            logger.info({ msg: 'no paywall service for entitlement', entitlement });
            return;
        }
        if (!this.paywallClient) {
            throw new PaywallError('Paywall client not initialized', 'PAYWALL_026', 500);
        }
        await this.invalidateCacheForTeamEntitlement(teamId, entitlement);
        await this.paywallClient.deleteRule(String(teamId), entitlement);
    }

    public async deleteEntitlementCounter(teamId: TeamId, entitlement: EntitlementNameCounter) {
        this.validateTeamId(teamId);
        const definition = this.getDefinition(entitlement, { teamId });
        if (!isEntitlementDefinitionCounter(definition)) {
            logger.info({ msg: 'no counter definition for entitlement', entitlement });
            return;
        }
        if (!definition.paywallService) {
            logger.info({ msg: 'no paywall service for entitlement', entitlement });
            return;
        }
        if (!this.paywallClient) {
            throw new PaywallError('Paywall client not initialized', 'PAYWALL_026', 500);
        }
        await this.invalidateCacheForTeamEntitlement(teamId, entitlement);
        await this.paywallClient.deleteCounter(String(teamId), entitlement);
    }

    /** does not reset dependent entitlements */
    async resetEntitlementCounter(
        teamId: TeamId,
        entitlement: EntitlementName,
        resetValue: number,
        client?: SimpleClient
    ) {
        await this.wrongShardGuard([teamId]);
        this.validateTeamId(teamId);
        const definition = this.getDefinition(entitlement, { teamId });
        const entitlementInfo = await this.getEntitlement(
            teamId,
            entitlement as EntitlementNameCounter,
            { skipUsage: true, invalidateCache: true, doNotCache: true },
            client
        );

        if (isEntitlementDefinitionCounter(definition) && definition.paywallService) {
            if (!this.paywallClient) {
                throw new PaywallError('Paywall client not initialized', 'PAYWALL_026', 500);
            }

            if (
                definition.usageServiceCounterType === BillingUsageServiceCounterType.CounterOnly &&
                entitlementInfo?.limit != null
            ) {
                return this.paywallClient.resetRule(String(teamId), entitlement, resetValue, {
                    newLimit: +entitlementInfo.limit,
                    clearExceeded: true,
                });
            }
            if (definition.usageServiceCounterType === BillingUsageServiceCounterType.CounterAndLimit) {
                return this.paywallClient.resetRule(String(teamId), entitlement, resetValue);
            }
        }

        await this.invalidateCacheForTeamEntitlement(teamId, entitlement);
        const { preUpdateValue, postUpdateValue } = await this.entitlementRepository.resetLimits(
            teamId,
            definition,
            resetValue,
            client
        );

        if (isEntitlementDefinitionCounter(definition)) {
            await this.scheduleCounterUsageEmailIfNecessary(
                teamId,
                definition,
                preUpdateValue,
                postUpdateValue,
                entitlementInfo.limit,
                client
            );
        }

        return { preUpdateValue, postUpdateValue };
    }

    public async setEntitlementCounterLimit(teamId: TeamId, entitlement: EntitlementNameCounter, limit: number) {
        this.validateTeamId(teamId);
        const definition = this.getDefinition(entitlement, { teamId });
        if (!isEntitlementDefinitionCounter(definition)) {
            throw new PaywallError('counter or limit expected in setLimitEntitlement', 'PAYWALL_006', 400);
        }
        if (!definition.paywallService) {
            throw new PaywallError('paywall service expected in setLimitEntitlement', 'PAYWALL_008', 400);
        }
        if (!this.paywallClient) {
            throw new PaywallError('paywall client not initialized', 'PAYWALL_008', 500);
        }
        if (definition.usageServiceCounterType === BillingUsageServiceCounterType.CounterOnly) {
            throw new PaywallError('cannot set limit for counter only entitlement', 'PAYWALL_006', 400);
        }
        await this.invalidateCacheForTeamEntitlement(teamId, entitlement);
        return this.paywallClient.updateRuleRestriction(String(teamId), entitlement, limit);
    }

    private getEntitlementInfoCacheKey<T extends EntitlementName>(
        teamId: TeamId,
        entitlement: T,
        options?: GetEntitlementOptionsByName[T]
    ) {
        return this.getCacheKey('info', { teamId, extra: `${entitlement}`, options });
    }

    private async getEntitlementInfo<T extends EntitlementName>(
        teamId: TeamId,
        entitlement: T,
        options?: GetEntitlementOptionsByName[T],
        client?: SimpleClient
    ): Promise<EntitlementInfoByName[T]> {
        this.validateTeamId(teamId);

        const { definition, planAndLimits, planId } = await this.getTeamEntitlementDefinition(
            teamId,
            entitlement,
            null,
            // includeLimitOverrides is always true as planAndLimits are required here
            { ...options, includeLimitOverrides: true },
            client
        );

        this.debugLog({
            msg: 'getEntitlementInfo',
            teamId,
            entitlement,
            options,
            planId,
        });

        const shouldCache =
            definition.paywallService ||
            ('limitQuery' in definition && definition.limitQuery) ||
            ('limitsProxyQuery' in definition && definition.limitsProxyQuery) ||
            ('entitledQuery' in definition && definition.entitledQuery) ||
            ('globalEntitledQuery' in definition && definition.globalEntitledQuery);

        // only cache entitlements which need to hit external services and/or the DB to get computed
        let cacheKey: string;
        if (shouldCache) {
            cacheKey = this.getEntitlementInfoCacheKey(teamId, entitlement);
            const cachedResult = await this.handleEntitlementInfoCacheGet<EntitlementInfoByName[T]>(
                'entitlement',
                cacheKey,
                {
                    teamId: String(teamId),
                    entitlement,
                },
                options,
                teamId
            );
            if (cachedResult) {
                return cachedResult;
            }
        }

        // dependencies need to be checked - not enough to compare usage to limit
        // ...but avoid needless recursion - the top level check will be enough
        const deepEntitled =
            !!options?.skipDependencies ||
            (isEntitlementDefinitionLimit(definition)
                ? await this.checkDependentEntitlementLimits(
                      teamId,
                      entitlement as EntitlementNameLimit,
                      planAndLimits,
                      {
                          ...options,
                          skipDependencies: true,
                          doNotCache: options?.doNotCache,
                          invalidateCache: options?.invalidateCache,
                      },
                      client
                  )
                : await this.checkDependentEntitlements(
                      teamId,
                      entitlement as EntitlementNameExcluding<EntitlementNameLimit>,
                      planAndLimits,
                      {
                          ...options,
                          skipDependencies: true,
                          doNotCache: options?.doNotCache,
                          invalidateCache: options?.invalidateCache,
                      },
                      client
                  )
            ).entitled;

        const currentDef = options?.skipDependencies
            ? definition
            : (await this.getDefinitionsFromProxy(definition, planId, teamId, client)).at(-1) ?? definition;

        let result: EntitlementInfoByName[T];

        if (currentDef.paywallService) {
            if (!this.paywallClient) {
                throw new PaywallError('Paywall client not initialized', 'PAYWALL_026', 500);
            }

            const ruleInfo = await this.paywallClient.getRuleInfo<T>(String(teamId), currentDef.name as T);

            if (!ruleInfo) {
                // this is expected for workspaces which did not upgrade in a while
                logger.info({
                    msg: 'ruleInfo not found for entitlement in usage service',
                    startEntitlement: entitlement,
                    currentEntitlement: currentDef.name,
                    team_id: teamId,
                });
                if (isEntitlementDefinitionCounter(currentDef)) {
                    const incrementFactors = await this.getIncrementFactors(currentDef, teamId);
                    const limit = await this.getDefinitionLimit(currentDef, planId, teamId, client);
                    const planLimits = await this.getDefinitionPlanLimits(currentDef, planId, teamId, client);

                    result = {
                        type: currentDef.type,
                        entitled: limit !== 0,
                        limit,
                        usage: 0,
                        usageLeft: limit,
                        ...(planLimits != null && { planLimits }),
                        ...(Object.keys(incrementFactors).length > 0 && { incrementFactors }),
                    } as EntitlementInfoByName[T];
                } else {
                    // TODO return a sane default - this is a basic hotfix failsafe
                    result = {
                        type: definition.type,
                        entitled: true, // failsafe - default to entitled
                    } as EntitlementInfoByName[T];
                }
            } else if (currentDef.type === EntitlementType.Counter) {
                const definitionLimit = await this.getDefinitionLimit(currentDef, planId, teamId, client);
                const ruleInfoLimit = (ruleInfo as { limit?: number }).limit;
                const ruleLimit = this.ruleLimitTransformer.from(ruleInfoLimit);
                const ruleUsage = (ruleInfo as { usage?: number }).usage;
                const trueLimit =
                    currentDef.usageServiceCounterType === BillingUsageServiceCounterType.CounterAndLimit
                        ? ruleLimit
                        : definitionLimit ?? undefined;

                const shallowEntitled = ruleUsage != null ? trueLimit > ruleUsage : ruleInfo.entitled;
                const incrementFactors = await this.getIncrementFactors(currentDef, teamId);
                const planLimits = this.ruleLimitTransformer.stringifyInfinity(
                    await this.getDefinitionPlanLimits(currentDef, planId, teamId, client)
                );

                result = {
                    ...ruleInfo,
                    limit: this.ruleLimitTransformer.stringifyInfinity(trueLimit),
                    // handle infinite limits, but also never return negative usageLeft
                    usageLeft: this.ruleLimitTransformer.stringifyInfinity(
                        ruleUsage !== undefined ? Math.max(0, trueLimit - ruleUsage) : undefined
                    ),
                    entitled: deepEntitled && shallowEntitled,
                    ...(planLimits != null && { planLimits }),
                    ...(Object.keys(incrementFactors).length > 0 && { incrementFactors }),
                };
            } else {
                result = { ...ruleInfo, entitled: ruleInfo.entitled && deepEntitled };
            }
        } else {
            const usage = planAndLimits?.[`${currentDef.name}_usage`] ?? 0;
            const baseResult = (await this.parseEntitlementInfoWithEntitledQueries({
                teamId,
                planId,
                client,
                definition: currentDef,
                usage,
                limitOverride: null,
                options,
            })) as EntitlementInfoByName[T];
            result = { ...baseResult, entitled: deepEntitled && baseResult.entitled };
        }

        // Track resolution metrics
        this.metricsClient.increment('entitlements.resolved.allowed', result.entitled ? 1 : 0, {
            planId: String(planId),
            entitlement: String(entitlement),
        });
        this.metricsClient.increment('entitlements.resolved.denied', result.entitled ? 0 : 1, {
            planId: String(planId),
            entitlement: String(entitlement),
        });

        if (shouldCache) {
            await this.handleEntitlementInfoCacheSet(cacheKey, result, options ?? {}, teamId);
        }

        return result;
    }

    private async getEntitlementsInfo(
        teamId: TeamId,
        entitlementsWithOption?: EntitlementNameWithOptions[],
        options?: GetEntitlementsOptions,
        client?: SimpleClient
    ) {
        const entitlements = this.getEntitlementsNameOfTypes(entitlementsWithOption, options?.types);

        for (const entitlement of entitlements ?? []) {
            this.debugLog({
                msg: 'getEntitlementsInfo',
                entitlement,
                teamId,
                options,
                entitlementsWithOption,
            });
        }

        if (this.billingShouldGetAllEntitlementsAlwaysFullData(String(teamId)) || options?.fullData) {
            const definitionsAndOptions = entitlements.map(entitlement => {
                const { entitlement: entitlementName, ...entitlementOptions } =
                    typeof entitlement === 'string' ? { entitlement } : entitlement;
                return { definition: { name: entitlementName }, entitlementOptions };
            });

            // Run getEntitlementInfo for each entitlement - expensive!
            return this.getEntitlementsInfoWithUsage(teamId, definitionsAndOptions, options, client);
        }

        // fast but inaccurate path - useful for checking e.g. paywalls, upgrade paths
        // TODO consider how limitsProxyQuery impacts performance
        return this.getEntitlementsInfoWithoutUsage(teamId, options, client, entitlementsWithOption);
    }

    private async getEntitlementsInfoWithoutUsage(
        teamId: TeamId,
        options: GetEntitlementsOptions,
        client: SimpleClient,
        entitlements: EntitlementNameWithOptions[]
    ) {
        const definitions = entitlements.map(entitlement =>
            this.getDefinition(typeof entitlement === 'string' ? entitlement : entitlement.entitlement, {
                ...options,
                teamId,
            })
        );

        const planAndLimit = await this.entitlementRepository.getPlanAndLimits(teamId, definitions, options, client);

        const skuTrialDefinition = await this.getTeamSkuTrial(teamId);

        const definitionsAndOptions = await Promise.all(
            entitlements.map(async entitlement => {
                const { entitlement: entitlementName, ...entitlementOptions } =
                    typeof entitlement === 'string' ? { entitlement } : entitlement;
                const definition = await this.generateFullTeamEntitlementDefinition(
                    teamId,
                    entitlementName,
                    skuTrialDefinition,
                    planAndLimit,
                    { includeLimitOverrides: true, keepOriginalEntitlement: true, ...entitlementOptions }
                );
                return { definition, name: entitlementName, entitlementOptions };
            })
        );

        const asyncPool = pLimit(25);
        const entitlementsResults = await Promise.all(
            definitionsAndOptions.map(({ definition, entitlementOptions }) =>
                asyncPool(async () => {
                    const entitlementInfo = await this.parseEntitlementInfoWithEntitledQueries({
                        teamId,
                        planId: this.getActualPlanId(definition.definition, planAndLimit, options),
                        client,
                        definition: definition.definition,
                        usage: definition.planAndLimits?.[`${definition.definition.name}_usage`] ?? 0,
                        limitOverride: null,
                        options: { ...options, ...entitlementOptions },
                    });

                    const entitlementName = definition.definition.name;
                    return {
                        [entitlementName]: {
                            ...entitlementInfo,
                            dataIsComplete:
                                !this.entitlementDefinitions[entitlementName].paywallService &&
                                !EntitlementService.definitionHasLimitsProxy(
                                    this.entitlementDefinitions[entitlementName]
                                ),
                        } as never,
                    };
                })
            )
        );

        return entitlementsResults.reduce((acc, result) => ({ ...acc, ...result }), {} as GetEntitlementsResult);
    }

    private async getEntitlementsInfoWithUsage(
        teamId: TeamId,
        definitionsAndOptions: {
            definition: {
                name: EntitlementName;
            };
            entitlementOptions: {
                entitlementOverride?: string;
                skipStatus?: boolean;
                skipUsage?: boolean;
                testUsage?: number;
            };
        }[],
        options: GetEntitlementsOptions,
        client: SimpleClient
    ) {
        const entitlementsResult = await Promise.all(
            definitionsAndOptions.map(async ({ definition, entitlementOptions }) => {
                const entitlementInfo = await this.getEntitlement(
                    teamId,
                    definition.name,
                    {
                        ...options,
                        ...entitlementOptions,
                    },
                    client
                );
                const planLimits = this.ruleLimitTransformer.stringifyInfinity(
                    (entitlementInfo as { planLimits?: number }).planLimits
                );
                if (isEntitlementInfoLimit(entitlementInfo)) {
                    const fixedInfinityInfo = {
                        ...entitlementInfo,
                        limit: this.ruleLimitTransformer.stringifyInfinity(entitlementInfo.limit),
                        ...(planLimits != null && { planLimits }),
                    };
                    return [definition.name, fixedInfinityInfo] as const;
                }
                if (isEntitlementInfoCounter(entitlementInfo)) {
                    const fixedInfinityInfo = {
                        ...entitlementInfo,
                        limit: this.ruleLimitTransformer.stringifyInfinity(entitlementInfo.limit),
                        usageLeft: this.ruleLimitTransformer.stringifyInfinity(entitlementInfo.usageLeft),
                        ...(planLimits != null && { planLimits }),
                    };
                    return [definition.name, fixedInfinityInfo] as const;
                }
                return [definition.name, entitlementInfo] as const;
            })
        ).then(entries => Object.fromEntries(entries) as GetEntitlementsResult);

        return entitlementsResult;
    }

    private async processEntitlementInfoUsingEntitledQueries<T extends EntitlementName>(
        teamId: TeamId,
        entitlementDefinition: EntitlementDefinition,
        entitlementInfo: EntitlementInfoByName[T],
        client: SimpleClient
    ): Promise<EntitlementInfoByName[T]> {
        if (entitlementInfo.entitled) {
            return entitlementInfo;
        }

        if (isEntitlementDefinitionFlag(entitlementDefinition)) {
            if (entitlementDefinition.entitledQuery) {
                const isEntitled = await this.entitlementRepository.runEntitledQuery({
                    teamId,
                    query: entitlementDefinition.entitledQuery,
                    isGlobal: false,
                    client,
                });
                return {
                    ...entitlementInfo,
                    entitled: isEntitled,
                };
            }
            if (entitlementDefinition.globalEntitledQuery) {
                const isEntitled = await this.entitlementRepository.runEntitledQuery({
                    teamId,
                    query: entitlementDefinition.globalEntitledQuery,
                    isGlobal: true,
                    client,
                });
                return {
                    ...entitlementInfo,
                    entitled: isEntitled,
                };
            }
        }

        return entitlementInfo;
    }

    /**
     * If team context is available, use parseEntitlementInfoWithEntitledQueries instead
     */
    private async parseEntitlementInfo(
        planId: number,
        definition: EntitlementDefinition,
        usage: `${number}` | number,
        limitOverride: `${number}` | number | null,
        teamId?: TeamId,
        options?: GetEntitlementOptions
    ): Promise<EntitlementInfo> {
        if (isEntitlementDefinitionFlag(definition)) {
            const isAvailableInPlan = await this.checkIfAvailableInPlan(definition, planId, teamId, options?.client);
            return {
                type: definition.type,
                entitled: isAvailableInPlan,
            };
        }

        const currentUsage = this.isNullish(usage) ? 0 : Number(usage);
        const planLimits = await this.getDefinitionPlanLimits(definition, planId, teamId, options?.client);
        const limit = this.ruleLimitTransformer.from(
            this.isNullish(limitOverride)
                ? await this.getDefinitionLimit(definition, planId, teamId, options?.client)
                : Number(limitOverride)
        );
        const usageLeft = limit - currentUsage > 0 ? limit - currentUsage : 0;
        const isAboveLimit = options?.skipUsage ? limit > 0 : usageLeft - (options?.testUsage ?? 0) > 0;

        if (isEntitlementDefinitionLimit(definition)) {
            return {
                type: definition.type,
                entitled: isAboveLimit,
                limit: limit === Infinity ? 'Infinity' : limit,
            };
        }

        return {
            type: definition.type,
            entitled: isAboveLimit,
            ...(planLimits != null && {
                planLimits: planLimits === Infinity || planLimits === -1 ? 'Infinity' : planLimits,
            }),
            limit: limit === Infinity || limit === -1 ? 'Infinity' : limit,
            usage: currentUsage,
            usageLeft: usageLeft === Infinity ? 'Infinity' : usageLeft, // just Infinity is not JSON-serializable (translates to null instead)
            ...(options?.testUsage && { usageLeftAfterTest: usageLeft - options.testUsage }),
            ...(options?.skipUsage && { skipUsage: true }),
        };
    }

    private async parseEntitlementInfoWithEntitledQueries<T extends EntitlementName>({
        teamId,
        planId,
        client,
        definition,
        usage,
        limitOverride,
        options,
    }: {
        teamId: TeamId;
        planId: number;
        client: SimpleClient;
        definition: EntitlementDefinition;
        usage: `${number}` | number;
        limitOverride: `${number}` | number | null;
        options?: GetEntitlementOptions;
    }): Promise<EntitlementInfoByName[T]> {
        const parseResult = (await this.parseEntitlementInfo(
            planId,
            definition,
            usage,
            limitOverride,
            teamId,
            options
        )) as EntitlementInfoByName[T];
        const amendedParseResult = await this.processEntitlementInfoUsingEntitledQueries(
            teamId,
            definition,
            parseResult,
            client
        );
        return amendedParseResult;
    }

    private getPlanUpgradePath(
        entitlementName: EntitlementName,
        currEntitlement: GetEntitlementsPlanResult[keyof GetEntitlementsPlanResult],
        nextEntitlement: GetEntitlementsPlanResult[keyof GetEntitlementsPlanResult],
        nextPlanId: PlanId
    ): {
        nextAvailable?: PlanId;
        nextUpgrade?: PlanId;
        topUpgrade?: PlanId;
    } {
        if (nextEntitlement.entitled) {
            const isFlag =
                currEntitlement.type === EntitlementType.Flag || nextEntitlement.type === EntitlementType.Flag;
            const isChange = isFlag
                ? !currEntitlement.entitled && nextEntitlement.entitled
                : nextEntitlement.limit > currEntitlement.limit;
            const isInfinity = isFlag ? isChange : isChange && nextEntitlement.limit === 'Infinity';

            /**
             * As stated in: https://app.clickup-stg.com/t/333/CLK-720798?comment=98070003129603&threadedComment=98070003136071, currently
             * we're hardcoding the logic for skipping recommendation of upgrade to Unlimited 2503 for ProjectStorageLimit entitlement.
             *
             * It will result in recommendation to Business Tier.
             */
            const shouldSkipRecommendation =
                nextPlanId === PlanIds.Unlimited2503 && entitlementName === EntitlementNameLimit.ProjectStorageLimit;

            return {
                nextAvailable: nextPlanId,
                ...(isChange && !shouldSkipRecommendation && { nextUpgrade: nextPlanId }),
                ...(isInfinity && !shouldSkipRecommendation && { topUpgrade: nextPlanId }),
            };
        }

        return {};
    }

    private getActualPlanId(
        definition: EntitlementDefinition,
        planAndLimit: { downgradedPlanId?: number; planId: number },
        options?: { skipStatus?: boolean }
    ) {
        const shouldValidateStatus = planAndLimit.downgradedPlanId && !definition.skipStatus && !options?.skipStatus;
        const planId = shouldValidateStatus ? planAndLimit.downgradedPlanId : planAndLimit.planId;

        return planId;
    }

    private isNullish(value: unknown): boolean {
        return value === undefined || value === null;
    }

    private getEntitlementsNamesFromTypes(
        entitlements: EntitlementNameWithOptions[],
        types?: (EntitlementType | `${EntitlementType}`)[]
    ): EntitlementName[] {
        const entitlementsWithTypes = this.getEntitlementsNameOfTypes(entitlements, types);

        return entitlementsWithTypes.map(entitlement =>
            typeof entitlement === 'string' ? entitlement : entitlement.entitlement
        );
    }

    private getEntitlementsNameOfTypes(
        entitlements: EntitlementNameWithOptions[],
        types?: (EntitlementType | `${EntitlementType}`)[]
    ): EntitlementNameWithOptions[] {
        if (entitlements?.length) {
            return entitlements;
        }

        if (!types?.length) {
            return Object.values(EntitlementName);
        }

        const entitlementsPerType: EntitlementName[] = [];

        if (types.includes(EntitlementType.Flag)) {
            entitlementsPerType.push(...Object.values(EntitlementNameFlag));
        }

        if (types.includes(EntitlementType.Counter)) {
            entitlementsPerType.push(...Object.values(EntitlementNameCounter));
        }

        if (types.includes(EntitlementType.Limit)) {
            entitlementsPerType.push(...Object.values(EntitlementNameLimit));
        }

        if (types.includes(EntitlementType.ReadOnlyCount)) {
            entitlementsPerType.push(...Object.values(EntitlementNameReadOnlyCount));
        }

        return entitlementsPerType;
    }

    getDefinition<T extends EntitlementName>(
        entitlement: T,
        options?: { entitlementOverride?: string; teamId?: TeamId }
    ): EntitlementDefinitionByName[T] {
        const definition: EntitlementDefinitionConfig[T] = this.entitlementDefinitions[entitlement];

        if (!definition) {
            throw new PaywallError('Wrong Entitlement', 'PAYWALL_002', 400);
        }

        if (options?.entitlementOverride && !definition.overrides[options.entitlementOverride]) {
            throw new PaywallError('Wrong Entitlement', 'PAYWALL_002', 400);
        }

        const result = {
            ...definition,
            ...(options?.entitlementOverride && definition.overrides[options.entitlementOverride]),
        } as EntitlementDefinitionByName[T];

        // availablePlans.includes(planId) <=> limits[planId] === Infinity
        if (
            (definition.type === EntitlementType.Limit || definition.type === EntitlementType.Counter) &&
            'limits' in result
        ) {
            result.limits = {
                ...result.limits,
                ...Object.fromEntries((result.availablePlans ?? []).map(plan => [plan, Infinity])),
            };
            // limits can be overriden by limit queries, sku trials, etc.; plan limits are persisted here
            if (definition.type === EntitlementType.Counter) {
                (result as EntitlementDefinitionCounter).planLimits = result.limits;
            }
        }

        if (
            (entitlement === EntitlementNameFlagEnum.UniversalSearchPrivate ||
                entitlement === EntitlementNameFlagEnum.UniversalSearchShared) &&
            this.connectedSearchTrialIsOn?.()
        ) {
            return {
                ...result,
                availablePlans: Object.values(PlanIds),
            };
        }

        return result;
    }

    private getErrorMessage(definition: EntitlementDefinition, entitlementInfo: EntitlementInfo) {
        if (
            !isEntitlementInfoLimit(entitlementInfo) &&
            !isEntitlementInfoFlag(entitlementInfo) &&
            Number(entitlementInfo.limit) > 0
        ) {
            return (
                definition.customError?.message ??
                `Your plan is limited to {{limit}} usages of feature, {{usage}} usage.`
            )
                .replace('{{limit}}', `${entitlementInfo.limit}`)
                .replace('{{usage}}', `${entitlementInfo.usage}`);
        }

        if (isEntitlementInfoLimit(entitlementInfo) && Number(entitlementInfo.limit) > 0) {
            return (definition.customError?.message ?? `Your plan is limited to {{limit}} usages of feature.`).replace(
                '{{limit}}',
                `${entitlementInfo.limit}`
            );
        }

        return definition.customError?.message ?? `Feature is not available on your plan.`;
    }

    private validateTeamId(teamId: TeamId) {
        if (!teamId) {
            throw new PaywallError('No Workspace provided', 'PAYWALL_001', 400);
        }
    }

    // Increment factors may be dynamic (based on the workspace state) which requires a db call.
    private async getIncrementFactors(definition: EntitlementDefinitionCounter, teamId: TeamId) {
        return 'incrementFactors' in definition
            ? definition.incrementFactors
            : this.entitlementRepository.getIncrementFactors(teamId, definition);
    }

    /**
     * Traverses the chain of limit proxies for a given definition and plan ID.
     * Yields each definition in the chain before returning the final non-proxy definition.
     *
     * @param definition - The initial entitlement definition
     * @param planId - The plan ID to check proxies against
     * @param teamId - The team ID to use for the entitlement repository
     * @param client - The client to use for the entitlement repository
     * @returns A generator that yields each proxy definition and returns the final definition
     */
    private async *traverseLimitsProxy<T extends EntitlementDefinition>(
        definition: T,
        planId: PlanId,
        teamId?: TeamId,
        client?: SimpleClient
    ): AsyncGenerator<T, T, undefined> {
        let currentDef: T = definition;

        while (true) {
            const yieldedDef = { ...currentDef };

            // this is what most of the entitlements run into immediately
            if (
                (!('limitsProxy' in currentDef && currentDef.limitsProxy) || !(planId in currentDef.limitsProxy)) &&
                !('limitsProxyQuery' in currentDef && currentDef.limitsProxyQuery)
            ) {
                yield yieldedDef;
                return;
            }

            yield yieldedDef;

            if ('limitsProxyQuery' in currentDef && currentDef.limitsProxyQuery && teamId) {
                const dependentEntitlement = await this.getDependentEntitlementFromProxyQuery(
                    currentDef as EntitlementDefinitionCounter,
                    teamId,
                    client
                );
                if (!isEntitlementName(dependentEntitlement)) {
                    logger.error({
                        msg: 'invalid dependentEntitlement for entitlement',
                        source: definition.name,
                        parent: yieldedDef.name,
                        cause: dependentEntitlement,
                    });
                    throw new PaywallError('Wrong Entitlement', 'PAYWALL_030', 500);
                }
                currentDef = this.getDefinition(dependentEntitlement) as T;
            } else {
                const proxyName = currentDef.limitsProxy[coerceToPlanIdsSafe(planId)];
                currentDef = this.getDefinition(proxyName) as T;
            }
        }
    }

    private async getDefinitionsFromProxy(
        definition: EntitlementDefinition,
        planId: PlanId,
        teamId?: TeamId,
        skuTrialDefinition?: SkuTrialDefinitionConfig,
        client?: SimpleClient
    ): Promise<EntitlementDefinition[]> {
        const defs: EntitlementDefinition[] = [];
        // Array.fromAsync() is... quite a bit more involved; maybe we don't need all of it.
        // TODO when moving to es2024+ replace this with Array.fromAsync()
        for await (const def of this.traverseLimitsProxy(definition, planId, teamId, client)) {
            defs.push({
                ...def,
                ...((skuTrialDefinition as Record<EntitlementName, 'entitled'>)?.[def.name] === 'entitled' && {
                    ...('limits' in def && {
                        limits: { ...Object.fromEntries(Object.values(PlanIds).map(plan => [plan, Infinity])) },
                        availablePlans: Object.values(PlanIds).map(Number),
                    }),
                }),
            });
        }
        return defs;
    }

    private async getFinalDefinitionFromProxy<T extends EntitlementDefinition>(
        definition: T,
        planId: PlanId,
        teamId: TeamId,
        skuTrialDefinition?: SkuTrialDefinitionConfig,
        client?: SimpleClient
    ): Promise<T> {
        const definitions = await this.getDefinitionsFromProxy(definition, planId, teamId, skuTrialDefinition, client);
        return (definitions.at(-1) ?? definition) as T;
    }

    private async checkIfAvailableInPlan(
        definition: EntitlementDefinition,
        planId: number,
        teamId?: TeamId,
        client?: SimpleClient
    ): Promise<boolean> {
        // if no teamId, cheat - take from workspace independent limits
        // TODO verify if this makes sense, even sort of
        if (!teamId) {
            return definition.availablePlans?.includes(planId) ?? false;
        }

        const defs = await this.getDefinitionsFromProxy(definition, planId, teamId, client);
        // check starting from the leaf
        return defs.reverse().some(def => def.availablePlans?.includes(planId));
    }

    private async getDefinitionPlanLimits(
        definition: Exclude<EntitlementDefinition, EntitlementDefinitionFlag>,
        planId: PlanId,
        teamId?: TeamId,
        client?: SimpleClient
    ): Promise<number | undefined> {
        // First check if available in plan
        if (await this.checkIfAvailableInPlan(definition, Number(planId), teamId, client)) {
            return Infinity;
        }

        // Then check plan limits starting from the leaf limit
        // if no teamId, cheat - take from workspace independent limits

        const defs = await this.getDefinitionsFromProxy(definition, planId, teamId, undefined, client);
        const planIdStr = String(planId);
        // get the first defined plan limit
        const defWithPlanLimit = (defs as { planLimits?: Record<string, number> }[]).find(
            def => 'planLimits' in def && def.planLimits !== undefined && def.planLimits[planIdStr] !== undefined
        );
        return defWithPlanLimit?.planLimits?.[planIdStr] != null
            ? this.ruleLimitTransformer.from(defWithPlanLimit.planLimits[planIdStr])
            : undefined;
    }

    private async getDefinitionLimit(
        definition: Exclude<EntitlementDefinition, EntitlementDefinitionFlag>,
        planId: PlanId,
        teamId?: TeamId,
        skuTrialDefinition?: SkuTrialDefinitionConfig,
        client?: SimpleClient
    ): Promise<number> {
        // First check if available in plan
        if (await this.checkIfAvailableInPlan(definition, Number(planId), teamId, client)) {
            return Infinity;
        }

        // Then check limits starting from the leaf limit
        // if workspace id is not present, the proxy will return some default limit values for plans
        const defs = await this.getDefinitionsFromProxy(definition, planId, teamId, client);
        // only if we don't find a limit, we look at if we even have a limitQuery

        // check if it has limitQuery and if it does, use it
        const planIdStr = String(planId);
        const defWithLimitQuery = defs.find(
            def => (def as EntitlementDefinitionLimit).limitQuery
        ) as EntitlementDefinitionLimit;
        if (defWithLimitQuery && teamId) {
            const result = await this.entitlementRepository.getPlanAndLimits(teamId, [defWithLimitQuery], {}, client);
            if (
                skuTrialDefinition &&
                defWithLimitQuery.name in skuTrialDefinition &&
                (skuTrialDefinition as Record<EntitlementName, 'entitled'>)[defWithLimitQuery.name] === 'entitled'
            ) {
                return Infinity;
            }

            if (result[`${defWithLimitQuery.name}_limit_override`] != null) {
                return this.ruleLimitTransformer.from(+result[`${defWithLimitQuery.name}_limit_override`]);
            }
        }

        // if no limitQuery, or if it didn't return a value, we look at the static limits
        // get the first non-zero limit
        const defWithLimit = defs.find(
            def => (def as EntitlementDefinitionLimit).limits?.[planIdStr]
        ) as EntitlementDefinitionLimit;
        if (
            skuTrialDefinition &&
            defWithLimit.name in skuTrialDefinition &&
            (skuTrialDefinition as Record<EntitlementName, 'entitled'>)[defWithLimit.name] === 'entitled'
        ) {
            return Infinity;
        }
        return defWithLimit?.limits[planIdStr] ?? 0;
    }

    private sendMetrics(metricName: string, startTime: [number, number], tags?: Record<string, string>) {
        this.metricsClient.increment(`entitlements.${metricName}.count`, 1, tags);
        this.metricsClient.timing(`entitlements.${metricName}.timing`, endTimer(startTime), tags);
    }

    private withMetrics<T>(metricName: string, callback: () => Promise<T>, tags?: Record<string, string>): Promise<T>;

    private withMetrics<T>(metricName: string, callback: () => T, tags?: Record<string, string>): T;

    private withMetrics<T>(
        metricName: string,
        callback: () => T | Promise<T>,
        tags?: Record<string, string>
    ): T | Promise<T> {
        const startTime = startTimer();
        try {
            const result = callback();

            if (result instanceof Promise) {
                return result
                    .then(res => {
                        this.sendMetrics(`${metricName}`, startTime, tags);
                        return res;
                    })
                    .catch(err => {
                        this.sendMetrics(`error.${metricName}`, startTime, {
                            ...(tags || {}),
                            errorCode: (err as ClickUpError)?.ECODE || 'UNKNOWN',
                        });
                        throw err;
                    });
            }

            this.sendMetrics(`${metricName}`, startTime, tags);

            return result;
        } catch (err) {
            this.sendMetrics(`error.${metricName}`, startTime, {
                ...(tags || {}),
                errorCode: (err as ClickUpError)?.ECODE || 'UNKNOWN',
            });
            throw err;
        }
    }

    private async getTeamEntitlementDefinition<T extends EntitlementName>(
        teamId: TeamId,
        entitlement: T,
        planAndLimits?: PlanAndLimits,
        options?: {
            entitlementOverride?: string;
            skipUsage?: boolean;
            invalidateCache?: boolean;
            includeLimitOverrides?: boolean;
        },
        client?: SimpleClient
    ): Promise<{
        definition: EntitlementDefinitionByName[T];
        planAndLimits?: PlanAndLimits;
        planId?: number;
    }> {
        const skuTrialDefinition = await this.getTeamSkuTrial(teamId);
        return this.generateFullTeamEntitlementDefinition(
            teamId,
            entitlement,
            skuTrialDefinition,
            planAndLimits,
            options,
            client
        );
    }

    /**
     * The function generates the final version of team entitlement definition based on the given entitlement and options, it also includes limit overrides so it's execution is expensive.
     *
     * The final entitlement's definition is a result of merge the plan definition and the sku trial definition.
     * The sku trial definition takes precedence over the plan definition.
     * If the entitlement is present in the sku trial definition the entitlement will behave as fully open.
     * So the limit will be Infinity if applicable, and status will be entitled.
     */
    private async generateFullTeamEntitlementDefinition<T extends EntitlementName>(
        teamId: TeamId,
        entitlement: T,
        skuTrialDefinition: SkuTrialDefinitionConfig,
        planAndLimit?: PlanAndLimits,
        options?: {
            entitlementOverride?: string;
            skipUsage?: boolean;
            invalidateCache?: boolean;
            skipStatus?: boolean;
            includeLimitOverrides?: boolean;
            keepOriginalEntitlement?: boolean;
        },
        client?: SimpleClient
    ): Promise<{
        definition: EntitlementDefinitionByName[T];
        planAndLimits?: PlanAndLimits;
        planId?: number;
    }> {
        const planDefinition = this.getDefinition(entitlement, { ...options, teamId });

        if (!options?.includeLimitOverrides) {
            return {
                definition: planDefinition,
            };
        }

        // this also fetches limit overrides of dependent entitlements
        const planAndLimits =
            planAndLimit ?? (await this.getPlanAndLimitsWithDependencies(teamId, [planDefinition], options, client));
        const planId = this.getActualPlanId(planDefinition, planAndLimits, options);

        const currentDef = (await this.getFinalDefinitionFromProxy(
            planDefinition,
            planId,
            teamId,
            client
        )) as EntitlementDefinitionByName[T];
        const proxyName = currentDef.name;

        const limitOverrideRaw =
            (skuTrialDefinition as Record<EntitlementName, 'entitled'>)?.[proxyName] === 'entitled'
                ? Infinity
                : planAndLimits?.[`${proxyName}_limit_override`];
        const limitOverride =
            limitOverrideRaw !== undefined && limitOverrideRaw !== null
                ? this.ruleLimitTransformer.from(Number(limitOverrideRaw))
                : null;

        if (
            !this.isNullish(limitOverride) &&
            (isEntitlementDefinitionLimit(currentDef) ||
                isEntitlementDefinitionCounter(currentDef) ||
                isEntitlementDefinitionReadOnlyCountBdr(currentDef))
        ) {
            currentDef.limits = Object.fromEntries(Object.keys(currentDef.limits).map(plan => [plan, limitOverride]));
        }

        return {
            definition: this.joinEntitlementConfigs(
                { ...currentDef, name: options.keepOriginalEntitlement ? entitlement : currentDef.name },
                !!skuTrialDefinition[entitlement]
            ),
            planAndLimits,
            planId,
        };
    }

    private async getTeamSkuTrial(teamId: TeamId): Promise<SkuTrialDefinitionConfig> {
        const activeSkuTrials = await this.skuTrialAccessService.getActiveSkuTrials(teamId);
        // TODO join logic needs to be smarter when more complex sku trial entitlements' configurations are implemented
        return activeSkuTrials.reduce<SkuTrialDefinitionConfig>(
            (acc, { sku_id: skuId }) => ({
                ...acc,
                ...(SkuEntitlementsConfigMap[skuId] ?? {}),
            }),
            {}
        );
    }

    private joinEntitlementConfigs<T extends EntitlementName>(
        config: EntitlementDefinitionByName[T],
        skipStatus: boolean
    ): EntitlementDefinitionByName[T] {
        // TODO implement more complex sku trial entitlements' configurations
        if (isEntitlementDefinitionFlag(config)) {
            return { ...config, availablePlans: skipStatus ? supportedPlanIds : config.availablePlans };
        }
        const planLimits = skipStatus ? config.limits : undefined;

        return {
            ...config,
            ...(planLimits != null && { planLimits }),
            limits: skipStatus
                ? Object.fromEntries(Object.keys(config.limits).map(key => [key, Infinity]))
                : config.limits,
            ...(skipStatus && 'limitQuery' in config && { limitQuery: undefined }),
            ...(skipStatus && 'limitsProxyQuery' in config && { limitsProxyQuery: undefined }),
        };
    }

    private async trackUsageLimitHit<T extends EntitlementName>(
        teamId: TeamId,
        entitlement: T,
        entitlementInfo: EntitlementInfoByName[T]
    ): Promise<void> {
        if (!isEntitlementNameCounter(entitlement) || !isEntitlementInfoCounter(entitlementInfo)) {
            return;
        }

        const skuTrialDefinition = await this.getTeamSkuTrial(teamId);

        const isInitialEntGracePeriodActive =
            !!skuTrialDefinition[entitlement] &&
            !!(await this.skuTrialAccessService.getActiveSkuTrialByType(teamId, SkuTrialId.InitialEntGracePeriod));

        const isUsageLimitHit =
            entitlementInfo.planLimits !== 'Infinity' &&
            typeof entitlementInfo.planLimits === 'number' &&
            entitlementInfo.usage >= entitlementInfo.planLimits;

        if (entitlementInfo.limit === 'Infinity' && isUsageLimitHit && isInitialEntGracePeriodActive) {
            // We don't have access to actual userId here so we're passing 1 instead.
            this.trackTypedEvent(SegmentEvent.UsageLimitHit, FakeUserId, {
                entitlement,
                useCount: entitlementInfo.usage,
            });
        }
    }

    private expandChecks(check: EntitlementCheck | EntitlementName): EntitlementDefinition[] {
        if (typeof check === 'string') {
            return [this.getDefinition(check)];
        }
        return check.checks.flatMap(c => this.expandChecks(c));
    }

    private expandLimitProxy(def: EntitlementDefinition): EntitlementDefinition[] {
        if (!('limitsProxy' in def) || !def.limitsProxy) {
            return [];
        }
        const directProxies = Object.values(def.limitsProxy).map(name => this.getDefinition(name as EntitlementName));
        const nestedProxies = directProxies.flatMap(proxy => this.expandLimitProxy(proxy));
        return [...directProxies, ...nestedProxies];
    }

    private async expandIncrementFactors(def: EntitlementDefinition, teamId: TeamId): Promise<EntitlementDefinition[]> {
        if (!EntitlementService.definitionHasIncrementFactors(def as EntitlementDefinitionCounter)) {
            return [];
        }

        const incrementFactors = await this.getIncrementFactors(def as EntitlementDefinitionCounter, teamId);

        const directFactors = Object.keys(incrementFactors).map(name => this.getDefinition(name as EntitlementName));
        const nestedFactors = await Promise.all(
            directFactors.map(factor => this.expandIncrementFactors(factor, teamId))
        );
        return [...directFactors, ...nestedFactors.flat()];
    }

    private async expandDefinitions(
        definitions: EntitlementDefinition[],
        teamId: TeamId
    ): Promise<EntitlementDefinition[]> {
        const staticDeps = [
            ...definitions,
            ...definitions.flatMap(def => (def.checks ? this.expandChecks(def.checks) : [])),
            ...definitions.flatMap(def => this.expandLimitProxy(def)),
        ];

        const incrementFactorDeps = await Promise.all(definitions.map(def => this.expandIncrementFactors(def, teamId)));

        return [...new Set([...staticDeps, ...incrementFactorDeps.flat()].map(def => def.name))].map(
            name => definitions.find(d => d.name === name) || this.getDefinition(name)
        );
    }

    private async getPlanAndLimitsWithDependencies(
        teamId: TeamId,
        definitions: EntitlementDefinition[],
        options?: { skipUsage?: boolean; invalidateCache?: boolean; skuTrialDefinition?: SkuTrialDefinitionConfig },
        client?: SimpleClient
    ) {
        const expandedDefinitions = await this.expandDefinitions(definitions, teamId);
        return this.entitlementRepository.getPlanAndLimits(teamId, expandedDefinitions, options, client);
    }

    private debugLog(data: Record<string, any>) {
        if (this.removingGlobalReplicationConfig()?.enable_logging) {
            logger.warn({ ...data, logContext: 'EntitlementService:RemovingGlobalData', stack: new Error().stack });
        }
    }

    private async scheduleCounterUsageEmailIfNecessary(
        teamId: TeamId,
        entitlementDefinition: EntitlementDefinitionCounter,
        preIncrementValue: number,
        postIncrementValue: number,
        limit: LimitWithInfinity,
        client: SimpleClient
    ): Promise<void> {
        if (!this.shouldSendEntitlementLifetimeUsageEmails(teamId, { entitlementName: entitlementDefinition.name })) {
            return;
        }

        if (limit === 'Infinity') {
            return;
        }

        const notificationsConfig = entitlementDefinition.usageNotifications;

        if (!notificationsConfig) {
            return;
        }

        const percentUsedAfterChange = (postIncrementValue / limit) * 100;
        const percentUsedBeforeChange = (preIncrementValue / limit) * 100;

        const justPassedThresholds = Object.keys(notificationsConfig.emails)
            .filter(threshold => percentUsedAfterChange >= +threshold && percentUsedBeforeChange < +threshold)
            // sort in descending order to get the highest threshold that was passed
            .sort((a, b) => +b - +a);

        if (!justPassedThresholds.length) {
            return;
        }

        if (notificationsConfig.dontNotifyIf?.addonPresent?.length) {
            try {
                const hasAddons = await this.entitlementRepository.checkAnyOfAddonsPresent(
                    teamId,
                    notificationsConfig.dontNotifyIf.addonPresent,
                    client
                );

                if (hasAddons) {
                    return;
                }
            } catch (error) {
                logger.error({
                    msg: 'Failed to check if addon is present',
                    error,
                    teamId,
                    entitlement: entitlementDefinition.name,
                });
                // intentionally not rethrowing to not fail whole request
            }
        }

        const emailConfig = notificationsConfig.emails[+justPassedThresholds[0]];
        const workflowId = `billing-usage-email-${teamId}-${entitlementDefinition.name}-${justPassedThresholds[0]}`;

        const scheduleWorkflow = async () => {
            try {
                const jobsApi = this.getJobsApi();
                await jobsApi.runWorkflow({
                    workspaceId: `${teamId}`,
                    namespace: JobSchedulerRunWorkflowNamespacePath.Crm,
                    jobSchedulerRunWorkflowRequest: {
                        workflowType: JobSchedulerRunWorkflowRequestWorkflowType.SendBillingEmailNotification,
                        workflowId,
                        // Delay the email by 2 minutes to prevent duplicated emails if multiple increments in multiple service instance pass the threshold at the same time
                        // Default temporal workflow id conflict resolution and reuse policies will prevent spawning workflows with duplicated id if one is already open
                        // but will allow spawning a new one after the previous one is closed (i.e. after counter is reset and incremented again)
                        startDelayInMs: 120000,
                        args: {
                            workspaceId: `${teamId}`,
                            iterableWorkflowConfigKey: emailConfig.iterableWorkflowIdConfigKey,
                            recipientRoles: emailConfig.recipientRoles,
                            payload: {
                                cu_addon_usage: postIncrementValue,
                                cu_addon_limit: limit,
                            },
                        } as SendIterableEmailNotificationTemporalWorkflowArgs<
                            typeof emailConfig.iterableWorkflowIdConfigKey
                        >,
                    },
                });
            } catch (e: any) {
                if (e?.ECODE === 'SCHEDULER_006') {
                    logger.info({
                        msg: `Attempting to schedule duplicated email workflow`,
                        workspaceId: teamId,
                        entitlement: entitlementDefinition.name,
                        workflow_id: workflowId,
                    });
                    return;
                }

                logger.error({
                    msg: `Failed to schedule usage notification workflow, retrying...`,
                    workspaceId: teamId,
                    entitlement: entitlementDefinition.name,
                    workflow_id: workflowId,
                    error: e,
                });
                throw e;
            }
        };

        setImmediate(async () => {
            try {
                await retry(scheduleWorkflow, 'scheduleCounterUsageEmail', logger);
            } catch (e) {
                logger.error({
                    msg: 'Failed to schedule usage notification workflow after retries',
                    workspaceId: teamId,
                    entitlement: entitlementDefinition.name,
                    workflow_id: workflowId,
                });
            }
        });
    }
}
