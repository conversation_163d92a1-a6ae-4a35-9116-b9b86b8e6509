import { TeamId } from '../../../../common/types';
import {
    TAX_IS_B2B_FORCED_KEY,
    TAX_IS_B2B_FORCED_TEMPORARY_KEY,
    TaxIsB2BForcedDto,
    TaxIsB2BForcedTemporaryDto,
} from './taxIsB2BForced.types';
import { TaxIsB2BForcedRepository } from './taxIsB2BForced.repository';
import { TaxIsB2BForcedErrorCodes, TaxIsB2BForcedError } from './taxIsB2BForced.errors';
import { TeamAuditData } from '../../../audit/taskMgmtTeamAudit/utils';
import { TaxIsB2BForcedTemporaryAuditAction } from '../../../audit/taskMgmtTeamAudit/types/taxIsB2BForcedTemporaryAudit';
import { TaxIsB2BForcedAuditAction } from '../../../audit/taskMgmtTeamAudit/types/taxIsB2BForcedAudit';

export class TaxIsB2BForcedService {
    constructor(
        private readonly taxIsB2BForcedRepository: TaxIsB2BForcedRepository // private readonly teamEditAuditService: TeamEditAuditService
    ) {}

    async isAnyB2BForcedSet(teamId: TeamId): Promise<boolean> {
        const taxIsB2BForced = await this.getTaxIsB2BForced(teamId);
        const taxIsB2BForcedTemporary = await this.getTemporaryTaxIsB2BForced(teamId);
        return taxIsB2BForced.tax_is_b2b_forced || taxIsB2BForcedTemporary.tax_is_b2b_forced_temporary;
    }

    async getTaxIsB2BForced(teamId: TeamId): Promise<TaxIsB2BForcedDto> {
        return this.taxIsB2BForcedRepository.getTaxIsB2BForced(teamId);
    }

    async upsertTaxIsB2BForced<TUserId>(
        userId: TUserId,
        teamId: TeamId,
        { taxIsB2BForced, note }: { taxIsB2BForced: boolean; note: string },
        insertAuditEntry: (userId: TUserId, teamId: TeamId, date: number, auditEntry: TeamAuditData) => Promise<void>
    ): Promise<TaxIsB2BForcedDto> {
        try {
            const result = await this.taxIsB2BForcedRepository.upsertTaxIsB2BForced(teamId, taxIsB2BForced);
            await insertAuditEntry(userId, teamId, Date.now(), {
                action: TaxIsB2BForcedAuditAction.TaxIsB2BForcedUpdated,
                data: {
                    tax_is_b2b_forced: taxIsB2BForced,
                    notes: note,
                },
            });
            return result;
        } catch (error) {
            throw new TaxIsB2BForcedError(
                `Failed to upsert ${TAX_IS_B2B_FORCED_KEY}`,
                TaxIsB2BForcedErrorCodes.TAX_IS_B2B_FORCED_500_UPSERT,
                500
            );
        }
    }

    async getTemporaryTaxIsB2BForced(teamId: TeamId): Promise<TaxIsB2BForcedTemporaryDto> {
        return this.taxIsB2BForcedRepository.getTaxIsB2BForcedTemporary(teamId);
    }

    async clearTemporaryTaxIsB2BForced<TUserId>(
        userId: TUserId,
        teamId: TeamId,
        note: string,
        insertAuditEntry: (userId: TUserId, teamId: TeamId, date: number, auditEntry: TeamAuditData) => Promise<void>
    ): Promise<void> {
        const tempB2BResult = await this.getTemporaryTaxIsB2BForced(teamId);
        if (tempB2BResult.tax_is_b2b_forced_temporary) {
            await this.upsertTemporaryTaxIsB2BForced(
                userId,
                teamId,
                {
                    taxIsB2BForcedTemporary: false,
                    note,
                },
                insertAuditEntry
            );
        }
    }

    async upsertTemporaryTaxIsB2BForced<TUserId>(
        userId: TUserId,
        teamId: TeamId,
        { taxIsB2BForcedTemporary, note }: { taxIsB2BForcedTemporary: boolean; note: string },
        insertAuditEntry: (userId: TUserId, teamId: TeamId, date: number, auditEntry: TeamAuditData) => Promise<void>
    ): Promise<TaxIsB2BForcedTemporaryDto> {
        try {
            const result = await this.taxIsB2BForcedRepository.upsertTaxIsB2BForcedTemporary(
                teamId,
                taxIsB2BForcedTemporary
            );
            await insertAuditEntry(userId, teamId, Date.now(), {
                action: TaxIsB2BForcedTemporaryAuditAction.TaxIsB2BForcedTemporaryUpdated,
                data: {
                    tax_is_b2b_forced_temporary: taxIsB2BForcedTemporary,
                    notes: note,
                },
            });
            return result;
        } catch (error) {
            throw new TaxIsB2BForcedError(
                `Failed to upsert ${TAX_IS_B2B_FORCED_TEMPORARY_KEY}`,
                TaxIsB2BForcedErrorCodes.TAX_IS_B2B_FORCED_500_UPSERT,
                500
            );
        }
    }
}
