/** the `team_limits` table in the `task_mgmt` schema */
export interface TeamLimits {
    team_id: number;
    gantt_critical_path?: number | null;
    goals?: number | null;
    portfolios?: number | null;
    list_exports?: number | null;
    views?: number | null;
    custom_fields?: number | null;
    box_count?: number | null;
    dashboards?: number | null;
    gantt_usage?: number | null;
    live_view?: number | null;
    milestones?: number | null;
    attachment_comments?: number | null;
    mind_maps?: number | null;
    timelines?: number | null;
    workloads?: number | null;
    points?: number | null;
    table_exports?: number | null;
    emails?: number | null;
    map_usage?: number | null;
    siml?: number | null;
    custom_roles?: number | null;
    document_tags?: number | null;
    location_overview?: number | null;
    embed_view_usage?: number | null;
    time_tracking_advanced_usage?: number | null;
    ai_usage?: number | null;
    team_view_usage?: number | null;
    timl?: number | null;
    time_tracking_basic_usage?: number | null;
    user_timesheet?: number | null;
    dashboards_basic_card_views_usage?: number | null;
    dashboards_advanced_card_views_usage?: number | null;
    field_manual_ai_count?: number | null;
    field_automated_ai_count?: number | null;
    chat_message_usage?: number | null;
    free_auto_ai?: number | null;
    chat_post_usage?: number | null;
    chat_syncup_usage?: number | null;
    free_ai_meeting?: number | null;
    timl_and_siml?: number | null;
    scheduled_reports?: number | null;
    default_views_usage?: number | null;
    protect_views_usage?: number | null;
    ai_voice_to_text?: number | null;
}

export type TeamLimitColumn = keyof Omit<TeamLimits, 'team_id'>;

export const teamLimitColumns: TeamLimitColumn[] = [
    'gantt_critical_path',
    'goals',
    'portfolios',
    'list_exports',
    'views',
    'custom_fields',
    'box_count',
    'dashboards',
    'gantt_usage',
    'live_view',
    'milestones',
    'attachment_comments',
    'mind_maps',
    'timelines',
    'workloads',
    'points',
    'table_exports',
    'emails',
    'map_usage',
    'siml',
    'custom_roles',
    'document_tags',
    'location_overview',
    'embed_view_usage',
    'time_tracking_advanced_usage',
    'ai_usage',
    'team_view_usage',
    'timl',
    'time_tracking_basic_usage',
    'user_timesheet',
    'dashboards_basic_card_views_usage',
    'dashboards_advanced_card_views_usage',
    'field_manual_ai_count',
    'field_automated_ai_count',
    'chat_message_usage',
    'free_auto_ai',
    'chat_post_usage',
    'chat_syncup_usage',
    'free_ai_meeting',
    'timl_and_siml',
    'scheduled_reports',
    'default_views_usage',
    'protect_views_usage',
    'ai_voice_to_text',
];
