import { SpecificAddonType } from '@clickup/billing/types';
import { EntitlementName } from '@clickup/entitlements';

export const AddonPurchaseEligibilityMap: Partial<Record<SpecificAddonType, EntitlementName>> = {
    [SpecificAddonType.Automations100]: EntitlementName.AutomationAddons,
    [SpecificAddonType.Automations1000]: EntitlementName.AutomationAddons,
    [SpecificAddonType.Automations2500]: EntitlementName.AutomationAddons,
    [SpecificAddonType.Automations10000]: EntitlementName.AutomationAddons,
    [SpecificAddonType.Automations25000]: EntitlementName.AutomationAddons,
    [SpecificAddonType.Automations100000]: EntitlementName.AutomationAddons,
    [SpecificAddonType.Automations1000000]: EntitlementName.AutomationAddons,
    [SpecificAddonType.AutomationsUnlimited]: EntitlementName.AutomationAddons,

    [SpecificAddonType.ProductivityPowerPack]: EntitlementName.ProductivityPowerPack,
    [SpecificAddonType.ProductivityStarterPack]: EntitlementName.ProductivityStarterPack,

    [SpecificAddonType.AiAddonSalesAssisted2506]: EntitlementName.PurchaseSaAiAddonV1,
    [SpecificAddonType.AiAddonSalesAssisted2507]: EntitlementName.PurchaseSaAiAddonV2,
};
