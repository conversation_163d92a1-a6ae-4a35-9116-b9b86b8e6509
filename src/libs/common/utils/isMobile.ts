export function isMobile(userAgent: string): boolean {
    if (
        userAgent?.toLowerCase().includes('chrome') ||
        userAgent?.toLowerCase().includes('safari') ||
        userAgent?.toLowerCase().includes('firefox')
    ) {
        return false;
    }

    if (
        (userAgent?.toLowerCase().includes('ios') && !userAgent?.toLowerCase().includes('axios')) ||
        userAgent?.toLowerCase().includes('android') ||
        userAgent?.toLowerCase().includes('dart')
    ) {
        return true;
    }

    return false;
}

export function isIonicMobileApp(userAgent: string): boolean {
    if (userAgent?.toLowerCase().includes('ios') || userAgent?.toLowerCase().includes('android')) {
        return true;
    }

    return false;
}

export function isFlutterMobileApp(userAgent: string): boolean {
    if (userAgent?.toLowerCase().includes('dart') || userAgent?.toLowerCase().includes('flutter')) {
        return true;
    }

    return false;
}

export function isBrainMaxApp(userAgent: string): boolean {
    // user-agent string options are: ClickUp Brain MAX macOS (Dart) and ClickUp Brain MAX Windows (Dart)
    if (userAgent?.toLowerCase().includes('clickup brain max')) {
        return true;
    }

    return false;
}
