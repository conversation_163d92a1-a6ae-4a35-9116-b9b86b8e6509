--- Source: syncup_db_migrations/1739293574103_init.sql

CREATE SCHEMA IF NOT EXISTS task_mgmt;

--- Source: syncup_db_migrations/1739293613222_add_invitations_table.sql

CREATE TABLE IF NOT EXISTS task_mgmt.syncup_invitations (
    id int8 NOT NULL GENERATED ALWAYS AS IDENTITY,
    workspace_id int8 NOT NULL,
    caller_id int4 NOT NULL,
    receiver_id int4 NOT NULL,
    state int4 NOT NULL,
    syncup_id varchar(126) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL,
    expire_at TIMESTAMPTZ NOT NULL,
    PRIMARY KEY (workspace_id, id)
) PARTITION BY HASH(workspace_id);

--- Source: syncup_db_migrations/1739325883086_create_syncup_invitations_index_on_workspace_id.sql

CREATE INDEX IF NOT EXISTS syncup_invitations_workspace_id_idx ON task_mgmt.syncup_invitations USING btree (workspace_id);

--- Source: syncup_db_migrations/1739902679292_add_invitations_table_partitions.sql

DO $$
DECLARE
    i integer;
BEGIN
    FOR i IN 0..31 LOOP
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS task_mgmt.syncup_invitations_p%s
                PARTITION OF task_mgmt.syncup_invitations
                    FOR VALUES WITH (MODULUS 32, REMAINDER %s);',
            i, i);
    END LOOP;
END $$;
