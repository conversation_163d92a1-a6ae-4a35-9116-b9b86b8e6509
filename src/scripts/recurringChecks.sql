SELECT
    count(*)
FROM
    task_mgmt.task_recur_settings,
    task_mgmt.items,
    task_mgmt.subcategories,
    task_mgmt.categories,
    task_mgmt.projects,
    task_mgmt.teams
WHERE 
    items.id = task_recur_settings.last_task
    AND items.subcategory = subcategories.id
    AND subcategories.category = categories.id
    AND categories.project_id = projects.id
    AND projects.team = teams.id
    AND (items.archived = FALSE OR items.archived IS NULL)
    AND (items.deleted = FALSE OR items.deleted IS NULL)
    AND (items.template = FALSE)
    AND (task_recur_settings.periodically = false OR task_recur_settings.periodically IS NULL)
    AND ((task_recur_settings.on_schedule_date < (SELECT extract(epoch from now()) * 1000) AND task_recur_settings.on_schedule_date IS NOT NULL) 
        OR (task_recur_settings.recur_next < (SELECT extract(epoch from now()) * 1000) AND task_recur_settings.on_schedule_date IS NULL))
    AND task_recur_settings.recur_on_schedule = true;


SELECT count(*)
    FROM   task_mgmt.items
        INNER JOIN task_mgmt.subcategories ON subcategories.id = items.subcategory
        INNER JOIN task_mgmt.categories ON categories.id = subcategories.category
        INNER JOIN task_mgmt.projects ON projects.id = categories.project_id
        INNER JOIN task_mgmt.teams ON teams.id = projects.team
        LEFT OUTER JOIN tasK_mgmt.statuses AS closed_status ON closed_status.type = 'closed' AND closed_status.status_group = subcategories.status_group
        LEFT OUTER JOIN tasK_mgmt.statuses AS open_status ON open_status.type = 'open' AND open_status.status_group = subcategories.status_group
        LEFT OUTER JOIN task_mgmt.statuses AS recur_on_status ON recur_on_status.status = items.recur_on_status AND recur_on_status.status_group = subcategories.status_group
        LEFT OUTER JOIN task_mgmt.statuses AS recur_new_status ON recur_new_status.status = items.recur_new_status AND recur_new_status.status_group = subcategories.status_group
    WHERE  items.deleted = false 
        AND items.template = false 
        AND items.archived = false 
        AND subcategories.deleted = false 
        AND subcategories.template = false 
        AND subcategories.archived = false 
        AND categories.deleted = false 
        AND categories.template = false 
        AND categories.archived = false 
        AND projects.deleted = false 
        AND projects.template = false 
        AND projects.archived = false 
        AND teams.deleted = false
        AND items.subcategory = subcategories.id 
        AND subcategories.category = categories.id 
        AND categories.project_id = projects.id 
        AND recurring = true 
        AND recur_next < (SELECT extract(epoch from now()) * 1000)
        AND recur_on = 1
        AND items.creator != 307792
        AND items.creator != 1505521
        AND (coalesce(recur_on_status.status, closed_status.status) = items.status OR items.recur_on_status IS NULL);
