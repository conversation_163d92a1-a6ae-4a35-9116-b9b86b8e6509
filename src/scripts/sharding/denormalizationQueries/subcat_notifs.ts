/*
    squad: core-services
    author: <PERSON><PERSON> 
 */

export const selectQuery = `
    SELECT
        subcat_notifs.subcategory,
        projects.team AS workspace_id 
    FROM task_mgmt.projects
    INNER JOIN task_mgmt.categories
        ON categories.project_id = projects.id
    INNER JOIN task_mgmt.subcategories
        ON subcategories.category = categories.id
    INNER JOIN task_mgmt.subcat_notifs
        ON subcat_notifs.subcategory = subcategories.id
    WHERE projects.team = ANY ($1)
        AND (subcat_notifs.team_id IS NULL OR subcat_notifs.team_id != projects.team)
    LIMIT $2
`;

// PK of subcat_notifs is: userid, hist_id
export const setQuery = `
    WITH q1 AS (
        ${selectQuery}
    )
    UPDATE
        task_mgmt.subcat_notifs
    SET
        team_id = q1.workspace_id
    FROM 
        q1
    WHERE
        subcat_notifs.subcategory = q1.subcategory
`;

export const deleteQuery = `
    DELETE FROM 
        task_mgmt.subcat_notifs
    WHERE
        team_id = ANY ($1)
    `;

export const collectionKey = `subcat_notifs`;
