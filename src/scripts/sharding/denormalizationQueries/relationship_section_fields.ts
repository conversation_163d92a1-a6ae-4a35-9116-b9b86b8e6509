/*
    squad: tasks
    author: <PERSON>
 */
export const selectQuery = `
    WITH q AS (
        SELECT id, workspace_id
        FROM task_mgmt.tasks
        WHERE tasks.workspace_id = ANY ($1)
    )
    SELECT 
        relationship_section_fields.item_id,
        q.workspace_id as workspace_id
    FROM q
        JOIN task_mgmt.relationship_section_fields
            ON relationship_section_fields.item_id = q.id
    WHERE 
        (relationship_section_fields.workspace_id != q.workspace_id OR relationship_section_fields.workspace_id IS NULL)
    LIMIT $2
`;

export const setQuery = `
    WITH q1 as (
        ${selectQuery}
    )
    UPDATE task_mgmt.relationship_section_fields
        SET workspace_id = q1.workspace_id
        FROM q1
    WHERE q1.item_id = relationship_section_fields.item_id 
`;

export const deleteQuery = `
    DELETE FROM task_mgmt.relationship_section_fields
    WHERE workspace_id = ANY ($1)`;

export const collectionKey = `relationship_section_fields`;
