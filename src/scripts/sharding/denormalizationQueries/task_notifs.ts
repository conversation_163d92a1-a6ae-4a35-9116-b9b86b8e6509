/*
    squad: core-services
    author: <PERSON><PERSON> 
*/

// PK of task_notifs: (userid, hist_id)
export const selectQuery = `
    WITH q1 AS (
        SELECT id, workspace_id
        FROM task_mgmt.tasks
        WHERE tasks.workspace_id = ANY ($1)
    )
    SELECT
        task_notifs.task_id,
        q1.workspace_id
    FROM
        task_mgmt.task_notifs
    JOIN q1 ON
        task_notifs.task_id = q1.id
    WHERE
        (task_notifs.team_id IS NULL OR task_notifs.team_id != q1.workspace_id)
    LIMIT $2
`;

export const setQuery = `
    WITH q1 AS (
        ${selectQuery}
    )
    UPDATE
    	task_mgmt.task_notifs
    SET
    	team_id = q1.workspace_id
    FROM 
        q1
    WHERE
        q1.task_id = task_notifs.task_id
`;

export const deleteQuery = `
    DELETE FROM 
    	task_mgmt.task_notifs
    WHERE
        team_id = ANY ($1)
`;

export const collectionKey = `task_notifs`;
