const SELECT = `
    SELECT 
        generic_links.left_id,
        generic_links.left_type,
        generic_links.right_id,
        generic_links.right_type,
        views.team_id as workspace_id
    FROM task_mgmt.views
        JOIN task_mgmt.generic_links
            ON generic_links.right_id = views.view_id
            AND generic_links.right_type = 1
    WHERE 
        (generic_links.workspace_id = ANY($1) AND (generic_links.workspace_id != views.team_id or views.team_id is null))
        AND (views.team_id != ALL($1) or views.team_id is null)

    UNION

    -- JOIN view_docs, type: 2
    SELECT 
        generic_links.left_id,
        generic_links.left_type,
        generic_links.right_id,
        generic_links.right_type,
        view_docs.team_id as workspace_id
    FROM task_mgmt.view_docs
        JOIN task_mgmt.generic_links
            ON generic_links.right_id = view_docs.id
            AND generic_links.right_type = 2
    WHERE 
        (generic_links.workspace_id = ANY($1) AND (generic_links.workspace_id != view_docs.team_id or view_docs.team_id is null))
        AND (view_docs.team_id != ALL($1) or view_docs.team_id is null)

    UNION

    -- JOIN tasks, type: 3
    SELECT 
        generic_links.left_id,
        generic_links.left_type,
        generic_links.right_id,
        generic_links.right_type,
        tasks.workspace_id as workspace_id
    FROM task_mgmt.tasks
        JOIN task_mgmt.generic_links
            ON generic_links.right_id = tasks.id
            AND generic_links.right_type = 3
    WHERE 
        (generic_links.workspace_id = ANY($1) AND (generic_links.workspace_id != tasks.workspace_id or tasks.workspace_id is null))
        AND (tasks.workspace_id != ALL($1) or tasks.workspace_id is null)
    
    LIMIT $2
`;
export const queries: Record<string, { select: string; update: string }> = {
    generic_links: {
        select: SELECT,
        update: `
    WITH q1 as (
        ${SELECT}
    )
    UPDATE task_mgmt.generic_links
        SET workspace_id = q1.workspace_id
        FROM q1
    WHERE q1.left_id = generic_links.left_id
        AND q1.left_type = generic_links.left_type
        AND q1.right_id = generic_links.right_id
        AND q1.right_type = generic_links.right_type

`,
    },
};
