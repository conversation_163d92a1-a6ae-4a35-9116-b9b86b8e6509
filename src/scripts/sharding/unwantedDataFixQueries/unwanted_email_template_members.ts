const SELECT = `
            SELECT
                email_template_members.template_id,
                email_template_members.userid,
                email_templates.team_id as workspace_id
            FROM task_mgmt.email_templates
                JOIN task_mgmt.email_template_members
                    ON email_templates.id = email_template_members.template_id
            WHERE
                (email_templates.team_id != ALL($1) or email_templates.team_id is null) 
              AND (email_template_members.workspace_id = ANY($1) AND (email_template_members.workspace_id != email_templates.team_id or email_templates.team_id is null))
            LIMIT  $2 `;
export const queries: Record<string, { select: string; update: string }> = {
    email_template_members: {
        select: SELECT,
        update: `
            WITH q AS (${SELECT})
                UPDATE task_mgmt.email_template_members
            SET    workspace_id = q.workspace_id
            FROM   q
            WHERE  email_template_members.template_id = q.template_id
                AND email_template_members.userid = q.userid

`,
    },
};
