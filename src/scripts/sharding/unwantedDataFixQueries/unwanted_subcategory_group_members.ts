const SELECT = `
    SELECT 
        subcategory_group_members.group_id,
        subcategory_group_members.subcategory,
        groups.team_id as workspace_id
    FROM 
    task_mgmt.groups
        JOIN task_mgmt.subcategory_group_members ON groups.id = subcategory_group_members.group_id
    WHERE
        (subcategory_group_members.workspace_id = ANY($1) AND (subcategory_group_members.workspace_id != groups.team_id or groups.team_id is null)) AND
        (groups.team_id != ALL($1) or groups.team_id is null)        

    LIMIT $2
`;
export const queries: Record<string, { select: string; update: string }> = {
    subcategory_group_members: {
        select: SELECT,
        update: `
    WITH q1 AS (${SELECT})
    UPDATE task_mgmt.subcategory_group_members 
    SET workspace_id = q1.workspace_id
    FROM q1
    WHERE 
        q1.group_id = subcategory_group_members.group_id AND 
        q1.subcategory = subcategory_group_members.subcategory

`,
    },
};
