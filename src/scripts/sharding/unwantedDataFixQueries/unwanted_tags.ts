const SELECT = `
    
    SELECT 
        tags.task_id,
        q.workspace_id
    FROM task_mgmt.tags
        JOIN (
        SELECT id, workspace_id
        FROM task_mgmt.tasks
        WHERE (tasks.workspace_id != ALL($1) or tasks.workspace_id is null)
    ) as q
            ON tags.task_id = q.id
    WHERE (tags.workspace_id = ANY($1) AND (tags.workspace_id != q.workspace_id or q.workspace_id is null))
    LIMIT $2
`;
export const queries: Record<string, { select: string; update: string }> = {
    tags: {
        select: SELECT,
        update: `
    WITH q1 as (
        ${SELECT}
    )
    UPDATE task_mgmt.tags
        SET workspace_id = q1.workspace_id
        FROM q1
    WHERE q1.task_id = tags.task_id 

`,
    },
};
