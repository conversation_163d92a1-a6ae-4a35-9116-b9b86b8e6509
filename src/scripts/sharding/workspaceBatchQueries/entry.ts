import { getLogger } from '@clickup/shared/utils-logging';
import { overrideDbSettings } from '../override_db_settings';

import { filterTableQueries, getTables } from '../utils';
import * as _tableQueries from './index';
import { RunType, TableQueryCollection } from '../interfaces';

import { run } from '../tableQueryRunner';
// must be the first import
overrideDbSettings();

const logger = getLogger('workspaceBatchQueries');

const runType = process.env.VERIFY_ONLY ? RunType.WORKSPACE_ID_VERIFY : RunType.WORKSPACE_ID_BACKFILL;

/*

** To Run: **

ALLOW_CONFIG_MUTATIONS=true \
LOG_FILE_FULL_PATH=`pwd`/run_`date +%s`.log \
STATE_FILE=./state.json \
NODE_CONFIG_DIR=src/config \
TABLES=<tables> \
npx ts-node ./src/scripts/sharding/backfills.ts


** To Debug: **
DB_HOST=*************
ALLOW_CONFIG_MUTATIONS=true \
LOG_FILE_FULL_PATH=`pwd`/run_`date +%s`.log \
NODE_CONFIG_DIR=src/config \
STATE_FILE=./state.json \
TABLES=<tables> \
node --require ts-node/register --inspect ./src/scripts/sharding/workspaceBatchQueries/entry.ts

 */
async function main() {
    const tables = getTables();
    let tableQueries = Object.values(_tableQueries) as any as Array<TableQueryCollection>;
    tableQueries = filterTableQueries(tables, tableQueries);

    await run(tableQueries, runType);
}

main()
    .catch(err => logger.error({ err }))
    .then(() => process.exit());
