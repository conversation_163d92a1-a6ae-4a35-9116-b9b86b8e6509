const selectQuery = `
    SELECT
        view_notifs.hist_id,
        view_notifs.userid,
        views.team_id as workspace_id
    FROM task_mgmt.view_notifs
        LEFT JOIN task_mgmt.view_history 
            ON view_history.id = view_notifs.hist_id
        LEFT JOIN task_mgmt.views
            ON views.view_id = view_history.view_id
    WHERE
        view_notifs.workspace_id IS NULL
    LIMIT $1
`;

export const batchQuery = `
    WITH q1 as (${selectQuery})
    UPDATE task_mgmt.view_notifs 
        SET workspace_id = COALESCE(q1.workspace_id, -1) 
    FROM q1
    WHERE 
        q1.hist_id = view_notifs.hist_id 
      AND q1.userid = view_notifs.userid
`;

export const collectionKey = 'view_notifs_initial_backfill';
