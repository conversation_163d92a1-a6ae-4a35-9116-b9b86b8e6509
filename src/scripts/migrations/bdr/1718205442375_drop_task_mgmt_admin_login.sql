-- START migration to global database: task_mgmt.admin_login (Auto-generated by src/scripts/local_database_setup/migrate_tables_to_global_db.sh)
-- Do NOT run in production. This migration is for setting up global databases for local development and testing only.
DROP TABLE IF EXISTS task_mgmt.admin_login;
-- END migration to global database: task_mgmt.admin_login (Auto-generated by src/scripts/local_database_setup/migrate_tables_to_global_db.sh)
