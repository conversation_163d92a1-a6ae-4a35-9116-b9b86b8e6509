/**
 * Changes:
 * - added task_private and task_archived columns (they inherit the value from the parent)
 */
CREATE OR REPLACE VIEW task_mgmt.tasks_with_list_acl
AS
SELECT
    tasks.*,
    object_acls.object_id_int as list_id,
    object_acls.private as list_private,
    object_acls.archived as list_archived,
    object_acls.user_acl as list_user_acl,
    object_acls.group_acl as list_group_acl,
    task_acls.private IS TRUE AS task_private,
    task_acls.archived IS TRUE AS task_archived
FROM task_mgmt.tasks
JOIN task_mgmt.object_acls
     ON tasks.subcategory = object_acls.object_id_int
         AND tasks.workspace_id = object_acls.workspace_id
         AND object_acls.object_type = 'list'
LEFT JOIN task_mgmt.object_acls task_acls
          ON tasks.id = task_acls.object_id
              AND tasks.workspace_id = task_acls.workspace_id
              AND task_acls.object_type = 'task';

/**
* Changes:
* - used task_acls.private instead of tasks.private
* - added task_private and task_archived columns (they inherit the value from the parent)
*/

CREATE OR REPLACE FUNCTION task_mgmt.get_accessible_tasks(_workspace_id bigint, _userid integer, _group_ids text[], _user_role integer, _hierarchy_scope jsonb DEFAULT NULL::jsonb, _explicit_archived boolean DEFAULT NULL::boolean)
    RETURNS SETOF task_mgmt.tasks_with_list_acl
    LANGUAGE sql
    STABLE
AS $function$
WITH list_acls AS (
    SELECT
        *
    FROM
        task_mgmt.get_hierarchy_object_acls(
            ARRAY['list'],
            _workspace_id,
            _hierarchy_scope
        ) list_acls
)
/** SELECT all homed tasks and subtasks **/
SELECT
    tasks.*,
    list_acls.object_id_int as list_id,
    list_acls.private as list_private,
    list_acls.archived as list_archived,
    list_acls.user_acl as list_user_acl,
    list_acls.group_acl as list_group_acl,
    task_acls.private AS task_private,
    task_acls.archived AS task_archived
FROM list_acls
INNER JOIN task_mgmt.tasks
    ON list_acls.object_id_int = tasks.subcategory
LEFT JOIN task_mgmt.object_acls task_acls
    ON tasks.id = task_acls.object_id
        AND task_acls.workspace_id = _workspace_id
        AND task_acls.object_type = 'task'
WHERE tasks.deleted IS NOT TRUE
  AND tasks.template IS NOT TRUE
  AND (
    task_acls.user_acl ? _userid::text
        OR task_acls.group_acl ?| _group_ids
        OR (
        COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
            AND (
            (
                list_acls.private IS NOT TRUE
                    AND _user_role != 4
                )
                OR list_acls.user_acl ? _userid::text
                OR list_acls.group_acl ?| _group_ids
            )
        )
    )
AND (
    _explicit_archived IS NULL
        OR tasks.archived = _explicit_archived
    )
UNION ALL
/** SELECT all TIMLs/SIMLs. Please note it doesn't return subtasks of TIMLs/SIMLs and this has to be retrieved recursively in a separate query **/
SELECT DISTINCT ON (tasks.id, list_acls.object_id_int)
    tasks.*,
    list_acls.object_id_int as list_id,
    list_acls.private as list_private,
    list_acls.archived as list_archived,
    list_acls.user_acl as list_user_acl,
    list_acls.group_acl as list_group_acl,
    task_acls.private AS task_private,
    task_acls.archived AS task_archived
FROM list_acls
INNER JOIN task_mgmt.task_subcategories
    ON list_acls.object_id_int = task_subcategories.subcategory
INNER JOIN task_mgmt.tasks
    ON task_subcategories.task_id = tasks.id
        AND tasks.subcategory != task_subcategories.subcategory
LEFT JOIN task_mgmt.object_acls task_acls
    ON tasks.id = task_acls.object_id
        AND task_acls.workspace_id = _workspace_id
        AND task_acls.object_type = 'task'
WHERE tasks.deleted IS NOT TRUE
  AND tasks.template IS NOT TRUE
  AND (
    task_acls.user_acl ? _userid::text
        OR task_acls.group_acl ?| _group_ids
        OR (
        COALESCE(task_acls.private, tasks.private, FALSE) IS FALSE
            AND (
            (
                list_acls.private IS NOT true
                    AND _user_role != 4
                )
                OR list_acls.user_acl ? _userid::text
                OR list_acls.group_acl ?| _group_ids
            )
        )
    )
  AND (
    _explicit_archived IS NULL
        OR tasks.archived = _explicit_archived
  )
$function$
;
