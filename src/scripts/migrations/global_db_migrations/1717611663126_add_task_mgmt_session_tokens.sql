-- START migration to global database: task_mgmt.session_tokens (Auto-generated by src/scripts/local_database_setup/migrate_tables_to_global_db.sh)
--
-- PostgreSQL database dump
--

-- Dumped from database version 11.19
-- Dumped by pg_dump version 16.3

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

--
-- Name: session_tokens; Type: TABLE; Schema: task_mgmt; Owner: clickup_admin
--

CREATE TABLE task_mgmt.session_tokens (
    userid bigint NOT NULL,
    ws_key text NOT NULL,
    last_ip text,
    last_refresh bigint,
    date_created bigint,
    active boolean,
    date_deactivated bigint,
    admin_token boolean,
    user_agent text,
    last_access_date bigint,
    created_via text,
    auth_context jsonb
);


ALTER TABLE task_mgmt.session_tokens OWNER TO clickup_admin;

--
-- Name: session_tokens session_tokens_pkey; Type: CONSTRAINT; Schema: task_mgmt; Owner: clickup_admin
--

ALTER TABLE ONLY task_mgmt.session_tokens
    ADD CONSTRAINT session_tokens_pkey PRIMARY KEY (userid, ws_key);


--
-- Name: TABLE session_tokens; Type: ACL; Schema: task_mgmt; Owner: clickup_admin
--

GRANT SELECT ON TABLE task_mgmt.session_tokens TO r_clickup_task_mgmt_read;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE task_mgmt.session_tokens TO r_clickup_task_mgmt_readwrite;


--
-- PostgreSQL database dump complete
--

-- END migration to global database: task_mgmt.session_tokens (Auto-generated by src/scripts/local_database_setup/migrate_tables_to_global_db.sh)
