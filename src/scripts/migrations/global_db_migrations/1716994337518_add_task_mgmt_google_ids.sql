-- START migration to global database: task_mgmt.google_ids (Auto-generated by src/scripts/local_database_setup/migrate_tables_to_global_db.sh)
--
-- PostgreSQL database dump
--

-- Dumped from database version 11.19
-- Dumped by pg_dump version 16.3

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

--
-- Name: google_ids; Type: TABLE; Schema: task_mgmt; Owner: clickup_admin
--

CREATE TABLE task_mgmt.google_ids (
    userid integer NOT NULL,
    org_id text NOT NULL,
    google_id text,
    google_email text,
    workspace_id bigint
);


ALTER TABLE task_mgmt.google_ids OWNER TO clickup_admin;

--
-- Name: google_ids google_ids_pkey; Type: CONSTRAINT; Schema: task_mgmt; Owner: clickup_admin
--

ALTER TABLE ONLY task_mgmt.google_ids
    ADD CONSTRAINT google_ids_pkey PRIMARY KEY (userid, org_id);


--
-- Name: google_ids_workspace_id; Type: INDEX; Schema: task_mgmt; Owner: clickup_admin
--

CREATE INDEX google_ids_workspace_id ON task_mgmt.google_ids USING btree (workspace_id);


--
-- Name: users_google_id_idx2; Type: INDEX; Schema: task_mgmt; Owner: clickup_admin
--

CREATE INDEX users_google_id_idx2 ON task_mgmt.google_ids USING btree (google_id);


--
-- Name: TABLE google_ids; Type: ACL; Schema: task_mgmt; Owner: clickup_admin
--

GRANT SELECT ON TABLE task_mgmt.google_ids TO r_clickup_task_mgmt_read;
GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE task_mgmt.google_ids TO r_clickup_task_mgmt_readwrite;


--
-- PostgreSQL database dump complete
--

-- END migration to global database: task_mgmt.google_ids (Auto-generated by src/scripts/local_database_setup/migrate_tables_to_global_db.sh)
