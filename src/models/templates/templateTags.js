import config from 'config';
import uuid from 'node-uuid';
import { getLogger } from '@clickup/shared/utils-logging';
import * as access from '../../utils/access2';
import * as db from '../../utils/db';
import * as templateHelpers from './templateHelpers';
import { ClickUpError } from '../../utils/errors';
import { getTemplateTags } from './getTemplateTags';

export { getTemplateTags };

const TagError = ClickUpError.makeNamedError('templateTag');
const logger = getLogger('templateTags');

// team template tag endpoints
async function _deleteTag(userid, team_id, tag_id) {
    const queries = [
        {
            query: `UPDATE task_mgmt.team_template_tags SET deleted = TRUE, date_deleted = $1, deleted_by = $2 WHERE team_id = $3 AND id = $4`,
            params: [Date.now(), userid, team_id, tag_id],
        },
        // remove tag from all templates
        {
            query: `UPDATE task_mgmt.template_tags SET deleted = TRUE, date_deleted = $1, deleted_by = $2 WHERE tag_id = $3`,
            params: [Date.now(), userid, tag_id],
        },
    ];

    try {
        return await db.promiseBatchQueriesSeries(queries);
    } catch (err) {
        throw new TagError(err, 'TTAGS_000');
    }
}

export async function deleteTagReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id, tag_id } = req.params;

    try {
        await access.promiseAccessTeam(userid, team_id, { permissions: [config.permission_constants.manage_tags] });
        await _deleteTag(userid, team_id, tag_id);
        resp.status(204).send({});
    } catch (err) {
        next(err);
    }
}

async function _editTag(userid, team_id, tag_id, options) {
    let trimmed_name;
    try {
        // check for tags with same name
        if (options.name) {
            trimmed_name = options.name.trim();
            const result = await db.promiseReadQuery(
                `SELECT * FROM task_mgmt.team_template_tags WHERE team_id = $1 AND name ILIKE $2`,
                [team_id, trimmed_name]
            );
            if (result.rows.length) {
                const [restore_tag] = result.rows;
                if (restore_tag.deleted === false) {
                    throw new TagError('Template tag already exists with that name', 'TTAGS_001', 400);
                } else {
                    const tag_result = await db.promiseWriteQuery(
                        `UPDATE task_mgmt.team_template_tags SET deleted = FALSE, deleted_by = NULL, date_deleted = NULL, creator = $1 WHERE team_id = $2 AND id = $3 RETURNING id, name, color`,
                        [userid, team_id, restore_tag.id]
                    );
                    return tag_result.rows[0];
                }
            }
        }

        let query = `UPDATE task_mgmt.team_template_tags SET `;
        const params = [];

        if (options.color) {
            if (params.length > 0) {
                query += `, `;
            }
            params.push(options.color);
            query += ` color = $${params.length}`;
        }

        if (options.name) {
            if (params.length > 0) {
                query += `, `;
            }
            params.push(trimmed_name);
            query += ` name = $${params.length}`;
        }

        params.push(team_id, tag_id);
        query += ` WHERE team_id = $${params.length - 1} AND id = $${params.length} RETURNING id, name, color`;
        const result = await db.promiseWriteQuery(query, params);
        return result.rows[0];
    } catch (err) {
        throw new TagError(err, 'TTAGS_002');
    }
}

export async function editTagReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id, tag_id } = req.params;
    const options = req.body;

    try {
        if (!options || (!options.name && !options.color)) {
            throw new TagError('name or color missing from body', 'TTAGS_003', 400);
        }
        await access.promiseAccessTeam(userid, team_id, { permissions: [config.permission_constants.manage_tags] });
        const result = await _editTag(userid, team_id, tag_id, options);
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}

async function _getTags(userid, team_id, options) {
    let query = `SELECT id, lower(name) AS name, color FROM task_mgmt.team_template_tags WHERE team_id = $1 AND deleted = false`;

    const params = [team_id];

    if (options.name) {
        params.push(`%${options.name}%`);
        query += ` AND name ILIKE $${params.length}`;
    }

    if (options.paging) {
        if (options.start) {
            params.push(options.start_id, options.start);
            query += ` 
                AND (
                    name > $${params.length}
                    OR (
                        name = $${params.length}
                        AND
                        id < $${params.length - 1}
                    )
                )`;
        }
        query += ` ORDER BY name ASC, id DESC LIMIT ${config.page_length.tags + 1}`;
    } else {
        query += ` ORDER BY name ASC`;
    }

    try {
        let last_page = true;
        const paging = {};
        const result = await db.promiseReplicaQuery(query, params);

        if (options.paging && result.rows.length > config.page_length.tags) {
            last_page = false;
            result.rows.splice(config.page_length.tags);
            const last_tag = result.rows[result.rows.length - 1];
            paging.start_id = last_tag.id;
            paging.start = last_tag.name;
        }

        return { tags: result.rows, last_page, paging };
    } catch (err) {
        throw new TagError(err, 'TTAGS_004');
    }
}

export async function getTagsReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const options = req.query;

    try {
        await access.promiseAccessTeam(userid, team_id, { permissions: [] });
        const result = await _getTags(userid, team_id, options);
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}

async function _createTag(userid, team_id, options) {
    let tag_result;
    const trimmed_name = options.name.trim();

    try {
        // check for tags with same name
        const result = await db.promiseReadQuery(
            `SELECT * FROM task_mgmt.team_template_tags WHERE team_id = $1 AND name ILIKE $2`,
            [team_id, trimmed_name]
        );
        if (result.rows.length) {
            const [restore_tag] = result.rows;
            if (restore_tag.deleted === false) {
                throw new TagError('Template tag already exists with that name', 'TTAGS_005', 400);
            } else {
                tag_result = await db.promiseWriteQuery(
                    'UPDATE task_mgmt.team_template_tags SET deleted = FALSE, deleted_by = NULL, date_deleted = NULL, creator = $1, color = $2 WHERE team_id = $3 AND id = $4 RETURNING id, name, color',
                    [userid, options.color, team_id, restore_tag.id]
                );
            }
        } else {
            tag_result = await db.promiseWriteQuery(
                `INSERT INTO task_mgmt.team_template_tags(id, name, team_id, color, deleted, creator) VALUES($1, $2, $3, $4, false, $5) RETURNING id, name, color`,
                [uuid.v4(), trimmed_name, team_id, options.color, userid]
            );
        }
        return tag_result.rows[0];
    } catch (err) {
        throw new TagError(err, 'TTAGS_006');
    }
}

export async function createTagReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const options = req.body;

    try {
        if (!options || !options.name) {
            throw new TagError('Tag missing from body', 'TTAGS_007', 400);
        }
        await access.promiseAccessTeam(userid, team_id, { permissions: [config.permission_constants.manage_tags] });
        const result = await _createTag(userid, team_id, options);
        resp.status(201).send(result);
    } catch (err) {
        next(err);
    }
}

// Template tag endpoints
async function _removeTemplateTag(userid, template_id, template_type, tag_id) {
    try {
        await templateHelpers.checkAccessTemplate(userid, template_id, template_type, {
            permissions: [config.permission_constants.manage_template_tags, config.permission_constants.remove_tags],
        });
        await db.promiseWriteQuery(
            `UPDATE task_mgmt.template_tags SET deleted = TRUE, date_deleted = $1, deleted_by = $2 WHERE template_id = $3 AND template_type = $4 AND tag_id = $5`,
            [Date.now(), userid, template_id, template_type, tag_id]
        );
    } catch (err) {
        throw new TagError(err, 'TTAGS_008');
    }
}

export async function removeTagReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { template_id, template_type, tag_id } = req.params;

    try {
        await _removeTemplateTag(userid, template_id, template_type, tag_id);
        resp.status(204).send({});
    } catch (err) {
        next(err);
    }
}

async function _addTemplateTag(userid, template_id, template_type, tag_id) {
    try {
        const result = await templateHelpers.checkAccessTemplate(userid, template_id, template_type, {
            permissions: [config.permission_constants.manage_template_tags, config.permission_constants.add_tags],
        });
        const team_id = result?.team || result?.team_id || result?.data?.team || result?.data?.team_id;

        if (!team_id) {
            logger.error({
                msg: 'Unable to extract team_id for template tag.',
                template_id,
                template_type,
                tag_id,
            });
        }

        await db.promiseWriteQuery(
            `
        INSERT INTO task_mgmt.template_tags (template_id, template_type, tag_id, deleted, workspace_id) VALUES($1, $2, $3, false, $4) 
            ON CONFLICT (template_id, template_type, tag_id) DO UPDATE SET deleted = FALSE, deleted_by = NULL, date_deleted = NULL`,
            [template_id, template_type, tag_id, team_id]
        );
    } catch (err) {
        throw new TagError(err, 'TTAGS_009');
    }
}

export async function addTagReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { template_id, template_type } = req.params;
    const { tag_id } = req.body;

    try {
        await _addTemplateTag(userid, template_id, template_type, tag_id);
        resp.status(201).send({});
    } catch (err) {
        next(err);
    }
}
