import { getLogger } from '@clickup/shared/utils-logging';
import { shouldUseSubtaskLimitForTaskTemplateMerging } from '@clickup-legacy/models/integrations/split/squadTreatments/templatesTreatments';
import { replicaQueryAsync } from '../../../utils/db';
import { ClickUpError } from '../../../utils/errors';
import { subtaskLimitChecker } from '../../task/utils/limits';

const logger = getLogger('subtaskLimitForTaskTemplateMerging');
const SubtaskLimitForTaskTemplateMergingError = ClickUpError.makeNamedError('SubtaskLimitForTaskTemplateMerging');

export const subtaskLimitForTaskTemplateMergingExceededECODE = 'SUBTASK_LIMIT_TASK_TEMPL_MERGE_001';

export async function validateSubtaskLimitForTaskTemplateMerging({
    taskId,
    templateId,
    teamId,
}: {
    taskId: string;
    templateId: string;
    teamId: string;
}) {
    const [{ rootTaskId, subtaskCount, subtaskArchivedCount, subtaskDeletedCount }, undeletedCountForTemplate] =
        await Promise.all([getSubtaskCountsForTask(taskId), getUndeletedSubtaskCountForTemplate(templateId)]);

    const limitConfig = shouldUseSubtaskLimitForTaskTemplateMerging();
    const undeletedLimitExceeded =
        limitConfig.undeletedLimit != null &&
        subtaskCount + subtaskArchivedCount + undeletedCountForTemplate > limitConfig.undeletedLimit;

    const deletedLimitExceeded = limitConfig.deletedLimit != null && subtaskDeletedCount > limitConfig.deletedLimit;

    if (undeletedLimitExceeded || deletedLimitExceeded) {
        const msg = 'Subtask limit for applying a template to an existing task exceeded';

        logger.warn({
            msg,
            ECODE: subtaskLimitForTaskTemplateMergingExceededECODE,
            taskId,
            templateId,
            subtaskCount,
            subtaskArchivedCount,
            subtaskDeletedCount,
            undeletedCountForTemplate,
        });

        throw new SubtaskLimitForTaskTemplateMergingError(msg, subtaskLimitForTaskTemplateMergingExceededECODE, 400);
    }

    await subtaskLimitChecker.checkSubtaskLimitForRootTask(
        teamId,
        rootTaskId,
        {
            newSubtasks: undeletedCountForTemplate,
            newArchivedSubtasks: 0, // no archived subtasks are created via templates
        },
        {
            currentSubtaskCount: subtaskCount,
            currentArchivedSubtaskCount: subtaskArchivedCount,
        }
    );
}

async function getSubtaskCountsForTask(taskId: string): Promise<{
    rootTaskId: string;
    subtaskCount: number;
    subtaskArchivedCount: number;
    subtaskDeletedCount: number;
}> {
    const query = `
        SELECT
            parent,
            COUNT(*) FILTER (WHERE archived IS NOT TRUE AND deleted IS NOT TRUE) as subtask_count, 
            COUNT(*) FILTER (WHERE archived=TRUE AND deleted IS NOT TRUE) as subtask_count_archived,
            COUNT(*) FILTER (WHERE deleted IS TRUE) as subtask_deleted_count
        FROM task_mgmt.tasks
        WHERE
            parent = (SELECT COALESCE(parent, id) FROM task_mgmt.tasks WHERE id = $1)
        GROUP BY parent`;

    const row = (await replicaQueryAsync(query, [taskId])).rows?.[0];

    return {
        rootTaskId: row?.parent,
        subtaskCount: row?.subtask_count ? Number(row.subtask_count) : 0,
        subtaskArchivedCount: row?.subtask_count_archived ? Number(row.subtask_count_archived) : 0,
        subtaskDeletedCount: row?.subtask_deleted_count ? Number(row.subtask_deleted_count) : 0,
    };
}

async function getUndeletedSubtaskCountForTemplate(templateId: string): Promise<number> {
    const query = `
        SELECT COUNT(*) AS count
        FROM task_mgmt.tasks
        WHERE
            parent = $1
            AND deleted IS NOT TRUE
            AND archived IS NOT TRUE`; // Archived subtasks are not copied during template merging

    const { rows } = await replicaQueryAsync(query, [templateId]);

    return Number(rows[0].count);
}
