import config from 'config';
import aws from 'aws-sdk';
import fs from 'fs';
import DatadogTag from 'dd-trace/ext/tags';
import { flatten, find } from 'lodash';

import * as db from '@clickup-legacy/utils/db';
import * as awsUtil from '@clickup-legacy/utils/awsUtils/awsUtil';
import * as attachmentsMod from '@clickup-legacy/models/attachment/attachment';
import * as templateHelpers from '@clickup-legacy/models/templates/templateHelpers';
import * as publicTemplateTagsMod from '@clickup-legacy/models/publicTemplateTags'; // eslint-disable-line import/no-cycle
import { ClickUpError } from '@clickup-legacy/utils/errors';
import { fallbackLanguage, getPreferredLanguage } from '@clickup-legacy/utils/i18n';
import { CACHE_KEYS, publicTemplatesSimpleCache } from '@clickup-legacy/models/templates/publicTemplatesSimpleCache';
import { GlobalTableMigrationBatchId } from '@clickup-legacy/utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import { ClickUpTracer } from '@clickup-legacy/utils/tracer';
import { CuPublicTemplatesRepository } from '@clickup-legacy/models/templates/publicTemplates/publicTemplateRepository';
import { getGroupedTemplates as getGroupedTemplatesFromGlobalDb } from '@clickup-legacy/models/templates/publicTemplates/groupedTemplates';
import { getSuggestedTemplates as getSuggestedTemplatesDataGlobalDb } from '@clickup-legacy/models/templates/publicTemplates/suggestedTemplates';

const tracer = new ClickUpTracer();

const PublicTemplateError = ClickUpError.makeNamedError('publicTemplates');

const profile_creds = awsUtil.awsProfile();
if (profile_creds) {
    aws.config.credentials = profile_creds;
}

aws.config.update({
    region: config.aws.region,
});
const s3 = new aws.S3();

const viewsTypeToTemplateMap = new Map([
    [config.views.view_types.doc, 'doc'],
    [config.views.view_types.clickboard, 'whiteboard'],
]);

function getTemplateType(template) {
    return viewsTypeToTemplateMap.get(template.view_type) ?? template.type;
}

const DEFAULT_PAGE_LIMIT = 10;
const DEFAULT_FIRST_PAGE_LIMIT = 6;

async function getPublicTemplateAttachments(rows) {
    const ids_for_attachments = {};
    let doc_counts = 0;
    let view_counts = 0;
    let whiteboard_counts = 0;

    rows.forEach(row => {
        if (!ids_for_attachments[row.type]) {
            ids_for_attachments[row.type] = [];
        }
        if (!ids_for_attachments[row.type].includes(row.template_id)) {
            ids_for_attachments[row.type].push(row.template_id);
        }
        if (row.type === 'view') {
            if (row.view_type === config.views.view_types.doc) {
                doc_counts += 1;
            } else if (row.view_type === config.views.view_types.clickboard) {
                whiteboard_counts += 1;
            } else {
                view_counts += 1;
            }
        }
    });

    const counts = {
        task: ids_for_attachments.task ? ids_for_attachments.task.length : 0,
        subcategory: ids_for_attachments.subcategory ? ids_for_attachments.subcategory.length : 0,
        category: ids_for_attachments.category ? ids_for_attachments.category.length : 0,
        project: ids_for_attachments.project ? ids_for_attachments.project.length : 0,
        view: view_counts,
        doc: doc_counts,
        whiteboard: whiteboard_counts,
    };

    try {
        const attachments_results = await Promise.all([
            attachmentsMod.getAttachmentsOfParents(ids_for_attachments.project || [], config.attachments.types.project),
            attachmentsMod.getAttachmentsOfParents(
                ids_for_attachments.category || [],
                config.attachments.types.category
            ),
            attachmentsMod.getAttachmentsOfParents(
                ids_for_attachments.subcategory || [],
                config.attachments.types.subcategory
            ),
            attachmentsMod.getAttachmentsOfParents(ids_for_attachments.task || [], config.attachments.types.task),
            attachmentsMod.getAttachmentsOfParents(ids_for_attachments.view || [], config.attachments.types.view),
        ]);
        const attachments = {
            project: attachments_results[0].attachments,
            category: attachments_results[1].attachments,
            subcategory: attachments_results[2].attachments,
            task: attachments_results[3].attachments,
            view: attachments_results[4].attachments,
        };

        return { attachments, counts };
    } catch (err) {
        throw new PublicTemplateError(err, 'PT_000');
    }
}

export async function getGroupedTemplatesCached(options) {
    // if there is no search params we can try to get full response from cache
    const isFilterApplied =
        options?.name ||
        options?.public_tags?.length ||
        options?.use_cases?.length ||
        options.group_id ||
        options.template_types?.length;
    if (!isFilterApplied) {
        if (options && +options.page === 0) {
            if (options?.skipCache === 'true') {
                return getFirstPageOfAllTemplateCenterGroups(options);
            }
            return publicTemplatesSimpleCache.cache.getObject(`${CACHE_KEYS.PUBLIC_TEMPLATE_GROUPS}_first_page`, () =>
                getFirstPageOfAllTemplateCenterGroups(options)
            );
        }
        return publicTemplatesSimpleCache.cache.getObject(CACHE_KEYS.PUBLIC_TEMPLATE_GROUPS, () =>
            getGroupedTemplate(options)
        );
    }
    return getGroupedTemplate(options);
}

async function getFirstPageOfAllTemplateCenterGroups(options) {
    const { groups, counts } = await (options?.skipCache === 'true'
        ? getGroupedTemplate(options)
        : publicTemplatesSimpleCache.cache.getObject(CACHE_KEYS.PUBLIC_TEMPLATE_GROUPS, () =>
              getGroupedTemplate(options)
          ));
    return { groups, counts };
}

export async function getGroupedTemplate(options) {
    let result = await getGroupedTemplatesFromGlobalDb(options);

    result = await extendGroupedTemplatesResultsByUsageStatGlobal([...result]);

    let groups = {};

    const { attachments, counts } = await getPublicTemplateAttachments(result);
    const { public_template_tags = [] } = await publicTemplateTagsMod.getPublicTemplateTags(options.language);

    const missing_counts = [];

    result.forEach(row => {
        if (row.id) {
            if (!groups[row.id]) {
                groups[row.id] = {
                    id: row.id,
                    use_case: row.use_case,
                    color: row.color,
                    description: row.description,
                    templates: [],
                };
            }

            if (row.template_id) {
                // add public template tags
                const public_tags_result = [];
                if (row.public_tags && row.public_tags.length) {
                    row.public_tags.forEach(tag_id => {
                        const tag_index = public_template_tags.findIndex(tag => tag.id === tag_id);
                        if (tag_index > -1) {
                            public_tags_result.push(public_template_tags[tag_index]);
                        }
                    });
                }

                row.public_tags = public_tags_result;

                const template = {
                    id: row.template_id,
                    name: row.name,
                    last_used: row.last_used,
                    orderindex: row.orderindex,
                    description: row.template_description,
                    long_description: row.long_description,
                    active: row.active,
                    type: getTemplateType(row),
                    tags: [],
                    use_cases: [],
                    images: attachments[row.type][row.template_id] || [],
                    options: getTemplateOptions(row.options),
                    counts: row.counts,
                    public_tags: row.public_tags,
                    used_count: row.used_count,
                    date_created: row.template_date_created,
                    creator: row.template_creator,
                };

                if (template.type === 'whiteboard') {
                    template.whiteboardsViewSettings = {
                        whiteboardVersion: row.template_extra?.whiteboardVersion || 'v2',
                    };
                }

                template.images = template.images.map(image => ({ ...image, user: null }));

                groups[row.id].templates.push(template);
            }

            if (row.type !== 'view' && (row.counts === null || !row.options || !row.options.count_details)) {
                missing_counts.push({ template_id: row.original_id, type: row.type, options: row.options });
            }
        }
    });

    const attempted_order = [
        'Project Management',
        'Getting Started',
        'Agile Project Management',
        'Education',
        'Design',
        'Media',
        'Sales',
        'Marketing',
        'Remote Work',
        'Website Management',
        'Finance',
        'Business Management',
        'Human Resources',
        'Events',
        'Resource Management',
    ];

    groups = Object.values(groups);
    groups.sort((a, b) => {
        if (attempted_order.includes(a.use_case) && attempted_order.includes(b.use_case)) {
            return attempted_order.indexOf(a.use_case) - attempted_order.indexOf(b.use_case);
        }
        if (attempted_order.includes(a.use_case)) {
            return -1;
        }
        if (attempted_order.includes(b.use_case)) {
            return 1;
        }

        return b.templates.length - a.templates.length;
    });

    const usePagination = Number.isInteger(+options.page) && +options.page >= 0;
    const isFirstpage = +options.page === 0;

    groups.forEach(group => {
        group.templates.sort((a, b) => (a.name > b.name ? 1 : -1));

        if (usePagination && isFirstpage) {
            group.count = group.templates.length;
            group.last_page = group.templates.length <= options.limit;
            group.templates = group.templates.slice(0, options.limit);
        } else if (usePagination) {
            group.last_page = group.templates.length !== DEFAULT_PAGE_LIMIT;
        }
    });

    templateHelpers.fillInMissingTemplateCounts(missing_counts);

    return { groups, counts };
}

async function extendGroupedTemplatesResultsByUsageStatGlobal(templatesData) {
    const globalStats = (
        await db.globalReadQueryAsync(
            'SELECT permanent_id, type, used_count, last_used FROM task_mgmt.template_usage_stat_global',
            [],
            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_37 }
        )
    ).rows;

    const indexedGlobalStat = globalStats.reduce((result, stat) => {
        const key = `${stat.permanent_id}_${stat.type}`;
        result[key] = stat;
        return result;
    }, {});

    const templatesWithGlobalStats = templatesData.map(template => {
        const key = `${template.template_id || template.id}_${template.type}`;
        const count = indexedGlobalStat[key];
        return {
            ...template,
            used_count: count?.used_count || '0',
            last_used: count?.last_used || null,
        };
    });

    return flatten(templatesWithGlobalStats);
}

function convertCsvQueryParamToArray(queryParam) {
    if (!queryParam) {
        return [];
    }

    if (typeof queryParam === 'string') {
        return queryParam.split(',');
    }

    return queryParam;
}

function getCustomResourceName(req, options) {
    const { method, route } = req;
    let resourceSuffix = '';
    resourceSuffix = options?.page !== undefined ? `::paginated::` : '';
    return `${method} ${route?.path}${resourceSuffix}`;
}

export async function getPublicTemplates(req, resp, next) {
    const options = req.query || {};
    const isFirstpage = options.page && +options.page === 0;
    const defaultLimit = isFirstpage ? DEFAULT_FIRST_PAGE_LIMIT : DEFAULT_PAGE_LIMIT;

    if (options.name && typeof options.name !== 'string') {
        options.name = '';
    }
    options.language = getPreferredLanguage(req);
    options.use_cases = convertCsvQueryParamToArray(options.use_cases);
    options.public_tags = convertCsvQueryParamToArray(options.public_tags);
    options.template_types = convertCsvQueryParamToArray(options.template_types);
    options.limit = options.limit ? +options.limit : defaultLimit;
    options.page = options.page ? +options.page : undefined;

    const resourceName = getCustomResourceName(req, options);

    tracer.addTagsToRootSpan({ userId: req?.unauthorizedUser || 'unknown', [DatadogTag.RESOURCE_NAME]: resourceName });

    let result;
    try {
        result = await getGroupedTemplatesCached(options);
    } catch (err) {
        next(err);
        return;
    }

    resp.status(200).send(result);
}

const outcomeTemplateMap = {
    remote_onboarding: [
        't-********', // quick start
        't-********', // remote onboarding
        't-4389460', // meetings
        't-1553831', // getting things done
        't-********', // board list
        't-********', // simple to-dos
    ],
    project_management: [
        't-********', // quick start
        `t-38266362`, // simple project mgmt list
        `t-4392236`, // project mgmt
        't-1553831', // getting things done
        't-********', // board list
        't-********', // simple to-dos
    ],
    to_dos_and_lists: [
        't-********', // quick start
        't-********', // simple task mgmt list
        't-********', // board list
        't-4404185', // task mgmt
        't-********', // simple to-dos
        't-1553831', // getting things done
    ],
    sales_and_crm: [
        't-********', // quick start
        't-********', // CRM
        't-4273883', // Comission tracking
        't-********', // simple task mgmt list
        't-********', // board list
        't-********', // simple to-dos
    ],
    finance_management: [
        't-********', // quick start
        't-********', // Budget
        't-4389484', // Accounting
        't-4389485', // Invoices
        't-6356353', // Bookeeping
        't-********', // simple to-dos
    ],
    campaign_management: [
        't-********', // quick start
        't-6510337', // campaign tracking
        't-4391884', // Promotional calendar
        't-4395328', // SEO mgmt
        't-********', // board list
        't-********', // simple to-dos
    ],
    sprints_and_backlogs: [
        't-********', // quick start
        't-********', // sprints
        't-8454971', // queues
        't-********', // board list
        't-********', // simple to-dos
    ],
    getting_things_done: [
        't-********', // quick start
        't-1553831', // getting things done
        't-********', // simpletask mgmt
        't-********', // board list
        't-********', // simple to-dos
    ],
    remote_work_management: [
        't-********', // quick start
        't-********', // remote onboarding
        't-4389460', // meetings
        't-********', // simple task mgmt
        't-********', // board list
        't-********', // simple to-dos
    ],
    event_planning: [
        't-********', // quick start
        't-4389477', // event mgmt
        't-4389476', // event planning
        't-4392203', // Vacation Planning
        't-********', // board list
        't-********', // simple to-dos
    ],
    personal: [
        't-********', // quick start
        't-10686843', // Job search
        't-6459489', // Apt search
        't-38266262', // class notes
        't-********', // board list
        't-********', // simple to-dos
    ],
    save_time: [
        't-********', // quick start
        `t-38266362`, // simple project mgmt list
        't-********', // board list
        't-1553831', // getting things done
        't-********', // simple to-dos
    ],
    replace_multiple_apps_with_one: [
        't-********', // quick start
        `t-38266362`, // simple project mgmt list
        't-********', // board list
        't-1553831', // getting things done
        't-********', // simple to-dos
    ],
    null: [
        't-********', // quick start
        `t-38266362`, // simple project mgmt list
        't-********', // board list
        't-1553831', // getting things done
        't-********', // simple to-dos
    ],
};
export const outcomeTemplateKeySet = new Set([...Object.keys(outcomeTemplateMap)]);

const superbowlTemplateIds = [
    't-970200002520', // simple (qa)
    't-970200002524', // intermediate (qa)
    't-970200002523', // advanced (qa)

    't-980200005163', // simple (staging NEW)
    't-980200005162', // intermediate (staging NEW)
    't-980200005160', // advanced (staging NEW)

    // Leaving Old Onboarding Templates for Backwards compatability
    't-1202610', // simple (staging)
    't-1202611', // intermediate (staging)
    't-1202612', // advanced (staging)

    't-162726746', // Simple SB (prod)
    't-162726743', // Intermediate SB (prod)
    't-162726740', // Advanced SB (prod)
];

export function getSuperbowlTemplateIds() {
    return superbowlTemplateIds;
}

function getTemplateOptions(options) {
    if (options && options.count_details) {
        return {
            ...options,
            count_details: {
                ...options.count_details,
                automations:
                    options.count_details.automations?.map(({ user: _user, updated_by, ...rest }) => rest) || [],
            },
        };
    }
    return {};
}

async function _getSuggestedTemplates(userid, superbowlTemplates = false, skipCache = false) {
    let user;
    const templateMap = {};
    const templateSet = new Set();
    try {
        const [userResult, templateResult, superbowlTemplateResults] = await Promise.all([
            db.globalReadQueryAsync(
                `SELECT * FROM task_mgmt.user_workspace_onboarding_segmentation WHERE userid = $1`,
                [userid],
                { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_15 }
            ),
            getSuggestedTemplatesDataGlobalDb(skipCache),
            db.promiseReplicaQuery(
                `
                SELECT 
                    null as options,
                    null as counts,
                    subcategories.permanent_template_id as template_id,
                    null as orderindex,
                    'subcategory' as type,
                    null as description,
                    subcategories.name,
                    subcategories.id::text as original_id
                FROM
                    task_mgmt.subcategories
                WHERE
                    permanent_template_id IS NOT NULL AND 
                    permanent_template_id = ANY($1) AND
                    deleted IS NOT TRUE
            `,
                [superbowlTemplateIds]
            ),
        ]);
        if (userResult.rows.length) {
            [user] = userResult.rows;
        }
        templateResult.forEach(row => {
            templateMap[row.template_id] = row;
        });

        if (superbowlTemplates) {
            superbowlTemplateResults.rows.forEach(superbowlTemplate => {
                templateSet.add(superbowlTemplate);
            });
        }
    } catch (err) {
        throw new PublicTemplateError(err, 'PT_002');
    }

    const outcomes = user && user.outcomes && user.outcomes.length ? user.outcomes : ['null'];

    outcomes.forEach(row => {
        let outcome = row;
        if (outcome) {
            outcome = outcome.toLowerCase();
        }
        const outcomeTemplates = outcomeTemplateMap[outcome];
        if (outcomeTemplates) {
            outcomeTemplates.forEach(templateId => {
                const public_template = templateMap[templateId];
                if (public_template) {
                    templateSet.add(public_template);
                }
            });
        }
    });

    if (!templateSet.size) {
        Object.values(templateMap).forEach(template => templateSet.add(template));
    }
    const templatesArr = Array.from(templateSet).filter(row => !!row.template_id);
    const { attachments } = await getPublicTemplateAttachments(templatesArr);

    return templatesArr.map(row => {
        const template = {
            id: row.template_id,
            name: row.name,
            orderindex: row.orderindex,
            description: row.description,
            type: row.type,
            tags: [],
            use_cases: [],
            images: attachments[row.type][row.template_id] || [],
            options: getTemplateOptions(row.options),
            counts: row.counts,
        };

        template.images = template.images.map(image => ({ ...image, user: null }));
        return template;
    });
}

export async function getSuggestedTemplates(req, resp, next) {
    const userid = req.decoded_token.user;
    const { superbowl_templates, skip_cache } = req.query;

    try {
        const result = await _getSuggestedTemplates(userid, superbowl_templates, skip_cache === 'true');
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}

async function _getUseCases() {
    const query = `
        SELECT 
            template_use_cases.id,
            template_use_cases.use_case,
            template_use_cases.color
        FROM task_mgmt.template_use_cases
        ORDER BY use_case ASc
    `;
    const params = [];

    const result = await db.globalReadQueryAsync(query, params, {
        globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_41,
    });

    const use_cases = result.rows;

    return { use_cases };
}

export async function getUseCasesReq(_req, resp, next) {
    try {
        const result = await _getUseCases();
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}

async function uploadFile(attachment, template_id, template_type, suffix) {
    const mime_type = attachment.mimetype;
    const bucket = config.aws.public_bucket;
    let extension = attachment.originalname.split('.');
    extension = extension[extension.length - 1].toLowerCase();
    const s3key = `/public_template_submissions/${template_type}/${template_id}/${
        attachment.originalname.split('.')[0]
    }_${suffix}.${extension}`;

    const params = {
        Bucket: bucket,
        Key: s3key,
        ContentType: mime_type || 'text/plain',
        Body: fs.createReadStream(attachment.path),
    };
    await s3.upload(params).promise();

    fs.unlink(attachment.path, () => {});

    return s3key;
}

async function submitTemplateForApproval(
    userid,
    template_id,
    template_type,
    use_cases = [],
    attribute = {},
    name,
    description,
    attribute_image,
    // eslint-disable-next-line no-unused-vars
    images = []
) {
    await templateHelpers.checkAccessTemplate(userid, template_id, template_type);
    // once uploaded we will fill these in
    let attribute_image_url = null;

    // upload all the files
    const image_promises = [];
    images.forEach((image, idx) => {
        image_promises.push(uploadFile(image, template_id, template_type, idx));
    });
    const image_urls = await Promise.all(image_promises);

    if (attribute_image && attribute_image[0]) {
        attribute_image_url = await uploadFile(attribute_image[0], template_id, template_type, 'attributor_image');
    }

    const queries = [
        {
            query: `
                INSERT INTO task_mgmt.user_submitted_templates_for_review(
                    template_id,
                    template_type,
                    userid,
                    name,
                    description,
                    use_cases,
                    attribute,
                    attribute_name,
                    attribute_image_url,
                    images,
                    date_submitted
                ) VALUES (
                    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
                )
            `,
            params: [
                template_id,
                template_type,
                userid,
                name,
                description,
                use_cases,
                attribute.attribute,
                attribute.name,
                attribute_image_url,
                image_urls,
                Date.now(),
            ],
        },
    ];

    try {
        await db.globalBatchQueriesAsync(queries, {
            globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_14,
        });
    } catch (e) {
        if (e.code === '23505') {
            throw new PublicTemplateError('Template already submitted.', 'PUBTEMP_001', 400);
        } else {
            throw e;
        }
    }

    return {};
}

export async function submitTemplateForApprovalReq(req, resp, next) {
    const userid = req.decoded_token.user;
    const { template_id, template_type, name, description } = req.body;
    let { use_cases, attribute } = req.body;
    const { images, attribute_image } = req.files || {};

    try {
        attribute = JSON.parse(attribute);
    } catch (e) {
        // do nothing
    }

    try {
        use_cases = JSON.parse(use_cases);
    } catch (e) {
        // do nothing
    }

    try {
        const result = await submitTemplateForApproval(
            userid,
            template_id,
            template_type,
            use_cases,
            attribute,
            name,
            description,
            attribute_image,
            images
        );
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}

async function getUseCasesById(use_case_ids) {
    try {
        const query = `SELECT * from task_mgmt.template_use_cases WHERE id = ANY($1)`;
        const result = await db.globalReadQueryAsync(query, [use_case_ids], {
            globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_41,
        });

        if (!result.rows.length) {
            return [];
        }

        return result.rows;
    } catch (err) {
        throw new PublicTemplateError('Failed to get use cases', 'PUBTEMP_024', 500);
    }
}

async function getPublicTemplate(template_id, language = fallbackLanguage) {
    const publicTemplateRow = await CuPublicTemplatesRepository.loadPublicTemplate(template_id);

    if (!publicTemplateRow) {
        throw new PublicTemplateError('Template not found', 'PUBTEMP_025', 500);
    }

    const entityQuery = find(
        [
            {
                type: 'task',
                query: `
                    SELECT name, id::text as id, date_created as template_date_created, creator as template_creator, NULL as view_type
                    FROM task_mgmt.items
                    WHERE items.permanent_template_id IS NOT NULL AND
                          items.permanent_template_id = $1 AND
                          (items.deleted = false OR items.deleted IS NULL)`,
            },
            {
                type: 'subcategory',
                query: `
                    SELECT name, id::text as id, date_created as template_date_created, creator as template_creator, NULL as view_type
                    FROM task_mgmt.subcategories
                    WHERE subcategories.permanent_template_id IS NOT NULL AND
                          subcategories.permanent_template_id = $1 AND
                          (subcategories.deleted = false OR subcategories.deleted IS NULL)`,
            },
            {
                type: 'category',
                query: `
                    SELECT name, id::text as id, date_created as template_date_created, creator as template_creator, NULL as view_type
                    FROM task_mgmt.categories
                    WHERE categories.permanent_template_id IS NOT NULL AND
                          categories.permanent_template_id = $1 AND
                          (categories.deleted = false OR categories.deleted IS NULL)`,
            },
            {
                type: 'project',
                query: `
                    SELECT name, id::text as id, date_created as template_date_created, creator as template_creator, NULL as view_type
                    FROM task_mgmt.projects
                    WHERE projects.permanent_template_id IS NOT NULL AND
                          projects.permanent_template_id = $1 AND
                          (projects.deleted = false OR projects.deleted IS NULL)`,
            },
            {
                type: 'view',
                query: `
                    SELECT name, view_id::text as id, date_created as template_date_created, creator as template_creator, type as view_type
                    FROM task_mgmt.views
                    WHERE views.view_id = $1 AND views.deleted IS NOT TRUE`,
            },
        ],
        ({ type }) => type === publicTemplateRow.template_type
    );
    if (!entityQuery) {
        throw new PublicTemplateError('Template not found', 'PUBTEMP_026', 500);
    }
    const { rows: entityRows } = await db.promiseReplicaQuery(entityQuery.query, [template_id]);

    if (!entityRows.length) {
        throw new PublicTemplateError('Template not found', 'PUBTEMP_027', 500);
    }
    const entityRow = entityRows[0];

    const optionsQuery = `
        SELECT template_options.options,
               template_options.settings_counts as counts,
               template_options.description,
               template_options.name
        FROM task_mgmt.template_options
        WHERE template_options.template_id = $1 AND
              template_options.type = $2`;

    const { rows: optionsRows } = await db.promiseReplicaQuery(optionsQuery, [
        entityRow.id,
        publicTemplateRow.template_type,
    ]);
    const optionsRow = optionsRows.length ? optionsRows[0] : {};

    const row = {
        ...publicTemplateRow,
        ...entityRow,
        ...optionsRow,
        type: publicTemplateRow.template_type,
        ...{ name: optionsRow.name ? optionsRow.name : entityRow.name },
    };

    const { attachments } = await getPublicTemplateAttachments([row]);
    const { public_template_tags = [] } = await publicTemplateTagsMod.getPublicTemplateTags(language);
    const use_cases = await getUseCasesById(row.use_cases || []);

    // add public template tags
    const public_tags_result = [];
    if (row.public_tags && row.public_tags.length) {
        row.public_tags.forEach(tag_id => {
            const tag_index = public_template_tags.findIndex(tag => tag.id === tag_id);
            if (tag_index > -1) {
                public_tags_result.push(public_template_tags[tag_index]);
            }
        });
    }

    row.public_tags = public_tags_result;

    const template = {
        id: row.template_id,
        name: row.name,
        orderindex: row.orderindex,
        description: row.description,
        type: getTemplateType(row),
        tags: [],
        use_cases,
        images: attachments[row.type][row.template_id] || [],
        options: getTemplateOptions(row.options),
        counts: row.counts,
        public_tags: row.public_tags,
        date_created: row.template_date_created,
        creator: row.template_creator,
    };

    if (template.type === 'whiteboard') {
        template.whiteboardsViewSettings = {
            whiteboardVersion: row.template_extra?.whiteboardVersion || 'v2',
        };
    }

    template.images = template.images.map(image => ({ ...image, user: null }));

    const [templateWithGlobalStats] = await extendGroupedTemplatesResultsByUsageStatGlobal([template]);

    return templateWithGlobalStats;
}

export async function getPublicTemplateReq(req, resp, next) {
    const { template_id } = req.params || {};

    try {
        const result = await getPublicTemplate(template_id, getPreferredLanguage(req));
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}
