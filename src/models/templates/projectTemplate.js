import config from 'config';
import { uniqBy } from 'lodash';
import { getLogger } from '@clickup/shared/utils-logging';
import {
    loadTemplatesUserUsageData,
    applyTemplatesPaginationByUsedCountUsingName,
} from '@clickup-legacy/models/templates/templateUserUsedCountHelper';
import { canUseGanttLibrary } from '@clickup-legacy/models/integrations/split/squadTreatments/unclaimedTreatments';
import { schedulingWorkerConfig } from '@clickup-legacy/models/integrations/split/squadTreatments/templatesTreatments';
import * as async from '../../utils/asyncHelper';
import * as access from '../../utils/access2';
import { canUseSchedulingServerForProject } from '../time/services/tempSchedulingFeatureFlag';
import { ClickUpError } from '../../utils/errors';
import * as db from '../../utils/db';
import * as genericHelpers from '../helpers';
import * as input from '../../utils/input_validation';
import * as projectMod from '../project/CRUD';
import * as groupMod from '../groups';
import * as projectCopy from '../project/projectCopy';
import * as sqs from '../../utils/v1-ws-message-sending';
import * as templateHelpers from './templateHelpers';
import * as templateTags from './templateTags';
import { formatUser } from '../user/userProvider';
import * as attachmentMod from '../attachment/attachment';
import * as groupSharingPaywall from '../group_sharing/paywall';
import { PublicSharingCodes, StatusErrorCodes } from '../../utils/errors/constants';
import { queryProxyFactory } from '../../utils/queryProxy/queryProxyFactory';
import { CachedTemplatesQueryProxyHttpClient } from '../../utils/queryProxy/CachedQueryProxyHttpClient';
import { QueryFamily } from './queryMap/queryProxyMap';
import { ProjectQueryKey } from './queryMap/queryClasses/ProjectQueries';
import { AttachmentQueryKey } from './queryMap/queryClasses/AttachmentQueries';
import { TemplateQueryKey } from './queryMap/queryClasses/TemplateQueries';
import { DurationMetricObject, EntityType, TemplateSource, TemplateAction } from './templateMetricsCommons';
import { TemplateTracer } from './templateTracer/templateTracer';
import { entitlementService, EntitlementName } from '../entitlements/entitlementService';
import { getWorkspaceSettings } from '../team/services/settingsService';
import { assertCorrectChallengeTokenOrThrow, getSecurePublicKey } from './publicKey';
import { deduplicateTemplateIdsByPermanentTemplateId, TemplateType } from './templateIdHelpers';
import { generateTemplateOptions } from './templateOptionsGenerator';
import { CuPublicTemplatesRepository } from './publicTemplates/publicTemplateRepository';

const logger = getLogger('projectTemplates');
const TemplateError = ClickUpError.makeNamedError('projectTemplates');
const AccessError = ClickUpError.makeNamedError('access');

async function promiseAccessProjectTemplateUpdate(userid, project_id, template_id, options = {}) {
    const [project, template] = await Promise.all([
        access.checkAccessProjectAsync(userid, project_id, options),
        access.promiseAccessProjectTemplate(userid, template_id),
    ]);

    if (project.team_id !== template.team) {
        logger.warn({ msg: 'Unauthorized space template edit attempt by user', template_id, project_id });
        throw new AccessError('You do not have permission to edit this template', 'ACCESS_040', 403);
    }
    return project;
}

function _getTemplates(userid, options, cb) {
    if (!options.team_id) {
        cb({
            err: 'Must provide a filter',
            status: 400,
            ECODE: 'PTEMP_003',
        });
        return;
    }
    async.parallel(
        {
            accessTeam(para_cb) {
                if (!options.team_id || options.team_id === config.public_template_team) {
                    para_cb();
                    return;
                }
                access.checkAccessTeam(
                    userid,
                    options.team_id,
                    { permissions: [config.permission_constants.can_create_spaces] },
                    para_cb
                );
            },
            templates(para_cb) {
                let query = `
                    SELECT projects.id,
                        projects.name,
                        projects.permanent_template_id,
                        projects.date_updated,
                        template_options.options
                    FROM task_mgmt.projects 
                        LEFT OUTER JOIN task_mgmt.template_options
                            ON ( projects.id::text = template_options.template_id
                                AND template_options.type = 'project')
                    WHERE projects.template = true
                        AND projects.deleted = false
                        AND projects.archived = false`;
                const params = [userid];

                if (options.team_id) {
                    params.push(options.team_id);
                    query += ` AND projects.team = $${params.length}`;
                }

                // handle project privacy settings
                query += `
                    AND (projects.private = false
                        OR (projects.id 
                            IN (SELECT project_id
                                FROM task_mgmt.project_members
                                WHERE userid = $1)))`;

                db.readQuery(query, params, para_cb);
            },
        },
        (err, result) => {
            if (err) {
                if (err.ECODE) {
                    cb(err);
                } else {
                    logger.error({
                        msg: 'Failed to look up templates',
                        err,
                        ECODE: 'PTEMP_002',
                        status: 500,
                    });
                    cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'PTEMP_002',
                    });
                }
            } else {
                const deduplicatedTemplateIds = deduplicateTemplateIdsByPermanentTemplateId(
                    TemplateType.Space,
                    result.templates.rows,
                    userid
                ).map(template => template.id);
                let templates = uniqBy(
                    result.templates.rows.filter(template => deduplicatedTemplateIds.includes(template.id)),
                    'id'
                );
                templates = templateHelpers.permTemplateIdIfExists(templates);

                cb(null, { templates });
            }
        }
    );
}

export function getTemplates(req, resp, next) {
    const userid = req.decoded_token.user;
    const { team_id } = req.query;

    _getTemplates(userid, { team_id }, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

async function getTemplateObject(userid, template_id, use_master) {
    const params = [template_id];
    const query = `
        SELECT  
            projects.id, 
            projects.id as original_id,
            coalesce(template_options.name, projects.name) as name,
            template_options.description,
            template_options.settings_counts as counts,
            projects.public_sharing,
            projects.permissions AS template_visibility,
            projects.permanent_template_id,
            template_options.options,
            projects.date_updated,
            projects.date_created,
            users.id as userid,
            users.email,
            users.username,
            users.color,
            users.profile_picture_key
        FROM task_mgmt.projects
        LEFT JOIN task_mgmt.template_options 
            ON ( 
                projects.id :: text = template_options.template_id 
                AND template_options.TYPE = 'project' 
            )
        LEFT OUTER JOIN task_mgmt.users
            ON users.id = projects.creator
        WHERE
            projects.template = TRUE
            AND projects.deleted = FALSE
            AND projects.id = $1
    `;

    try {
        await access.promiseAccessProjectTemplate(userid, template_id);

        let result;
        if (use_master) {
            result = await db.promiseReadQuery(query, params);
        } else {
            result = await db.promiseReplicaQuery(query, params);
        }

        const [template] = result.rows;
        const [attachments, tags] = await Promise.all([
            attachmentMod.getAttachmentsOfParents([template.permanent_template_id], config.attachments.types.project),
            templateTags.getTemplateTags([template.permanent_template_id], 'project'),
        ]);

        template.template_members = [];
        if (template.template_visibility === config.template_permissions.members) {
            const response = await db.promiseReadQuery(
                `SELECT userid
                FROM task_mgmt.project_template_members
                WHERE project_id = $1`,
                [template.id]
            );
            template.template_members = response.rows.map(row => row.userid);
        }

        template.attachments = attachments.attachments[template.permanent_template_id] || [];
        template.tags = tags[template.permanent_template_id] || [];

        const { permanent_template_id } = template;
        template.id =
            permanent_template_id && permanent_template_id.startsWith('t-') ? permanent_template_id : template.id;
        delete template.permanent_template_id;
        template.public_key = template.public_sharing ? getSecurePublicKey(template.id) : null;

        if (template.userid) {
            template.creator = formatUser({
                id: template.userid,
                username: template.username,
                email: template.email,
                color: template.color,
                profile_picture_key: template.profile_picture_key,
            });
        } else {
            template.creator = null;
        }

        delete template.userid;
        delete template.username;
        delete template.email;
        delete template.color;
        delete template.profile_picture_key;

        return template;
    } catch (e) {
        throw new TemplateError(e, 'PRJTEMP_777', 500);
    }
}

export async function _getTemplatesV2(userid, team_id, options = {}) {
    let accessResponse;
    try {
        accessResponse = await access.promiseAccessTeam(userid, team_id, {});

        let last_template;
        const params = [];
        let adminWhere = '';
        if ([config.team_roles.owner, config.team_roles.admin].includes(accessResponse.role)) {
            adminWhere = ` OR projects.permissions = $${params.push(config.template_permissions.admin)} `;
        }

        let nameWhere = '';
        let searchForNameInTagsJoin = '';
        if (options.name) {
            if (options.desc) {
                params.push(`%${options.name.toLowerCase()}%`);
                params.push(`%${options.desc.toLowerCase()}%`);
                nameWhere = ` AND (LOWER(coalesce(template_options.name, projects.name)) ILIKE $${
                    params.length - 1
                } OR LOWER(template_options.description) ILIKE $${params.length} OR team_template_tags.name ILIKE $${
                    params.length - 1
                })`;
            } else {
                params.push(`%${options.name.toLowerCase()}%`);
                nameWhere = ` AND LOWER(coalesce(template_options.name, projects.name)) ILIKE $${params.length} OR team_template_tags.name ILIKE $${params.length} )`;
            }

            searchForNameInTagsJoin = `
                    LEFT OUTER JOIN task_mgmt.template_tags
                        ON template_tags.template_id = projects.permanent_template_id
                        AND template_tags.template_type = 'project'
                        AND template_tags.deleted IS NOT TRUE
                    LEFT OUTER JOIN task_mgmt.team_template_tags
                    ON template_tags.tag_id = team_template_tags.id `;
        }

        let date_created_where = ``;
        if (options.date_created) {
            params.push(options.date_created);
            date_created_where = `AND projects.date_created > $${params.length}`;
        }

        let tagsJoin = ``;
        let tagsWhere = ``;
        if (options.tags && options.tags.length) {
            tagsJoin = `
                LEFT OUTER JOIN task_mgmt.template_tags ON template_tags.template_id = projects.permanent_template_id AND template_tags.template_type = 'project' AND (template_tags.deleted = false OR template_tags.deleted IS NULL)
            `;
            params.push(options.tags);
            tagsWhere = ` AND template_tags.tag_id = ANY($${params.length})`;
        }

        let query = `
            SELECT 
            ${
                options.only_counts
                    ? `count(*)::INT`
                    : `    
                team_members.userid, 
                projects.id, 
                projects.id as original_id,
                coalesce(template_options.name, projects.name) as name,
                template_options.description,
                template_options.settings_counts as counts,
                projects.public_sharing,
                projects.permissions AS template_visibility,
                projects.permanent_template_id,
                template_options.options,
                projects.date_updated,
                projects.date_created,
                users.id as userid,
                users.email,
                users.username,
                users.color,
                users.profile_picture_key`
            }
            FROM task_mgmt.team_members
            JOIN task_mgmt.projects
            ON projects.team = team_members.team_id
            LEFT JOIN task_mgmt.template_options 
                ON ( 
                    projects.id :: text = template_options.template_id 
                    AND template_options.TYPE = 'project' 
                )
            LEFT OUTER JOIN task_mgmt.users
                ON users.id = projects.creator
            ${searchForNameInTagsJoin || tagsJoin}
            WHERE
                team_members.userid = $${params.push(userid)}
                AND projects.template = TRUE
                AND projects.deleted = FALSE
                AND projects.team = $${params.push(team_id)}
                ${nameWhere}
                ${tagsWhere}
                ${date_created_where}
                AND (
                    (
                        projects.permissions = $${params.push(config.template_permissions.public)}
                        OR projects.permissions IS NULL
                    )
                    OR
                    (
                        projects.permissions = $${params.push(config.template_permissions.team_members)}
                        AND team_members.role != $${params.push(config.team_roles.guest)}
                    )
                    ${adminWhere}
                    OR (
                        projects.permissions = $${params.push(config.template_permissions.members)}
                        AND EXISTS(
                            SELECT 1
                            FROM
                                task_mgmt.group_members
                                JOIN task_mgmt.project_template_group_members ON
                                    project_template_group_members.group_id = group_members.group_id AND
                                    project_template_group_members.project_id = projects.id
                            WHERE
                                group_members.userid = $${params.push(userid)}
                        )
                    )
                    OR (
                        projects.permissions = $${params.push(config.template_permissions.members)}
                        AND EXISTS(
                            SELECT 1 
                            FROM task_mgmt.project_template_members
                            WHERE
                            project_template_members.project_id = projects.id
                                AND project_template_members.userid = $${params.push(userid)}
                        )
                    )
                    OR (
                        projects.permissions = $${params.push(config.template_permissions.private)}
                        AND projects.owner = $${params.push(userid)}
                    )
                )`;

        if (options.creator) {
            params.push(options.creator);
            query += ` AND projects.creator = $${params.length}`;
        }

        let last_page = true;
        let { rows: templates } = await db.promiseReadQuery(query, params);

        if (!options.only_counts) {
            templates = await loadTemplatesUserUsageData(
                userid,
                'project',
                templates,
                row => row.permanent_template_id,
                { used_count: 0, last_used: null }
            );
            ({ result: templates, isLastPage: last_page } = applyTemplatesPaginationByUsedCountUsingName(
                templates,
                options.start
            ));

            const deduplicatedTemplateIds = deduplicateTemplateIdsByPermanentTemplateId(
                TemplateType.Space,
                templates,
                userid
            ).map(template => template.id);
            templates = uniqBy(
                templates.filter(row => deduplicatedTemplateIds.includes(row.id)),
                'id'
            );
        }
        if (!last_page) {
            last_template = templates[templates.length - 1];
        }

        const permanent_template_ids = templates.map(template => template.permanent_template_id);
        const [attachments, tags] = await Promise.all([
            attachmentMod.getAttachmentsOfParents(permanent_template_ids, config.attachments.types.project),
            templateTags.getTemplateTags(permanent_template_ids, 'project'),
        ]);

        for (const template of templates) {
            template.template_members = [];
            if (template.template_visibility === config.template_permissions.members) {
                const [{ rows: project_members }, { rows: project_group_members }] = await Promise.all([
                    db.promiseReadQuery(
                        `SELECT userid
                        FROM task_mgmt.project_template_members
                        WHERE project_id = $1`,
                        [template.id]
                    ),
                    db.promiseReadQuery(
                        `SELECT group_id
                        FROM task_mgmt.project_template_group_members
                        WHERE project_id = $1`,
                        [template.id]
                    ),
                ]);
                template.template_members = project_members.map(row => row.userid);
                const template_group_members = project_group_members.map(row => row.group_id);
                template.template_group_members = await groupMod.getUserGroupsAsync(userid, null, {
                    skipAccess: true,
                    group_ids: template_group_members,
                });
            }

            template.attachments = attachments.attachments[template.permanent_template_id] || [];
            template.tags = tags[template.permanent_template_id] || [];

            const { permanent_template_id } = template;
            template.id =
                permanent_template_id && permanent_template_id.startsWith('t-') ? permanent_template_id : template.id;
            delete template.permanent_template_id;
            template.public_key = template.public_sharing ? getSecurePublicKey(template.id) : null;

            if (template.userid) {
                template.creator = formatUser({
                    id: template.userid,
                    username: template.username,
                    email: template.email,
                    color: template.color,
                    profile_picture_key: template.profile_picture_key,
                });
            } else {
                template.creator = null;
            }

            delete template.userid;
            delete template.username;
            delete template.email;
            delete template.color;
            delete template.profile_picture_key;
        }

        return {
            templates: options.only_counts ? [] : templates,
            last_page,
            paging: { ...(last_template && { start_id: last_template.template_id, start: last_template.name }) },
            full_count: options.only_counts ? templates[0].count : null,
        };
    } catch (err) {
        throw new TemplateError(err, 'PRJTEMP_003', 500);
    }
}

export async function getTemplatesV2(req, res) {
    const userid = req.decoded_token.user;
    const { team_id } = req.query;

    const options = {
        team_id,
    };

    let results;
    try {
        results = await _getTemplatesV2(userid, team_id, options);
    } catch (err) {
        res.status(err.status || 500).send(err);
        return;
    }
    res.status(200).send(results);
}

async function _getTemplate(template_id, userid, options = {}) {
    const public_key = !!options.public_key; // default to false if not provided
    const { proxyClient = queryProxyFactory() } = options;

    try {
        const { return_template } = options;
        const { rows } = await proxyClient.readQueryAsync({
            queryFamily: QueryFamily.Project,
            queryKey: ProjectQueryKey.SelectProjectTemplate,
            queryParams: [template_id],
            options: { return_template },
        });

        if (!rows.length) {
            throw new TemplateError('Could not find template', 'PRJTEMP_001', 404);
        }
        const template = rows[0];

        if (!template.public_sharing) {
            await access.promiseAccessProjectTemplate(userid, template_id);
        }

        if (!options.return_template) {
            const { permanent_template_id } = template;
            template.id =
                permanent_template_id && permanent_template_id.startsWith('t-') ? permanent_template_id : template_id;
            delete template.permanent_template_id;
            delete template.public_sharing;
        }

        if (public_key) {
            template.public_key = getSecurePublicKey(template.id);
        }
        return template;
    } catch (err) {
        throw new TemplateError(err, 'PRJTEMP_000', 500);
    }
}

export async function getPublicTemplate(req, res) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    const token = req.query ? req.query.token : null;

    try {
        assertCorrectChallengeTokenOrThrow(token, template_id);

        const options = {
            proxyClient: await templateHelpers.createProxyClient(template_id, 'project', userid),
        };

        template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.spaces, options);
        const template = await _getTemplate(template_id, userid, options);
        res.status(200).send(template);
    } catch (err) {
        res.status(err.status || 400).send(err);
    }
}

export function getDefaultTemplates(req, resp, next) {
    const userid = req.decoded_token.user;
    const team_id = config.public_template_team;

    _getTemplates(userid, { team_id }, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

/**
 * Create a Project template
 * @param {number} userid
 * @param {object} options - Options list is not complete
 * @param {boolean} [options.custom_fields] - Include All CFs, but Relationships
 * @param {boolean} [options.retain_permissions] - If FALSE make Project and underlying objects public
 * @param {boolean} [options.relationships] - Include Relationships: List and Task
 * @param {function} cb
 */
function _createTemplate(userid, options, cb) {
    const include_tasks = !(options.include_tasks === 'false' || options.include_tasks === false);
    const now = Date.now();
    let date_created;
    let team_id;

    const { proxyClient = queryProxyFactory() } = options;

    const is_template_update = !!options.template_id;
    const PROJECT_TEMPLATE_TYPE = config.get('template_types.project');

    async.parallel(
        {
            async checkAccessProject(para_cb) {
                if (!is_template_update && options.public_template) {
                    para_cb();
                    return;
                }
                try {
                    const permissions = [config.permission_constants.template];
                    let result;
                    if (is_template_update) {
                        result = await promiseAccessProjectTemplateUpdate(
                            userid,
                            options.project_id,
                            options.template_id,
                            { permissions }
                        );
                    } else {
                        result = await access.checkAccessProjectAsync(userid, options.project_id, {
                            permissions,
                            check_deleted: true,
                        });
                    }
                    para_cb(null, result);
                } catch (err) {
                    para_cb(err);
                }
            },
        },
        err => {
            if (err) {
                cb(err);
                return;
            }
            let template_id = null;
            let permanent_template_id;
            let settings_counts = {};
            let count_details = {};
            let workspace_id;
            let old_to_new;

            options.template = true;

            if (options.accept_submitted_template) {
                options.return_immediately = false;
            }

            if (options.return_immediately) {
                cb(null, {});
            }

            async.series(
                [
                    series_cb => {
                        const _options = Object.assign({}, options);

                        _options.graceful_shutdown = options.return_immediately;
                        _options.return_immediately = false;
                        _options.default_skip_access = true;
                        _options.set_deleted = true;
                        _options.validate_name = false;
                        _options.top = true;
                        _options.skip_paywall = true;
                        _options.skip_privacy = !options.retain_permissions;
                        _options.proxyClient = proxyClient;

                        delete _options.retain_permissions;

                        options[TemplateTracer.KEY]?.suspendFinishProcessing();
                        projectCopy
                            ._copyProject(userid, options.project_id, _options, (err2, result) => {
                                options[TemplateTracer.KEY]?.restoreFinishProcessing();

                                if (err2) {
                                    series_cb(err2);
                                } else {
                                    template_id = result.id;
                                    permanent_template_id = result.permanent_template_id;
                                    workspace_id = result.workspace_id;
                                    old_to_new = result.old_to_new;

                                    if (options.accept_submitted_template) {
                                        cb(null, { template_id, permanent_template_id });
                                        cb = () => {};
                                    }

                                    series_cb();
                                }
                            })
                            .catch(e =>
                                logger.error({
                                    msg: 'Failed to copy project',
                                    user_id: userid,
                                    project_id: options.project_id,
                                    e,
                                })
                            );
                    },
                    async series_cb => {
                        // get settings counts
                        try {
                            ({ counts: settings_counts, count_details } = await templateHelpers.getSettingsCounts(
                                template_id,
                                'project'
                            ));
                            series_cb();
                        } catch (err2) {
                            series_cb(err2);
                        }
                    },
                    series_cb => {
                        const _options = {
                            archived: options.archived,
                            assigned_comment: options.assigned_comment,
                            assignees: options.assignees,
                            attachments: options.attachments,
                            automation: options.automation,
                            comment: options.comment,
                            comment_attachments: options.comment_attachments,
                            content: options.content,
                            custom_fields: options.custom_fields,
                            due_date: options.due_date,
                            due_date_time: options.due_date_time,
                            external_dependencies: options.external_dependencies,
                            followers: options.followers,
                            relationships: options.relationships,
                            include_tasks,
                            include_views: options.include_views,
                            internal_dependencies: options.internal_dependencies,
                            old_assignees: options.old_assignees,
                            old_checklists: options.old_checklists,
                            old_checklist_item_status: options.old_checklist_item_status,
                            old_due_date: options.old_due_date,
                            old_followers: options.old_followers,
                            old_members: options.old_members,
                            old_start_date: options.old_start_date,
                            old_status: options.old_status,
                            old_statuses: options.old_statuses,
                            old_subcat_content: options.old_subcat_content,
                            old_subtask_assignees: options.old_subtask_assignees,
                            old_tags: options.old_tags,
                            old_duration: options.old_duration,
                            recur_settings: options.recur_settings,
                            remap_start_date: options.remap_start_date,
                            priority: options.priority,
                            project_id: options.project_id,
                            retain_permissions: options.retain_permissions,
                            start_date: options.start_date,
                            start_date_time: options.start_date_time,
                            status: options.status,
                            subtasks: options.subtasks,
                            subtask_assignees: options.subtask_assignees,
                            tags: options.tags,
                            time_estimate: options.time_estimate,
                            custom_type: options.custom_type,
                            count_details,
                        };

                        db.writeQuery(
                            "INSERT INTO task_mgmt.template_options(type, template_id, options, settings_counts, description, workspace_id) VALUES('project', $1, $2, $3, $4, $5)",
                            [template_id, _options, settings_counts, options.description, workspace_id],
                            series_cb
                        );
                    },
                    async series_cb => {
                        if (options.template_id) {
                            let result;
                            try {
                                result = await db.writeQueryAsync(
                                    `UPDATE task_mgmt.projects
                                            SET deleted = true, date_deleted = $2, deleted_by = $3, permanent_template_id = NULL
                                            WHERE id = $1 RETURNING date_created`,
                                    [options.template_id, new Date().getTime(), userid]
                                );
                                if (result.rows.length) {
                                    [{ date_created }] = result.rows;
                                }
                            } catch (err2) {
                                series_cb(err2);
                                return;
                            }
                            if (permanent_template_id) {
                                CachedTemplatesQueryProxyHttpClient.invalidateCachedQueryResult(
                                    { templateType: 'project', templateId: permanent_template_id },
                                    {
                                        queryFamily: QueryFamily.Templates,
                                        queryKey: TemplateQueryKey.IdByPermanentId,
                                        queryParams: [permanent_template_id],
                                        options: { table: 'projects' },
                                    }
                                );
                            }
                        }
                        series_cb();
                    },
                    series_cb => {
                        db.writeQuery(
                            'UPDATE task_mgmt.projects SET deleted = false, date_deleted = null, date_created = $2, date_updated = $3 WHERE id = $1 RETURNING team',
                            [template_id, date_created || now, now],
                            (err2, result) => {
                                if (err2) {
                                    series_cb(err2);
                                    return;
                                }

                                team_id = result.rows[0].team;
                                series_cb();
                            }
                        );
                    },
                    series_cb => {
                        if (!options.tags || !options.tags.length || !permanent_template_id) {
                            series_cb();
                            return;
                        }
                        let values = `VALUES`;
                        const params = [permanent_template_id, 'project', team_id];
                        options.tags.forEach((tag, idx) => {
                            if (idx > 0) {
                                values += `,`;
                            }
                            params.push(tag.id);
                            values += `($1, $2, $${params.length}, false, $3)`;
                        });
                        db.writeQuery(
                            `
                            INSERT INTO task_mgmt.template_tags (template_id, template_type, tag_id, deleted, workspace_id) ${values}
                                ON CONFLICT (template_id, template_type, tag_id) DO UPDATE SET deleted = FALSE, deleted_by = NULL, date_deleted = NULL`,
                            params,
                            series_cb
                        );
                    },
                    series_cb => {
                        // copy images
                        proxyClient.replicaQuery(
                            {
                                queryFamily: QueryFamily.Attachment,
                                queryKey: AttachmentQueryKey.GetAttachmentsByParent,
                                queryParams: [`t-${options.project_id}`],
                            },
                            (attErr, attResult) => {
                                if (attErr) {
                                    series_cb(attErr);
                                    return;
                                }

                                if (!attResult || !attResult.rows.length) {
                                    series_cb();
                                    return;
                                }

                                const { rows: attachments } = attResult;

                                attachmentMod.copyAttachments(
                                    userid,
                                    attachments,
                                    permanent_template_id,
                                    'project',
                                    { for_template: true, team_id },
                                    series_cb
                                );
                            }
                        );
                    },
                ],
                err2 => {
                    if (err2) {
                        if (!options.return_immediately) {
                            if (err2.ECODE) {
                                if (
                                    err2.ECODE === 'ITEM_076' ||
                                    err2.ECODE === 'CAT_205' ||
                                    err2.ECODE === 'SUBCAT_040' ||
                                    err2.ECODE === 'PROJ_165' ||
                                    err2.ECODE === 'PROJ_166' ||
                                    err2.ECODE === 'PROJ_167'
                                ) {
                                    sqs.sendWSMessage(
                                        options.permanent_template_id
                                            ? 'sendTemplateUpdateFailed'
                                            : 'sendTemplateCreateFailed',
                                        [
                                            userid,
                                            { ...err2, templateName: options.name },
                                            PROJECT_TEMPLATE_TYPE,
                                            options.ws_key,
                                            options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                        ]
                                    );
                                }
                                cb(err2);
                            } else {
                                cb({
                                    err: 'Internal server error',
                                    status: 500,
                                    ECODE: 'PTEMP_001',
                                });
                            }
                        } else {
                            if (
                                err2.ECODE === 'ITEM_076' ||
                                err2.ECODE === 'CAT_205' ||
                                err2.ECODE === 'SUBCAT_040' ||
                                err2.ECODE === 'PROJ_165'
                            ) {
                                sqs.sendWSMessage(
                                    options.template_id ? 'sendTemplateUpdateFailed' : 'sendTemplateCreateFailed',
                                    [
                                        userid,
                                        { ...err2, templateName: options.name },
                                        PROJECT_TEMPLATE_TYPE,
                                        options.ws_key,
                                        options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                    ]
                                );
                            }
                            const returnedError = {
                                msg: 'Can not create a Template',
                                ECODE: 'PRJTEMP_019',
                                status: 500,
                                project_id: options.project_id,
                                err: err2,
                            };
                            logger.error(returnedError);
                            options[TemplateTracer.KEY]?.finishTracing(returnedError);
                        }

                        return;
                    }

                    if (options.return_template) {
                        getTemplateObject(userid, template_id, true)
                            .then(template => {
                                if (options.template_id) {
                                    template.updating = true;
                                }

                                template.templateName = options.name;
                                sqs.sendWSMessage(is_template_update ? 'sendTemplateUpdated' : 'sendTemplateCreated', [
                                    userid,
                                    template,
                                    PROJECT_TEMPLATE_TYPE,
                                    options.ws_key,
                                    options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                ]);

                                if (!options.return_immediately) {
                                    cb(null, { template_id });
                                }
                            })
                            .catch(() => {});
                    } else if (!options.return_immediately) {
                        cb(null, { template_id });
                    } else if (options.template_v2) {
                        _getTemplate(template_id, userid)
                            .then(template => {
                                sqs.sendWSMessage(is_template_update ? 'sendTemplateUpdated' : 'sendTemplateCreated', [
                                    userid,
                                    template,
                                    PROJECT_TEMPLATE_TYPE,
                                    options.ws_key,
                                    options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                ]);
                            })
                            .catch(() => {});
                    } else {
                        projectMod._getProject(
                            userid,
                            template_id,
                            { include_removed: false, use_master: true },
                            (err3, project) => {
                                if (err3) {
                                    return;
                                }
                                sqs.sendWSMessage('sendProjectCopyComplete', [
                                    userid,
                                    project,
                                    options.ws_key,
                                    true,
                                    options[TemplateTracer.KEY]?.getHistoryTracingId(),
                                    true,
                                ]);
                            }
                        );
                    }

                    options[TemplateTracer.KEY]?.finishTracing();
                }
            );
        }
    );
}
async function _promiseCreateTemplate(userid, options) {
    return new Promise((res, rej) => {
        _createTemplate(userid, options, (err, data) => {
            if (err) rej(err);
            else res(data);
        });
    });
}

export async function createTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    const { project_id } = req.params;
    const options = req.body;
    const { template_id } = options;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    options.template_v2 = req.v2;
    options.ws_key = ws_key;
    options.project_id = project_id;
    options.return_template = true;
    options.workspace_id = req.headers['x-workspace-id'];

    const templateTracer = new TemplateTracer(EntityType.PROJECT, TemplateAction.TEMPLATE_SAVING, {
        userId: userid,
        sourceId: project_id,
        destTemplateId: template_id,
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        if (template_id && template_id.startsWith('t-')) {
            try {
                options.old_template_id = template_id;
                options.template_id = await templateHelpers._idByPermanentId(
                    template_id,
                    config.hierarchy_tables.spaces
                );
            } catch (err) {
                next(err);
                templateTracer.finishTracing(err);
                return;
            }
        }

        /** @todo set to false */
        options.retain_permissions = genericHelpers.toBooleanDefault(options.retain_permissions, true);

        _createTemplate(userid, options, (err, result) => {
            if (err) {
                next(err);
                templateTracer.finishTracing(err);
            } else {
                resp.status(200).send(result);
            }
        });
    });
}

function _deleteTemplate(userid, options, cb) {
    projectMod._deleteProjectCheckingAccess(userid, options.template_id, null, true, { isTemplate: true }, cb);
}

export async function deleteTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    try {
        template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.spaces);
    } catch (err) {
        next(err);
        return;
    }

    _deleteTemplate(userid, { template_id }, (err, result) => {
        if (err) {
            next(err);
        } else {
            resp.status(200).send(result);
        }
    });
}

function _createProjectFromDefaultTemplate(userid, options, cb) {
    if (!options.team_id) {
        cb({ err: 'Team id required', status: 500, ECODE: '' });
        return;
    }

    const { proxyClient = queryProxyFactory() } = options;

    proxyClient.readQuery(
        {
            queryFamily: QueryFamily.Project,
            queryKey: ProjectQueryKey.SelectTeamFromProject,
            queryParams: [options.template_id],
        },
        async (err, result) => {
            if (err) {
                cb({ err: 'Internal server error', status: 500, ECODE: 'PTEMP_008' });
                return;
            }
            if (result.rows.length === 0) {
                cb({ err: 'Template not found', status: 404, ECODE: 'PTEMP_009' });
                return;
            }

            // if (result.rows[0].team !== config.public_template_team) {
            //     cb({ err: 'Template not a default template', status: 404, ECODE: 'PTEMP_010' });
            //     return;
            // }
            try {
                const is_public = await templateHelpers.isTemplatePublic(options.permanent_template_id, 'project');
                const { defaultNewSpacesToPrivate } = await getWorkspaceSettings(options.team_id, {
                    only: { defaultNewSpacesToPrivate: 1 },
                });
                if (is_public) {
                    options.public_template = true;
                    options.skip_privacy = !defaultNewSpacesToPrivate;
                    options.default_skip_access = true;
                }
            } catch (err1) {
                cb(new TemplateError(err1, 'PTEMP_014'));
                return;
            }

            options.set_deleted = true;
            options.set_undeleted = true;

            options.include_col_settings = true;
            options.from_template = true;
            projectCopy._copyProject(userid, options.template_id, options, cb).catch(e =>
                logger.error({
                    msg: 'Failed to copy project',
                    user_id: userid,
                    project_id: options.template_id,
                    e,
                })
            );
        }
    );
}

export async function createProjectFromDefaultTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    options.ws_key = ws_key;
    options.send_project_create_ws = true;
    options.workspace_id = req.headers['x-workspace-id'];

    /* saving for usage tracking */
    options.permanent_template_id = template_id;

    options[DurationMetricObject.KEY] = new DurationMetricObject(EntityType.PROJECT, TemplateSource.DEFAULT_TEMPLATE);

    const templateTracer = new TemplateTracer(EntityType.PROJECT, TemplateAction.TEMPLATE_APPLICATION, {
        userId: userid,
        sourceTemplateId: template_id,
        templateSource: TemplateSource[TemplateSource.DEFAULT_TEMPLATE],
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        try {
            options.proxyClient = await templateHelpers.createProxyClient(template_id, 'project', userid);
            templateTracer.proxyClientForSource = options.proxyClient;

            template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.spaces, options);
        } catch (err) {
            next(err);
            templateTracer.finishTracing(err);
            return;
        }

        options.template_id = template_id;

        _createProjectFromDefaultTemplate(userid, options, (err, result) => {
            if (err) {
                next(err);
                templateTracer.finishTracing(err);
            } else {
                resp.status(200).send(result);
            }
        });
    });
}

async function _copyPublicTemplate(userid, template_id, options) {
    if (options.proxyClient == null) {
        options.proxyClient = queryProxyFactory();
    }

    if (options.team_id == null) {
        throw new TemplateError('Team id must be provided', 'PRJTEMP_034', 400);
    }

    if (
        options.template_visibility != null &&
        !Object.values(config.template_permissions).includes(options.template_visibility)
    ) {
        throw new TemplateError('Invalid permission type', 'PRJTEMP_034', 400);
    }

    let result;
    let approval_status = null;
    let permanent_template_id;
    try {
        result = await options.proxyClient.readQueryAsync({
            queryFamily: QueryFamily.Project,
            queryKey: ProjectQueryKey.TemplateProject,
            queryParams: [template_id],
        });
        [{ permanent_template_id }] = result.rows;
        approval_status = permanent_template_id
            ? await CuPublicTemplatesRepository.loadPublicTemplateApprovalStatus(permanent_template_id, 'project')
            : null;
    } catch (err) {
        throw new TemplateError(err, 'PRJTEMP_030');
    }

    if (!result.rows.length) {
        throw new TemplateError('Page not found', 'PRJTEMP_031', 404);
    }

    const [{ public_sharing, description }] = result.rows;

    if (!(options.accept_submitted_template || public_sharing || approval_status)) {
        throw new TemplateError('Page not found', 'PRJTEMP_032', 404);
    }

    if (!approval_status && public_sharing) {
        assertCorrectChallengeTokenOrThrow(options.public_key, permanent_template_id);
    }

    const template_options = result.rows[0].options ? { ...result.rows[0].options } : {};
    delete template_options.parent;
    const { defaultNewSpacesToPrivate } = await getWorkspaceSettings(options.team_id, {
        only: { defaultNewSpacesToPrivate: 1 },
    });
    const copy_options = generateTemplateOptions(
        {
            ...options,
            ...template_options,
            default_skip_access: true,
            set_deleted: true,
            set_undeleted: true,
            create_notifications: true,
            from_template: true,
            public_template: true,
            validate_name: false,
            skip_privacy: !defaultNewSpacesToPrivate,
            permanent_template_id,
            return_immediately: true,
        },
        userid
    );

    copy_options.external_dependencies = false;

    let response;
    if (options.copy !== false) {
        try {
            response = await projectCopy._promiseCopyProject(userid, template_id, copy_options);
        } catch (err) {
            throw err;
        }
    }
    if (options.template_visibility === undefined) {
        return response;
    }
    const temp_options = generateTemplateOptions(
        {
            ...options,
            ...template_options,
            template_v2: true,
            default_skip_access: true,
            public_template: true,
            project_id: template_id,
            validate_name: false,
            name: options.template_name,
            from_v2_template: true,
            description: options.description || description,
            return_template: true,
            return_immediately: true,
        },
        userid
    );
    delete temp_options.template_id;
    let template_response;
    try {
        template_response = await _promiseCreateTemplate(userid, temp_options);
    } catch (err) {
        throw err;
    }
    return response || template_response;
}

export async function copyPublicTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    options.workspace_id = req.headers['x-workspace-id'];
    options.ws_key = ws_key;
    options.middleware_queries = req.middleware_queries;
    options.return_template = true;
    // options.proxyClient = queryProxyFactory(config.get('app.url'));

    options[DurationMetricObject.KEY] = new DurationMetricObject(EntityType.PROJECT, TemplateSource.PUBLIC_TEMPLATE);

    const templateTracer = new TemplateTracer(EntityType.PROJECT, TemplateAction.TEMPLATE_COPYING, {
        userId: userid,
        sourceTemplateId: template_id,
        templateSource: TemplateSource[TemplateSource.PUBLIC_TEMPLATE],
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        try {
            if (template_id === 'default_template') {
                options.proxyClient = await templateHelpers.createProxyClient(template_id, 'project', userid);
                templateTracer.proxyClientForSource = options.proxyClient;

                template_id = await templateHelpers._idByPermanentId(
                    config.default_onboarding_template_id,
                    config.hierarchy_tables.spaces,
                    options
                );
                const result = await _copyPublicTemplate(userid, template_id, options);
                resp.status(200).send(result);
            } else {
                options.proxyClient = await templateHelpers.createProxyClient(template_id, 'project', userid);
                templateTracer.proxyClientForSource = options.proxyClient;

                template_id = await templateHelpers._idByPermanentId(
                    template_id,
                    config.hierarchy_tables.spaces,
                    options
                );
                const result = await _copyPublicTemplate(userid, template_id, options);
                resp.status(200).send(result);
            }
        } catch (err) {
            next(err);
            templateTracer.finishTracing(err);
        }
    });
}

function checkWillHaveAccessToProject(userid, category_id, cb) {
    db.replicaQuery(
        `
        SELECT 
            projects.private, project_members.userid
        FROM 
            tasK_mgmt.projects
            LEFT JOIN task_mgmt.project_members
            ON project_members.userid = $2 AND project_members.project_id = projects.id
        WHERE
            projects.id = $1
    `,
        [category_id, userid],
        (err, result) => {
            if (err) {
                cb(err);
            } else if (result.rows && result.rows[0] && result.rows[0].private && !result.rows[0].userid) {
                cb({
                    err: 'You would not have access to the space created by this template',
                    status: 400,
                    ECODE: 'PTEMP_012',
                });
            } else {
                cb();
            }
        }
    );
}

function _createProjectFromTemplate(userid, options, cb) {
    options.default_skip_access = false;
    options.from_template = true;
    options.list_relationships = options.relationships;
    options.task_relationships = options.relationships;

    checkWillHaveAccessToProject(userid, options.template_id, async err => {
        if (err) {
            cb(err);
            return;
        }

        try {
            /** @todo REMOVE here and all references when we confident in gantt library */
            options.use_gantt_library = canUseGanttLibrary(userid);
            options.use_scheduling_server = await canUseSchedulingServerForProject(options.project_id);
            options.dry_run = schedulingWorkerConfig().dryRun;

            const is_public = await templateHelpers.isTemplatePublic(options.permanent_template_id, 'project');

            if (is_public) {
                options.public_template = true;
            }
        } catch (err1) {
            cb(new TemplateError(err1, 'PTEMP_013'));
            return;
        }

        projectCopy._copyProject(userid, options.template_id, options, cb).catch(e =>
            logger.error({
                msg: 'Failed to copy project',
                user_id: userid,
                project_id: options.template_id,
                e,
            })
        );
    });
}

export async function createProjectFromTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    const { template_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;
    options.workspace_id = req.headers['x-workspace-id'];

    options.ws_key = ws_key;
    options.set_deleted = true;
    options.set_undeleted = true;
    /** saving permanent_template_id for template usage */
    options.permanent_template_id = template_id;

    options[DurationMetricObject.KEY] = new DurationMetricObject(EntityType.PROJECT, TemplateSource.TEMPLATE_CENTER);

    const templateTracer = new TemplateTracer(EntityType.PROJECT, TemplateAction.TEMPLATE_APPLICATION, {
        userId: userid,
        sourceTemplateId: template_id,
        templateSource: TemplateSource[TemplateSource.TEMPLATE_CENTER],
    });
    options[TemplateTracer.KEY] = templateTracer;

    await templateTracer.withTracing(async () => {
        try {
            options.template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.spaces);
        } catch (err) {
            next(err);
            templateTracer.finishTracing(err);
            return;
        }

        _createProjectFromTemplate(userid, options, (err, result) => {
            if (err) {
                next(err);
                templateTracer.finishTracing(err);
            } else {
                resp.status(200).send(result);
            }
        });
    });
}

/**
 * Edit template name and description. Checks access, including verifying
 * workspace membership when updating public templates.
 * @param {string} userid - The user ID of the user making the request.
 * @param {Object} options
 * @param {string} options.template_id - The ID of the template to edit.
 * @param {string} options.template_name - The new name for the template.
 * @param {string} options.name - Alternative option to provide the template name.
 * @param {string} options.description - The new description for the template.
 * @param {boolean} options.template_v2 - Using the v2 access check logic.
 */
async function promiseEditTemplateName(userid, options) {
    const name = options.template_name || options.name;
    if (!input.validateSectionName(name)) {
        throw new TemplateError('Template name invalid', 'PTEMP_005', 400);
    }

    const accessCheck = async () => {
        if (options.template_v2) {
            const { team, public_sharing } = await access.promiseAccessProjectTemplate(userid, options.template_id);
            if (public_sharing) {
                await access.promiseAccessTeam(userid, team, {});
            }
        } else {
            await access.checkAccessProjectAsync(userid, options.template_id, {
                permissions: [config.permission_constants.template],
            });
        }
    };

    const validateName = async () => {
        const query = `
            SELECT id
            FROM task_mgmt.projects
            WHERE LOWER(name) = LOWER($1)
                AND template = true
                AND team = (SELECT team FROM task_mgmt.projects WHERE id = $2)
                AND projects.id != $2
                AND (SELECT name FROM task_mgmt.projects WHERE id = $2) != $1`;
        const params = [name, options.template_id];
        try {
            const result = await db.promiseReadQuery(query, params);
            if (result.rows?.length && result.rows[0].id !== options.template_id) {
                throw new TemplateError('Space name taken', 'PTEMP_006', 400);
            }
        } catch (err) {
            logger.error({
                msg: 'Failed to check if project name is taken',
                err,
                status: 500,
                ECODE: 'PTEMP_007',
            });
            throw new TemplateError('Internal server error', 'PTEMP_007', 500);
        }
    };

    // Run validation steps
    await Promise.all([accessCheck(), validateName()]);

    try {
        await db.promiseBatchQueries([
            {
                query: 'UPDATE task_mgmt.projects SET name = $1 WHERE id = $2',
                params: [name, options.template_id],
            },
            {
                query: `UPDATE task_mgmt.template_options SET name = $1, description = $2 WHERE template_id = $3 AND type = 'project'`,
                params: [name, options.description, options.template_id],
            },
        ]);
    } catch (err) {
        logger.error({
            msg: 'Failed to change template name and description',
            err,
            status: 500,
            ECODE: 'PTEMP_020',
        });
        throw new TemplateError(err, 'PTEMP_020', 500);
    }
}

// skips access it assumes it follows editTemplateName
async function editTemplatePermissions(template_id, options) {
    if (!template_id) {
        return;
    }

    if (
        options.template_visibility != null &&
        !Object.values(config.template_permissions).includes(options.template_visibility)
    ) {
        throw new TemplateError('Invalid permission type', 'PRJTEMP_010', 400);
    }
    if (options.public_sharing != null && typeof options.public_sharing !== 'boolean') {
        throw new TemplateError('Public sharing option must be of type BOOLEAN', 'PRJTEMP_027', 400);
    }

    let response;
    try {
        response = await db.promiseReadQuery(
            `
                SELECT 
                    permissions,
                    coalesce(teams.disable_template_pub_sharing, false) as disable_template_pub_sharing,
                    teams.disable_public_sharing,
                    teams.admin_public_share_override,
                    team_members.role,
                    teams.id as team_id
                FROM 
                    task_mgmt.projects
                    JOIN task_mgmt.teams ON teams.id = projects.team
                    JOIN task_mgmt.team_members ON team_members.team_id = teams.id AND team_members.userid = $2
                WHERE 
                    projects.id = $1
        `,
            [template_id, options.userid]
        );
    } catch (err) {
        throw new TemplateError(err, 'PRJTEMP_011', 500);
    }
    if (!response.rows || !response.rows.length) {
        throw new TemplateError('Could not find template', 'PRJTEMP_012', 404);
    }
    const prev_permissions = response.rows[0].permissions;
    const [
        { disable_template_pub_sharing = false, disable_public_sharing, admin_public_share_override, role, team_id },
    ] = response.rows || [{}];
    const queries = [];

    const template_updates = [];
    const template_updates_params = [];
    if (options.template_visibility !== undefined) {
        template_updates.push(`
            permissions = $${template_updates_params.push(options.template_visibility)}
        `);
        if (
            prev_permissions !== options.template_visibility &&
            prev_permissions === config.template_permissions.members
        ) {
            queries.push({
                query: `
                    DELETE FROM task_mgmt.project_template_members
                    WHERE project_id = $1
                `,
                params: [template_id],
            });
        }
    }

    if (
        disable_template_pub_sharing &&
        options.public_sharing &&
        (await entitlementService.checkEntitlement(team_id, EntitlementName.PublicLinkRestrictions))
    ) {
        throw new TemplateError('Do not have permission to perform that action', 'PRJTEMP_099', 400);
    } else if (
        options.public_sharing &&
        disable_public_sharing === true &&
        (!admin_public_share_override ||
            (Number(role) !== config.get('team_roles.owner') && Number(role) !== config.get('team_roles.admin')))
    ) {
        throw new TemplateError(
            'Cannot make folder template public',
            PublicSharingCodes.DisabledPublicSharingError,
            StatusErrorCodes.Unauthorized
        );
    } else if (options.public_sharing) {
        logger.debug({
            method: 'edit space template',
            userid: options.userid,
            template_id,
            public_sharing: options.public_sharing,
            disable_template_pub_sharing,
            disable_public_sharing,
            admin_public_share_override,
            role,
            can_plan_do_this_public_link_restrictions: await entitlementService.checkEntitlement(
                team_id,
                EntitlementName.PublicLinkRestrictions
            ),
        });
    }

    if (options.public_sharing !== undefined) {
        template_updates.push(`
            public_sharing = $${template_updates_params.push(options.public_sharing)}
        `);
    }
    if (template_updates.length) {
        queries.push({
            query: `
                UPDATE task_mgmt.projects 
                SET 
                    ${template_updates.join(', ')}
                WHERE id = $${template_updates_params.push(template_id)}
            `,
            params: template_updates_params,
        });
    }
    if (
        options.template_visibility === config.template_permissions.members ||
        // using prev_permissions if not provided in options
        ((options.template_visibility === null || options.template_visibility === undefined) &&
            prev_permissions === config.template_permissions.members &&
            (options.template_members || options.template_group_members))
    ) {
        const { template_members, template_group_members = {} } = options;
        if (!template_members || !template_members.add || !template_members.rem) {
            throw new TemplateError('Malformed template_members field', 'PRJTEMP_013', 400);
        }
        if (template_members.add.some(user => template_members.rem.includes(user))) {
            throw new TemplateError('User cannot appear in both add and remove field', 'PRJTEMP_014', 400);
        }
        let prev_members = [];
        if (prev_permissions === config.template_permissions.members) {
            let member_response;
            try {
                member_response = await db.promiseReadQuery(
                    `
                        SELECT userid
                        FROM task_mgmt.project_template_members
                        WHERE project_id = $1
                `,
                    [template_id]
                );
            } catch (err) {
                throw new TemplateError(err, 'PRJTEMP_015', 500);
            }
            prev_members = member_response.rows.map(row => row.userid);
        }
        const all_members = [...prev_members, ...template_members.add]
            .filter((user, i, arr) => arr.indexOf(user) === i)
            .filter(user => !template_members.rem.includes(user));
        if (!all_members.length) {
            throw new TemplateError('Cannot create a template with no members', 'PRJTEMP_016', 500);
        }

        if (template_group_members.add && template_group_members.add.length) {
            try {
                await groupSharingPaywall.checkGroupSharingPaywall(template_id, 'project');

                const grp_inserts = [];
                const grp_params = [template_id, team_id];
                template_group_members.add.forEach(grp_id => {
                    grp_params.push(grp_id);
                    grp_inserts.push(`($1, $${grp_params.length}, $2)`);
                });

                if (grp_inserts.length) {
                    queries.push({
                        query: `INSERT INTO task_mgmt.project_template_group_members(project_id, group_id, workspace_id) VALUES ${grp_inserts.join(
                            ','
                        )} ON CONFLICT DO NOTHING`,
                        params: grp_params,
                    });
                }
            } catch (e) {
                throw new TemplateError(e, 'PRJTEMP_050', 500);
            }
        }

        if (template_group_members.rem && template_group_members.rem.length) {
            try {
                await groupSharingPaywall.checkGroupSharingPaywall(template_id, 'project');

                const query = `SELECT DISTINCT userid FROM task_mgmt.group_members WHERE group_id = ANY($1) AND userid != $2`;
                const params = [template_group_members.rem, options.userid];
                const result = await db.promiseReplicaQuery(query, params);
                if (result.rows && result.rows.length) {
                    const userids = result.rows.map(row => row.userid);
                    const combined_members = [...new Set([...template_members.rem, ...userids])];
                    template_members.rem = combined_members;
                }

                queries.push({
                    query: `DELETE FROM task_mgmt.project_template_group_members WHERE project_id = $1 AND group_id = ANY($2)`,
                    params: [template_id, template_group_members.rem],
                });
            } catch (e) {
                throw new TemplateError(e, 'PRJTEMP_049', 500);
            }
        }

        if (template_members.add.length) {
            try {
                await Promise.all(
                    template_members.add.map(member =>
                        access.promiseAccessProjectTemplate(member, template_id, { skip_permissions: true })
                    )
                );
            } catch (err) {
                throw err;
            }
            const add_params = [];
            let add_query = `
                INSERT INTO task_mgmt.project_template_members
                (project_id, userid, workspace_id) 
                VALUES
            `;
            template_members.add.forEach(member => {
                add_query += `(
                    $${add_params.push(template_id)},
                    $${add_params.push(member)},
                    $${add_params.push(team_id)}
                ), `;
            });
            add_query = add_query.slice(0, -2);
            add_query += `
                ON CONFLICT (project_id, userid)
                DO NOTHING
            `;
            queries.push({ query: add_query, params: add_params });
        }
        if (template_members.rem.length) {
            queries.push({
                query: `
                    DELETE FROM task_mgmt.project_template_members
                    WHERE
                        project_id = $1
                        AND userid = ANY ($2)
            `,
                params: [template_id, template_members.rem],
            });
        }
    }
    try {
        await db.promiseBatchQueries(queries);
        if (options.public_sharing !== undefined) {
            CachedTemplatesQueryProxyHttpClient.invalidateCachedQueryResult(
                { templateType: 'project', templateId: template_id },
                {
                    queryFamily: QueryFamily.Project,
                    queryKey: ProjectQueryKey.TemplateProject,
                    queryParams: [template_id],
                }
            );
        }
    } catch (err) {
        throw new TemplateError(err, 'PRJTEMP_017', 500);
    }
}

export async function editTemplate(req, resp, next) {
    const userid = req.decoded_token.user;
    let { template_id } = req.params;
    const options = req.body;
    const ws_key = req.headers.sessionid || req.decoded_token.ws_key;

    options.ws_key = ws_key;
    options.template_v2 = req.v2;

    try {
        template_id = await templateHelpers._idByPermanentId(template_id, config.hierarchy_tables.spaces);
        options.template_id = template_id;

        const result = await promiseEditTemplateName(userid, options);
        if (!req.v2) {
            resp.status(200).send(result);
            return;
        }

        options.userid = userid;
        await editTemplatePermissions(template_id, options);
        options.public_key = true;
        const template = await _getTemplate(template_id, userid, options);
        resp.status(200).send(template);
    } catch (err) {
        next(err);
    }
}
