import uuid from 'node-uuid';
import * as log from '@clickup/shared/utils-logging';
import { EntityType } from '@clickup/utils/constants';
import * as db from '../../../utils/db';
import { ClickUpError } from '../../../utils/errors';
import { EntitiesSupportingWatcher, SharedEntityErrorCodes } from '../util/Constants';
import { QueryObject } from '../../../utils/interfaces/QueryObject';
import { DbQueryResult } from '../../../utils/interfaces/DbQueryResult';
import {
    createSharedEntityFollowersQuery,
    createSharedEntityGroupFollowersQuery,
    createSharedEntityUnfollowersQuery,
} from '../util/sharedEntityQueryBuilder';
import { writeToCorrectCommentsDb, readFromCorrectCommentsDb } from '../../../utils/comment/db';

const logger = log.getLogger('shared_entity_datastore');
const SharedEntityDatastoreError = ClickUpError.makeNamedError('shared_entity_datastore');

export enum EntityTableName {
    projects = 'task_mgmt.projects',
    categories = 'task_mgmt.categories',
}

export async function getSharedEntityAttributesAndAssignees(
    entityId: string,
    entityType: number,
    entityTableName: EntityTableName
) {
    try {
        const [attrs, assignees] = await Promise.all([
            getSharedEntityAttributesAndEntityValues(entityId, entityType, entityTableName),
            getEntityAssignees(entityId, entityType),
        ]);
        // todo handle this differently when multiple assignees will be allowed
        return { ...attrs, assignee: assignees[0]?.assignee_id ?? null };
    } catch (err) {
        logger.error({ msg: 'Failed to get shared entity attributes', err });
        return {};
    }
}

async function getSharedEntityAttributesAndEntityValues(
    entityId: string,
    entityType: number,
    entityTableName: EntityTableName
) {
    const getSharedAttributesQuery = `SELECT parentTable.name,
                                                   attr.priority,
                                                   parentTable.color,
                                                   sts.status as status_name,
                                                   attr.avatar,
                                                   parentTable.due_date,
                                                   parentTable.due_date_time,
                                                   attr.start_date,
                                                   attr.start_date_time
                                            FROM ${entityTableName} parentTable
                                                     LEFT OUTER JOIN task_mgmt.shared_entity_attributes attr 
                                                        ON attr.entity_id = cast(parentTable.id as text)
                                                        AND attr.entity_type = $1
                                                     LEFT OUTER JOIN task_mgmt.shared_entity_statuses sts 
                                                        ON attr.status_id = sts.id
                                            where parentTable.id = $2`;

    const { rows } = await db.replicaQueryAsync(getSharedAttributesQuery, [entityType, entityId]);
    return rows[0];
}

export async function getSharedEntityAttributesRecordId(workspaceId: string, entityId: string, entityType: number) {
    const selectEntityAttributesIdQuery = `SELECT id FROM task_mgmt.shared_entity_attributes
    WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3 `;
    const selectEntityAttributesIdParams = [workspaceId, entityId, entityType];
    const { rows } = await db.readQueryAsync(selectEntityAttributesIdQuery, selectEntityAttributesIdParams);
    return rows?.[0]?.id ?? null;
}

export async function getEntityAssignees(
    entityId: string,
    entityType: number,
    assigneeId = '',
    includeAssigneeId = false
) {
    const getAssigneesQuery = `SELECT id, assignee_id, deleted
            FROM task_mgmt.shared_entity_assignees assignees 
            where assignees.entity_id = $1
                AND assignees.entity_type = $2
                ${includeAssigneeId ? 'AND (NOT assignees.deleted OR assignee_id = $3)' : 'AND NOT assignees.deleted'}`;
    const getAssigneesParams = includeAssigneeId ? [entityId, entityType, assigneeId] : [entityId, entityType];
    const { rows } = await db.replicaQueryAsync(getAssigneesQuery, getAssigneesParams);
    return rows;
}

// creates and returns the status id in case it doesn't exist in the shared entity status table
export async function getCategoryStatusIdAsync(
    team: string,
    entityId: string,
    entityType: number,
    statusName: string,
    statusType: string
) {
    const getStatusIdQuery = `SELECT id FROM task_mgmt.shared_entity_statuses
                                            WHERE entity_id = $1 AND entity_type = $2 AND status = $3 AND workspace_id = $4`;
    const getStatusIdParams = [entityId, entityType, statusName, BigInt(team)];
    const statusIdToInsert = uuid.v4();
    const insertStatusQuery = `INSERT INTO task_mgmt.shared_entity_statuses 
                            (id, workspace_id, status, status_type, entity_id, entity_type, date_created, date_updated) 
                            VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`;
    const insertStatusParams = [
        statusIdToInsert,
        BigInt(team),
        statusName,
        statusType,
        entityId,
        entityType,
        Date.now(),
        Date.now(),
    ];
    const { rows } = await db.readQueryAsync(getStatusIdQuery, getStatusIdParams);
    try {
        if (rows.length === 0) {
            try {
                await db.writeQueryAsync(insertStatusQuery, insertStatusParams);
                return statusIdToInsert;
            } catch (err) {
                logger.error({ msg: `Failed to insert new status`, err });
                throw err;
            }
        } else {
            return rows[0].id;
        }
    } catch (err2) {
        throw new SharedEntityDatastoreError('Missing rows object', SharedEntityErrorCodes.MissingRowObject, 500);
    }
}

export async function addEntityFollowers(
    entityType: number,
    entityId: string,
    workspaceId: string,
    followersToAdd: number[]
): Promise<DbQueryResult<any>> {
    const queryObject: QueryObject = createSharedEntityFollowersQuery(
        entityType,
        entityId,
        workspaceId,
        followersToAdd
    );
    return db.writeQueryAsync(queryObject.query, queryObject.params);
}

export async function getEntityFollowers(
    workspaceId: string,
    entityId: string,
    entityType: number
): Promise<{ user_id: number }[]> {
    const query = `SELECT user_id from task_mgmt.shared_entity_followers 
                    WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3`;
    const params = [workspaceId, entityId, entityType];
    const { rows } = await db.readQueryAsync(query, params);
    return rows;
}

export async function removeEntityFollower(
    workspaceId: string,
    entityId: string,
    entityType: number,
    followerToRemove: number
): Promise<DbQueryResult<any>> {
    const query = `DELETE FROM task_mgmt.shared_entity_followers 
                    WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3 AND user_id = $4`;
    const params = [workspaceId, entityId, entityType, followerToRemove];
    return db.writeQueryAsync(query, params);
}

export async function removeEntityFollowers(
    workspaceId: string,
    entityId: string,
    entityType: number,
    followersToRemove: number[]
): Promise<DbQueryResult<any>> {
    const query = `DELETE FROM task_mgmt.shared_entity_followers 
                    WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3 AND user_id = ANY($4)`;
    const params = [workspaceId, entityId, entityType, followersToRemove];
    return db.writeQueryAsync(query, params);
}

export async function addEntityGroupFollowers(
    entityType: number,
    entityId: string,
    workspaceId: string,
    groupFollowersToAdd: string[]
): Promise<DbQueryResult<any>> {
    const queryObject: QueryObject = createSharedEntityGroupFollowersQuery(
        entityType,
        entityId,
        workspaceId,
        groupFollowersToAdd
    );
    return db.writeQueryAsync(queryObject.query, queryObject.params);
}

export async function getEntityGroupFollowers(
    workspaceId: string,
    entityId: string,
    entityType: number
): Promise<{ group_id: string }[]> {
    const query = `SELECT group_id from task_mgmt.shared_entity_group_followers 
                    WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3`;
    const params = [workspaceId, entityId, entityType];
    const { rows } = await db.replicaQueryAsync(query, params);
    return rows;
}

export async function removeEntityGroupFollowers(
    workspaceId: string,
    entityId: string,
    entityType: number,
    groupFollowersToRemove: string[]
): Promise<DbQueryResult<any>> {
    const query = `DELETE FROM task_mgmt.shared_entity_group_followers 
                    WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3 AND group_id = ANY($4)`;
    const params = [workspaceId, entityId, entityType, groupFollowersToRemove];
    return db.writeQueryAsync(query, params);
}

// TODO: Add workspaceId to the Entity data model so we don't have to use this function
export async function getWorkspaceId(entityId: string, entityType: number): Promise<string> {
    let query = '';
    const params = [entityId];
    switch (entityType) {
        case EntitiesSupportingWatcher.Project:
            query = `SELECT team FROM task_mgmt.projects where id=$1`;
            break;
        case EntitiesSupportingWatcher.Category:
            query = `
                    SELECT p.team 
                    FROM task_mgmt.categories c
                    JOIN task_mgmt.projects p ON c.project_id=p.id
                    WHERE c.id=$1`;
            break;
        case EntitiesSupportingWatcher.Page:
            query = `
                    SELECT v.team_id as team
                    FROM task_mgmt.view_docs AS vd
                    JOIN task_mgmt.views AS v ON v.view_id = vd.view_id
                    WHERE vd.id=$1`;
            break;
        default:
            throw new SharedEntityDatastoreError(
                `Unsupported entity type ${entityType}`,
                SharedEntityErrorCodes.UnsupportedEntity,
                400
            );
    }
    const dbResult = await db.promiseReadQuery(query, params);

    if (dbResult.rows.length) {
        return dbResult.rows[0].team;
    }
    throw new SharedEntityDatastoreError(`Invalid entityId passed`, SharedEntityErrorCodes.InvalidEntityId, 404);
}

export async function addEntityUnfollowers(
    entityType: number,
    entityId: string,
    workspaceId: string,
    unfollowersToAdd: number[]
): Promise<DbQueryResult<any>> {
    const queryObject = createSharedEntityUnfollowersQuery(entityType, entityId, workspaceId, unfollowersToAdd);

    if (entityType === EntityType.COMMENT) {
        return new Promise((resolve, reject) => {
            writeToCorrectCommentsDb(
                {
                    context: { commentId: entityId },
                    mainDbQueryFunc: db.writeQueryAsync,
                    query: queryObject.query,
                    params: queryObject.params,
                },
                (err, result) => (err ? reject(err) : resolve(result))
            );
        });
    }
    return db.writeQueryAsync(queryObject.query, queryObject.params);
}

export async function getEntityUnfollowers(
    workspaceId: string,
    entityId: string,
    entityType: number
): Promise<{ user_id: number }[]> {
    const query = `SELECT user_id from task_mgmt.shared_entity_unfollowers 
                   WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3`;
    const params = [workspaceId, entityId, entityType];
    if (entityType === EntityType.COMMENT) {
        return new Promise((resolve, reject) => {
            readFromCorrectCommentsDb(
                { context: { commentId: entityId }, mainDbQueryFunc: db.readQueryAsync, query, params },
                (err, result) => (err ? reject(err) : resolve(result.rows as { user_id: number }[]))
            );
        });
    }
    const { rows } = await db.readQueryAsync(query, params);
    return rows;
}

export async function removeEntityUnfollower(
    workspaceId: string,
    entityId: string,
    entityType: number,
    unfollowerToRemove: number
): Promise<DbQueryResult<any>> {
    const query = `DELETE FROM task_mgmt.shared_entity_unfollowers 
                   WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3 AND user_id = $4`;
    const params = [workspaceId, entityId, entityType, unfollowerToRemove];
    if (entityType === EntityType.COMMENT) {
        return new Promise((resolve, reject) => {
            writeToCorrectCommentsDb(
                { context: { commentId: entityId }, mainDbQueryFunc: db.writeQueryAsync, query, params },
                (err, result) => (err ? reject(err) : resolve(result))
            );
        });
    }
    return db.writeQueryAsync(query, params);
}

export async function removeEntityUnfollowers(
    workspaceId: string,
    entityId: string,
    entityType: number,
    unfollowersToRemove: number[]
): Promise<DbQueryResult<any>> {
    const query = `DELETE FROM task_mgmt.shared_entity_unfollowers 
                   WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3 AND user_id = ANY($4)`;
    const params = [workspaceId, entityId, entityType, unfollowersToRemove];
    if (entityType === EntityType.COMMENT) {
        return new Promise((resolve, reject) => {
            writeToCorrectCommentsDb(
                { context: { commentId: entityId }, mainDbQueryFunc: db.writeQueryAsync, query, params },
                (err, result) => (err ? reject(err) : resolve(result))
            );
        });
    }
    return db.writeQueryAsync(query, params);
}
