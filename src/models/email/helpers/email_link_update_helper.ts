import * as config from 'config';
import * as logging from '@clickup/shared/utils-logging';
import * as jwt_helper from '../../../utils/jwt';

import { updateInviteLinksExperiment } from '../../integrations/split/squadTreatments/growthTreatments';

const logger = logging.getLogger('newLinksHelper');

export const SHARED_VIEW_NOTIF = 'SHARED_VIEW_NOTIF';

export type ClickUpEntityLocation = {
    path: string[];
    query?: Record<string, string>;
};

export type WorkspaceAndInviteData = {
    team_id: number;
    invite_code: string | null | undefined;
    outstanding_invite: boolean | null | undefined;
};

export function replacePlaceholderWithLink(args: {
    user_id: number;
    regexToReplace: RegExp;
    workspaceAndInviteData: WorkspaceAndInviteData;
    email: string;
    template: string;
    template_id: string;
    oldInviteLink: string;
    useNewInviteLinkFeature: boolean;
}): string {
    const {
        user_id,
        regexToReplace,
        workspaceAndInviteData,
        email,
        template,
        template_id,
        oldInviteLink,
        useNewInviteLinkFeature,
    } = args;

    const templateWithOldLink = template.replace(regexToReplace, oldInviteLink);
    try {
        const { team_id, invite_code, outstanding_invite } = workspaceAndInviteData;

        if (!useNewInviteLinkFeature || !invite_code || !outstanding_invite) {
            return templateWithOldLink;
        }

        const token = jwt_helper.getGenericJWT('14d', {
            team_id,
            email,
            invite_code,
            invite_location: getInviteLocation(regexToReplace, oldInviteLink, team_id, template_id),
        });
        const url = `${config.app.app_url}/teamInvite?email=${encodeURIComponent(
            email
        )}&token=${token}&team_id=${team_id}`;
        return updateInviteLinksExperiment(user_id) ? template.replace(regexToReplace, url) : templateWithOldLink;
    } catch (err) {
        logger.error({
            msg: `Failed to apply new link for ${String(regexToReplace)}`,
            err,
        });
        return templateWithOldLink;
    }
}

function getInviteLocation(
    regexToReplace: RegExp,
    oldInviteLink: string,
    team_id: number,
    template_id: string
): ClickUpEntityLocation {
    const objectUrlParts = oldInviteLink.split('?');
    const objectPathParts = objectUrlParts[0].split('/');

    const query = Object.fromEntries(new URLSearchParams(objectUrlParts[1]));

    if (regExpEq(regexToReplace, /\*\| TASK_URL \|\*/g)) {
        return {
            path: ['/', 't', objectPathParts[objectPathParts.length - 1]],
            query,
        };
    }
    const locationRoot = ['/', `${team_id}`, 'v', 'l'];

    if (regExpEq(regexToReplace, /\*\| LIST_URL \|\*/g)) {
        return {
            path: [...locationRoot, 'li', objectPathParts[objectPathParts.length - 1]],
            query,
        };
    }
    if (regExpEq(regexToReplace, /\*\| FOLDER_URL \|\*/g)) {
        const defaultLocation = {
            path: [...locationRoot, 'f', objectPathParts[objectPathParts.length - 1]],
            query,
        };

        // When template_id === SHARED_VIEW_NOTIF, the '*| FOLDER_URL |*' placeholder
        // is actually used to store the URL for the view that the notification is for.
        if (template_id === SHARED_VIEW_NOTIF) {
            return getInviteLocationForView(objectPathParts, defaultLocation);
        }

        return defaultLocation;
    }
    throw new Error('Unrecognized regex for object');
}

function getInviteLocationForView(
    objectPathParts: string[],
    defaultLocation: ClickUpEntityLocation
): ClickUpEntityLocation {
    // From a URL like http://staging.clickup.com/12345/docs/916eyu6-25/978yu6-11?a=b
    // objectPathParts will be ['http:', '', 'staging.clickup.com', '12345', 'docs', '916eyu6-25', '978yu6-11']
    // We want to ignore the first three parts, and get this path array:
    // ['/', '12345', 'docs', '916eyu6-25', '978yu6-11']
    try {
        return {
            ...defaultLocation,
            path: ['/', ...objectPathParts.slice(3)],
        };
    } catch (err: unknown) {
        logger.error({
            msg: `Failed to get invite location for template_id ${SHARED_VIEW_NOTIF}`,
            err,
        });
        return defaultLocation;
    }
}

function regExpEq(a: RegExp, b: RegExp): boolean {
    return String(a) === String(b);
}
