import { ClickUpError } from '@clickup-legacy/utils/errors';

import { getTeamId } from '../../../utils/entities/services/entitiesService';
import { DashboardNotFound } from '../utils/errors/error';
import { DashboardType, queryDashboardType } from '../datastores/dashboards/common';

const logger = ClickUpError.getBasicLogger('dashboard-paywall-utils');

export async function resolveDashboardWorkspaceId(args: {
    dashboardId: string;
    workspaceId?: number;
}): Promise<number | never> {
    const { dashboardId, workspaceId } = args;

    const resolvedWorkspaceId: number | undefined = args.workspaceId
        ? workspaceId
        : await getTeamId({ dashboard_ids: [args.dashboardId] });

    if (!resolvedWorkspaceId) {
        // That means that the function could not find workspace for dashboard which means it does not exist.
        throw new DashboardNotFound({ dashboardId });
    }

    return resolvedWorkspaceId;
}

export async function shouldSkipPaywallForDashboard(
    dashboardId: string,
    { useReplica }: { useReplica?: boolean } = {}
) {
    try {
        const dashboardType = await queryDashboardType(dashboardId);
        return dashboardType === DashboardType.LocationOverview || dashboardType === DashboardType.DashboardChatView;
    } catch (err) {
        if (err instanceof DashboardNotFound) {
            logger.error({ msg: 'Could not find dashboard for paywall check, assuming should not skip paywall' });
            return false;
        }
        throw err;
    }
}
