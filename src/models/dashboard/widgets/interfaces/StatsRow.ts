import { TaskInfoDashboard } from '../datastores/utilsDatastore';

export type StatsRowGroup = string | boolean | number | string[] | number[]; // currently `number` is used for CF Date on Pie Charts

export const isStatsRowGroupString = (param: StatsRowGroup): param is string => typeof param === 'string';

type CalculationStatsRow = {
    value: any;
};

export interface CategoricalStatsRow {
    group: StatsRowGroup;
    count: number;
    min?: number;
    max?: number;
    avg?: number;
    segment?: StatsRowGroup;
    task_infos?: Array<TaskInfoDashboard>;
    sample_tasks_ids?: Array<string>;
}

export interface TimeStatsRow {
    y: number;
    label: string;
    max: string;
    point: string;
    to_timestamp: Date;
    sample_tasks_ids?: Array<string>;
}

export type StatsRow = CalculationStatsRow | CategoricalStatsRow | TimeStatsRow;

export const isCalculationStatsRow = (param: StatsRow): param is CalculationStatsRow => !!param && 'value' in param;
export const isCategoricalStatsRow = (param: StatsRow): param is CategoricalStatsRow =>
    !!param && ['group', 'count'].every(f => f in param);
export const isTimeStatsRow = (param: StatsRow): param is TimeStatsRow =>
    !!param && ['label', 'max', 'point', 'to_timestamp', 'y'].every(f => f in param);
