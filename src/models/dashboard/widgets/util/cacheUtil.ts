import { getCacheClient } from '../../client/cacheClient';
import { DbQueryResult } from '../../../../utils/interfaces/DbQueryResult';

/**
 * EXIST: value is in cache
 * NOT_EXIST: value is not in cache or database
 * EMPTY: value is not in cache, but might be in database
 */
export enum CACHE_VALUE_STATUS {
    EXIST,
    NOT_EXIST,
    EMPTY,
}

const NOT_EXIST = 'NOT_EXIST';

/**
 * if data is in cache, returned status is EXIST
 * if data does not exist in database, returned status is NOT_EXIST
 * if data is not in cache, returned status is EMPTY
 * @param cacheKey
 */
export async function getCacheValue(cacheKey: string): Promise<{ data: any; status: CACHE_VALUE_STATUS }> {
    const cacheClient = getCacheClient();
    const data = await cacheClient.getClient().get(cacheKey);
    if (data && data !== NOT_EXIST) {
        return {
            data: JSON.parse(data),
            status: CACHE_VALUE_STATUS.EXIST,
        };
    }
    if (data === NOT_EXIST) {
        return {
            data: null,
            status: CACHE_VALUE_STATUS.NOT_EXIST,
        };
    }
    return {
        data: null,
        status: CACHE_VALUE_STATUS.EMPTY,
    };
}

/**
 * if data is in cache, returned status is EXIST
 * if data does not exist in database, returned status is NOT_EXIST
 * if data is not in cache, returned status is EMPTY
 * @param cacheKeys
 * @return record<string, {}>, the key is the cache key and the value is {data: any, status: CACHE_VALUE_STATUS}
 * {
 *     'cacheKey1': {data: 123, status: CACHE_VALUE_STATUS.EXIST},
 *     'cacheKey2': {data: null, status: CACHE_VALUE_STATUS.NOT_EXIST},
 *     'cacheKey3': {data: null, status: CACHE_VALUE_STATUS.EMPTY}
 * }
 */
export async function getCacheValues(
    cacheKeys: string[]
): Promise<Map<string, { data: any; status: CACHE_VALUE_STATUS }>> {
    const cacheClient = getCacheClient();
    const values = await cacheClient.getClient().mget(cacheKeys);
    const response = new Map<string, { data: any; status: CACHE_VALUE_STATUS }>();
    cacheKeys.forEach((cacheKey, index) => {
        const data = values[index];
        if (data && data !== NOT_EXIST) {
            response.set(cacheKey, { data: JSON.parse(data), status: CACHE_VALUE_STATUS.EXIST });
        } else if (data === NOT_EXIST) {
            response.set(cacheKey, { data: null, status: CACHE_VALUE_STATUS.NOT_EXIST });
        } else {
            response.set(cacheKey, { data: null, status: CACHE_VALUE_STATUS.EMPTY });
        }
    });
    return response;
}

/**
 * Put the first row of db result into cache, if there is 0 row, then put `NOT_EXIST`
 * Return the first row of db result or null.
 * @param cacheKey
 * @param expInSec
 * @param dbQueryResult
 */
export async function setCacheValue(cacheKey: string, expInSec: number, dbQueryResult: DbQueryResult<any>) {
    const cacheClient = getCacheClient();
    if (dbQueryResult.rowCount > 0) {
        const data = dbQueryResult.rows[0];
        await cacheClient.getClient().setex(cacheKey, expInSec, JSON.stringify(data));
        return data;
    }
    await cacheClient.getClient().setex(cacheKey, expInSec, NOT_EXIST);
    return null;
}
