import { TypedLoggerWithContext } from '@clickup/utils/typed-logger';

import { WhosBehindWidgetError } from './error';
import {
    WhosBehindSettledResultMaps,
    WhosBehindTeamMember,
    Whos<PERSON><PERSON><PERSON><PERSON><PERSON>,
    WhosBehindWidgetStatsResponse,
} from './types';

const logger = TypedLoggerWithContext.create('WhosBehindWidgetStatsService.common.utils');

export function aggregateWhosBehindResponse(
    members: WhosBehindTeamMember[],
    resultMaps: WhosBehindSettledResultMaps
): WhosBehindWidgetStatsResponse {
    const users: WhosBehindUser[] = [];

    const containsInboxV3Data = true;

    members.forEach((member: WhosBehindTeamMember) => {
        const behindUser: WhosBehindUser = {
            user: member,
            tasks_overdue: null,
            uncleared_notifs: null,
        };

        behindUser.uncleared_notifs = resultMaps.v3InboxUnreadMessagesByUserMap
            ? resultMaps.v3InboxUnreadMessagesByUserMap.get(member.id)
            : null;

        // Return null for overdue notifications only if the whole query failed, otherwise user simply has no overdue tasks
        behindUser.tasks_overdue = resultMaps.overdueTasksByUserMap
            ? resultMaps.overdueTasksByUserMap.get(member.id) ?? 0
            : null;

        users.push(behindUser);
    });

    return { users, containsInboxV3Data };
}

export function resolveWhosBehindSettledResults(
    promiseSettledResults: PromiseSettledResult<Map<number, number>>[]
): WhosBehindSettledResultMaps {
    const rejectedSettledResult = promiseSettledResults.find(x => x.status === 'rejected') as PromiseRejectedResult;

    if (rejectedSettledResult) {
        // handle rejection from data dependency
        const [inboxV3SettledResult, unclearedNotifsSettledResult] = promiseSettledResults;

        if (
            unclearedNotifsSettledResult.status === 'rejected' &&
            (inboxV3SettledResult.status === 'rejected' || !inboxV3SettledResult.value.size)
        ) {
            logger.error('Failed to resolve whos behind results', {
                inboxV3Error:
                    inboxV3SettledResult.status === 'rejected'
                        ? {
                              message: String(inboxV3SettledResult.reason),
                              status: inboxV3SettledResult.reason.status,
                              code: inboxV3SettledResult.reason.code,
                          }
                        : 'empty',
                unclearedNotifsError: {
                    message: String(unclearedNotifsSettledResult.reason),
                    stack: unclearedNotifsSettledResult.reason.stack,
                },
            });

            // if the legacy notifs query failed and we don't have any inbox v3 data, throw error
            throw new WhosBehindWidgetError({
                msg: 'Failed to get team report',
                status: 500,
                err: rejectedSettledResult.reason, // reason will be first rejected in settled results array
                ECODE: 'REPORTING_008', // Still old error code for backwards compatimbility.
            });
        }
    }

    const [v3InboxUnreadMessagesByUserMap, overdueTasksByUserMap] = promiseSettledResults.map(x =>
        x.status === 'fulfilled' ? x.value : null
    );

    return { v3InboxUnreadMessagesByUserMap, overdueTasksByUserMap };
}
