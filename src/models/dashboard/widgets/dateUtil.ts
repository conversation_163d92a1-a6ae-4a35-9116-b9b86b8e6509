import moment from 'moment-timezone';
import { Widget } from './interfaces';
import { isStep, RangeType, Step } from './api-contracts/Widget';
import type { DateRange } from './interfaces/WidgetOptions';
import { WidgetPremade } from './interfaces/WidgetPremade';

/**
 * Prepares a date range object based on the input widget and options.
 *
 * TODO: This could use some more refactoring.
 *
 * @param widget        Widget.
 * @param options       Options.
 * @return              Date range object.
 */
export function getDateRange(
    widget: Pick<Widget, 'x_axis' | 'sprint_dates' | 'sprint_source' | 'pre_made' | 'delta' | 'sample_period'>,
    options: { timezone: string | undefined }
) {
    let stop = widget.x_axis.end || new Date().getTime() + 24 * 60 * 60 * 1000;
    // use beginning of the year if no start date is specified
    let start =
        widget.x_axis.start ||
        moment
            .tz(Number(stop), options.timezone || 'UTC')
            .startOf('year')
            .valueOf();
    const { delta, sample_period } = widget;
    const { range_type } = widget.x_axis;
    let step: string | number = widget.x_axis.step || Step.Day;
    let series_dates = [];
    let include_latest = false;
    let last_date_override = null;
    let force_sundays = false;
    let last_sprints;
    let sprint_duration;

    // automatically set start and stop dates for sprint widgets
    if (widget.sprint_dates && widget.sprint_dates.length && widget.sprint_source) {
        sprint_duration = widget.sprint_dates[0].sprint_duration;
        if (
            widget.pre_made === WidgetPremade.Burndown ||
            widget.pre_made === WidgetPremade.Burnup ||
            widget.pre_made === WidgetPremade.CumulativeFlow
        ) {
            start = widget.sprint_dates[0].set_start_date;
            stop = widget.sprint_dates[widget.sprint_dates.length - 1].set_end_date;
        }
    }

    if (
        range_type &&
        Object.values(RangeType).includes(range_type) &&
        ![RangeType.Range, RangeType.AllAvailableDates].includes(range_type)
    ) {
        // for rollingDays option number of days is sent in widget.x_axis.end
        const rollingDays = widget.x_axis?.end;
        ({ start, stop } = getTimeRangeBounds(range_type, rollingDays, options.timezone));
        include_latest = true;
        force_sundays = shouldEnforceSundays(range_type);
        last_sprints = getNumberOfLastSprints(range_type);
        // todo: double check if this is used, possibly it can be removed
        step = [RangeType.Last30d, RangeType.Last60d, RangeType.Last90d].includes(range_type) ? Step.Day : step;
        if (range_type === RangeType.Last7d) {
            if (widget.pre_made === WidgetPremade.Velocity && widget.sprint_source) {
                // Handles corner case when the source for Velocity widget are sprints
                // but the time range wasn't selected and the FE sent a default 'Time range' equal 'last_7d'.
                // So the code overrides the step to 'week'.
                // The proper sprint duration is calculated at the end of this function
                // and bases on 'sprint_duration' attribute.
                step = Step.Week;
            } else {
                step = Step.Day;
            }
        }
    } else if (range_type && range_type.startsWith('last_x_')) {
        let count = 5;
        let step_string = Step.Day;

        try {
            count = Number(range_type.split('last_x_')[1].split('_')[1]) || 5;
        } catch (e) {
            // do nothing
        }

        try {
            const rangeTypeStepString = range_type.split('last_x_')[1].split('_')[0];
            if (rangeTypeStepString && isStep(rangeTypeStepString)) {
                step_string = rangeTypeStepString;
            }
        } catch (e) {
            // do nothing
        }

        start = moment()
            .tz(options.timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .subtract(count - 2, step_string)
            .valueOf();
        stop = moment()
            .tz(options.timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .add(1, step_string)
            .valueOf();
        include_latest = true;
    } else {
        start = moment(Number(start))
            .tz(options.timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .valueOf();
        stop = moment(Number(stop))
            .tz(options.timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .valueOf();

        if (
            widget.pre_made === WidgetPremade.Burndown ||
            widget.pre_made === WidgetPremade.Burnup ||
            widget.pre_made === WidgetPremade.CumulativeFlow
        ) {
            // User pick List as Sprint source
            if (!widget.sprint_source) {
                stop -= 24 * 60 * 60 * 1000;
            }
        } else {
            stop += 24 * 60 * 60 * 1000;
        }
    }

    if (step === Step.Month) {
        if (delta) {
            start = subtractNMonths(start, 1, options.timezone || 'UTC');
        } else if (sample_period && sample_period > 1) {
            start = subtractNMonths(start, sample_period - 1, options.timezone || 'UTC');
        }
        series_dates.push(start);
        let next_date = moment(start)
            .tz(options.timezone || 'UTC')
            .set({ date: 1, hour: 0, minute: 0, second: 0, millisecond: 0 })
            .add(1, 'month')
            .valueOf();
        while (next_date < stop) {
            series_dates.push(next_date);
            next_date = moment(next_date)
                .tz(options.timezone || 'UTC')
                .add(1, 'month')
                .valueOf();
        }
        include_latest = true;
        last_date_override = moment(next_date)
            .tz(options.timezone || 'UTC')
            .add(1, 'month')
            .valueOf();
    } else if (step === Step.Day) {
        if (delta) {
            start = subtractNDays(start, 1, options.timezone || 'UTC');
        } else if (sample_period && sample_period > 1) {
            start = subtractNDays(start, sample_period - 1, options.timezone || 'UTC');
        }
        series_dates.push(start);
        let next_date = moment(start)
            .tz(options.timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .add(1, 'day')
            .valueOf();
        while (next_date < stop) {
            series_dates.push(next_date);
            next_date = moment(next_date)
                .tz(options.timezone || 'UTC')
                .add(1, 'day')
                .valueOf();
        }
        include_latest = true;
        step = 24 * 60 * 60 * 1000;
    } else if (step === Step.Week) {
        let next_date = start;
        if (delta) {
            start = subtractNDays(start, 7, options.timezone || 'UTC');
            series_dates.push(start);
        } else if (sample_period && sample_period > 1) {
            start = subtractNDays(start, 7 * (sample_period - 1), options.timezone || 'UTC');
            series_dates.push(start);
            for (let i = sample_period - 2; i > 0; i--) {
                series_dates.push(subtractNDays(next_date, 7 * i, options.timezone || 'UTC'));
            }
        }
        series_dates.push(next_date);
        const step_value = widget.sprint_source && sprint_duration ? sprint_duration * 7 : 7;

        if (force_sundays && !widget.sprint_source) {
            next_date = moment(next_date)
                .tz(options.timezone || 'UTC')
                .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
                .day(step_value)
                .valueOf();
            while (next_date < stop) {
                series_dates.push(next_date);
                next_date = moment(next_date)
                    .tz(options.timezone || 'UTC')
                    .day(step_value)
                    .valueOf();
            }
            last_date_override = moment(next_date)
                .tz(options.timezone || 'UTC')
                .day(step_value)
                .valueOf();
        } else {
            next_date = moment(next_date)
                .tz(options.timezone || 'UTC')
                .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
                .add(step_value, 'day')
                .valueOf();
            while (next_date < stop) {
                series_dates.push(next_date);
                next_date = moment(next_date)
                    .tz(options.timezone || 'UTC')
                    .add(step_value, 'day')
                    .valueOf();
            }
            last_date_override = moment(next_date)
                .tz(options.timezone || 'UTC')
                .add(step_value, 'day')
                .valueOf();
        }

        include_latest = true;

        step = step_value * 24 * 60 * 60 * 1000;
    } else if (typeof step === 'number' && step > 0) {
        series_dates.push(start);
        let next_date = start;
        while (next_date < stop) {
            next_date += step;
            series_dates.push(next_date);
        }
    }

    if (stop && stop < new Date().getTime()) {
        include_latest = false;
    }

    if (stop && stop > series_dates[series_dates.length - 1]) {
        series_dates.push(stop);
    }

    // remap dates to align with sprint dates for velocity chart
    if (
        widget.sprint_dates &&
        widget.sprint_dates.length &&
        widget.sprint_source &&
        widget.pre_made === WidgetPremade.Velocity
    ) {
        series_dates = [];
        const target_sprints = widget.sprint_dates.slice(last_sprints * -1);
        target_sprints.forEach(sprint => {
            series_dates.push(sprint.sprint_start_date);
            // const ref = series_dates[series_dates.length - 1];
            // // the "end" of a sprint may be longer than the "start" of the next sprint
            // if (ref && Number(ref) < Number(sprint.sprint_end_date)) {
            //     series_dates.push(sprint.sprint_end_date);
            // }
        });
        start = moment(Number(target_sprints[0].sprint_start_date))
            .tz(options.timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .valueOf();
        stop = moment(Number(target_sprints[target_sprints.length - 1].sprint_end_date))
            .tz(options.timezone || 'UTC')
            .set({ hour: 0, minute: 0, second: 0, millisecond: 0 })
            .valueOf();
        stop += 24 * 60 * 60 * 1000;
    }

    if (widget.sprint_source && widget.pre_made === WidgetPremade.Velocity) {
        // exclude velocity sprint widgets
    } else if (include_latest) {
        series_dates.push(moment().set({ minute: 0, second: 0, millisecond: 0 }).add(1, 'hour').valueOf());
    }

    return { start, stop, step, series_dates, last_date_override, include_latest };
}

/**
 * Sanitizes the rangeDates by ensuring that the series_dates attribute only
 * contains dates before the end of the current day. If any dates are removed
 * from series_dates and last_date_override is present, it is set to the last
 * date in the modified series_dates. Modifications to series_dates and
 * last_date_override are committed to the original rangeDates object. Returns
 * the original, unmodified value of series_dates.
 *
 * TODO: This function is a bit clunky in that it modifies the rangeDates in
 *  place as well as returns allDates. Might want to consider updating this
 *  function to just return a new sanitized rangeDates instead.
 *
 * @param timeZone      Time zone.
 * @param rangeDates    Range dates.
 * @return              Original, unmodified value of series_dates.
 */
export function sanitizeRangeDates(timeZone: string, rangeDates: DateRange) {
    let { series_dates: seriesDates, last_date_override: lastDateOverride } = rangeDates;
    const allDates = seriesDates;

    if (seriesDates) {
        const end_of_today = moment()
            .tz(timeZone || 'UTC')
            .set({ hour: 24, minute: 0, second: 0, millisecond: 0 })
            .valueOf();
        seriesDates = seriesDates.filter(date => date < end_of_today);
        if (lastDateOverride && seriesDates.length !== allDates.length) {
            lastDateOverride = seriesDates[seriesDates.length - 1];
        }
    }

    rangeDates.series_dates = seriesDates;
    rangeDates.last_date_override = lastDateOverride;

    return allDates;
}
/**
 * Substracts N days and rounds the date to the beginning of the the day
 */
function subtractNDays(date: number, n: number, tz: string) {
    return moment(date).tz(tz).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).subtract(n, 'days').valueOf();
}

/**
 * Substracts N month and rounds the date to the beginning of the the month
 */
function subtractNMonths(date: number, n: number, tz: string) {
    return moment(date).tz(tz).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).subtract(n, 'month').valueOf();
}

/**
 * Adds N days and rounds the date to the beginning of the the day
 */
export function addNDays(date: number, n: number, tz: string) {
    return moment(date).tz(tz).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).add(n, 'days').valueOf();
}

/**
 * Adds N weeks and rounds the date to the beginning of the the week
 */
export function addNWeeks(date: number, n: number, tz: string) {
    return moment(date).tz(tz).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).add(n, 'week').valueOf();
}

/**
 * Adds N month and rounds the date to the beginning of the the month
 */
export function addNMonths(date: number, n: number, tz: string) {
    return moment(date).tz(tz).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).add(n, 'month').valueOf();
}

/**
 * Adds N years and rounds the date to the beginning of the the year
 */
function addNYears(date: number, n: number, tz: string) {
    return moment(date).tz(tz).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).add(n, 'year').valueOf();
}

/**
 * Gets the very start of the current day
 */
function getBeginningOfCurrentDay(tz: string) {
    return moment().tz(tz).set({ hour: 0, minute: 0, second: 0, millisecond: 0 }).valueOf();
}

/**
 * Gets the very start of the current week
 */
function getBeginningOfCurrentWeek(tz: string) {
    return moment().tz(tz).set({ day: 0, hour: 0, minute: 0, second: 0, millisecond: 0 }).valueOf();
}

/**
 * Gets the very start of the current month
 */
function getBeginningOfCurrentMonth(tz: string) {
    return moment().tz(tz).set({ date: 1, hour: 0, minute: 0, second: 0, millisecond: 0 }).valueOf();
}

/**
 * Gets the very start of the current year
 */
function getBeginningOfCurrentYear(tz: string) {
    return moment().tz(tz).set({ month: 0, date: 1, hour: 0, minute: 0, second: 0, millisecond: 0 }).valueOf();
}

export function isTimestamp(timestamp: number | string) {
    return moment(timestamp).isValid();
}

export function getTimeRangeBounds(
    rangeType: RangeType,
    rollingDays: number,
    timezone: string
): { start: number; stop: number } {
    const tz = timezone || 'UTC';
    const currentDay = getBeginningOfCurrentDay(tz);

    let start;
    let stop;

    switch (rangeType) {
        case RangeType.ThisMonth:
            start = getBeginningOfCurrentMonth(tz);
            stop = addNMonths(start, 1, tz);
            break;
        case RangeType.ThisYear:
            start = getBeginningOfCurrentYear(tz);
            stop = addNYears(start, 1, tz);
            break;
        case RangeType.ThisWeek:
            start = getBeginningOfCurrentWeek(tz);
            stop = addNDays(start, 7, tz);
            break;
        case RangeType.LastWeek:
            stop = getBeginningOfCurrentWeek(tz);
            start = subtractNDays(stop, 7, tz);
            break;
        case RangeType.ThisDay:
            start = currentDay;
            stop = addNDays(start, 1, tz);
            break;
        case RangeType.Yesterday:
            start = subtractNDays(currentDay, 1, tz);
            stop = currentDay;
            break;
        case RangeType.RollingDays:
            start = subtractNDays(currentDay, rollingDays, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.Last7d:
            start = subtractNDays(currentDay, 7, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.Last30d:
            start = subtractNDays(currentDay, 30, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.Last60d:
            start = subtractNDays(currentDay, 60, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.Last90d:
            start = subtractNDays(currentDay, 90, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.LastMonth:
            start = subtractNDays(currentDay, 28, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.LastMonth2:
            start = subtractNDays(currentDay, 28 * 2, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.LastMonth3:
            start = subtractNDays(currentDay, 28 * 3, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.LastMonth6:
            start = subtractNDays(currentDay, 28 * 6, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        case RangeType.LastMonth12:
            start = subtractNDays(currentDay, 28 * 12, tz);
            stop = addNDays(currentDay, 1, tz);
            break;
        default:
            break;
    }

    return { start, stop };
}

function shouldEnforceSundays(rangeType: RangeType): boolean {
    const rangeTypesEnforcingSundays = [
        RangeType.ThisMonth,
        RangeType.ThisYear,
        RangeType.LastMonth,
        RangeType.LastMonth2,
        RangeType.LastMonth3,
        RangeType.LastMonth12,
    ];
    return rangeTypesEnforcingSundays.includes(rangeType);
}

function getNumberOfLastSprints(rangeType: RangeType): number {
    switch (rangeType) {
        case RangeType.LastMonth:
            return 1;
        case RangeType.LastMonth2:
            return 2;
        case RangeType.LastMonth3:
            return 3;
        case RangeType.LastMonth6:
            return 6;
        case RangeType.LastMonth12:
            return 12;
        default:
            return 0;
    }
}
