import express from 'express';
import * as dashboards from '../CRUD';
import { authChecksMiddleware } from '../../../utils/access/services/authChecksService';

const dashboardGroupMemberRouter = express.Router();

dashboardGroupMemberRouter
    .post('/:dashboard_id/groupMember', authChecksMiddleware, dashboards.addGroupMemberReq)
    .post('/:dashboard_id/groupMembers', authChecksMiddleware, dashboards.addDashboardGroupMembersReq)
    .put('/:dashboard_id/groupMember', authChecksMiddleware, dashboards.editGroupMembersReq)
    .put('/:dashboard_id/groupMembers', authChecksMiddleware, dashboards.editDashboardGroupMembersReq)
    .delete('/:dashboard_id/groupMembers', authChecksMiddleware, dashboards.deleteDashboardGroupMembersReq)
    .delete('/:dashboard_id/groupMember/:group_id', authChecksMiddleware, dashboards.removeGroupMemberReq);

export default dashboardGroupMemberRouter;
