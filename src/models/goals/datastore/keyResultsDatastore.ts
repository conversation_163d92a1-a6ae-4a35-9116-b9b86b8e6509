import { DbQueryResult } from '../../../utils/interfaces/DbQueryResult';
import { KeyResultTeamIdResult } from '../interfaces/KeyResultQuery';
import { promiseReadQuery } from '../../../utils/db';
import { prepareKeyResultTeamId } from '../factories/keyResultsFactory';

export async function getKeyResultTeamId(keyResultId: string): Promise<string | null> {
    const keyResultTeamIdQuery = prepareKeyResultTeamId(keyResultId);
    const results: DbQueryResult<KeyResultTeamIdResult> = await promiseReadQuery(
        keyResultTeamIdQuery.query,
        keyResultTeamIdQuery.params
    );
    return results.rows[0]?.team_id;
}
