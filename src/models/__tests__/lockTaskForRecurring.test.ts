import '../../../test/jest/_mocks/legacyMocks';
import { lockTaskForRecurring } from '../recurrence_2';
import { client as redis } from '../../utils/redis';

describe('checkIsNotRecurringInProgress', () => {
    it('should return true when there is no such key in redis', async () => {
        // means that the key is not in redis
        (redis as jest.Mock).set.mockResolvedValue('OK');

        const result = await lockTaskForRecurring('somerecurkey');
        expect(result).toBe(true);
    });

    it('should return false when there is such key in redis', async () => {
        // means that the key has been added already added and didn't expire yet
        (redis as jest.Mock).set.mockResolvedValue(null);

        const result = await lockTaskForRecurring('somerecurkey');
        expect(result).toBe(false);
    });
});
