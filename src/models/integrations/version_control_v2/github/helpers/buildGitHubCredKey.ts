import { AppTenantKind } from '../../../../sd/api/sdConnectAppURL';

/**
 * Build EntCred.key from userId, workspaceId and tenantKind. Legacy GitHub
 * integration uses tokens saved by a user in a workspace to make GitHub API
 * calls. However, since we are moving to having two types of creds
 * (workspace-wide and user-specific) in Slapdash infra we need a way to locate
 * a specific cred within Token Storage. We need this so we can pick a correct
 * workspace-wide cred for specific repository (we don't have machinery to get a
 * cred by creator ID).
 */
export function buildGitHubCredKey({
    userId,
    workspaceId,
    tenantKind,
}: {
    userId: string | number;
    workspaceId: string | number;
    tenantKind: AppTenantKind;
}) {
    return `github:${workspaceId}:${userId}:${tenantKind}`;
}
