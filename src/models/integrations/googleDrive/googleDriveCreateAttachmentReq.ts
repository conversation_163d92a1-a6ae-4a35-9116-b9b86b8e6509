import { v4 } from 'node-uuid';
import config from 'config';
import { Response } from 'express';

import { ObjectType, ObjectVersionUpdateRequest, OperationType } from '@time-loop/ovm-object-version';
import logging from '@clickup/shared/utils-logging';
import { sendNotifMessage } from '@clickup-legacy/utils/sqs-notif';
import { getAttachmentParentRelationship } from '../../attachment/attachmentHelpers';
import { parallel } from '../../../utils/asyncHelper';
import { formatAttachment } from '../../attachment/attachment';
import { addAttachmentsToES } from '../../elastic/producer';
import { batchQueriesAsync } from '../../../utils/db2';
import { getFileFromDrive } from './getFileFromDrive';
import { createFileFromDrive } from './createFileFromDrive';
import { ClickUpRequestV2 } from '../../../interfaces/ClickUpRequest';
import { ClickUpError } from '../../../utils/errors';
import { fixWorkspaceIdColumn } from '../../attachment/userDataAttachments';
import { getAttachmentTeamIdByParent } from '../../attachment/attachmentTeamHelpers';
import checkUserCanAddAttachments from '../cloud-attachments/checkUserCanAddAttachments';
import isEntId from '../../sd/helpers/isEntId';
import { nullthrows } from '../../../utils/nullthrows';
import sdGetUserCredAccessToken from '../../sd/api/sdGetUserCredAccessToken';

interface AttachmentsConfig {
    sources: Record<string, number>;
    types: Record<string, number>;
}

const logger = logging.getLogger('google_drive');
const attachments = config.get<AttachmentsConfig>('attachments');

export function googleDriveCreateAttachmentReq(req: ClickUpRequestV2, resp: Response) {
    const userid = req.decoded_token.user;
    const options = req.body;

    // Needed for sd auth.
    options.req = req;

    googleDriveCreateAttachment(userid, options, (err, result) => {
        if (err) {
            resp.status(err.status).send({ err: err.err, ECODE: err.ECODE });
        } else {
            resp.status(200).send(result);
        }
    });
}

interface Options {
    type: number;
    title: string;
    hidden: boolean;
    permanent: boolean;
    // TODO: Remove it once no clients using SD-flow are online.
    token_id?: string;
    access_token: string;
    parent: string;
    file_id: string;
    url: string;
    edit_url: string;
    ws_key: string;
    is_folder: boolean;
    mime_type: string;
    req: ClickUpRequestV2;
}

export async function googleDriveCreateAttachment(
    userid: number,
    options: Options,
    cb: (err: Partial<ClickUpError>, res?: unknown) => void
) {
    try {
        const source = attachments.sources.drive;
        const attachment_id = v4();
        const { hidden, type } = options;
        const now = new Date().getTime();
        let { permanent } = options;
        const extension = '';

        if (permanent == null) {
            permanent = true;
        }

        // validate type
        if ([1, 2, 4, 5, 6, 7, 8, 11, 14, 16].indexOf(type) < 0) {
            return cb({ err: 'Type not valid', status: 400, ECODE: 'GDRIVE_011' });
        }

        const workspaceId: number = await resolveAccess(userid, options.parent, type);
        return parallel<{
            tokenAccess: {
                id: string;
                webViewLink: string;
                hasThumbnail: boolean;
                mimeType: string;
            };
        }>(
            {
                async tokenAccess(para_cb) {
                    try {
                        const access_token = isEntId(options.token_id)
                            ? (
                                  await sdGetUserCredAccessToken(
                                      { ...options.req, params: { team_id: workspaceId } },
                                      { credID: options.token_id }
                                  )
                              ).token
                            : nullthrows(options.access_token);

                        if (!options.file_id) {
                            return createFileFromDrive(
                                userid,
                                {
                                    access_token,
                                    undefined, // refresh_token.
                                    title: options.title,
                                    mime_type: options.mime_type,
                                },
                                para_cb
                            );
                        }

                        return getFileFromDrive(
                            userid,
                            {
                                access_token,
                                undefined, // refresh_token.
                                file_id: options.file_id,
                            },
                            para_cb
                        );
                    } catch (err) {
                        logger.error({
                            msg: 'Failed to make drive attachment',
                            err,
                            ECODE: 'GDRIVE_012',
                        });
                        return para_cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'GDRIVE_012',
                        } as any);
                    }
                },
            },
            async (err, result) => {
                if (err) {
                    logger.error({
                        msg: 'Failed to make drive attachment',
                        err,
                        ECODE: 'GDRIVE_014',
                    });
                    return cb(err);
                }

                let properties: Partial<typeof result['tokenAccess']> = {};
                if (result.tokenAccess) {
                    properties = result.tokenAccess;
                }

                if ((result.tokenAccess && !options.file_id) || !options.url) {
                    options.file_id = properties.id;
                    options.url = properties.webViewLink;
                    options.edit_url = properties.webViewLink;
                }

                let thumbnail = null;
                if (properties.hasThumbnail) {
                    thumbnail = `https://drive.google.com/thumbnail?authuser=0&sz=w320&id=${options.file_id}`;
                }

                const location_hist_id = v4();
                const queries = [
                    {
                        query: 'INSERT INTO task_mgmt.attachments(id, parent_id, title, userid, date, permanent, type, hidden, encrypted, source, token_id, file_id, url, version, extension, edit_url, thumb_small, thumb_medium, thumb_large, is_folder, mimetype, file_deleted, workspace_id, date_created) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $17, $17, $18, $19, false, $20, $21) returning *',
                        params: [
                            attachment_id,
                            options.parent,
                            options.title,
                            userid,
                            now,
                            permanent,
                            type,
                            hidden,
                            false,
                            source,
                            // TODO: Consider removing token_id from the table.
                            DUMMY_TOKEN_ID,
                            options.file_id,
                            options.url,
                            0,
                            extension,
                            options.edit_url,
                            thumbnail,
                            options.is_folder,
                            properties.mimeType,
                            fixWorkspaceIdColumn(type, workspaceId),
                            now,
                        ],
                    },
                ];

                const relationships = await getAttachmentParentRelationship(options.parent, type, workspaceId);
                const versionUpdates: ObjectVersionUpdateRequest[] = [
                    {
                        object_type: ObjectType.ATTACHMENT,
                        object_id: attachment_id,
                        workspace_id: workspaceId,
                        operation: OperationType.CREATE,
                        data: {
                            relationships,
                            context: { ws_key: options.ws_key },
                        },
                    },
                ];

                if (type === attachments.types.task && permanent) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.task_history(task_id, field, date, after, userid) VALUES($1, $2, $3, $4, $5) RETURNING id, field',
                        params: [options.parent, 'attachments', now, attachment_id, userid],
                    });

                    if (workspaceId) {
                        versionUpdates.push({
                            object_type: ObjectType.TASK,
                            object_id: options.parent,
                            workspace_id: workspaceId,
                            operation: OperationType.UPDATE,
                            data: {
                                context: { ws_key: options.ws_key },
                            },
                        });
                    } else {
                        logger.error({
                            msg: 'Failed to write OVM update, Missing workspace ID',
                            ECODE: 'OVM_WS_933',
                            OVM_CRITICAL: true,
                        });
                    }
                }
                if (type === attachments.types.subcategory && permanent) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.subcat_history(id, subcategory, field, date, after, userid, workspace_id) VALUES($1, $2, $3, $4, $5, $6, $7) RETURNING id, field',
                        params: [
                            location_hist_id,
                            options.parent,
                            'attachments',
                            now,
                            attachment_id,
                            userid,
                            workspaceId,
                        ],
                    });
                } else if (type === attachments.types.category && permanent) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.category_history(id, category, field, date, after, userid, workspace_id) VALUES($1, $2, $3, $4, $5, $6, $7) RETURNING id, field',
                        params: [
                            location_hist_id,
                            options.parent,
                            'attachments',
                            now,
                            attachment_id,
                            userid,
                            workspaceId,
                        ],
                    });
                } else if (type === attachments.types.project && permanent) {
                    queries.push({
                        query: 'INSERT INTO task_mgmt.project_history(id, project, field, date, after, userid) VALUES($1, $2, $3, $4, $5, $6) RETURNING id, field',
                        params: [location_hist_id, options.parent, 'attachments', now, attachment_id, userid],
                    });
                }

                let rows: Array<{ field: unknown; id: string }> = [];
                try {
                    rows = (await batchQueriesAsync(queries, undefined, versionUpdates)) as Array<{
                        field: unknown;
                        id: string;
                    }>;
                } catch (error) {
                    logger.error({
                        msg: `Failed to make drive attachment for "${options.parent}"`,
                        err: error,
                        ECODE: 'GDRIVE_010',
                    });
                    return cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'GDRIVE_010',
                    });
                }

                let attachment_obj;
                try {
                    for (let i = 0; i < rows.length; i += 1) {
                        const row = rows[i];
                        if (row.field) {
                            if (type === attachments.types.task && permanent) {
                                sendNotifMessage('createTaskNotifications', [userid, options.parent, row.id, {}]);
                            }
                        } else {
                            attachment_obj = await formatAttachment(row);
                        }
                    }
                } catch (e) {
                    return cb(e);
                }
                if (type === attachments.types.subcategory && permanent) {
                    sendNotifMessage('createSubcategoryNotifications', [userid, options.parent, location_hist_id, {}]);
                } else if (type === attachments.types.category && permanent) {
                    sendNotifMessage('createCategoryNotifications', [userid, options.parent, location_hist_id, {}]);
                } else if (type === attachments.types.project && permanent) {
                    sendNotifMessage('createProjectNotifications', [userid, options.parent, location_hist_id, {}]);
                }

                addAttachmentsToES(userid, [attachment_id]);
                return cb(null, attachment_obj);
            }
        );
    } catch (err) {
        return cb(err);
    }
}

async function resolveAccess(userid: number, parent: string, type: number) {
    await checkUserCanAddAttachments({ userid, type, parent });
    return Number(await getAttachmentTeamIdByParent(type, parent));
}
// TODO: Get rid of token_id completely.
/** attachments.token_id is not used. This is a dummy value to use until the
 * field is gone. */
const DUMMY_TOKEN_ID = '00000000-0000-0000-0000-000000000000';
