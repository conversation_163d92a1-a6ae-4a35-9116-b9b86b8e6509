import { setWorkspaceIdForDbAffinity } from '@clickup/data-platform/context-db';
import { Job } from 'bullmq-v1';
import { getLogger } from '@clickup/shared/utils-logging';
import moment from 'moment';

import { getTaskIdsToSyncForCalendar } from './queries';
import { queue as taskSyncTaskQueue } from '../task-sync-task/queue';
import { queryGlobalRead } from '../shared/query';
import { SyncConfigQuery } from '../shared/queries/SyncConfigQuery';
import { QueueMessage } from './types';
import { join } from '../../../../utils/promise';
import { shouldPauseReconcileTasksInCalendarQueue } from '../../split/squadTreatments/gcalServiceTreatments';

const logger = getLogger('calendar.task-sync-incremental');

async function taskSyncIncremental(job: Job<QueueMessage, void, string>) {
    const { sync_id, team_id } = job.data;
    setWorkspaceIdForDbAffinity(Number(team_id));

    if (shouldPauseReconcileTasksInCalendarQueue()) {
        logger.warn({
            msg: `Skipping, shouldPauseReconcileTasksInCalendarQueue is set`,
            sync_id,
        });
        return;
    }

    const periodStart = +moment().startOf('week');
    const periodEnd = +moment().startOf('week').add({ weeks: 2 }).endOf('week');

    const [config] = await queryGlobalRead(SyncConfigQuery, { sync_id });
    if (!config || config.is_deleted || config.calendar_direction !== 'google') {
        logger.warn({ msg: `Skipping, sync_id not found or marked as deleted: ${sync_id}` });
        return;
    }

    const taskIds = await getTaskIdsToSyncForCalendar({
        sync_id,
        userid: config.userid,
        team_id: config.team_id,
        assignees: config.my_tasks_only ? [`${config.userid}`] : null,
        project_ids: config.project_id ? [config.project_id] : null,
        category_ids: config.category_id ? [config.category_id] : null,
        subcategory_ids: config.subcategory_id ? [config.subcategory_id] : null,
        archived: false,
        start_date_or_due_date_gte: periodStart,
        start_date_or_due_date_lte: periodEnd,
    });

    await join(
        taskIds.map(task_id =>
            taskSyncTaskQueue.add('task-sync-task', {
                task_id,
                sync_id,
                origin: 'task-sync-incremental',
            })
        )
    );

    await taskSyncTaskQueue.add('task-sync-task', {
        sync_id,
        calendar_id: config.calendar_id,
        garbage_collect: { task_ids: taskIds },
        origin: 'task-sync-incremental',
    });
}

export function processWorkFn() {
    return taskSyncIncremental;
}
