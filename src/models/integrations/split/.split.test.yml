- automation-squad-queue-dynamic-configs:
      treatment: on
      config: '{}'
- block-invalid-websockets-origin:
      treatment: on
- data-platform-is-migration-state-store-available:
      treatment: on
- should-use-improved-get-guest-count-task-query:
      treatment: off
- speed-up-group-calculations:
      treatment: on
- escape-view-export-data:
      treatment: on
- read-only-member-feature:
      treatment: on
- check-time-tracking-before-start:
      treatment: on
- generic-security-endpoint-ip-rate-limiter:
      treatment: on
      config: '{ "enabled": true, "whitelisted_ips": ["::1"], "rate_limiters": {
          "create_user": {"enabled": false, "limit": 1001}, "email_check":
          {"enabled": false, "limit": 1002}, "task_invite": {"enabled": false,
          "limit": 1003}, "payment": {"enabled": false, "limit": 1004} ,
          "short_term_login": {"enabled": true, "limit": 1005}, "long_term_login":
          {"enabled": false, "limit": 1006}, "super_long_term_login": {"enabled":
          false, "limit": 1007}, "short_term_password_change": {"enabled": false,
          "limit": 1} } }'
- should-verify-recaptcha-token:
      treatment: on
      config: '{ "enterprise": true, "allowed_ips" : [], "timeout":11000,
          "allowSkipRecaptchaFromCRM":true, "login" : { "delay_failure": true,
          "enabled": false, "enforce": false, "threshold": -1, "v2only": false,
          "acceptTimeoutTokens": true, "allowToPassOnError": true,
          "allowToPassOnTimeout": true } }'
- should-check-banned-status-before-recaptcha:
      treatment: off
- check-time-tracking-before-start:
      treatment: on
- growth_chat_gpt_limits:
      treatment: on
      key:
          - 300001
          - 612
      config: '{ "workspaceLevelHardLimit": 3, "workspaceLevelSoftLimit": 1,
          "userLevelHardLimit": 2, "userLevelSoftLimit": 0 }'
- growth_open_ai_secret_migration:
      treatment: off
- should-use-rds-historydb-for-scratchpad-history:
      treatment: on
- should-check-private-entities-permissions-for-tags:
      treatment: on
- should-enforce-latest-refresh-token-params:
      treatment: on
      config: '{ "enforce": true, "skip_mobile_for_enforcement": true }'
- should-assignee-filter-include-empty-groups:
      treatment: on
- location-overview-v3:
      treatment: on
- should-skip-rollup-custom-field:
      treatment: on
- should-copy-subtasks-outside-for-create-task-ws:
      treatment: on
- cu_v3_workspace_enrollment:
      treatment: on
- should-show-time-tracked-seconds-export:
      treatment: on
- hierarchy-personal-lists-enabled:
      treatment: on
- core_enable_api_circuit_breaker_per_role:
      treatment: off
- write-ovm-cdc:
      treatment: on
- use-websocket-processor-v2:
      treatment: on
- use-websocket-payload-v2:
      treatment: on
- should-restrict-workspace-creation-for-corporate-users:
      treatment: on
- should-show-empty-status-with-group-by-list-off:
      treatment: on
- should-use-workspace-settings-service:
      treatment: on
- automation-squad-enable-paywall-service-restrictions:
      treatment: on
      keys: dummy_id
- automation-squad-enable-paywall-service-count-increment:
      treatment: on
      keys: dummy_id
- automation-squad-enable-paywall-service-limit-check:
      treatment: on
      keys: dummy_id
- automation-squad-disable-paywall-service-count-increment-legacy:
      treatment: on
      keys: dummy_id
- automation-squad-disable-paywall-legacy-check:
      treatment: on
      keys: dummy_id
- should_track_same_statuses_in_different_groups_separately:
      treatment: on
- should_show_timl_statuses_in_filters:
      treatment: on
- data-platform-should-use-shard-service:
      treatment: on
- should-use-v3-service-get-tasks:
      treatment: off
      config: '{"getTask": false, "getTasks": false}'
- task_task_v3_service_configs:
      treatment: off
      config: '{"tasksApiCacheSize": 1000, "bulkCallMaxCount": 5, "useKeepAliveAgent":
          true}'
- task_bulk_task_api_config:
      treatment: on
      config: '{"batchSize": 250, "callBatchCallInParallel": false}'
- inbox-should-perform-delete-task-notifs-query-user-level:
      treatment: on
- shouldUseBlockingRedisCommandsReliableQueue:
      treatment: on
- use-authz-lib-new-access-checks:
      treatment: on
- automation-squad-should-call-template-service-for-template-merging:
      treatment: on
- disable-websocket-payload:
      treatment: off
- should_reorder_list_view_subtasks_for_exporting:
      treatment: on
- wms_workspace_in_flight_limit:
      treatment: on
      config: '{"limit": 10, "per_size_limit": {"small": 2, "medium": 3, "large": 4}}'
- wms_migration_timeout:
      treatment: on
      config: '{"migrationTimeout": {"small": 0, "medium": 1, "large": 420000}}'
- wms_cleaner_limit:
      treatment: on
      config: '{"limit": 1}'
- wms_admin_users:
      treatment: on
      config: '[-1]'
- wms_cursor_limit:
      treatment: on
      config: '{"limit": 3}'
- wms_parallel_read_threshold:
      treatment: on
      config: '{"source":{"bdr": {"task_history":3, "default":5}},
          "destination":{"bdr": {"task_history":3, "default":5}}}'
- wms_max_retries:
      treatment: on
      config: '{"small": 4, "medium": 5, "large":1}'
- wms_workflow_config:
      treatment: on
      config: '{"disableTemporalFreeze": true}'
- should_filter_groups_on_timeline_view:
      treatment: on
- fields-ovm-write:
      treatment: on
- task_utilize_multiple_connections:
      treatment: on
      config: '{"enabled": true, "max_connections": 2}'
- billing-should-use-new-user-teams-query:
      treatment: on
- billing-should-clients-use-billing-usage-service:
      treatment: on
- templates_should_use_subtask_limit_for_task_template_merging:
      treatment: on
      config: '{ "undeletedLimit": 1000, "deletedLimit": 1000 }'
- granular-field-permissions:
      treatment: on
- field-level-permissions:
      treatment: on
- should-load-available-fields-for-guest-with-shared-tasks:
      treatment: on
- should_update_threaded_comments_on_resolve_all_items:
      treatment: on
- tasks_time_in_status_history_records_limit_config:
      treatment: on
      config: '{ "loadPartially": 10000, "skipLoading": 1000000 }'
- transform-ovm-event-to-websocket-v2-message:
      treatment: on
- tasks_use_multi_connection_db_client:
      treatment: on
      config: |-
          {
            "getBulkTasks": { "connectionCount": 3 },
            "prefetchBulkTasks": { "connectionCount": 3 },
            "getTasks": { "connectionCount": 3 },
            "prefetchBulkTasks": { "connectionCount": 3 },
            "experience.getTask": { "connectionCount": 3 },
            "experience.getTasks": { "connectionCount": 3 },
            "experience.getSubTasks": { "connectionCount": 3 },
            "experience.getSubTaskTree": { "connectionCount": 3 },
            "experience.getSubTaskTrees": { "connectionCount": 3 },
            "experience.getUsersLastViewOfTask": { "connectionCount": 3 },
            "experience.getTaskTags": { "connectionCount": 3 },
            "experience.getListsWithHierarchy": { "connectionCount": 3 },
            "publicTaskExperience.getPublicTask": { "connectionCount": 3 }
          }
- task_queued_deleter_interval_config:
      treatment: on
      config: |-
          {
            "checkIntervalMillis": 60000,
            "shouldRunTheJob": true,
            "debugging": false
          }
- task_queued_task_deleter_config:
      treatment: on
      config: |-
          {
            "interval_size": 1000,
            "interval_batch_size": 100,
            "log_sample_size": 10,
            "debugging": false
          }
- billing-double-renewal-mitigation:
      treatment: cache-local_eq
      config: |-
          {
              "type": "local",
              "op": "old-nbd-eq"
          }
- billing-renewal-duplicate-processing:
      treatment: on
      config: |-
          {
              "processPendingTransactions": true,
              "removeDanglingPendingTransactions": true
          }
- should-force-redirect-invite-to-new-shard:
      treatment: on
- tasks_should_retry_neural_api_get_message_on_5xx:
      treatment: on
      config: '{ "retries": 3, "factor": 1, "minTimeout": 10 }'
- competitor-spy-blocking-rules:
      treatment: on
      config: |-
          {
              "ips": ["*************"],
              "domains": ["monday.com"],
              "emails": ["<EMAIL>"],
              "workspaces": []
          }
- use-bind-attachment-function:
      treatment: on
      keys: dummy_id
- foundational_events:
      treatment: on
- should-check-groups-members-for-subcategory-access:
      treatment: on
- should-set-show-recurring-tasks-to-false-for-free-plan:
      treatment: on
- folder-create-permissions:
      treatment: on
- use-dedup-cache-for-websocket-v2-messages:
      treatment: on
- should-prime-layout-version-during-workspace-creation:
      treatment: on
- tasks_public_api_markdown_description_enabled:
      treatment: on
- tasks_should_use_new_get_tasks_hierarchy_query:
      treatment: on
- tasks_should_skip_content_history_with_no_changes:
      treatment: on
- tasks_edit_position_task_count_limit_config:
      treatment: on
      config: |-
          {
              "taskCountLimit": 1000
          }
- use-internal-guests-count-settings:
      treatment: on
      config: |-
          {
              "threshold": 0.5,
              "minimumTeamTotalUserCount": 4
          }
- hierarchy-subcategories-query-should-filter-other-users-personal-list:
      treatment: on
- should-collect-workspace-settings-from-remote-shard:
      treatment: on
- strict-follower-policy:
      treatment: on
      config: '{ "strictViewsPolicy": true, "strictSubcategoriesPolicy": true,
          "strictCategoriesPolicy": true, "strictProjectsPolicy": true }'
- request-access:
      treatment: on
      config: '{ "spaces": true, "tasks": true }'
- time-estimates-simls-cache-fix:
      treatment: on
      config: '{ "enabled": true, "taskBatchSize": 10 }'
- should-proxy-user-creation-to-the-global-shard:
      treatment: on
- use-refactored-user-creation:
      treatment: on
- time-estimates-simls-count-fix:
      treatment: on
- data-platform-trash-field-deletion-config:
      treatment: on
      config: '{ "includeAdditionalRemovals": true, "includeViewColsRemoval": true }'
- inbox-interval-redis:
      treatment: on
      config: '{ "consume": "autoqw", "produce": "autoqw" }'
- tasks_should_use_items_orderindex_as_default_for_custom_items_orderindex:
      treatment: on
- task_created_event_concurrency_limit:
      treatment: on
      config: '{ "limit": 10 }'
- generic-view-query-optimizations:
      treatment: on
      config: |-
          {
              "useCteWithListRelationshipCfSort": true,
              "extraWorkspaceIdConditions": true,
              "extraWorkspaceIdConditionsForTIML": false,
              "renameItemsToTasks": true,
              "useIsNotBoolOptimization": true,
              "removeUnusedJoinWithLinkedSubcategoryTasks": true
          }
- should-skip-fetching-guest-fields:
      treatment: on
- inbox-ws-clip-comment-notification:
      treatment: off
- use-workspace-id-for-query:
      enable_for_modern_shards: true
      check_if_new_paid_guest: true
      check_if_user_updated_paid_guest: true
      get_guest_count: true
      get_guest_attributes: true
      get_team_members: true
      authorization_checks: true
      check_if_new_free_guest: true
      project_member_hierarchy_task_members: true
- templates-is-unified-field-remapping-enabled:
      treatment: on
- should-use-new-password-hashing:
      treatment: on
      config: '{ "allowScryptOnStore": true, "allowScryptOnVerification": true }'
- disable-websocket-producer-customized-batch:
      treatment: on
- billing-should-check-ai-addon-eligibility:
      treatment: on
- billing-tax-country-code-settings:
      treatment: on
      config: |-
          {
              "taxableCountryCodes": ["US", "PR"]
          }
- billing-should-use-taxamo-by-default:
      treatment: off
- billing-available-ai-addon:
      treatment: on
      config: '{"availableAi":"ai_addon"}'
- billing-available-ai-addon:
      treatment: new_ai_addon
      keys:
          - '9001001'
      config: |-
          {
              "availableAi": "ai_addon_2024"
          }
- new-workspace-role-permissions:
      treatment: on
      config: '{"dev_key_authorization":{"enabled":true},"can_use_chat": {"enabled":
          false,"override_workspaces": []}}'
- hide-inaccessible-custom-field-view-columns:
      treatment: off
- use-custom-fields-cache:
      treatment: on
      config: '{ "enableClickbotQueryOverride": true }'
- should-filter-out-attachments-from-fields-hidden-from-guests:
      treatment: on
- exp_billing-should-start-entitlements-grace-period:
      treatment: off
- templates-copied-object-reference-remapping:
      treatment: on
      config: '{"relationships":true,"workInProgressStatuses":true,"widgetCustomFields":true}'
- should-always-update-from-history-rows-in-add-field-values:
      treatment: on
- should-skip-form-read-permissions:
      treatment: on
- billing-cc-trials:
      treatment: on
      config: '{"trialsHistoryCutOffDate": "2020-01-01","enableIntervals": true}'
- cards_blocking:
      treatment: on
- billing-should-track-usage-limit-hit:
      treatment: off
- billing-unified-intervals-config:
      treatment: on
      config: |-
          {
            "rollup":
            {
                "crm": true
            }
          }
- billing-should-send-segment-events:
      treatment: on
      config: |-
          {
            "rollup": false
          }
- cards_calendar_include_only_assigned_timl:
      treatment: on
- chat-v3-should-use-comments-db:
      treatment: off
- chat-v3-comment-migration-chunk-config:
      treatment: on
      config: |-
          {
              "chunkSize": 50,
              "migrateAllChunks": true
          }
- chat-v3-auth-filter-cache-bypass-options:
      treatment: on
      config: |-
          {
              "bypassCacheThreshold": 50,
              "prefetchBatchSize": 1000
          }
- crm-should-disable-global-table-checks:
      treatment: on
- role-subtype:
      treatment: on
      config: |-
          {
              "import": {
                  "enabled": true,
                  "verboseLogging": false,
                  "workerSleepMs": 10000,
                  "workspaceBatchSize": 10,
                  "memberBatchSize": 100
              },
              "kafka": {
                  "enabled": true,
                  "verboseLogging": true,
                  "useMasterForObjectAclsQuery": true
              },
              "limited_member_enabled": true,
              "workspace_role_permissions_enabled": true,
              "raise_validate_role_subtype_err": true
          }
- limited-members:
      treatment: on
- refresh-token-cookie-config:
      treatment: on
      config: |-
          {
               "domain": ".id.app.clickup-qa.com"
          }
- cards-egress-query-refactors:
      treatment: on
      config: |-
          {
              "legacy_inbox": {
                  "inbox_tasks": true,
                  "inbox_future": true,
                  "inbox_done": true,
                  "inbox_unscheduled": true,
                  "inbox_delegated": true
              },
              "assigned_comments": true
          }
- jwt-token-cookie-config:
      treatment: on
      config: |-
          {
               "domain": "localhost"
          }
- should-batch-delete-followers:
      treatment: on
- user-platform-special-user-sign-in-prevention-config:
      treatment: on
      config: |-
          {
              "createUser": true,
              "putUser": true,
              "patchUser": true,
              "loginWithSso": true
          }
- chat-v3-use-comments-db-for-room-relationships-config:
      treatment: on
      config: |-
          {
              "enabled": true,
              "commentLimit": 10000
          }
- app-custom-fields:
      treatment: on
- views-fix-copy-standard-view-id:
      treatment: on
- should-people-custom-field-work-same-as-assignee:
      treatment: on
- sso-workspace-scoping-config:
      treatment: on
      config: |-
          {
              "enabled": true,
              "enabledByDefaultForProviders": ["saml"],
              "enabledForSsoOrgs": [],
              "disabledForSsoOrgs": [],
              "allowSkippingRequiredSsoCheck": true,
              "shouldExtendRefreshTokenWithAcceptedInvitationScope": true,
              "shouldExtendRefreshTokenWithWorkspaceCreatedScope": true
          }
- templates-restrict-pin-shared-templates:
      treatment: on
- fields-templates-copy-inherited-statuses:
      treatment: on
- cards-everywhere:
      treatment: on
- cards-processing-use-fields-fetch-for-access-check:
      treatment: on
- cards-display-deactivated-users-in-widgets:
      treatment: on
- cards_use_new_grid_ux:
      treatment: on
- automation-squad-email-users-on-failed-actions:
      treatment: on
- cards_delegated_permissions:
      treatment: on
- workspace-user-profile-sync-config:
      treatment: on
      config: |-
          {
              "loadHierarchyEndpoint": {
                  "enabled": true
              },
              "forwardUserUpdatesToShards": {
                  "enabled": true
              },
              "syncForWorkspaceMembers": {
                  "enabled": true,
                  "updatesChunkSize": 2
              }
          }
- billing-should-get-all-entitlements-always-full-data:
      treatment: off
- should-send-email-template-via-iterable:
      treatment: off
- should-fan-out-task-events-during-team-member-removal:
      treatment: on
      config: |-
          {
              "forFollowers": true,
              "forTaskMembers": true
          }
- views-should-include-subtasks-of-siml-to-exports:
      treatment: on
- task_should_prevent_copying_with_deleted_parent:
      treatment: on
- cards-processing-due-date-query-optimization-enabled:
      treatment: on
- task_should_de_duplicate_dependencies:
      treatment: on
- task_should_use_recursive_parent_validation:
      treatment: on
- data-platform-global-team-orderindex-config:
      treatment: on
      config: |-
          {
              "readFromGlobal": false,
              "writeToShard": true
          }
- billing-should-apply-offer:
      treatment: on
- custom-fields-by-custom-task-type:
      treatment: on
      config: |-
          {
              "default": false,
              "fieldAPIs": true,
              "fallbackIncludeAllFields": true
          }
- should-use-new-billing-pricing-logic:
      treatment: off
- cards-min-max-avg-on-bar-chart-enabled:
      treatment: on
- should-use-new-plan-cost-computations:
      treatment: off
- field-ai-skip-entitlement-usage:
      treatment: on
- tasks_should_use_master_for_orderindexes_after_reordering:
      treatment: on
- authz-request-configuration:
      treatment: on
      config: >-
          {
              "accessCheckAsyncLoop": { "enabled": true, "synchronousBatchSize": 1 },
              "accessCheckWorkspaceDataStore": {
                  "enabled": true,
                  "minKeysForMemoization": 2
              }
          }
- bypass-filter-accessible-tasks-for-clickbot:
      treatment: on
- search-custom-fields-query-refactor:
      treatment: on
- should-load-task-permissions-in-experience-service:
      treatment: on
- tile-canvas-in-chat:
      treatment: on
- access-management-default-location-permissions:
      treatment: on
- templates-copy-task-options:
      treatment: off
      config: '{"operations":{"validateTeamAccess":["all"],"validateTaskAccess":["all"],"validateAttachmentAccess":["all"],"validateTemplateMembers":["all"],"validateSubcategoryAccess":["all"]}}'
- billing-should-include-limit-overrides:
      treatment: on
      config: '{"dependantEntitlements":true,"checkEntitlement":true,"checkEntitlementLimit":true}'
- task_undeletion_config:
      treatment: on
      config: |-
          {
              "useFixedLogicForTaskUndeletion": true,
              "useFixedLogicForAttachmentUndeletion": false
          }
- tasks_should_suppress_auto_replies:
      treatment: on
- filter-accessible-tasks-config:
      treatment: on
      config: |-
          {
              "shouldRunSomePartsInParallel": true,
              "shouldSkipInBulkForUnrequestedRelatedTasks": true
          }
- should-prevent-copy-task-by-subtask-limit:
      treatment: off
- task_should_limit_tasks_per_list:
      treatment: off
- dashboards_show_empty_field:
      treatment: on
- should-calculate-formula-values-on-export:
      treatment: off
- estimates-support-in-bulk-actions:
      treatment: on
- billing-usage-transactional-counter-increment:
      treatment: on
- copy-task-concurrency:
      treatment: on
      config: '{"enabled": true, "maxConcurrency": 10}'
- task-as-object-config:
      treatment: on
      config: |-
          {
              "monolithAccessChecks": true,
              "taskAsObjectBulkInterceptor": true,
              "getTaskGuard": true,
              "taskPermissionsService": true,
              "taskMemberPrivacyCheck": true,
              "genericViewPrivacyCheck": true
          }
- billing-ai-credit-packs-available:
      treatment: on
- crm-should-use-refactored-salesforce:
      treatment: on
- chat-v3:
      treatment: on
      key:
          - 222
- access-management-authenticated-forms:
      treatment: on
- enable-security-measures-for-dev-key:
      treatment: on
- should-use-replica-option-for-custom-fields-in-task-creation:
      treatment: on
- explicitly-set-replica-to-false-in-create-task-get-item:
      treatment: on
- enable-missing-authz-checks:
      treatment: on
      config: |-
          {
              "endpoints": [
                  {
                      "endpoint": "v2/task/:task_id/time_in_status",
                      "enabled": true
                  },
                  {
                      "endpoint": "v2/task/bulk_time_in_status/task_ids",
                      "enabled": true
                  },
                  {
                      "endpoint": "/v2/space/:space_id/tag",
                      "enabled": true
                  },
                  {
                      "endpoint": "v2/team",
                      "enabled": true
                  },
                  {
                      "endpoint": "v2/list/:list_id/taskTemplate/:template_id",
                      "enabled": true
                  }
              ]
          }
- should-make-task-ids-unique-before-calling-get-time-spent-records:
      treatment: on
- should-enable-scheduling-downgrade-requests:
      treatment: off
- access-management-default-location-permissions:
      treatment: on
- limited-members-free-group-membership:
      treatment: off
- access-management-custom-permission-levels:
      treatment: on
      config: '{"enabled": true}'
- access-management-update-tasks-on-remove-followers:
      treatment: off
- templates-use-entities-datastore-v2-queries:
      treatment: off
- use-fixed-view-widget-parent-legacy-access-check:
      treatment: on
- prevent-external-guest-space-access:
      treatment: on
- use-default-roles-permissions:
      treatment: on
      config: |-
          {
              "enabled": true,
              "override_workspaces": []
          }
- task_should_skip_deletion_of_manual_task_links:
      treatment: on
- task_should_use_origin_for_skipping_links_during_cleanup:
      treatment: on
- views-use-new-activity-view-query:
      treatment: on
      config: '{"enabled": true, "taskLimit": 500}'
- should_reject_blank_statuses:
      treatment: on
- task_should_run_task_property_merging:
      treatment: on
- cards-enhanced-status-visual-filter:
      treatment: on
- task_should_run_task_timl_merge:
      treatment: on
- access-management-authenticated-views:
      treatment: on
- views-new-generic-view-label-custom-field-sorting:
      treatment: on
- templates-should-check-default-view-entitlements:
      treatment: on
- sso-provider-token-config:
      treatment: on
      config: |-
          {
              "ssoTokenExpiry": "2m",
              "ssoLoginTokenExpiry": "2m",
              "singleUseAssertionId": true,
              "assertionIdExpiry": 600,
              "nonceExpiry": 600
          }
- prevent-alternative-emails:
      treatment: on
      config: |-
          {
              "enabled": true,
              "banned_base_emails": ["<EMAIL>"]
          }
- allow-workspace-level-view-delete-view-permission:
      treatment: on
- should-check-unshare-permission:
      treatment: on
      config: |-
          {
              "enabled": true,
              "override_workspaces": []
          }

- billing-ai-brainwave-rollout:
      treatment: on
      config: |-
          {
              "showOnBillingPage": true,
              "supportOnBE": true
          }

- billing-should-use-entitlements-cache:
      treatment: on

- views-should-activity-view-have-filters-grouping:
      treatment: on

- billing-should-enable-addons-on-free-forever:
      treatment: on
      config: |-
          {
              "showOnBillingPage": false,
              "showOnPaywall": false,
              "allowQueryParam": false,
              "supportOnBE": false
          }

- task_use_refactored_get_subcategory_time_spent:
      treatment: on
      config: |-
          {
              "enabled": true,
              "sequentialChunkSize": 50,
              "parallelChunkSize": 5
          }

- downgrade-user:
      treatment: on
      config: |-
          {
              "enabled": true,
              "override_workspaces": []
          }

- imports-slack-import-config:
      treatment: on
      config: '{"importEmojis":true,"messagesLimit":5000,"commentConcurrency":10}'
- enable-app-user-creation:
      treatment: on
      key:
          - 300001
