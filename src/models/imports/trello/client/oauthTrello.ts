import { OAuth } from 'oauth';
import { ACCESS_URL, APP_KEY, APP_SECRET, APP_URL, OAUTH_ENCRYPTION, OAUTH_VERSION, REQUEST_URL } from './config';

// TODO: determine if this `loginCallback` URL is safe to be removed. Seems to only be needed for `getReqToken()`
const loginCallback = `${APP_URL}/assets/html/jira-oauth-callback.html?trello=true&state=${APP_URL}`;
export const oauth = new OAuth(
    REQUEST_URL,
    ACCESS_URL,
    APP_KEY,
    APP_SECRET,
    OAUTH_VERSION,
    loginCallback,
    OAUTH_ENCRYPTION
);
