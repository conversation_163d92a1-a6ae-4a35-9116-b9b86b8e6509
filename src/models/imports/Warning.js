import config from 'config';

export class Warning {
    constructor(msg, description, group, example) {
        this.msg = msg;
        this.description = description;
        this.group = group;
        this.example = example;
    }

    makeGroup() {
        this.examples = [this.example];
        delete this.example;
        return this;
    }

    addExample(example) {
        if (!this.examples) {
            return;
        }

        if (this.examples.length > config.imports.max_examples) {
            return;
        }

        this.examples.push(example);
    }
}
