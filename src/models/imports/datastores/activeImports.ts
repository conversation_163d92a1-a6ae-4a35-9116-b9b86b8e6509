import * as db from '../../../utils/db';
import { ClickUpError } from '../../../utils/errors';
import { ImportErrors } from '../../../utils/errors/constants';
import { <PERSON>b<PERSON>uery<PERSON><PERSON>ult } from '../../../utils/interfaces/DbQueryResult';
import { CURRENT_SHARD_ID } from '../../../config/domains/shardingConfig';
import { IMPORT_LOOK_BACK } from '../scheduler/config';

const ImportError = ClickUpError.makeNamedError('imports');

export function fetchActiveImports(): Promise<DbQueryResult<{ source: string; active_import_count: number }>> {
    const now = new Date().getTime();
    const twoWeeksAgo = now - IMPORT_LOOK_BACK; // 6 days
    const query = `
        SELECT imports.source,
               count(*)::int AS active_import_count
        FROM task_mgmt.imports
                 JOIN task_mgmt.workspace_to_shard
                      ON workspace_to_shard.workspace_id = imports.team_id
        WHERE (last_update = 0 OR last_update > $2)
          AND import_step NOT IN ('complete', 'failed', 'deleted', 'canceled')
          AND workspace_to_shard.shard_id = $1
        GROUP BY imports.source
        UNION ALL
        SELECT imports_docs.source,
               count(*)::int AS active_import_count
        FROM task_mgmt.imports_docs
                 JOIN task_mgmt.workspace_to_shard
                           ON workspace_to_shard.workspace_id = imports_docs.workspace_id
        WHERE (updated_date > TO_TIMESTAMP($2 / 1000))
          AND status NOT IN ('completed', 'failed', 'canceled')
          AND workspace_to_shard.shard_id = $1
        GROUP BY imports_docs.source`;

    const params = [CURRENT_SHARD_ID, twoWeeksAgo];

    return db.replicaQueryAsync(query, params, {
        NamedError: ImportError,
        ECODE: ImportErrors.FailedToGetActiveImportCount,
    });
}
