import config from 'config';
import { randomInt } from 'crypto';
import { getLogger } from '@clickup/shared/utils-logging';
import { emailCodeTestParams } from '@clickup-legacy/models/integrations/split/squadTreatments/userPlatformTreatments';
import { isAnyProd } from '@clickup-legacy/utils/environment';

const logger = getLogger('userHelpers');

/**
 * Generates a random numeric email code.
 * If the email address is provided and it is configured in email-code-test-params Split Treatment
 * and the static_test_email_code config file entry has a valid code it will return that code (non-prod environments only).
 * @param email - The email the code is being generated for..
 * @returns The generated email code.
 */
export function generateEmailCode(email?: string) {
    return getTestEmailCode(email) || randomInt(1000, 10000);
}

function getTestEmailCode(email?: string): number | undefined {
    if (isAnyProd || !email) {
        return undefined;
    }

    try {
        const testCode = config.get<number | null | undefined>('static_test_email_code');

        if (typeof testCode === 'number' && String(testCode).match(/^\d+$/)) {
            const hasMatch = emailCodeTestParams()?.email_patterns?.some(pattern =>
                email?.match(new RegExp(pattern, 'g'))
            );

            if (hasMatch) {
                logger?.info({
                    email,
                    msg: `Using test email code for user`,
                });

                return testCode;
            }
        }
    } catch (err) {
        logger?.error({
            err,
            email,
            msg: 'Error checking email code test params',
        });
    }

    return undefined;
}
