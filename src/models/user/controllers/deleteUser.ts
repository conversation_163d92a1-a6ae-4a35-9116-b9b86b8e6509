import { NextFunction, Response, Request } from 'express';
import { shardBroadcastService } from '@clickup/data-platform/sharding/routing/broadcast';
import { isGlobalShard } from '@clickup-legacy/config/domains/shardingConfig';
import { ClickupBasicAuthRequest } from '../../../interfaces/ClickUpRequest';
import { RequestStatusCodes } from '../../../utils/statusCodes/constants';

import {
    cleanWorkspaceDataBeforeUserDeletion,
    deleteUserAndGlobalData,
    checkIfUserCanBeDeleted,
} from '../services/deleteUser';

export async function deleteUserReq(req: Request, resp: Response, next: NextFunction): Promise<void> {
    const { user, decoded_token } = req as ClickupBasicAuthRequest;
    const { id: user_id } = user;
    const { user: jwt_user_id } = decoded_token;

    try {
        if (shardBroadcastService.isBroadcast(req)) {
            await cleanWorkspaceDataBeforeUserDeletion(user_id, jwt_user_id);
        } else {
            await checkIfUserCanBeDeleted(user_id, jwt_user_id);
            await shardBroadcastService.broadcastRequest(req);
            await cleanWorkspaceDataBeforeUserDeletion(user_id, jwt_user_id);
            await deleteUserAndGlobalData(user_id, jwt_user_id);
        }
        resp.status(RequestStatusCodes.SuccessNoContent).send();
    } catch (error) {
        next(error);
    }
}
