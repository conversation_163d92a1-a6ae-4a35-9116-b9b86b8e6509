export interface NotifSetting {
    browser_enabled: number | null;
    browser_my_tasks: number | null;
    clickbot_browser_enabled?: number;
    clickbot_email_enabled?: number;
    clickbot_mobile_push?: number;
    clickbot_web_app?: number;
    date_format?: string | null;
    email_enabled: number;
    email_my_tasks: number | null;
    field: string;
    notif_setting_table_row: string;
    invite?: boolean;
    minutes_after: number | null;
    mobile_push: number | null;
    mobile_push_my_tasks: number | null;
    mobile_push_sound: number | null;
    team: string;
    time_of_day_hour: number | null;
    time_of_day_minute: number | null;
    time_of_day_tz: string | null;
    timezone?: string;
    twenty_four_hr_setting?: boolean | null;
    userid: string;
    web_app: number | null;
    web_app_my_tasks: number | null;
    integrations?: Partial<Record<string, string[]>> | null;
}
