import { ApiProperty } from '@nestjs/swagger';
import { ResourceType, ClickUpResourceName } from '../../../../utils/notification/entityResourceName';

import { BaseHistoryItem } from './base-history-item';
import { HistoryItemType } from './history-item-type';
import { DynamoDBAttribute, ResourceNameApiProperty } from '../../../../utils/notification/decorators';

export class FollowerRemoved extends BaseHistoryItem<HistoryItemType.FollowerRemoved> {
    @ApiProperty({ enum: [HistoryItemType.FollowerRemoved] })
    @DynamoDBAttribute()
    readonly type: HistoryItemType.FollowerRemoved = HistoryItemType.FollowerRemoved;

    @ResourceNameApiProperty({ required: true, exampleType: ResourceType.User })
    @DynamoDBAttribute()
    follower: ClickUpResourceName<ResourceType.User>;

    static fromHydratedLegacyHistoryItem(hydratedHistoryItem: any): FollowerRemoved {
        const result = super.fromHydratedLegacyHistoryItem(hydratedHistoryItem) as FollowerRemoved;
        result.follower = new ClickUpResourceName(ResourceType.User, null, hydratedHistoryItem.before?.id);
        return result;
    }

    retrieveUsers() {
        return [this.actorId.id, this.follower.id].filter(id => !!id);
    }
}
