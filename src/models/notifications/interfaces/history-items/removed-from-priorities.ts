import { ApiProperty } from '@nestjs/swagger';
import { ResourceType, ClickUpResourceName } from '../../../../utils/notification/entityResourceName';

import { BaseHistoryItem } from './base-history-item';
import { HistoryItemType } from './history-item-type';
import { DynamoDBAttribute, ResourceNameApiProperty } from '../../../../utils/notification/decorators';

export class RemovedFromPriorities extends BaseHistoryItem<HistoryItemType.RemovedFromPriorities> {
    @ApiProperty({ enum: [HistoryItemType.RemovedFromPriorities] })
    @DynamoDBAttribute()
    readonly type: HistoryItemType.RemovedFromPriorities = HistoryItemType.RemovedFromPriorities;

    @ResourceNameApiProperty({ required: true, exampleType: ResourceType.User })
    @DynamoDBAttribute()
    assignee: ClickUpResourceName<ResourceType.User>;

    static fromHydratedLegacyHistoryItem(hydratedHistoryItem: any): RemovedFromPriorities {
        const result = super.fromHydratedLegacyHistoryItem(hydratedHistoryItem) as RemovedFromPriorities;
        result.assignee = ClickUpResourceName.fromUserId(hydratedHistoryItem.before?.id);
        return result;
    }

    retrieveUsers(): string[] {
        return [this.actorId.id, this.assignee.id].filter(id => !!id);
    }
}
