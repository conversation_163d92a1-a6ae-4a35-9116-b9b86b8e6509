import {
    AssigneeAdded,
    As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Comment,
    CustomField,
    Follower<PERSON>dded,
    Follower<PERSON><PERSON>oved,
    GroupAssigneeAdded,
    GroupAssigneeRemoved,
    TypedHistoryItem,
    HistoryItemClassByType,
    HistoryItemType,
} from './interfaces/history-items';

export class UnknownHistoryItemTypeException extends Error {
    name = 'UnknownHistoryItemTypeException';
}

export function createHistoryItemFromHydratedLegacyHistoryItem<T extends TypedHistoryItem>(
    hydratedHistoryItem: any
): T {
    let correctClass = HistoryItemClassByType[hydratedHistoryItem.field as HistoryItemType];

    // cf_uuid -> CustomField
    if (hydratedHistoryItem.field.toString().startsWith('cf_')) {
        correctClass = CustomField;
    }

    // follower -> follower_add | follower_rem
    if (hydratedHistoryItem.field === 'follower') {
        correctClass = hydratedHistoryItem.before ? FollowerRemoved : FollowerAdded;
    }

    // assignee -> assignee_add | assignee_rem
    if (hydratedHistoryItem.field === 'assignee') {
        correctClass = hydratedHistoryItem.before ? AssigneeRemoved : AssigneeAdded;
    }

    // group_assignee -> group_assignee_add | group_assignee_rem
    if (hydratedHistoryItem.field === 'group_assignee') {
        correctClass = hydratedHistoryItem.before ? GroupAssigneeRemoved : GroupAssigneeAdded;
    }

    // attachment_comment -> comment
    if (hydratedHistoryItem.field === 'attachment_comment') {
        correctClass = Comment;
    }

    if (!correctClass) {
        throw new UnknownHistoryItemTypeException(`Unknown history item type: ${hydratedHistoryItem.field}`);
    }
    return correctClass.fromHydratedLegacyHistoryItem(hydratedHistoryItem) as T;
}
