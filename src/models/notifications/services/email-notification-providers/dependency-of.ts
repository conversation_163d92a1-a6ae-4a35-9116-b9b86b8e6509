import config from 'config';
import { EmailTemplateValuesProvider } from './email-template-values-provider';
import type { HistoryItemType as NotificationType } from '../../interfaces/history-items';

export class DependencyOfEmailNotificationProvider extends EmailTemplateValuesProvider {
    supportedNotificationTypes = ['dependency_of' as NotificationType];

    getEmailTemplateValues(userid: string | number, hist_item: any, options: any, timezoneOptions: any): Promise<any> {
        const templateValues: any = {};
        templateValues.template_id = 'DEPENDENCY_OF_NOTIF';
        if (hist_item.task) {
            templateValues.dependency_html = `
                <a href="*| DEPENDENCY_OF_TASK |*" target="_blank" style="Margin: 0; color: #F27272; font-family: 'Gotham',Helvetica,Arial,sans-serif; font-size: 14px; font-weight: 500; line-height: 1.3; margin: 0 !important; padding: 0; text-decoration: underline;">*| DEPENDENCY_OF_TASK_NAME |*</a>
            `;
            templateValues.dependency_of_name = hist_item.task.name;
            templateValues.dependency_of_link = `${config.app.app_url}/t/${hist_item.task_id}`;
        } else {
            templateValues.dependency_html = `
                <span style="Margin: 0; color: #F27272; font-family: Arial,sans-serif; font-size: 14px; font-weight: bold; line-height: 1.3; margin: 0 !important; padding: 0;">*| DEPENDENCY_OF_TASK_ID |*</span>
            `;
            templateValues.dependency_of_name = `#${hist_item.after}`;
        }

        return templateValues;
    }
}
