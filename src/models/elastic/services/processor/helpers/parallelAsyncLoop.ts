export interface ParallelAsyncLoopOptions {
    retryIntervalMs?: number;
    parallelism: number | (() => Promise<number> | number);
    next: () => Promise<unknown>;
}

export default function parallelAsyncLoop({ retryIntervalMs, parallelism, next }: ParallelAsyncLoopOptions) {
    const parallelismResolver = typeof parallelism === 'number' ? () => parallelism : parallelism;
    let runningPromises = 0;
    let active = true;
    const onComplete = async () => {
        while (active && (await parallelismResolver()) > runningPromises) {
            runningPromises++;
            Promise.resolve()
                .then(() => next())
                .catch(() => null)
                // eslint-disable-next-line no-loop-func
                .finally(() => {
                    runningPromises--;
                    onComplete();
                });
        }
    };

    setImmediate(onComplete);
    const interval = retryIntervalMs ? setInterval(onComplete, retryIntervalMs) : false;
    return () => {
        active = false;
        if (interval) {
            clearInterval(interval);
        }
    };
}
