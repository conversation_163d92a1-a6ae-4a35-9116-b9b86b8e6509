/** @group functional/elastic */
import { sum } from 'lodash';
import { redis } from '../../../../../utils/redis';
import ScoredIdQueue from '../ScoredIdQueue';
import { ElasticQueueMessage, EventType } from '../types';
import delay from '../../../../../utils/delay';
import { metricsClient } from '../../../../../metrics/metricsClient';

const spyMetricsClientGauge = jest.spyOn(metricsClient, 'gauge');

const anyAge = { age: expect.any(Number) };

describe('ScoredIdQueue with Sorted Set and Without Stale Read Prevention', () => {
    const onParseError = jest.fn();
    const testQueue = new ScoredIdQueue<ElasticQueueMessage>('TEST_ID_QUEUE', 'TEST_ID_QUEUE', onParseError, message =>
        message.doubleIndex ? 60000 : message.eventType === EventType.CREATE ? -10000 : 0
    );

    beforeEach(async () => {
        await testQueue.dangerouslyTruncateQueue();
    });

    it('should have size 0 if nothing added', async () => {
        expect(testQueue.size()).resolves.toBe(0);
    });

    it('should be able to read and write from queue without respecting delay', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }]);
        await expect(testQueue.pop(3)).resolves.toEqual(
            expect.arrayContaining([
                { id: 'a', ...anyAge },
                { id: 'b', ...anyAge },
                { id: 'c', ...anyAge },
            ])
        );
    });

    it('should return correct size', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }]);
        await expect(testQueue.size()).resolves.toBe(3);
    });

    it('should return correct size when double write (ignore)', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }], true);
        await expect(testQueue.size()).resolves.toBe(3);
    });

    it('it should skip invalid JSON', async () => {
        await redis!.zadd(testQueue.key, [1, JSON.stringify({ id: 'a' }), 2, JSON.stringify({ id: 'b' }), 3, '{c}']);
        await expect(testQueue.pop(3)).resolves.toEqual([
            { id: 'a', ...anyAge },
            { id: 'b', ...anyAge },
        ]);
    });

    it('it should call onParseError on invalid JSON', async () => {
        await redis!.zadd(testQueue.key, [1, JSON.stringify({ id: 'a' }), 2, JSON.stringify({ id: 'b' }), 3, '{c}']);
        await testQueue.pop(3);
        expect(onParseError).toBeCalledWith(expect.any(Error), '{c}');
    });

    it('should process in sorted order', async () => {
        await redis!.zadd(testQueue.key, [
            9999,
            JSON.stringify({ id: 'a' }),
            2,
            JSON.stringify({ id: 'b' }),
            3,
            JSON.stringify({ id: 'c' }),
        ]);
        await expect(testQueue.pop(3)).resolves.toEqual([
            { id: 'b', ...anyAge },
            { id: 'c', ...anyAge },
            { id: 'a', ...anyAge },
        ]);
    });

    it('should move create events to the front', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b', eventType: EventType.CREATE }, { id: 'c' }]);
        await expect(testQueue.size()).resolves.toBe(3);
        await expect(testQueue.pop(3)).resolves.toEqual([
            { id: 'b', eventType: EventType.CREATE, ...anyAge },
            { id: 'a', ...anyAge },
            { id: 'c', ...anyAge },
        ]);
    });

    it('should not fail on pop larger than queue', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b', eventType: EventType.CREATE }, { id: 'c' }]);
        await expect(testQueue.size()).resolves.toBe(3);
        await expect(testQueue.pop(1000)).resolves.toEqual([
            { id: 'b', eventType: EventType.CREATE, ...anyAge },
            { id: 'a', ...anyAge },
            { id: 'c', ...anyAge },
        ]);
    });

    it('should respect passed size parameter', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b', eventType: EventType.CREATE }, { id: 'c' }]);
        await expect(testQueue.size()).resolves.toBe(3);
        await expect(testQueue.pop(1)).resolves.toEqual([{ id: 'b', eventType: EventType.CREATE, ...anyAge }]);
    });
});

describe('ScoredIdQueue with Sorted Set and Stale Read Prevention', () => {
    const onParseError = jest.fn();
    const testQueue = new ScoredIdQueue<ElasticQueueMessage>('TEST_ID_QUEUE', 'TEST_ID_QUEUE', onParseError, () => 0);

    beforeEach(async () => {
        await testQueue.dangerouslyTruncateQueue();
    });

    it('should have size 0 if nothing added', async () => {
        await expect(testQueue.size()).resolves.toBe(0);
    });

    it('should be able to read and write from queue (single)', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }]);
        await expect(testQueue.pop(3)).resolves.toEqual(
            expect.arrayContaining([
                { id: 'a', ...anyAge },
                { id: 'b', ...anyAge },
                { id: 'c', ...anyAge },
            ])
        );
    });

    it('should be able to read and write from queue (double)', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }], true);
        await expect(testQueue.pop(6)).resolves.toEqual(
            expect.arrayContaining([
                { id: 'a', ...anyAge },
                { id: 'b', ...anyAge },
                { id: 'c', ...anyAge },
                { id: 'a', ...anyAge },
                { id: 'b', ...anyAge },
                { id: 'c', ...anyAge },
            ])
        );
    });

    it('should respect delay of queue', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }]);
        await expect(testQueue.pop(3)).resolves.toEqual(expect.arrayContaining([]));
    });

    it('should not add unexpected delay to fast path queue', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }]);
        await expect(testQueue.pop(3)).resolves.toEqual(
            expect.arrayContaining([
                { id: 'a', ...anyAge },
                { id: 'b', ...anyAge },
                { id: 'c', ...anyAge },
            ])
        );
    });

    it('should return correct size', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }]);
        await expect(testQueue.size()).resolves.toBe(3);
    });

    it('should return correct size when double write', async () => {
        await testQueue.push([{ id: 'a' }, { id: 'b' }, { id: 'c' }], true);
        await expect(testQueue.size()).resolves.toBe(6);
    });

    it('it should skip invalid JSON', async () => {
        await redis!.zadd(testQueue.key, [1, JSON.stringify({ id: 'a' }), 2, JSON.stringify({ id: 'b' }), 3, '{c}']);
        await expect(testQueue.pop(3)).resolves.toEqual([
            { id: 'a', ...anyAge },
            { id: 'b', ...anyAge },
        ]);
    });

    it('it should call onParseError on invalid JSON', async () => {
        await redis!.zadd(testQueue.key, [1, JSON.stringify({ id: 'a' }), 2, JSON.stringify({ id: 'b' }), 3, '{c}']);
        await testQueue.pop(3);
        expect(onParseError).toBeCalledWith(expect.any(Error), '{c}');
    });

    it('should process in sorted order', async () => {
        await redis!.zadd(testQueue.key, [
            9999,
            JSON.stringify({ id: 'a' }),
            2,
            JSON.stringify({ id: 'b' }),
            3,
            JSON.stringify({ id: 'c' }),
        ]);
        await expect(testQueue.pop(3)).resolves.toEqual([
            { id: 'b', ...anyAge },
            { id: 'c', ...anyAge },
            { id: 'a', ...anyAge },
        ]);
    });
});

describe('Message visibility', () => {
    const scoreMessage = jest.fn();
    const testQueue = new ScoredIdQueue<{ id: string; boost?: number }>(
        'TEST_ID_QUEUE',
        'TEST_ID_QUEUE',
        jest.fn(),
        scoreMessage
    );

    beforeEach(async () => {
        await testQueue.dangerouslyTruncateQueue();
        scoreMessage.mockReset();
        scoreMessage.mockImplementation(() => 0);
    });

    it('should work with legacy message format', async () => {
        await redis!.zadd(testQueue.key, [9999, JSON.stringify({ id: 'a' })]);
        await expect(testQueue.pop(3)).resolves.toEqual([{ id: 'a', ...anyAge }]);
        expect(testQueue.size()).resolves.toBe(0);
    });

    it('size should only include visible messages', async () => {
        scoreMessage.mockImplementation(message => (message.id === 'a' ? 1000 : -1000));
        await testQueue.push([{ id: 'a' }, { id: 'b' }]);
        await expect(testQueue.size()).resolves.toBe(1);
        await delay(1500);
        await expect(testQueue.size()).resolves.toBe(2);
    });

    it('should only pop visible messages', async () => {
        scoreMessage.mockImplementation(message => (message.id === 'a' ? 1000 : -1000));
        await testQueue.push([{ id: 'a' }, { id: 'b' }]);
        await expect(testQueue.pop(100)).resolves.toEqual([{ id: 'b', ...anyAge }]);
        await delay(1500);
        await expect(testQueue.pop(100)).resolves.toEqual([{ id: 'a', ...anyAge }]);
    });

    it('should pop messages in visibility order', async () => {
        scoreMessage.mockImplementation(message => (message.id === 'a' ? 1000 : -1000));
        await testQueue.push([{ id: 'a' }, { id: 'b' }]);
        await delay(1500);
        await expect(testQueue.pop(100)).resolves.toEqual([
            { id: 'b', ...anyAge },
            { id: 'a', ...anyAge },
        ]);
    });

    it('should compute age regardless of boosted score', async () => {
        scoreMessage.mockImplementation(m => m.boost ?? 0);
        await testQueue.push([
            { id: 'a', boost: -1 },
            { id: 'b', boost: -1000 },
            { id: 'c', boost: -100000 },
        ]);
        await testQueue.pop(100);
        expect(spyMetricsClientGauge.mock.lastCall[1]).toBeLessThan(4000);
    });

    it('should compute age properly with processing delay', async () => {
        scoreMessage.mockImplementation(m => m.boost ?? 0);
        await testQueue.push([
            { id: 'a', boost: -1 },
            { id: 'b', boost: -1000 },
            { id: 'c', boost: -100000 },
        ]);
        await delay(3000);
        await testQueue.pop(100);
        expect(spyMetricsClientGauge.mock.lastCall[1]).toBeGreaterThanOrEqual(2990);
        expect(spyMetricsClientGauge.mock.lastCall[1]).toBeLessThan(4000);
    });

    it('should compute age properly with visibility delay', async () => {
        scoreMessage.mockImplementation(m => m.boost ?? 0);
        const startedAt = Date.now();
        const expectedVisibilityDelay = 3000;
        await testQueue.push([{ id: 'a', boost: expectedVisibilityDelay }]);
        await delay(expectedVisibilityDelay + 1);
        await testQueue.pop(100);
        const totalDelay = Date.now() - startedAt;
        expect(spyMetricsClientGauge.mock.lastCall[1]).toBeLessThanOrEqual(
            totalDelay - expectedVisibilityDelay + 100 /* buffer */
        );
    });
});

describe('Message analytics', () => {
    const scoreMessage = jest.fn();
    const trackPopAnalytics = jest.fn();
    const testQueue = new ScoredIdQueue<{ id: string; boost?: number }>(
        'TEST_ID_QUEUE',
        'TEST_ID_QUEUE',
        jest.fn(),
        scoreMessage,
        trackPopAnalytics
    );

    beforeEach(async () => {
        await testQueue.dangerouslyTruncateQueue();
        scoreMessage.mockReset();
        scoreMessage.mockImplementation(() => 0);
    });

    it('should call trackPopAnalytics callback', async () => {
        scoreMessage.mockImplementation(m => m.boost ?? 0);
        await testQueue.push([
            { id: 'a', boost: -1 },
            { id: 'b', boost: -1000 },
            { id: 'c', boost: -100000 },
        ]);
        await delay(3000);
        await testQueue.pop(100);
        const [messages, prefix] = trackPopAnalytics.mock.lastCall;
        expect(prefix).toBe('TEST_ID_QUEUE');
        expect(messages).toEqual([
            { id: 'c', boost: -100000, ...anyAge },
            { id: 'b', boost: -1000, ...anyAge },
            { id: 'a', boost: -1, ...anyAge },
        ]);
        expect(sum(messages.map(msg => msg.age))).toBeGreaterThanOrEqual(3000 * 3);
        expect(sum(messages.map(msg => msg.age))).toBeLessThan(3000 * 3 * 1.5);
    });
});
