import config from 'config';
import moment from 'moment';
import { toBoolean } from '../helpers';
import { getConditionForMultipleLists } from '../helpers_v2';

const unstarted_status_types = config.get<string[]>('unstarted_status_types');
const active_status_types = config.get<string[]>('active_status_types');
const done_status_types = config.get<string[]>('done_status_types');
const open_status_types = config.get<string[]>('open_status_types');
const closed_status_types = config.get<string[]>('closed_status_types');

/**
 * filters: date created, date updated, start date, due date, priority, tags
 */
export function getTaskIDQuery(userid: string | number, options: TaskIDQueryOptions) {
    if (options.exclude_multiple_lists === 'false') {
        options.exclude_multiple_lists = false;
    }
    if (options.exclude_sub_multiple_lists === 'false') {
        options.exclude_sub_multiple_lists = false;
    }

    const params = options.params || [];
    if (!options.from_queries) {
        params.push(userid);
    }

    let where_query = ``;

    if (options.filter_conditions) {
        where_query += options.filter_conditions;
    }

    if (options.assignee) {
        options.join_queries = ` LEFT OUTER JOIN task_mgmt.assignees ON items.id = assignees.task_id `;
    }

    const from_queries = `
        task_mgmt.items
        INNER JOIN task_mgmt.subcategories ON subcategories.id = items.subcategory
        INNER JOIN task_mgmt.categories ON categories.id = subcategories.category
        INNER JOIN task_mgmt.projects ON projects.id = categories.project_id
        INNER JOIN task_mgmt.team_members
        ON (team_members.userid = $${params.length} AND team_members.team_id = projects.team)
        INNER JOIN task_mgmt.statuses
           ON statuses.status_group = subcategories.status_group
               AND statuses.status = items.status
        ${options.join_queries || ''}
        LEFT OUTER JOIN task_mgmt.items AS parent ON items.parent = parent.id
        LEFT OUTER JOIN task_mgmt.task_members ON task_members.userid = $${params.length} 
            AND task_members.task_id = coalesce(items.parent, items.id)
        LEFT OUTER JOIN task_mgmt.subcategory_members on subcategories.id = subcategory_members.subcategory AND subcategory_members.userid = $${
            params.length
        }
        LEFT OUTER JOIN task_mgmt.category_members on categories.id = category_members.category AND category_members.userid = $${
            params.length
        }
        LEFT OUTER JOIN task_mgmt.project_members ON project_members.userid = $${
            params.length
        } AND project_members.project_id = projects.id
    `;

    const from_multi_queries = `
        task_mgmt.items
        INNER JOIN task_mgmt.task_subcategories ON task_subcategories.task_id = items.id 
                    AND task_subcategories.subcategory != items.subcategory
        INNER JOIN task_mgmt.subcategories ON subcategories.id = task_subcategories.subcategory
        INNER JOIN task_mgmt.categories ON categories.id = subcategories.category
        INNER JOIN task_mgmt.projects ON projects.id = categories.project_id
        INNER JOIN task_mgmt.subcategories AS permission_subcat
            ON items.subcategory = permission_subcat.id
        INNER JOIN task_mgmt.categories as permission_cat
            ON permission_subcat.category = permission_cat.id
        INNER JOIN task_mgmt.projects as permission_proj
            ON permission_cat.project_id = permission_proj.id
        INNER JOIN task_mgmt.team_members
        ON (team_members.userid = $${params.length} AND team_members.team_id = projects.team)
        INNER JOIN task_mgmt.statuses
        ON statuses.status_group = subcategories.status_group
            AND statuses.status = items.status
        ${options.join_queries || ''}
        LEFT OUTER JOIN task_mgmt.items AS parent ON items.parent = parent.id
        LEFT OUTER JOIN task_mgmt.task_members ON task_members.userid = $${
            params.length
        } AND task_members.task_id = coalesce(items.parent, items.id)
        LEFT OUTER JOIN task_mgmt.subcategory_members on permission_subcat.id = subcategory_members.subcategory AND subcategory_members.userid = $${
            params.length
        }
        LEFT OUTER JOIN task_mgmt.category_members on permission_cat.id = category_members.category AND category_members.userid = $${
            params.length
        }
        LEFT OUTER JOIN task_mgmt.project_members ON project_members.userid = $${
            params.length
        } AND project_members.project_id = permission_proj.id
       
    `;

    if (!toBoolean(options.include_subtasks)) {
        where_query += ` AND items.parent IS NULL`;
    }

    if (options.assignee) {
        if (options.assignee === 'unassigned') {
            where_query += ` AND assignees.userid IS NULL `;
        } else {
            params.push(options.assignee);
            where_query += ` AND assignees.userid = $${params.length}`;
        }
    }

    if (options.statuses && options.statuses.length) {
        params.push(options.statuses);
        where_query += ` AND (items.status = ANY($${params.length}))`;
    } else if (options.all_but_closed) {
        params.push(closed_status_types);
        where_query += ` AND (statuses.type != ALL($${params.length}))`;
    }

    if (options.status_types && options.status_types.length) {
        const status_type: any = [];
        options.status_types.forEach((status: any) => {
            const value = status.type;
            if (value === 'unstarted') {
                status_type.push(...unstarted_status_types);
            }
            if (value === 'active') {
                if (options.unstarted_status_group) {
                    status_type.push(active_status_types);
                } else {
                    status_type.push(active_status_types.concat(open_status_types));
                }
            }
            if (value === 'done') {
                status_type.push('done');
            }
            if (value === 'closed') {
                status_type.push('closed');
            }
            if (value === 'all') {
                status_type.push(...unstarted_status_types, ...active_status_types, ...done_status_types);
            }
        });

        params.push(status_type);
        where_query += ` AND (statuses.type = ANY($${params.length}))`;
    }

    if (options.not_done_statuses) {
        params.push(done_status_types);
        where_query += ` AND (statuses.type != ALL($${params.length}))`;
    }

    if (options.closed_status_types) {
        params.push(closed_status_types);
        where_query += ` AND (statuses.type = ANY($${params.length}))`;
    }

    if (options.priorities) {
        params.push(options.priorities);
        where_query += ` AND items.priority = ANY($${params.length})`;
    }

    if (options.no_priority) {
        where_query += ` AND items.priority IS NULL`;
    }

    if (options.notif_team) {
        where_query += `
            AND task_notifs.archived = false`;
    }

    if (options.tags) {
        params.push(options.tags);
        if (options.all_tags) {
            where_query += ` AND (
                ${options.tags.length} = (
                    SELECT count(*) 
                    FROM task_mgmt.tags 
                    WHERE tags.task_id = items.id
                        AND tags.name = ANY($${params.length})
                )
            )`;
        } else if (options.exclude_tags) {
            where_query += ` AND items.id NOT IN (SELECT task_id FROM task_mgmt.tags WHERE name = ANY ($${params.length}))`;
        } else {
            where_query += ` AND items.id IN (SELECT task_id FROM task_mgmt.tags WHERE name = ANY($${params.length}))`;
        }
    }

    if (options.due_date) {
        params.push(options.due_date);
        where_query += ` AND items.due_date = $${params.length}`;
    }

    if (options.due_date_gt) {
        params.push(options.due_date_gt);
        where_query += ` AND items.due_date >= $${params.length}`;
    }

    if (options.due_date_lt) {
        params.push(options.due_date_lt);
        where_query += ` AND items.due_date <= $${params.length}`;
    }

    if (options.overdue) {
        const now = moment().valueOf();
        const start_of_day = moment()
            .tz(options.timezone || 'UTC')
            .startOf('day')
            .valueOf();

        params.push(start_of_day, now);

        where_query += `
            AND (
                CASE
                    WHEN items.due_date_time THEN items.due_date < $${params.length}
                    ELSE items.due_date < $${params.length - 1}
                END
            )`;
    }

    if (options.no_due_date) {
        where_query += ` AND items.due_date IS NULL`;
    }

    if (options.any_due_date) {
        where_query += ` AND items.due_date IS NOT NULL`;
    }

    if (options.start_date) {
        params.push(options.start_date);
        where_query += ` AND items.start_date = $${params.length}`;
    }

    if (options.start_date_gt) {
        params.push(options.start_date_gt);
        where_query += ` AND items.start_date >= $${params.length}`;
    }

    if (options.start_date_lt) {
        params.push(options.start_date_lt);
        where_query += ` AND items.start_date <= $${params.length}`;
    }

    if (options.no_start_date) {
        where_query += ` AND items.start_date IS NULL`;
    }

    if (options.any_start_date) {
        where_query += ` AND items.start_date IS NOT NULL`;
    }

    if (options.date_created_gt) {
        params.push(options.date_created_gt);
        where_query += ` AND items.date_created >= $${params.length}`;
    }

    if (options.date_created_lt) {
        params.push(options.date_created_lt);
        where_query += ` AND items.date_created <= $${params.length}`;
    }

    if (options.date_updated_gt) {
        params.push(options.date_updated_gt);
        where_query += ` AND items.date_updated >= $${params.length}`;
    }

    if (options.date_updated_lt) {
        params.push(options.date_updated_lt);
        where_query += ` AND items.date_updated <= $${params.length}`;
    }

    if (options.date_closed_gt) {
        params.push(options.date_closed_gt);
        where_query += ` AND items.date_closed >= $${params.length}`;
    }

    if (options.date_closed_lt) {
        params.push(options.date_closed_lt);
        where_query += ` AND items.date_closed <= $${params.length}`;
    }

    if (options.history_gt) {
        params.push(options.history_gt);
        where_query += ` AND task_history.date >= $${params.length}`;
    }

    if (options.history_lt) {
        params.push(options.history_lt);
        where_query += ` AND task_history.date <= $${params.length}`;
    }

    if (options.recurring) {
        if ([true, 'true'].includes(options.recurring)) {
            where_query += ` AND items.recurring IS NOT NULL`;
        }

        if ([false, 'false'].includes(options.recurring)) {
            where_query += ` AND (items.recurring IS NULL
                                OR (items.recurring = false AND items.recur_settings IS NULL))`;
        }
    }

    if (options.creator) {
        params.push(options.creator);
        where_query += ` AND items.creator = ANY($${params.length})`;
    }

    if (![true, 'true'].includes(options.archived) && ![true, 'true'].includes(options.archived_tasks)) {
        where_query += `
            AND (projects.archived = FALSE OR projects.archived IS NULL)
            AND categories.archived = FALSE
            AND (subcategories.archived = FALSE OR subcategories.archived IS NULL)
            AND (items.archived = FALSE OR items.archived IS NULL)
            AND (parent.archived = FALSE OR parent.archived IS NULL)`;
    }

    if ([true, 'true'].includes(options.archived_tasks)) {
        where_query += `
            AND (
                projects.archived = TRUE
                OR categories.archived = TRUE
                OR subcategories.archived = TRUE
                OR items.archived = TRUE
            ) `;
    }

    if (
        (options.project_ids && options.project_ids.length) ||
        (options.category_ids && options.category_ids.length) ||
        (options.subcategory_ids && options.subcategory_ids.length)
    ) {
        let add_or = false;
        where_query += ` AND ( `;

        if (options.project_ids) {
            add_or = true;
            params.push(options.project_ids);
            where_query += ` (projects.id = ANY($${params.length})) `;
        }

        if (options.category_ids) {
            if (add_or) {
                where_query += ` OR `;
            }
            add_or = true;

            params.push(options.category_ids);
            where_query += ` (categories.id = ANY($${params.length})) `;
        }

        if (options.subcategory_ids) {
            if (add_or) {
                where_query += ` OR `;
            }

            params.push(options.subcategory_ids);
            where_query += ` (subcategories.id = ANY($${params.length})) `;
        }

        where_query += ` ) `;
    }

    params.push(options.team || options.team_id);

    if (options.check_time_estimate_enabled) {
        where_query += ' AND projects.time_estimates = TRUE ';
    }

    let query = `
      SELECT ${options.select_list || 'items.id'}
                 ${
                     options.skip_default_selects
                         ? ''
                         : `, parent.id AS parent_id,
                        parent.name AS parent_name,
                        items.status,
                        statuses.color AS status_color, 
                        'false' AS ex`
                 }
      FROM ${options.from_queries || from_queries}
      WHERE items.deleted = false
        AND items.template = false
        AND subcategories.deleted = false
        AND subcategories.template = false
        AND categories.deleted = false
        AND categories.template = false
        AND (items.importing = false)
        AND (categories.importing = false OR categories.importing IS NULL)
        AND (subcategories.importing = false OR subcategories.importing IS NULL)
        AND projects.team = $${params.length} 
            AND projects.deleted = false
            AND projects.template = false
            AND (parent.template = false OR parent.template IS NULL)
            AND ( task_members.userid IS NOT NULL 
                OR ( ((items.PRIVATE = FALSE AND items.parent IS NULL)
                        OR parent.private = false)
                        AND ( subcategory_members.userid IS NOT NULL
                            OR ( subcategories.PRIVATE = FALSE 
                                    AND ( category_members.userid IS NOT NULL
                                        OR ( categories.PRIVATE = FALSE 
                                                AND ( project_members.userid IS NOT NULL
                                                    OR ( team_members.ROLE != 4 
                                                            AND projects.PRIVATE = FALSE ) ) ) ) ) ) ) )
        
          
          
          
          ${where_query}
  `;

    if (options.group_by) {
        query += options.group_by;
    }

    if (!options.exclude_multiple_lists || !options.exclude_sub_multiple_lists) {
        query += `
        UNION
        SELECT ${options.select_list || 'items.id'}
        ${
            options.skip_default_selects
                ? ''
                : `, parent.id AS parent_id,
                    parent.name AS parent_name,
                    items.status,
                        statuses.color AS status_color,
                        'true' AS ex`
        }
        FROM 
        ${options.from_multi_queries || from_multi_queries}
        WHERE items.deleted = false 
        AND items.template = false
            AND subcategories.deleted = false 
            AND subcategories.template = false
            AND categories.deleted = false 
            AND categories.template = false
            AND (items.importing = false)
            AND (categories.importing = false OR categories.importing IS NULL)
            AND (subcategories.importing = false OR subcategories.importing IS NULL)
            AND projects.team = $${params.length} 
            AND projects.deleted = false
            AND projects.template = false
            AND (parent.template = false OR parent.template IS NULL)
            AND ( task_members.userid IS NOT NULL 
                OR ( ((items.PRIVATE = FALSE ${getConditionForMultipleLists(
                    !options.exclude_multiple_lists,
                    !options.exclude_sub_multiple_lists
                )}) OR parent.private = false)
                        AND ( subcategory_members.userid IS NOT NULL
                            OR ( permission_subcat.PRIVATE = FALSE 
                                    AND ( category_members.userid IS NOT NULL
                                        OR ( permission_cat.PRIVATE = FALSE 
                                                AND ( project_members.userid IS NOT NULL
                                                    OR ( team_members.ROLE != 4 
                                                            AND permission_proj.PRIVATE = FALSE ) ) ) ) ) ) ) )
            ${where_query}
    `;

        if (options.group_by) {
            query += options.group_by;
        }
    }
    return { query, params };
}

export interface TaskIDQueryOptions {
    exclude_multiple_lists?: boolean | string;
    exclude_sub_multiple_lists?: boolean | string;
    params?: any[];
    filter_conditions?: string;
    assignee?: string;
    join_queries?: string;
    include_subtasks?: boolean | string;
    statuses?: string[];
    all_but_closed?: boolean;
    status_types?: { type: string }[];
    not_done_statuses?: boolean;
    closed_status_types?: boolean;
    priorities?: string[];
    no_priority?: boolean;
    notif_team?: boolean;
    tags?: string[];
    all_tags?: boolean;
    exclude_tags?: boolean;
    due_date?: string;
    due_date_gt?: string;
    due_date_lt?: string;
    overdue?: boolean;
    timezone?: string;
    no_due_date?: boolean;
    any_due_date?: boolean;
    start_date?: string;
    start_date_gt?: string;
    start_date_lt?: string;
    no_start_date?: boolean;
    any_start_date?: boolean;
    date_created_gt?: string;
    date_created_lt?: string;
    date_updated_gt?: string;
    date_updated_lt?: string;
    date_closed_gt?: string;
    date_closed_lt?: string;
    history_gt?: string;
    history_lt?: string;
    recurring?: boolean | string;
    creator?: string;
    archived?: boolean | string;
    archived_tasks?: boolean | string;
    project_ids?: string[] | string;
    category_ids?: string[] | string;
    subcategory_ids?: string[] | string;
    team?: string;
    team_id?: string;
    check_time_estimate_enabled?: boolean;
    select_list?: string;
    skip_default_selects?: boolean;
    from_queries?: string;
    group_by?: string;
    from_multi_queries?: string;
    unstarted_status_group?: boolean;
}
