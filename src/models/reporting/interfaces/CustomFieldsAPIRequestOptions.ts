import { CustomFieldsMappedField } from './CustomFieldsMappedField';

export interface CustomFieldsAPIRequestOptions {
    // Exists and are used
    team_id: string;
    fields: Array<string> | CustomFieldsMappedField[];
    calculate?: Array<'sum' | string>;
    project_ids?: string;
    category_ids?: string;
    subcategory_ids?: string[];
    archived?: string;
    from_portfolio?: string | boolean;
    include_subtasks?: string;
    include_archived?: boolean;

    // Exists but not used
    skip_access: boolean;
    skipAccess: boolean;
    default_skip_access: boolean;
    dontFail: boolean;

    // Unknown from this list comes
    user_view?: any;
    order?: any;
    assignees?: any;
    statuses?: any;
    all_but_closed?: any;
    priorities?: any;
    no_priority?: any;
    tags?: any;
    all_tags?: any;
    exclude_tags?: any;
    due_date?: any;
    due_date_gt?: any;
    due_date_lt?: any;
    no_due_date?: any;
    any_due_date?: any;
    start_date?: any;
    start_date_lt?: any;
    start_date_gt?: any;
    no_start_date?: any;
    any_start_date?: any;
    date_created_gt?: any;
    date_created_lt?: any;
    date_updated_gt?: any;
    date_updated_lt?: any;
    recurring?: any;
    creator?: any;
    archived_tasks?: any;
}

export const OPTIONS_TO_PICK_FROM_REQUEST = [
    'team_id',
    'fields',
    'user_view',
    'order',
    'calculate',
    'assignees',
    'statuses',
    'all_but_closed',
    'priorities',
    'no_priority',
    'tags',
    'all_tags',
    'exclude_tags',
    'due_date',
    'due_date_gt',
    'due_date_lt',
    'no_due_date',
    'any_due_date',
    'start_date',
    'start_date_lt',
    'start_date_gt',
    'no_start_date',
    'any_start_date',
    'date_created_gt',
    'date_created_lt',
    'date_updated_gt',
    'date_updated_lt',
    'recurring',
    'creator',
    'project_ids',
    'category_ids',
    'subcategory_ids',
    'archived',
    'archived_tasks',
    'from_portfolio',
    'include_subtasks',
];
