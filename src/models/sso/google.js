import config from 'config';
import { OAuth2Client } from 'google-auth-library';
import { getRoleValidationConfigs } from '../team/helpers/roleValidationHelper';
import { entitlementService, EntitlementName } from '../entitlements/entitlementService';
import * as async from '../../utils/asyncHelper';
import * as db from '../../utils/db';
import { GlobalTableMigrationBatchId } from '../../utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import { ClickUpError } from '../../utils/errors';
import { getRequireSsoValue } from './ssoUtil';

const { providers: sso_providers, require_levels } = config.sso;

const SSOError = ClickUpError.makeNamedError('sso', ['sso', 'google']);
const logger = ClickUpError.getBasicLogger('sso');

const googleClient = new OAuth2Client(config.google_sso.client_id);

function getGoogleProfile(google_token, cb) {
    logger.info({
        msg: 'The google client id used for integration',
        client_id: config.google_sso.client_id,
    });
    googleClient
        .verifyIdToken({
            idToken: google_token,
            audience: config.google_sso.client_audiences,
        })
        .then(ticket => cb(null, ticket.getPayload()))
        .catch(err => cb(err));
}
export { getGoogleProfile };

// ---- ------------------- ---- \\
// ---- Common SSO Features ---- \\
// ---- ------------------- ---- \\

function getGoogleIDAndCheckUser(userid, google_token, { team_id }, cb) {
    let payload;

    async.waterfall(
        [
            function getGooglePayload(water_cb) {
                getGoogleProfile(google_token, (err, result) => {
                    if (err) {
                        water_cb(err);
                    } else {
                        payload = result;
                        water_cb();
                    }
                });
            },

            function checkTeam(water_cb) {
                if (!team_id) {
                    water_cb();
                    return;
                }

                const query = `SELECT google_sso FROM task_mgmt.teams_global teams WHERE id = $1`;

                db.globalReadQuery(
                    query,
                    [team_id],
                    { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_50 },
                    (err, result) => {
                        if (err) {
                            water_cb(new SSOError(err, 'SSO_065'));
                            return;
                        }

                        const [team] = result.rows;

                        if (!team) {
                            water_cb(new SSOError('Team Not Found', 'SSO_064', 404));
                            return;
                        }

                        if (team.google_sso !== payload.hd) {
                            let msg;

                            if (!payload.hd) {
                                msg = `You selected a gmail account, `;
                                msg += `but we expected a G-suite account for ${team.google_sso}`;
                            } else {
                                msg = `You selected an account for the G-suite organization ${payload.hd}, `;
                                msg += `but we expected an account for ${team.google_sso}`;
                            }
                            water_cb(new SSOError(msg, 'SSO_063', 400));
                            return;
                        }

                        water_cb();
                    }
                );
            },

            function checkExisting(water_cb) {
                const { sub: google_id, hd: hosted_domain, email: google_email } = payload;
                const query = `
                    SELECT google_ids.userid, teams.id AS team_id
                    FROM task_mgmt.google_ids, task_mgmt.teams_global teams 
                    WHERE google_ids.google_id = $1
                        AND teams.google_sso = google_ids.org_id`;

                db.globalReadQuery(
                    query,
                    [google_id],
                    { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_17 },
                    (err, result) => {
                        if (err) {
                            water_cb(new SSOError(err, 'SSO_012'));
                            return;
                        }

                        if (!userid && result.rows[0]) {
                            const msg = 'Google account associated with existing user';
                            logger.warn({ msg, google_id });
                            water_cb(new SSOError(msg, 'SSO_025', 400));
                            return;
                        }

                        if (userid && result.rows[0] && result.rows[0].userid !== userid) {
                            const msg = 'Google account associated with existing user';
                            logger.warn({ msg, google_id, userid });
                            water_cb(new SSOError(msg, 'SSO_013', 400));
                            return;
                        }

                        const workspace_id = result.rows?.[0]?.team_id;

                        water_cb(null, { google_id, hosted_domain, google_email, workspace_id });
                    }
                );
            },
        ],
        (err, result) => {
            cb(err, result);
        }
    );
}
export { getGoogleIDAndCheckUser };

async function getGoogleUserPolicies(userid, options, cb) {
    try {
        const query = `
            SELECT 
                'google_sso' AS provider,
                teams.google_sso AS org_id,
                google_ids.google_id,
                teams.require_sso,
                teams.id AS team_id, 
                teams.name AS team_name,
                team_members.role,
                team_members.role_subtype,
                team_members.bypass_sso
            FROM task_mgmt.teams_global teams
            INNER JOIN task_mgmt.team_members_global team_members
                ON team_members.team_id = teams.id
            INNER JOIN task_mgmt.users
                ON users.id = team_members.userid
            LEFT JOIN task_mgmt.google_ids
                ON google_ids.org_id = teams.google_sso
                AND google_ids.userid = team_members.userid
            WHERE users.${options.email ? 'email' : 'id'} = $1
                AND teams.google_sso IS NOT NULL
            
            UNION
            
            SELECT
                'google_sso' AS provider,
                google_ids.org_id,
                google_ids.google_id,
                0 AS require_sso,
                NULL AS team_id,
                NULL AS team_name,
                1 AS role,
                0 as role_subtype,
                NULL as bypass_sso
            FROM task_mgmt.google_ids, task_mgmt.users
            WHERE users.${options.email ? 'email' : 'id'} = $1
                AND google_ids.userid = users.id
                AND google_ids.org_id = 'personal'`;

        const result = await db.globalReadQueryAsync(query, [options.email || userid], {
            globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_17,
        });
        const teamIds = result.rows.map(row => row.team_id);

        const entitlements = await entitlementService.getEntitlementForTeams(teamIds, EntitlementName.GoogleSso);

        const entitledTeamIds = entitlements.filter(item => item.entitled).map(item => item.teamId);
        const entitledRows = result.rows.filter(row => entitledTeamIds.includes(row.team_id));

        // Get the roleValidationConfigs to check for guest and limitedMembers to enforce SSO or not
        options.roleValidationConfigs = await getRoleValidationConfigs(entitledRows.map(row => row.team_id));

        cb(null, getMappedGooglePolices(entitledRows, options));
    } catch (error) {
        cb(new SSOError(error, 'SSO_026'));
    }
}
export { getGoogleUserPolicies };

function loginWithGoogle(email, sso_options, cb) {
    const google_tokens = sso_options.filter(({ provider }) => provider === 'google_sso');

    async.map(
        google_tokens,
        (token_obj, map_cb) => {
            const { token } = token_obj;
            let google_id;
            let user;
            let google_email;

            async.series(
                [
                    function fetchGoogleData(series_cb) {
                        getGoogleProfile(token, (err, result) => {
                            if (err) {
                                series_cb(new SSOError(err, 'SSO_029'));
                                return;
                            }

                            ({ sub: google_id, email: google_email } = result);

                            logger.info({ msg: 'Logging in with google id', google_id });

                            series_cb();
                        });
                    },

                    function getUserByGoogleId(series_cb) {
                        const query = `
                            SELECT users.id AS userid, google_ids.org_id
                            FROM task_mgmt.users, task_mgmt.google_ids
                            WHERE users.email = lower($1)
                                AND google_ids.userid = users.id
                                AND lower(google_ids.google_id) = lower($2)`;
                        const params = [email, google_id];

                        db.globalReadQuery(
                            query,
                            params,
                            { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_17 },
                            (err, result) => {
                                if (err) {
                                    series_cb(new SSOError(err, 'SSO_030'));
                                    return;
                                }

                                [user = {}] = result.rows;

                                series_cb();
                            }
                        );
                    },

                    async function checkTeam(series_cb) {
                        if (!user.userid) {
                            series_cb();
                            return;
                        }

                        try {
                            const query = `
                            SELECT 
                                id AS team_id,
                                service_status, 
                                failed_transaction_attempts
                            FROM 
                                task_mgmt.teams_global teams
                            WHERE 
                                google_sso = $1 AND
                                google_sso IS NOT NULL
                            `;

                            const result = await db.globalReadQueryAsync(query, [user.org_id], {
                                globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_50,
                            });

                            if (!result.rows.length) {
                                series_cb(new SSOError('Team not found', 'SSO_073', 404));
                                return;
                            }

                            const [{ team_id, service_status, failed_transaction_attempts }] = result.rows;
                            const isEntitled = await entitlementService.checkEntitlement(
                                team_id,
                                EntitlementName.GoogleSso
                            );

                            if (!isEntitled) {
                                series_cb(new SSOError('Team must be on business plan or higher', 'SSO_074', 400));
                                return;
                            }

                            if (
                                Number(service_status) === config.service_status.suspended &&
                                failed_transaction_attempts < 3
                            ) {
                                series_cb();
                                return;
                            }

                            if (
                                ![config.service_status.active, config.service_status.trial].includes(
                                    Number(service_status)
                                )
                            ) {
                                series_cb(
                                    new SSOError(`Account billing in bad standing`, 'SSO_074', 400, {
                                        log_tags: ['billing'],
                                    })
                                );
                                return;
                            }

                            series_cb();
                        } catch (error) {
                            series_cb(new SSOError(error, 'SSO_072'));
                        }
                    },
                ],
                err => {
                    if (err) {
                        map_cb(new SSOError(err, 'SSO_031'));
                        return;
                    }

                    token_obj.authed = Boolean(user.userid);
                    token_obj.org_id = user.org_id;

                    if (token && !user.userid) {
                        // need to tell user, that this SSO account is not linked to his CU user account (BA_015)
                        token_obj.sso_user = google_email;
                    }

                    map_cb(null, token_obj);
                }
            );
        },
        (err, result) => {
            if (err) {
                cb(new SSOError(err, 'SSO_032'));
            } else {
                cb(null, result);
            }
        }
    );
}
export { loginWithGoogle };

function associateGoogleWithTeam(userid, team_id, options, cb) {
    const other_providers = sso_providers.filter(provider => provider !== 'google_sso');
    let google_id;
    let google_sso;
    let google_email;

    if (other_providers.some(provider => options.team[provider])) {
        const msg = 'Team can only have one SSO provider. Unset existing first';
        cb(new SSOError(msg, 'SSO_014', 400));
        return;
    }

    const token = options.token || options.google_token;

    if (!token) {
        cb(new SSOError('Must pass google token to associated g suite team', 'SSO_015', 400));
        return;
    }

    async.waterfall(
        [
            function getHostedDomain(water_cb) {
                googleClient
                    .verifyIdToken({
                        idToken: token,
                        audience: config.google_sso.client_audiences,
                    })
                    .then(ticket => {
                        const payload = ticket.getPayload();

                        google_sso = payload.hd;
                        google_id = payload.sub;
                        google_email = payload.email;

                        if (!google_sso) {
                            water_cb(new SSOError('Invalid g suite account', 'SSO_016', 400));
                            return;
                        }

                        other_providers.forEach(provider => {
                            delete options[provider];
                        });

                        water_cb();
                    })
                    .catch(err => water_cb(new SSOError(err, 'SSO_017')));
            },

            function checkExisting(water_cb) {
                const query = `
                    SELECT name
                    FROM task_mgmt.teams 
                    WHERE 
                        google_sso = $1 
                        AND google_sso IS NOT NULL
                        AND deleted = FALSE 
                        AND id != $2`;
                const params = [google_sso, team_id];

                db.readQuery(query, params, (err, result) => {
                    if (err) {
                        water_cb(new SSOError(err, 'SSO_019'));
                        return;
                    }

                    if (result.rows.length) {
                        const msg = 'This G Suite organization is already associated with another team.';
                        water_cb(new SSOError(msg, 'SSO_020', 400));
                        return;
                    }

                    water_cb();
                });
            },
        ],
        err => {
            if (err) {
                cb(new SSOError(err, 'SSO_018'));
                return;
            }

            const queries = [
                {
                    query: `
                        INSERT INTO task_mgmt.google_ids(userid, org_id, workspace_id, google_email) (
                            SELECT $1, $2, $3, $4 WHERE NOT EXISTS (
                                SELECT 1 FROM task_mgmt.google_ids WHERE userid = $1 AND org_id = $2
                            )
                        )`,
                    params: [userid, google_sso, team_id, google_email],
                },
                {
                    query: `UPDATE task_mgmt.google_ids SET google_id = $3, google_email=$4 WHERE userid = $1 AND org_id = $2`,
                    params: [userid, google_sso, google_id, google_email],
                },
            ];

            cb(null, { queries, google_sso, google_id });
        }
    );
}
export { associateGoogleWithTeam };

function getMappedGooglePolices(policies, options) {
    return policies.map(policy => {
        policy.require_sso = getRequireSsoValue(policy, options);

        policy.enabled = Boolean(policy.google_id);

        delete policy.google_id;

        if (!options.preserve_role) {
            delete policy.role;
            delete policy.role_subtype;
        }

        return policy;
    });
}
