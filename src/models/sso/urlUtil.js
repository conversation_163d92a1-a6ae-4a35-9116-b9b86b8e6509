import config from 'config';

const HTTPS_PROTOCOL = 'https:';
const CLICKUP_PROTOCOL = 'clickup:';

export function validateRedirect(redirect_url) {
    try {
        const url_object = new URL(redirect_url);
        const { hostname, origin } = url_object;

        // make this condition at least in synch with azure-oauth-callback.html
        return (
            hostname.endsWith('.clickup.com') ||
            hostname === 'clickup.com' ||
            ((hostname.endsWith('.clickup-stg.com') || hostname === 'clickup-stg.com') && config.env === 'dev') ||
            // (hostname.endsWith('.clickup-eu.com') || hostname === 'clickup-eu.com')
            ((hostname.endsWith('.clickup-qa.com') || hostname === 'clickup-qa.com') &&
                (config.env === 'usqa' || config.env === 'local')) ||
            (origin === 'http://localhost:4200' && config.env === 'local')
        );
    } catch (e) {
        return false;
    }
}

export function verifyProtocolHttps(url_to_verify) {
    try {
        const url_object = new URL(url_to_verify);
        const { protocol, origin } = url_object;
        return (
            protocol === HTTPS_PROTOCOL ||
            protocol === CLICKUP_PROTOCOL ||
            // (XXX) allow local UI to be used for local debugging
            (origin === 'http://localhost:4200' && config.env === 'local')
        );
    } catch (e) {
        return false;
    }
}

export function appendLoginHint(login_url, email, idp_user_id, idp_email) {
    // when idp_user_id is a sequence of numbers, it is most-likely an "internal-id" (or hash)
    // and is not human-friendly user-id (which supposed to be provided by the user)
    // in that case we suppose, that CU-email could be used as a login_hint
    const login_hint =
        !idp_email && !Number.isNaN(Number(idp_user_id)) ? email || '' : idp_email || idp_user_id || email || '';

    if (login_url && login_hint) {
        const login_hint_encoded = encodeURIComponent(login_hint);
        if (login_url.indexOf('?') < 0) {
            login_url += '?';
        } else {
            login_url += '&';
        }
        login_url += `login_hint=${login_hint_encoded}`;
    }

    return login_url;
}

export function validateRedirectUrlOrRelayState(urlToValidate) {
    if (urlToValidate) {
        if (validateRedirect(urlToValidate)) {
            if (!verifyProtocolHttps(urlToValidate)) {
                return { value: null, error: 'Expected https protocol', valid: false };
            }
            return { value: urlToValidate, valid: true, error: null }; // Valid HTTPS URL
        }
        // If validateRedirect returns false, it means the URL is not in the allowed list of redirects.
        return { value: 'none', valid: true, error: null };
    }
    return { value: urlToValidate, valid: true, error: null }; // No URL provided, use the provided value
}
