import { NextFunction, Response } from 'express';
import config from 'config';
import * as access from '../../utils/access2';
import * as db from '../../utils/db';
import { CommentsConfig } from '../../config/interfaces/CommentsConfig';
import { ClickUpRequestV2 } from '../../interfaces/ClickUpRequest';

type CommentId = string;
type GetThreadUnviewedCountResult = Record<CommentId, GetThreadUnviewedCountRow>;

interface GetThreadUnviewedCountRow {
    id: string;
    unread_count: number;
    mentions: number;
}

interface GetCommentCountResult {
    mentions?: number;
    count?: number;
    thread_count: number;
}

interface GetCommentUnviewedCountResult {
    mentions: number;
    count?: number;
    unread_count?: number;
}

const commentsConfig: CommentsConfig = config.get('comments');
const { types: comment_types } = commentsConfig;

const { task: TASK_TYPE, comment: COMMENT_TYPE } = comment_types;

export async function getThreadUnviewedCount(
    userid: string,
    commentIds: string[]
): Promise<GetThreadUnviewedCountResult> {
    const query = `
        SELECT 
            comments.id,
            count(DISTINCT threads.id) AS unread_count,
            count(comment_tags.userid) as mentions
        FROM 
            task_mgmt.comments
            JOIN task_mgmt.comments as threads
                ON comments.id::text = threads.parent
                AND threads.type = ${COMMENT_TYPE}
            LEFT JOIN task_mgmt.viewed_comments 
                ON threads.id = viewed_comments.comment_id
                AND viewed_comments.userid = $1
            LEFT JOIN task_mgmt.comment_tags
                ON comment_tags.comment_id = threads.id
                AND comment_tags.userid = $1
        WHERE
            comments.id = ANY($2)
            AND comments.deleted = false
            AND viewed_comments.date_viewed IS NULL
            AND threads.userid != $1
            AND threads.deleted = false
        GROUP BY comments.id
    `;
    const params = [userid, commentIds];

    const result = await db.replicaQueryAsync(query, params);

    const ret_val: GetThreadUnviewedCountResult = result.rows.reduce(
        (map: GetThreadUnviewedCountResult, obj: GetThreadUnviewedCountRow) => {
            map[obj.id] = {
                id: obj.id,
                unread_count: Number(obj.unread_count),
                mentions: Number(obj.mentions),
            };
            return map;
        },
        {}
    );

    commentIds.forEach(cid => {
        if (!ret_val[cid]) {
            ret_val[cid] = {
                id: cid,
                unread_count: 0,
                mentions: 0,
            };
        }
    });

    return ret_val;
}

export async function getCommentUnviewedCount(
    userid: number,
    options: { parent: string; type: number }
): Promise<GetCommentUnviewedCountResult> {
    const { parent, type } = options;

    const query = `
        SELECT 
            count(DISTINCT threads.parent) AS unread_count,
            count(comment_tags.userid) as mentions
        FROM 
            task_mgmt.comments
            JOIN task_mgmt.thread_followers
                ON comments.id = thread_followers.comment_id
                AND thread_followers.userid = $1
            JOIN task_mgmt.comments as threads
                ON comments.id::text = threads.parent
                AND threads.type = ${COMMENT_TYPE}
            LEFT JOIN task_mgmt.viewed_comments 
                ON threads.id = viewed_comments.comment_id
                AND viewed_comments.userid = $1
            LEFT JOIN task_mgmt.comment_tags
                ON comment_tags.comment_id = threads.id
                AND comment_tags.userid = $1
        WHERE
            comments.parent = $2
            AND comments.type = $3
            AND comments.deleted = false
            AND threads.deleted = false
            AND viewed_comments.date_viewed IS NULL
            AND threads.userid != $1
    `;
    const params = [userid, parent, type];

    const result = await db.replicaQueryAsync(query, params);

    return result.rows.length === 0
        ? { count: 0, mentions: 0 }
        : {
              unread_count: Number(result.rows[0].unread_count),
              mentions: Number(result.rows[0].mentions),
          };
}

export async function getCommentCount(
    userid: number,
    options: { parent: string; type: number }
): Promise<GetCommentCountResult> {
    const { parent, type } = options;

    const query = `
        SELECT 
            count(DISTINCT threads.parent) AS thread_count
        FROM 
            task_mgmt.comments
            JOIN task_mgmt.comments as threads
                ON comments.id::text = threads.parent
                AND threads.type = ${COMMENT_TYPE}
            LEFT JOIN task_mgmt.viewed_comments 
                ON threads.id = viewed_comments.comment_id
                AND viewed_comments.userid = $1
            LEFT JOIN task_mgmt.comment_tags
                ON comment_tags.comment_id = threads.id
                AND comment_tags.userid = $1
        WHERE
            comments.parent = $2
            AND comments.type = $3
            AND comments.deleted = false
    `;
    const params = [userid, parent, type];

    const result = await db.replicaQueryAsync(query, params);

    return result.rows.length === 0
        ? { count: 0, mentions: 0, thread_count: 0 }
        : { thread_count: Number(result.rows[0].thread_count) };
}

async function getThreadCounts(userid: number, parent: string) {
    const [, getCommentCountRes, getCommentUnviewedCountRes] = await Promise.all([
        access.checkAccessTasksAsync(userid, [parent], { permissions: [] }),
        getCommentCount(userid, { parent, type: TASK_TYPE }),
        getCommentUnviewedCount(userid, { parent, type: TASK_TYPE }),
    ]);

    return {
        new_thread_count: getCommentUnviewedCountRes.unread_count,
        new_mentioned_thread_count: getCommentUnviewedCountRes.mentions,
        thread_count: getCommentCountRes.thread_count,
    };
}

export async function getThreadCountsReq(req: ClickUpRequestV2, resp: Response, next: NextFunction): Promise<void> {
    const { user: userid } = req.decoded_token;
    const parent = req.query.parent as string;

    try {
        const result = await getThreadCounts(userid, parent);
        resp.status(200).send(result);
    } catch (err) {
        next(err);
    }
}
