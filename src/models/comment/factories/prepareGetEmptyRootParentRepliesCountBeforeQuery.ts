import { CURRENT_SHARD_ID } from '@clickup-legacy/config/domains/shardingConfig';
import { QueryObject } from '@clickup-legacy/utils/interfaces/QueryObject';

export function prepareGetEmptyRootParentRepliesCountBeforeQuery(shardId = CURRENT_SHARD_ID): QueryObject {
    const query = `
        SELECT COUNT(*)
        FROM task_mgmt."comments" c
        JOIN task_mgmt."comments" c1 ON c.parent::bigint=c1.id
        WHERE c."type"=2
        AND c.root_parent_id IS NULL
        AND c.root_parent_type IS NULL
        AND c.parent IS NOT NULL
        AND c."type" IS NOT NULL
        AND c1.parent IS NOT NULL
        AND c1."type" IS NOT NULL
        AND NOT EXISTS (
            SELECT FROM task_mgmt.workspace_to_shard AS wts
            WHERE wts.workspace_id = c.workspace_id
                AND (wts.shard_id <> $1 OR wts.migrating IS TRUE)
        )`;
    const params = [shardId];
    return { query, params };
}
