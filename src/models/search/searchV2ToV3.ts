import { omit } from 'lodash';
import config from 'config';
import SearcherEsAssetsUnified from '../search-v3/searchers/SearcherEsAssetsUnified';
import * as access from '../../utils/access2';
/* eslint-disable import/no-relative-packages */
import { AssetType } from '../search-v3/search-io/src/request/filters';
import { ClickUpError } from '../../utils/errors';

export interface Paging {
    from?: number;
    last_page: boolean;
}

export interface SearchV2Response {
    data: any;
    paging: Paging;
}

const DOC_TYPE = config.get<number>('views.view_types.doc');
const COUNT = 20;

/** This function is a bridge between search V2 and search V3. It uses the search V3 ES cluster
 * to provide responses in the search V2 schema to enable the deprecation of V2 ES clusters.
 */
export function searchV2toV3(
    userid: string | number,
    team: string | number,
    options: { query?: string; from?: string },
    cb: (err: ClickUpError | { err: string; status: number; ECODE: string }, result?: SearchV2Response) => void,
    assetType: AssetType
): void;
export function searchV2toV3(
    userid: string | number,
    team: string | number,
    options: { query?: string; from?: string },
    cb: (err: ClickUpError | { err: string; status: number; ECODE: string }, result?: { all: SearchV2Response }) => void
): void;
export function searchV2toV3(
    userid: string | number,
    team: string | number,
    options: { query?: string; from?: string },
    cb:
        | ((err: ClickUpError | { err: string; status: number; ECODE: string }, result?: SearchV2Response) => void)
        | ((
              err: ClickUpError | { err: string; status: number; ECODE: string },
              result?: { all: SearchV2Response }
          ) => void),
    assetType?: AssetType
) {
    access.checkAccessTeam(userid, team, { permissions: [] }, async (access_err: ClickUpError) => {
        if (access_err) {
            cb(access_err);
            return;
        }

        const searchV3Request: any = {
            keywords: options.query || '',
            filters: assetType ? { asset_types: [assetType] } : undefined,
            COUNT,
        };

        try {
            const offset = options?.from ? Number(options.from) : 0;
            const res = await new SearcherEsAssetsUnified(`${team}`, `${userid}`, searchV3Request).v2Results();
            const results = res?.results.map(r => {
                if (r.type === 'doc') {
                    return {
                        view: { ...r.doc, page_name: r.doc.page.name, type: DOC_TYPE },
                        page_name: r.doc.page.name,
                        page_id: r.doc.page.id,
                    };
                }
                return r;
            });
            if (assetType) {
                cb(null, {
                    data: results ? results.map(r => omit(r, ['type', 'obj_id'])) : [],
                    paging: res.paging,
                    // TODO: figure out how to fix it. The overloads have
                    // correct TS definition, but it will fail without.
                    all: undefined as any,
                });
            } else {
                cb(null, {
                    all: {
                        data: results ? results.map(r => omit(r, ['type', 'obj_id'])) : [],
                        paging: res.paging,
                    },
                    // TODO: figure out how to fix it. The overloads have
                    // correct TS definition, but it will fail without.
                    data: undefined as any,
                    paging: undefined as any,
                });
            }
        } catch (err) {
            cb({
                err: 'Internal server error',
                status: 500,
                ECODE: 'SRCH2_025',
            });
        }
    });
}
