import { ClickUpFieldsDependencyTracker } from '@time-loop/hot-formula-parser';
import { getFormulaCustomFields } from '../datastores/customFieldsDatastore';

export interface DependencyTrackerVariable {
    name: string;
    value: string;
    isFormula: boolean;
}

export async function getFormulasDependentOnFields(workspaceId: string | number, fieldIds: string[]) {
    if (fieldIds.length === 0) {
        return [];
    }
    const variables = await getAllFormulaVariables(workspaceId);
    const dependencyTracker = new ClickUpFieldsDependencyTracker(getMapValues(variables));
    const resultSet = new Set<string>();
    for (const fieldId of fieldIds) {
        const formulaVariable = formulaVariableNameFromFieldId(fieldId);
        const dependentFields = dependencyTracker.getDependentFields(formulaVariable);
        dependentFields.forEach(dependentField => resultSet.add(fieldIdFromFormulaVariableName(dependentField)));
    }
    return Array.from(resultSet);
}

export async function getAllFormulaVariables(workspaceId: string | number) {
    const formulaFields = await getFormulaCustomFields(workspaceId);
    const variablesMap: Map<string, DependencyTrackerVariable> = new Map();
    for (const { id: fieldId, formula } of formulaFields) {
        variablesMap.set(fieldId, createFormulaVariable(fieldId, formula));
    }
    return variablesMap;
}

export function formulaVariableNameFromFieldId(fieldId: string): string {
    return `CUSTOM_FIELD_${fieldId.replace(/-/g, '_')}`;
}

export function fieldIdFromFormulaVariableName(variableName: string): string {
    return variableName.replace('CUSTOM_FIELD_', '').replace(/_/g, '-');
}

export function createFormulaVariable(fieldId: string, formula: string): DependencyTrackerVariable {
    return {
        name: formulaVariableNameFromFieldId(fieldId),
        value: formula,
        isFormula: true,
    };
}

export function getMapValues<K, V>(map: Map<K, V>): V[] {
    return Array.from(map.values());
}

type FormulaDependenciesViolationBase = {
    isValid: false;
};

type FormulaDependenciesViolationNesting = FormulaDependenciesViolationBase & {
    violation: 'nesting';
    maxNesting: number;
    nesting: number;
    variableName: string;
    fieldId: string;
};

type FormulaDependenciesViolationCircular = FormulaDependenciesViolationBase & {
    violation: 'circularDependency';
};

export type FormulaDependenciesValidationResult =
    | { isValid: true }
    | FormulaDependenciesViolationNesting
    | FormulaDependenciesViolationCircular;

function isFormulaValid(result: FormulaDependenciesValidationResult): result is { isValid: true } {
    return result.isValid === true;
}

function isNestingViolation(
    result: FormulaDependenciesValidationResult
): result is FormulaDependenciesViolationNesting {
    return result.isValid === false && result.violation === 'nesting';
}

function isCircularDependencyViolation(
    result: FormulaDependenciesValidationResult
): result is FormulaDependenciesViolationCircular {
    return result.isValid === false && result.violation === 'circularDependency';
}

export function matchFormulaViolation<N, C, V>(
    result: FormulaDependenciesValidationResult,
    handlers: {
        nesting: (v: FormulaDependenciesViolationNesting) => N;
        circularDependency: (v: FormulaDependenciesViolationCircular) => C;
        valid: () => V;
    }
): N | C | V {
    if (isFormulaValid(result)) return handlers.valid();
    if (isCircularDependencyViolation(result)) return handlers.circularDependency(result);
    if (isNestingViolation(result)) return handlers.nesting(result);
    throw new Error('Unhandled formula validation result'); // type-safe fallback
}

export async function areValidDependencies(
    fieldId: string | null | undefined,
    formula: string,
    maxNesting: number,
    getFormulaVariables: () => Promise<Map<string, DependencyTrackerVariable>>
): Promise<FormulaDependenciesValidationResult> {
    const formulasMap = await getFormulaVariables();
    const existingFormulasValidationResult = new ClickUpFieldsDependencyTracker(getMapValues(formulasMap)).validate();

    // if fieldId is not provided, we assume it's a new field without ID
    // and this formula is not used in any other formula
    const updatedMap = updateMapWithFieldId(formulasMap, fieldId ?? 'NEW_FIELD_ID', formula);
    const updatedFormulasValidationResult = new ClickUpFieldsDependencyTracker(getMapValues(updatedMap)).validate();

    if (updatedFormulasValidationResult.hasCycle) {
        return {
            isValid: false,
            violation: 'circularDependency',
        };
    }

    for (const [node, nesting] of Object.entries(updatedFormulasValidationResult.nestingByNode)) {
        if (nesting > maxNesting) {
            // if the formula was exceeding nesting level before (because the allowance was higher), allow it
            // to continue doing so, unless the nesting level exceeds the previous nesting level
            const prevNesting = existingFormulasValidationResult.nestingByNode[node] ?? 0;
            if (nesting > prevNesting) {
                return {
                    isValid: false,
                    violation: 'nesting',
                    maxNesting,
                    nesting,
                    variableName: node,
                    fieldId: fieldIdFromFormulaVariableName(node),
                };
            }
        }
    }
    return { isValid: true };
}

export function updateMapWithFieldId(map: Map<string, DependencyTrackerVariable>, fieldId: string, formula: string) {
    const updatedMap = new Map(map);
    return updatedMap.set(fieldId, createFormulaVariable(fieldId, formula));
}
