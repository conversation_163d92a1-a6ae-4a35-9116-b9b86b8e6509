export type StoreWorkspaceExportOptions = Pick<StoreExportOptions, 'signed' | 'file' | 'filename'>;

export type StoreExportType = 'full_export' | 'view_export' | 'markdown' | 'html' | 'pdf';
export type StoreExportExtension = 'md' | 'html' | 'pdf' | 'xlsx' | 'csv' | 'excel';

export interface StoreExportOptions {
    dont_unlink?: boolean;
    extension?: StoreExportExtension;
    expires_ms?: number;
    exportFilename?: string;
    file?: string; // e.g. "/Users/<USER>/projects/cu3/clickup/uploads/9999000001MdItuFfK"
    filename?: string; // e.g. "9999000001MdItuFfK"
    local_file_extension?: boolean;
    location?: string;
    page_id?: any;
    reference_id?: any;
    dont_save?: boolean; // unfortunately hard to remove due to View's functional tests
    save_locally?: boolean;
    signed?: boolean;
    skip_ws?: boolean;
    timezone?: string;
    type?: StoreExportType;
    view_id?: any;
}

export interface StoreExportOptionsWithExtensionTypo extends StoreExportOptions {
    extention?: StoreExportExtension;
}
