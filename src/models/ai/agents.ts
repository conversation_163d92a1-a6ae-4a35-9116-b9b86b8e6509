import { AsyncStorage } from '../../../libs/shared/utils-async-storage/src';
import { getUsersInfo } from '../user/datastores/CRUD/getUsers';
import { UserType } from '../user/userTypes';

/**
 * Check if an "Agent" user, which are created counting down with negative numbers.
 *
 * eg: -300001, -300002, ...
 */
export async function isAgentUser(userId?: number) {
    userId = Number(userId);
    if (!userId || userId >= -1 || Number.isNaN(userId)) {
        return false;
    }

    const { rows } = await getUsersInfo([userId], ['id', 'user_type']);
    return rows.length && rows[0].user_type === UserType.AI_AGENT;
}

export async function setIsAgentUserInContext(userid?: number) {
    const value = await isAgentUser(userid);

    const context = AsyncStorage.getInstance().getContext();

    if (!context) {
        return;
    }

    context.isAgentUser = value;
}

/**
 * Checks async context if it was initialized as an agent.
 */
export function isAgentUserRequest(): boolean {
    const asyncContext = AsyncStorage.getInstance().getContext();
    return !!asyncContext?.isAgentUser;
}
