import { replicaQueryAsync, writeQueryAsync } from '../../../utils/db';
import { DbQueryResult } from '../../../utils/interfaces/DbQueryResult';
import { CustomRole, CustomRoleWithMembers } from '../interfaces/customRole';

interface CreateCustomRoleArgs {
    id: number;
    team_id: number;
    name: string;
    inherited_role: number;
    date_created: number;
    global_id: string;
    is_read_only: boolean;
    max_permission_to_share_with: number;
    role_description: string;
}

export function getCustomRolesForTeam(
    team_id: number,
    include_members = false
): Promise<DbQueryResult<CustomRole | CustomRoleWithMembers>> {
    let include_members_select = '';
    let include_members_join = '';
    if (include_members) {
        include_members_select = ', ARRAY_REMOVE(ARRAY_AGG(team_members.userid), NULL) AS members';
        include_members_join = `
            LEFT JOIN task_mgmt.team_members ON 
                team_members.team_id = custom_roles.team_id AND 
                team_members.deleted = FALSE AND 
                team_members.custom_role = custom_roles.id
        `;
    }

    const query = `
        SELECT 
        custom_roles.*
            ${include_members_select} 
        FROM 
            task_mgmt.custom_roles 
            ${include_members_join}
        WHERE 
            custom_roles.team_id = $1
        GROUP BY 
            custom_roles.id, 
            custom_roles.team_id, 
            custom_roles.name, 
            custom_roles.inherited_role, 
            custom_roles.date_created, 
            custom_roles.global_id
        ORDER BY custom_roles.date_created ASC
    `;

    return replicaQueryAsync(query, [team_id]);
}

export function insertCustomRole(createCustomRoleArgs: CreateCustomRoleArgs) {
    const params = [
        createCustomRoleArgs.id,
        createCustomRoleArgs.team_id,
        createCustomRoleArgs.name,
        createCustomRoleArgs.inherited_role,
        createCustomRoleArgs.date_created,
        createCustomRoleArgs.global_id,
        createCustomRoleArgs.is_read_only,
        createCustomRoleArgs.max_permission_to_share_with,
        createCustomRoleArgs.role_description,
    ];

    const query = `
        INSERT INTO task_mgmt.custom_roles(
            id, 
            team_id, 
            name, 
            inherited_role, 
            date_created, 
            global_id, 
            is_read_only, 
            max_permission_to_share_with, 
            role_description
        )
        VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `;

    return writeQueryAsync(query, params);
}

export function getCustomRoleForWorkspaceById(
    customRoleIds: number[],
    workspaceId: number
): Promise<DbQueryResult<CustomRole>> {
    const query = `
        SELECT 
            custom_roles.*
        FROM 
            task_mgmt.custom_roles 
        WHERE 
            custom_roles.id = ANY($1) AND
            custom_roles.team_id = $2
    `;

    return replicaQueryAsync(query, [customRoleIds, workspaceId]);
}
