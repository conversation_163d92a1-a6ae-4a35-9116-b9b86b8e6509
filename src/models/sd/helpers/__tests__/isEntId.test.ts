import { keys, toPairs } from 'lodash';
import isEntId, { entId2Uuid, uuid2EntId } from '../isEntId';

describe('UUIDs', () => {
    const entId2Uuids = {
        '1012235782287479154': '00000000-0000-4f72-bf51-2fb8142f0c0e',
        '0': '00000000-0000-4f00-bf00-000000000000',
        '1012260903529140476': '00000000-0000-4ffc-bfd0-deb6ed450c0e',
        '1012225632845460147': '00000000-0000-4fb3-bf72-4c9ed9250c0e',
        '5797235916373632561': '00000000-0000-4f31-bf26-96d259eb7350',
        '9223372036854775807': '00000000-0000-4fff-bfff-ffffffffff7f',
        '-9223372036854775808': '00000000-0000-4f00-bf00-000000000080',
    };

    test.each(keys(entId2Uuids))('ent id %s sanity', entId => {
        expect(BigInt(entId).toString()).toBe(entId);
    });

    test.each(toPairs(entId2Uuids))('ent id %s -> to uuid %s', (entId, uuid) => {
        expect(entId2Uuid(entId)).toBe(uuid);
    });

    test.each(toPairs(entId2Uuids))('ent id %s <- uuid %s', (entId, uuid) => {
        expect(uuid2EntId(uuid)).toBe(entId);
    });

    it('should fail on invalid id', () => {
        expect(() => entId2Uuid('asdasdas')).toThrowErrorMatchingInlineSnapshot(
            `"Cannot convert asdasdas to a BigInt"`
        );
    });

    it('should fail on invalid uuid', () => {
        expect(() => uuid2EntId('00000000-0000-4f00-bf00-0000axx0000080')).toThrowErrorMatchingInlineSnapshot(
            `"Invalid UUID"`
        );
    });
    it('should fail on non-ent uuid', () => {
        expect(() => uuid2EntId('110ec58a-a0f2-4ac4-8393-c866d813b8d1')).toThrowErrorMatchingInlineSnapshot(
            `"UUID was not created from Ent Framework id: 110ec58a-a0f2-4ac4-8393-c866d813b8d1"`
        );
    });
});

describe('isEntId', () => {
    it.each([
        '1012260903529140476',
        '1012225632845460147',
        '9223372036854775807',
        '5797235916373632561',
        '9223372036454775807',
    ])(`%s is valid Ent Framework id`, id => {
        expect(isEntId(id)).toBe(true);
    });

    it.each([
        '101226029140476',
        '9323372036854775807',
        '101222563284546014733',
        'a5797235916373632561',
        '00000000-0000-4f00-bf00-0000axx0000080',
    ])(`%s is not valid Ent Framework id`, id => {
        expect(isEntId(id)).toBe(false);
    });
});
