import memoize from 'lodash/memoize';
import { RestError, RestRateLimitError, RestTimeoutError } from '@clickup/rest-client';
import { EntitlementName, GetEntitlementsResult } from '@clickup/entitlements';
import config from 'config';
import QuickLRU from 'quick-lru';
import { number, object, string } from 'superstruct';
import { inspect } from 'util';
import first from 'lodash/first';
import { AccessError } from '../../../utils/access/services/authorization/accessError';
import { RoleConfig } from '../../../config/interfaces/RoleConfig';
import { isLocal, isQA } from '../../../utils/environment';
import { join } from '../../../utils/promise';
import sdApiRestService from './sdApiRestService';
import SdUserAccessTokenServiceError from '../errors/SdUserAccessTokenServiceError';
import { getLocalTunnelIfLocalEnv } from '../../integrations/localtunnel';
import { getUsersInfo } from '../../user/datastores/CRUD/getUsers';
import { elasticParams } from '../../integrations/split/squadTreatments/searchTreatments';
import { sdFromNestNoMemoize } from '../../integrations/split/squadTreatments/integrationsTreatments';
import getTeamNameAndUserRoleCheckingAccess from '../helpers/getTeamNameAndUserRoleCheckingAccess';
import { ClickUpTracer } from '../../../utils/tracer';
import { entitlementService } from '../../entitlements/entitlementService';

const tracer = new ClickUpTracer();

// eslint-disable-next-line global-require, @typescript-eslint/no-var-requires
const requireUserMemoized = memoize(() => require('../../user/userProvider'));
const requireGetTeamNameAndUserRoleCheckingAccessMemoized = memoize(
    // eslint-disable-next-line global-require, @typescript-eslint/no-var-requires
    () => require('../helpers/getTeamNameAndUserRoleCheckingAccess').default
);

// Access Token cache expiration time in the local in-memory cache.
const CACHE_EXPIRES_MS = 60 * 1000;

/**
 * This is a slice of Express Request object which represents the user who's
 * making the call to the API. Thus, the Express Request object may be passed to
 * all API binding functions as is.
 */
export interface UserReqExpress {
    locale?: string;
    decoded_token?: {
        user: number;
    };
    params: {
        team_id?: string | number;
        workspaceId?: string | number;
    };
}

/**
 * You can also pass userId/workspaceId manually (in a relaxed format, string or
 * number for both, since e.g. workspaceId is sometimes stored in the DB as
 * bigint or is received in request params).
 */
export interface UserReqManual {
    locale?: string;
    userId: number | string;
    workspaceId: number | string;
    enforceAsyncContext?: boolean;
}

/**
 * Union of the two types above.
 */
export type UserReq = UserReqExpress | UserReqManual;

/**
 * Result of validating & loading UserReq from the DB.
 */
export type UserReqResolved = {
    user: {
        id: string;
        email: string;
        username: string | null;
        occupation: string | null;
        timezone: string | null;
    };
    workspace: {
        id: string;
        name: string;
        ai_enabled: boolean;
        ai_hidden: boolean;
        chat_enabled?: boolean;
        plan_id: number;
        custom_origin?: string | null;
        entitlements?: GetEntitlementsResult;
        using_private_cf?: boolean;
        hipaa_compliant: boolean;
    };
    role: number;
};

/**
 * Return type of the call.
 */
const ApiResponse = object({
    access_token: string(),
    expires_in: number(),
});

/**
 * Fetches a short-lived authorization token that can be used to send requests
 * to Sd API on behalf of the workspaceId/userId pair provided from the
 * client-side.
 *
 * Uses in-process LRU cache to minimize the number of DB queries and the number
 * of requests sent.
 */
export default async function sdUserAccessTokenService(reqIn: UserReq): Promise<typeof ApiResponse.TYPE> {
    let req: UserReqManual;
    if (
        'decoded_token' in reqIn &&
        reqIn.decoded_token?.user &&
        'params' in reqIn &&
        (reqIn.params?.team_id || reqIn.params?.workspaceId)
    ) {
        req = {
            locale: reqIn.locale,
            userId: Number(reqIn.decoded_token.user),
            workspaceId: Number(reqIn.params.team_id ?? reqIn.params.workspaceId),
        };
    } else if ('userId' in reqIn && 'workspaceId' in reqIn) {
        req = {
            locale: reqIn.locale,
            userId: Number(reqIn.userId),
            workspaceId: Number(reqIn.workspaceId),
            enforceAsyncContext: reqIn.enforceAsyncContext ?? undefined,
        };
    } else {
        throw new SdUserAccessTokenServiceError(`Failed to get auth token from the request`, 'SD_003');
    }

    try {
        const hash = `${req.userId}:${req.workspaceId}`;
        let slot = cache.get(hash);
        const slotExpired = !slot || Date.now() > slot.fetchedAt + CACHE_EXPIRES_MS;
        if (slotExpired || isLocal || isQA) {
            // On dev, we ALWAYS send a request (this way, we always upsert the
            // user, even after Sd DB got wiped), but we don't panic on errors
            // if we have a fresh enough cache available.
            const res = slotExpired
                ? await sdUserAccessTokenServiceImpl(req)
                : await sdUserAccessTokenServiceImpl(req, 1000).catch(() => null);
            if (res) {
                slot = { res, fetchedAt: Date.now() };
            }
            cache.set(hash, slot);
        }
        return {
            ...slot.res,
            expires_in: slot.res.expires_in - Math.ceil((Date.now() - slot.fetchedAt) / 1000),
        };
    } catch (error: unknown) {
        // We don't need to log anything separately here, because errors
        // constructors already do such a logging (see ClickUpError.logError()).
        // These logs show up at Logs tab in the trace view.
        const status =
            error instanceof RestRateLimitError
                ? 429
                : error instanceof RestTimeoutError
                ? 504
                : error instanceof AccessError
                ? 403
                : undefined;

        throw error instanceof RestError || error instanceof AccessError
            ? new SdUserAccessTokenServiceError(
                  'Failed to fetch user access token from SD API',
                  status ? `SD_${status}` : 'SD_001',
                  status,
                  {
                      cause: inspect(error),
                      ...reqIn,
                  }
              )
            : error;
    }
}

/**
 * Called from Nest world to override the DB logic.
 */
export function overrideUserReqResolver(resolver: typeof userReqResolver): void {
    userReqResolver = resolver;
}

/**
 * Sends the actual request to Sd (exceptions-unchecked).
 */
async function sdUserAccessTokenServiceImpl(req: UserReqManual, timeoutMs?: number): Promise<typeof ApiResponse.TYPE> {
    const info = await tracer.trace('sd.user_req_resolver', {}, async () => userReqResolver(req));
    const apiRequest = {
        role: ['owner', 'admin'].includes(getRoleNameByNumber(info.role)) ? 'ADMIN' : 'MEMBER',
        passport: {
            external_id: info.user.id,
            email: info.user.email,
            name: info.user.username || info.user.email,
        },
        workspace: {
            external_id: info.workspace.id,
            name: info.workspace.name,
            ai_enabled: info.workspace.ai_enabled,
            chat_enabled: info.workspace.chat_enabled,
            plan_id: info.workspace.plan_id,
            server_url: isLocal ? (await getLocalTunnelIfLocalEnv('main')) ?? config.app.url : undefined,
            custom_origin: info.workspace.custom_origin,
            entitlements: info.workspace.entitlements,
            using_private_cf: info.workspace.using_private_cf,
            hipaa_compliant: info.workspace.hipaa_compliant,
        },
        role_id: info.role,
        occupation: info.user.occupation,
        timezone: info.user.timezone,
    };

    return sdApiRestService('/lt/upsert_user', apiRequest, ApiResponse, {
        timeoutMs,
        headers: { 'clickup-locale': req.locale },
        requestName: 'access_token_service.upsert_user',
    });
}

/**
 * Resolves (user_id, team_id) into the real user/team data from the DB. This
 * function uses require() intentionally, to let Nest world not include legacy
 * DB related code.
 */
async function userReqResolverDynamicRequire(req: UserReqManual): Promise<UserReqResolved> {
    const { promiseGetUser } = requireUserMemoized();

    // split using a feature flag so it's safer to deploy
    // without breaking changes
    const featureFlagOn = sdFromNestNoMemoize(req.userId);

    const userInfoPromise: Promise<{
        id: number;
        email: string;
        username: string | null;
        timezone: string | null;
    }> = featureFlagOn
        ? getUsersInfo([Number(req.userId)], ['id', 'email', 'username', 'timezone']).then(rows => first(rows?.rows))
        : promiseGetUser(req.userId);

    const teamInfoPromise = featureFlagOn
        ? getTeamNameAndUserRoleCheckingAccess(req.userId, req.workspaceId)
        : (requireGetTeamNameAndUserRoleCheckingAccessMemoized()(req.userId, req.workspaceId) as ReturnType<
              typeof getTeamNameAndUserRoleCheckingAccess
          >);

    const [teamInfo, userInfo, entitlements] = await join([
        teamInfoPromise,
        userInfoPromise,
        elasticParams().sendWorkspaceEntitlementsToSD
            ? entitlementService.getEntitlements(req.workspaceId, [
                  EntitlementName.ChatHistoryLimit,
                  EntitlementName.AiUsage,
              ])
            : Promise.resolve(undefined),
    ]);

    return {
        user: {
            id: String(userInfo.id),
            email: String(userInfo.email),
            username: userInfo.username ? String(userInfo.username) : null,
            occupation: teamInfo.occupation ? String(teamInfo.occupation) : null,
            timezone: userInfo.timezone ? String(userInfo.timezone) : null,
        },
        workspace: {
            id: String(teamInfo.id),
            name: String(teamInfo.name),
            ai_enabled: !!teamInfo.ai_enabled,
            ai_hidden: !!teamInfo.ai_hidden,
            chat_enabled: teamInfo.chat_enabled,
            plan_id: Number(teamInfo.plan_id),
            custom_origin: teamInfo.custom_url ? `https://${String(teamInfo.custom_url)}` : null,
            entitlements,
            using_private_cf: teamInfo.using_private_cf,
            hipaa_compliant: !!teamInfo.hipaa_compliant,
        },
        role: Number(teamInfo.role),
    };
}

/**
 * Returns the role name by its number.
 */
export function getRoleNameByNumber(role: number): RoleConfig['name'] | null {
    return config.get<Record<string, RoleConfig>>('roles')[`${role}`]?.name ?? null;
}

/**
 * In legacy src/ world, we use legacy DB functions to load user/team data by
 * ID. In Nest world, uses Nest DB tooling.
 */
let userReqResolver = userReqResolverDynamicRequire;

const cache = new QuickLRU<
    string,
    {
        res: typeof ApiResponse.TYPE;
        fetchedAt: number;
    }
>({ maxSize: 1000 });
