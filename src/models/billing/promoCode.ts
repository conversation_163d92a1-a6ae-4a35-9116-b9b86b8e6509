import config from 'config';
import { GlobalTableMigrationBatchId } from '@clickup-legacy/utils/pg/interfaces/GlobalDatabaseByBatchConfig';
import logging from '@clickup/shared/utils-logging';
import { WorkspacePaymentsCycle } from '@clickup/billing/types';
import moment from 'moment';
import { checkAccessTeam } from '../../utils/access2';
import { globalReadQuery, globalReadQueryAsync, readQuery } from '../../utils/db';
import { parallel } from '../../utils/asyncHelper';
import { TeamId, UserId } from '../../libs/common/types';
import {
    CheckPromoCodeValidOptions,
    PromoCode,
    PromoCodeAddon,
    PromoCodeValidated,
} from '../../libs/billing/promoCode/interface';
import { TeamInfo } from '../../libs/billing/plan/interface';
import { ClickUpRequestV2, ClickUpResponseV2 } from '../../interfaces/ClickUpRequest';
import { PromoCodeProductType, TeamPromoCode } from '../../libs/billing/promoCode/teamPromoCode/types';
import { TeamPromoCodeService } from '../../libs/billing/promoCode/teamPromoCode/teamPromoCodeService';

const logger = logging.getLogger('promoCode');

interface PromoCodeError {
    status: number;
    err: unknown;
    ECODE: string;
}

export function _checkPromoCodeValid(
    userid: UserId,
    team_id: TeamId,
    promo_code: string,
    productType: PromoCodeProductType | undefined,
    options: CheckPromoCodeValidOptions,
    cb: (err: PromoCodeError | null, result?: PromoCodeValidated) => void
): void {
    if (!promo_code || typeof promo_code !== 'string') {
        cb({
            err: 'Promo code not provided',
            ECODE: 'PROMO_001',
            status: 400,
        });
        return;
    }

    parallel<{ rows: TeamInfo[] | PromoCode[] | PromoCodeAddon[] | string[] }>(
        {
            team(para_cb) {
                if (!team_id) {
                    para_cb(null, { rows: [] });
                    return;
                }
                readQuery(
                    `SELECT teams.*, task_plans.name as plan_name FROM task_mgmt.vw_active_team_billing_info teams
                        LEFT JOIN task_mgmt.task_plans ON teams.plan_id = task_plans.id WHERE teams.id = $1`,
                    [team_id],
                    para_cb
                );
            },
            productTypePromoCode(para_cb) {
                if (!productType || !team_id) {
                    para_cb(null, { rows: [] });
                    return;
                }
                const selectPromosQuery = TeamPromoCodeService.getSelectTeamProductTypePromosQuery(
                    team_id,
                    productType
                );
                readQuery(selectPromosQuery.query, selectPromosQuery.params, para_cb);
            },
            promotion(para_cb) {
                // unfortunately the pg driver doesn't convert PG arrays to JS arrays, so have to issue two queries
                globalReadQuery(
                    `SELECT pc.*
                    FROM crm.promo_codes pc
                    WHERE LOWER(pc.promo_code) = $1`,
                    [promo_code.toLowerCase()],
                    { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_13 },
                    para_cb
                );
            },
            promotion_addons(para_cb) {
                globalReadQuery(
                    `SELECT pca.*
                    FROM crm.promo_code_addon pca
                    WHERE LOWER(pca.promo_code) = $1`,
                    [promo_code.toLowerCase()],
                    { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_13 },
                    para_cb
                );
            },
            authorized_promos(para_cb) {
                if (!team_id) {
                    para_cb(null, { rows: [] });
                    return;
                }
                // it's read from shard (main app) - CLK-440363
                readQuery(
                    `SELECT promo_code FROM task_mgmt.promo_code_history WHERE LOWER(promo_code) = $1 AND team_id = $2 UNION SELECT promo_code FROM task_mgmt.promo_code_authorizations WHERE LOWER(promo_code) = $1 AND team_id = $2`,
                    [promo_code.toLowerCase(), team_id],
                    para_cb
                );
            },
        },
        async (err, _result: unknown) => {
            if (err) {
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'PROMO_001',
                });
                logger.error({
                    msg: 'Failed to get team or promo',
                    err,
                    ECODE: 'PROMO_001',
                    userid,
                    team_id,
                });
                return;
            }

            const result = _result as {
                team: { rows: TeamInfo[] & { plan_name: string } };
                promotion: { rows: PromoCode[] };
                promotion_addons: { rows: PromoCodeAddon[] };
                authorized_promos: { rows: string[] };
                productTypePromoCode: { rows: TeamPromoCode[] };
            };

            let valid = true;
            let valid_err = null;
            let cycles;

            if (result.team.rows[0] && result.team.rows[0].cycles) {
                cycles = result.team.rows[0].cycles;
            }

            if (options.cycles) {
                cycles = options.cycles;
            }

            if (result.promotion.rows.length === 0) {
                valid_err = {
                    err: 'Promotion not found',
                    ECODE: 'PROMO_003',
                };
                valid = false;

                logger.warn({
                    msg: 'Provided promo code not found',
                    ECODE: 'PROMO_003',
                    promo_code,
                });

                cb(null, {
                    promo: null,
                    valid,
                    valid_err,
                });
                return;
            }
            if (
                options.checkExpiration &&
                result.promotion.rows[0].expiration &&
                +result.promotion.rows[0].expiration < new Date().getTime()
            ) {
                valid_err = {
                    err: 'Promotion has expired',
                    ECODE: 'PROMO_004',
                };
                valid = false;
            }
            if (result.promotion.rows[0].active === false) {
                valid_err = {
                    err: 'Promotion is no longer active',
                    ECODE: 'PROMO_004',
                };
                valid = false;
            }
            if (
                cycles &&
                cycles === WorkspacePaymentsCycle.Monthly &&
                !options.ignoreCycles &&
                !result.promotion.rows[0].valid_monthly
            ) {
                valid_err = {
                    err: 'Promotion not valid for monthly plan',
                    ECODE: 'PROMO_010',
                };
                valid = false;
            } else if (
                cycles &&
                cycles === WorkspacePaymentsCycle.Yearly &&
                !options.ignoreCycles &&
                !result.promotion.rows[0].valid_yearly
            ) {
                valid_err = {
                    err: 'Promotion not valid for yearly plan',
                    ECODE: 'PROMO_011',
                };
                valid = false;
            } else if (
                result.productTypePromoCode.rows.length > 0 &&
                !options.ignoreHasPromo &&
                (!options.ignoreHasSamePromo ||
                    result.productTypePromoCode.rows[0].promo_code.toLowerCase() !== promo_code.toLowerCase())
            ) {
                valid_err = {
                    err: `You already have a promo code applied for ${productType}`,
                    ECODE: 'PROMO_006',
                };
                valid = false;
            }

            if (
                result.promotion.rows[0].protected &&
                result.authorized_promos.rows.length === 0 &&
                !options.ignoreAuthorization
            ) {
                valid_err = {
                    err: 'You are not authorized to use this promo code',
                    ECODE: 'PROMO_013',
                };
                valid = false;
            }

            if (valid && !options.ignoreOnlyValidOnSignup && result?.promotion?.rows?.[0]?.only_valid_on_signup) {
                if (result?.team?.rows?.[0]?.stored_promo_code?.toLowerCase() !== promo_code.toLowerCase()) {
                    valid_err = {
                        err: 'This promo code is only applicable on signup',
                        ECODE: 'PROMO_014',
                    };
                    valid = false;
                }
            }

            if (valid && team_id && !options.ignoreMaxWorkspaceAge && result?.promotion?.rows?.[0]?.max_workspace_age) {
                const wsDateCreated = result?.team?.rows?.[0]?.date_created;
                const isWorkspaceAgeWithinInterval = await globalReadQueryAsync(
                    `SELECT 
                        TO_TIMESTAMP($1::bigint / 1000) >= 
                            (now() - COALESCE(pc.max_workspace_age, '0 seconds'::interval)) 
                        as is_within_interval
                    FROM crm.promo_codes pc
                    WHERE LOWER(pc.promo_code) = $2`,
                    [wsDateCreated, promo_code.toLowerCase()],
                    { globalTableMigrationBatchId: GlobalTableMigrationBatchId.BATCH_13 }
                );
                if (!isWorkspaceAgeWithinInterval?.rows?.[0]?.is_within_interval) {
                    valid_err = {
                        err: 'This promo code cannot be applied to this workspace as it was created too long ago',
                        ECODE: 'PROMO_015',
                    };
                    valid = false;
                }
            }

            if (valid_err) {
                logger.info({ msg: valid_err.err, ECODE: valid_err.ECODE, promo_code });
            }

            const cyclesApplied =
                productType && result.productTypePromoCode.rows[0]
                    ? result.productTypePromoCode.rows[0].cycles_applied
                    : null;

            const promoResult = {
                ...result.promotion.rows[0],
                addons: result.promotion_addons.rows,
                cycles_applied: cyclesApplied,
            };

            cb(null, {
                promo: promoResult,
                valid,
                valid_err,
            });
        }
    );
}

export async function checkPromoCodeValidAsync(
    userId: UserId,
    teamId: TeamId,
    promoCode: string,
    productType: PromoCodeProductType | undefined,
    options: CheckPromoCodeValidOptions
): Promise<PromoCodeValidated> {
    return new Promise((resolve, reject) => {
        _checkPromoCodeValid(userId, teamId, promoCode, productType, options, (err, res) => {
            if (err) {
                reject(err);
            } else {
                resolve(res);
            }
        });
    });
}

function _checkPromoCodeValidWithAdminCheck(
    userId: UserId,
    teamId: TeamId,
    promoCode: string,
    productType: PromoCodeProductType,
    cb: (err: PromoCodeError | null, result?: PromoCodeValidated) => void
): void {
    checkAccessTeam(
        userId,
        teamId,
        { permissions: [config.permission_constants.billing] },
        (err: PromoCodeError | null) => {
            if (err) {
                cb(err);
                return;
            }

            _checkPromoCodeValid(
                userId,
                teamId,
                promoCode,
                productType,
                { ignoreCycles: true, ignoreHasPromo: true, checkExpiration: true },
                cb
            );
        }
    );
}

export function checkPromoCodeValid(req: ClickUpRequestV2, resp: ClickUpResponseV2) {
    const userid = req.decoded_token.user;
    const { team_id } = req.params;
    const { promo_code, productType } = req.query as {
        promo_code: string;
        productType: PromoCodeProductType | undefined;
    };

    _checkPromoCodeValidWithAdminCheck(userid, team_id, promo_code, productType, (err, result: any) => {
        if (err) {
            resp.status(err.status).send({
                err: err.err,
                ECODE: err.ECODE,
            });
        } else {
            resp.status(200).send(result);
        }
    });
}

// used by the frontend on signup to check if the promo code is valid
// note that there is no team context here
export function getPromoCode(req: ClickUpRequestV2, resp: ClickUpResponseV2) {
    const { promo_code, productType } = req.query as {
        promo_code: string;
        productType: PromoCodeProductType | undefined;
    };

    _checkPromoCodeValid(
        null,
        null,
        promo_code,
        productType,
        { checkExpiration: true, ignoreOnlyValidOnSignup: true, ignoreMaxWorkspaceAge: true },
        (err, result) => {
            if (err) {
                resp.status(err.status).send({
                    err: err.err,
                    ECODE: err.ECODE,
                });
            } else {
                resp.status(200).send(result);
            }
        }
    );
}

export const checkPromoCodeValidNoThrow = async (
    userId: UserId,
    teamId: TeamId,
    promoCode: string,
    productType: PromoCodeProductType | undefined,
    options: CheckPromoCodeValidOptions
): Promise<PromoCodeValidated> =>
    new Promise(resolve => {
        _checkPromoCodeValid(userId, teamId, promoCode, productType, options, (err, res) => {
            if (err) {
                resolve({ valid: false, valid_err: err as unknown as any });
            } else {
                resolve(res);
            }
        });
    });

export default {
    _checkPromoCodeValid,
    checkPromoCodeValid,
    checkPromoCodeValidAsync,
    checkPromoCodeValidNoThrow,
    getPromoCode,
};
