import { InternalGuestsRepository } from '@clickup-legacy/libs/billing/internalGuests/InternalGuestsRepository';
import { simpleBdrReadClient } from '@clickup-legacy/utils/simpleClient/simpleClient';
import { areLimitedMembersEnabled } from '@clickup-legacy/models/integrations/split/squadTreatments/accessManagementTreatments';
import { useInternalGuestsCountSettings } from '@clickup-legacy/models/integrations/split/squadTreatments/crmBillingTreatments';
import { UserId } from '@clickup/user/core';

export const internalGuestsRepository = new InternalGuestsRepository<UserId>(
    simpleBdrReadClient,
    (target?: string) => useInternalGuestsCountSettings(target),
    id => +id,
    areLimitedMembersEnabled
);
