import config from 'config';

import { keyBy, map } from 'lodash';
import { ObjectType, OperationType } from '@time-loop/ovm-object-version';
import { EntitlementName, entitlementService } from '@clickup-legacy/models/entitlements/entitlementService';
import { filterOvmChangeValues } from '@clickup-legacy/models/task/ovm/filter-ovm-change-values';
import { CustomType } from '@clickup/task/extensions';
import { getLogger } from '@clickup/shared/utils-logging';
import { areClickappsEnabledByTaskId } from '@clickup-legacy/models/task/helpers/clickappHelper';
import {
    getQueriesForPointsUpdate,
    getQueriesForTimeEstimateUpdate,
} from '@clickup-legacy/models/task/services/editTaskAssigneeQueryFactory';
import { dateInterval, isValidDateInterval, compareDateIntervals } from '@clickup/scheduler/common';
import { validateCustomTypeAllowedForTask } from '@clickup-legacy/models/task/validation/custom-type-validation';
import { checkAccessTaskOrTaskAsObjectBulkCb } from '@clickup-legacy/models/task_as_object/accessChecks';
import * as sqsNotif from '@clickup-legacy/utils/sqs-notif';
import { getObjectVersionManager } from '@clickup/object-version-manager-singleton';
import { TransactionClientImpl } from '@clickup/object-version-transaction-client';
import { DbPool } from '@clickup/data-platform/pg-pools';
import { getParentTaskChangeEventRelationship } from '@clickup-legacy/models/task/ovm/relationships';
import { adjustAutomationsAfterEditTask } from '@clickup-legacy/models/task/helpers/editTasksHelper';
import { updateRecurringForDueDateChange } from '@clickup-legacy/models/recurrence_2';
import uniq from 'lodash/uniq';
import { EntityType } from '@clickup/utils/constants';
import { logDateConsistency } from '../dateHelpers';
import { checkAccessTasks } from '../../../utils/access2';
import { addTasksToES } from '../../elastic/producer';
import { addUserFollowerQuery } from '../../follower/addUserFollowerQuery';
import { _getAssignees, getVirtualAssignees, unassignedTaskAssigneeId } from '../../assignee';
import { ClickUpError } from '../../../utils/errors';
import { verifyCustomItemWithTask } from '../../custom_items/customItems';
import * as db from '../../../utils/db';
import { readQuery, getConn, rollback } from '../../../utils/db';
import {
    batchQueriesSeriesWithClientAsync,
    getTransactionClientAsync,
    readClientQuery,
    writeAsync,
} from '../../../utils/db2';
import { createDueDateNotifs, createStartDateNotifs } from '../../notifications/delayedNotification';
import {
    _deleteUnblockedNotificationsIfNecessary,
    _unblockedNotificationIfNecessary,
} from '../../dependencies/taskDependencies.service';
import { _getFollowers, _removeFollowerIfNecessary } from '../../follower/follower';
import { _getDependencyChain } from '../../dependencies/remapDependencies.service';
import { getSummaryTaskBehavior, getSummaryTasksState } from '../../scheduling/summary_task/services/getSummaryInfo';
import { _getItems } from '../CRUD/getTask/get-items';
import { CUTasksUpdated } from '../../integrations/googleCalendar/taskNotifications';
import * as groupMod from '../../groups';
import * as input from '../../../utils/input_validation';
import { shouldLogTrail } from '../../integrations/split/squadTreatments/automationTreatments';
import { estimatesSupportInBulkActions } from '../../integrations/split/squadTreatments/fieldTreatments';
import {
    shouldUseRemapDependencyV2,
    shouldUseRemapSubtasksV2,
    summaryTaskV2Enabled,
    isDurationEnabled,
} from '../../integrations/split/squadTreatments/projectManagementTreatments';
import {
    hideRecentActivityHistoryWindow,
    shouldCheckAccessForTaskFollowerRemove,
    shouldLogEditTaskDateConsistency,
    shouldLogLargeTaskEdits,
    taskAsObjectConfig,
} from '../../integrations/split/squadTreatments/taskTreatments';
import { maybeDateToInt } from '../../time/utils';
import * as nestedHelpers from '../nestedHelpers';
import { postTaskUpdatedNotification } from '../../integrations/notifications/notifications';
import { prepareSetSummaryTasksDatesQueries } from '../../scheduling/summary_task/factories/setSummaryTasksDatesQueryFactory';
import { prepareUpdateSummaryTasksQueries } from '../../scheduling/summary_task/factories/summaryTasksQueryFactories';
import { queryTeamClickApps } from '../../team/datastores/queryTeamClickApps';
import { getDateOfTasks, getSubtaskIds } from '../datastores/getTaskDatastore';
import { setTaskAtMaxIndex } from '../../ordering/task/helpers';
import * as recurrence from '../../recurrence';
import * as remapMW from '../../due_dates/remapMW';
import { _removeOldAssigneeNotifications } from '../CRUD/editTask';
import * as subcatHelper from '../../subcategory/helpers';
import * as taskHelpers from '../taskHelpers';
import { triggerSubtasksResolvedEvent } from '../../../automation/resources/task/utils/triggerSubtasksResolvedEvent';
import * as unresolvedMW from '../unresolvedTasksMW';
import { updateDatesService } from '../../scheduling/datesUpdateServices';
import { behaveAsSummaryTask } from '../../scheduling/helpers';
import { doesUserWantToFollowTaskEdits } from '../helpers/userWatcherPreferences';
import * as webhook from '../../../utils/webhook';
import * as async from '../../../utils/asyncHelper';
import { sdAssetsHotUpdateNoWait } from '../../sd/api/sdAssetsHotUpdate';
import { VirtualTaskAssigneeHandler, EstimateType } from './assigneeEstimates/virtualTaskAssigneeHandler';

// constants
import { updateObjectVersionUpdatesFromTaskHistoryRows } from '../ovm/taskHistoryHelpers';
import { getUpdatedResolvedComments } from './getUpdatedResolvedComments';
import { TaskSamplingService } from '../utils/sampling';
import { ClickUpTracer } from '../../../utils/tracer';
import { TaskProperty } from '../ovm/types';
import { updateRequestForUser } from '../../../libs/user/factories/ovm-update-requests';
import { getVirtualTaskAssigneeQueries } from './assigneeEstimates/virtualTaskAssigneeQueryFactory';

const tracer = new ClickUpTracer();
const logger = getLogger('item');
const TaskError = ClickUpError.makeNamedError('task');
const AccessError = ClickUpError.makeNamedError('access');

const CLICKBOT = config.get('clickbot_assignee');
const parent_types_task = config.get('views.parent_types.task');

const MAX_OTHER_TASKS_UPDATE_COUNT = 50;
const MAX_COUNT_TO_CHECK_RELATIONS = 10;

const getDependedByTasksQuery = `SELECT 
            DISTINCT ON(id, depends_on) task_dependencies.task_id AS id
        FROM task_mgmt.task_dependencies 
            JOIN task_mgmt.items ON items.id = task_dependencies.task_id
            JOIN task_mgmt.subcategories ON items.subcategory = subcategories.id
            JOIN task_mgmt.categories ON subcategories.category = categories.id
            JOIN task_mgmt.projects ON categories.project_id = projects.id
            LEFT JOIN task_mgmt.items AS parent ON items.parent = parent.id
        WHERE            
            depends_on = ANY ($1)
            AND items.deleted = FALSE
            AND items.template = FALSE
            AND (parent.deleted IS NULL OR parent.deleted = false)
            AND (parent.template IS NULL OR parent.template = false)            
            AND subcategories.deleted = FALSE
            AND subcategories.template = FALSE            
            AND categories.template = FALSE
            AND categories.deleted = FALSE            
            AND projects.template = FALSE
            AND projects.archived = FALSE`;

const getDependsOnTasksQuery = `SELECT
    DISTINCT ON(id, depends_on) task_dependencies.depends_on AS id
FROM task_mgmt.task_dependencies
         JOIN task_mgmt.items ON items.id = task_dependencies.depends_on
         JOIN task_mgmt.subcategories ON items.subcategory = subcategories.id
         JOIN task_mgmt.categories ON subcategories.category = categories.id
         JOIN task_mgmt.projects ON categories.project_id = projects.id
         LEFT JOIN task_mgmt.items AS parent ON items.parent = parent.id
WHERE
        task_id = ANY ($1)
  AND items.deleted = FALSE
  AND items.template = FALSE
  AND (parent.deleted IS NULL OR parent.deleted = false)
  AND (parent.template IS NULL OR parent.template = false)
  AND subcategories.deleted = FALSE
  AND subcategories.template = FALSE
  AND categories.template = FALSE
  AND categories.deleted = FALSE
  AND projects.template = FALSE
  AND projects.archived = FALSE`;

async function setDatesAndOrderindex(options) {
    const { item_ids, team_id, new_inbox_users, dateOfTasksBeforeEdit, trigger_id, auto_id } = options;
    return new Promise((res, rej) => {
        async.parallel(
            {
                async adjustAutomations(para_cb) {
                    try {
                        await adjustAutomationsAfterEditTask(dateOfTasksBeforeEdit, {
                            scheduled_from: 'edit_task_service',
                            trigger_id,
                            auto_id,
                        });
                        para_cb();
                    } catch (para_err) {
                        para_cb(para_err);
                    }
                },
                addOrderindices(para_cb) {
                    if (!new_inbox_users.length) {
                        para_cb();
                        return;
                    }

                    const rows = new_inbox_users.reduce((acc, assignee) => {
                        acc.push(...item_ids.map(item_id => `${assignee}:${item_id}`));
                        return acc;
                    }, []);

                    async.each(
                        rows,
                        async (row, each_cb) => {
                            const [assignee, item_id] = row.split(':');
                            try {
                                const type = `inbox:${assignee}`;
                                await setTaskAtMaxIndex(item_id, type, {
                                    team_id,
                                    extra: {
                                        from: 'editItemsNewInboxUsers',
                                        assignee,
                                        item_id,
                                        team_id,
                                    },
                                });
                                each_cb();
                            } catch (e) {
                                each_cb(e);
                            }
                        },
                        para_cb
                    );
                },
            },
            async paraErr => {
                if (paraErr) {
                    logger.error({
                        msg: 'Failed to edit multiple items',
                        err: paraErr,
                        status: 500,
                    });
                    rej(paraErr);
                    return;
                }
                res();
            }
        );
    });
}

/**
 * Build an individual update query for a task, by the provided params.
 *
 * Caution: params are not type checked and validated and this method is only meant to be used inside
 * `editTasksService()` method.
 * @param task
 * @param {object} newProps
 * @param newProps.newStatus
 * @param newProps.remOldAssignees
 * @param newProps.priority
 * @param {CustomType} newProps.custom_type
 * @param newProps.newStatusesByTaskId
 * @param {number|null} [newProps.points]
 * @param status_objs
 * @param points_per_assignee
 * @param estimates_per_assignee
 * @param result
 * @param now
 * @return {{query: string, params: *[]}}
 */
function buildUpdateQuery(task, newProps, status_objs, points_per_assignee, estimates_per_assignee, result, now) {
    const { newStatus, priority, customType, newStatusesByTaskId, points, timeEstimate, timeEstimateString } = newProps;

    const individualUpdateQueryParams = [];
    let individualUpdateQuery = `UPDATE task_mgmt.tasks SET
                        date_updated = $${individualUpdateQueryParams.push(now)}`;

    const changes = [];
    const field_changes_map = {};
    field_changes_map[task.id] = changes;

    if (newStatus && task.status !== newStatus) {
        logger.info({
            msg: 'Updating task status',
            task_id: task.id,
            old_status: task.status,
            new_status: newStatus,
            new_status_id: newStatusesByTaskId[task.id].id,
        });

        individualUpdateQuery += `, status_id = $${individualUpdateQueryParams.push(newStatusesByTaskId[task.id].id)}`;
        individualUpdateQuery += `, status = $${individualUpdateQueryParams.push(newStatus)}`;

        const isClosed = config.closed_status_types.includes(status_objs[task.id].type);
        const isDone = config.done_status_types.includes(status_objs[task.id].type);

        // Don't overwrite the if closed/done date is already set - use the existing value
        const dateClosed = isClosed ? task.date_closed ?? now : null;
        individualUpdateQuery += `, date_closed = $${individualUpdateQueryParams.push(dateClosed)}`;

        const dateDone = isDone ? task.date_done ?? now : null;
        individualUpdateQuery += `, date_done = $${individualUpdateQueryParams.push(dateDone)}`;

        changes.push(
            {
                field: TaskProperty.StatusId,
                before: task.status_id,
                after: newStatusesByTaskId[task.id].id,
            },
            {
                field: TaskProperty.Status,
                before: task.status,
                after: newStatus,
            }
        );
    }

    if (priority != null) {
        // Normalize the updated priority value so that "none" -> null
        const updatedPriority = priority === 'none' ? null : priority;
        individualUpdateQuery += `, priority = $${individualUpdateQueryParams.push(updatedPriority)}`;

        changes.push({
            field: TaskProperty.Priority,
            before: task.priority,
            after: updatedPriority,
        });
    }

    // We support null points for unsetting the point
    if (points !== undefined && !points_per_assignee && result.areClickappsEnabledByTaskId[task.id].points) {
        individualUpdateQuery += `, points = $${individualUpdateQueryParams.push(points)}`;
    }

    if (
        timeEstimate !== undefined &&
        !estimates_per_assignee &&
        result.areClickappsEnabledByTaskId[task.id].time_estimates
    ) {
        individualUpdateQuery += `, time_estimate = $${individualUpdateQueryParams.push(timeEstimate)}`;
        individualUpdateQuery += `, time_estimate_string = $${individualUpdateQueryParams.push(timeEstimateString)}`;
    }

    if (!customType.isUnset()) {
        individualUpdateQuery += `, custom_type = $${individualUpdateQueryParams.push(customType.value)}`;
    }

    individualUpdateQuery += ` WHERE id = $${individualUpdateQueryParams.push(task.id)};`;

    return {
        query: individualUpdateQuery,
        params: individualUpdateQueryParams,
        field_changes_map,
    };
}

/**
 * Validate task params.
 * @param taskParams
 * @returns {{err: string, ECODE: string, status: number}|undefined}
 */
function validateTaskParams(taskParams) {
    if (!input.validatePriority(taskParams.priority)) {
        return {
            err: 'Task priority invalid',
            status: 400,
            ECODE: 'ITEM_071',
        };
    }

    if (!input.validatePoints(taskParams.points)) {
        return {
            err: 'Task point invalid',
            status: 400,
            ECODE: 'ITEM_075',
        };
    }

    if (input.validateTimeEstimate(taskParams.time_estimate) !== null) {
        return {
            err: 'Task time estimate invalid',
            status: 400,
            ECODE: 'ITEM_078',
        };
    }

    if (taskParams.time_estimate !== undefined && taskParams.time_estimate_string === undefined) {
        return {
            err: 'Missing time estimate string',
            status: 400,
            ECODE: 'ITEM_083',
        };
    }

    return undefined;
}

/**
 * editTaskService is a 3 step bulk operation service that edits tasks.
 * Step 1 - Permissions checks and data gathering. Most operations here can be run in parallel and are outside the
 * transaction.
 * Step 2 - Query building phase. Build queries for updating the tasks table and related
 * association tables. Optimize for performance and move fetch queries possibly to step 1.
 * Step 3 - Run the queries that is gathered in step 2 in a transaction, following up with executing history update &
 * OVM update queries.
 *
 * Update Oct 31, 2022: We're seeing a high number of deadlocks & blocking queries: https://staging.clickup.com/t/333/CLK-184724.
 * Besides the noticeable blocks, there's an extensive number of errors happening associated with the execution of this
 * method: https://app.datadoghq.com/logs?query=%40logname%3Aitem%20%20%20%22Failed%20to%20edit%20multiple%20items%22&cols=host%2Cservice&index=&messageDisplay=inline&stream_sort=time%2Cdesc&viz=stream&from_ts=1666551125098&to_ts=1667155925098&live=true.
 * To streamline the execution bit better, we're updating the tasks update queries from separate bulk updates to
 * individual update queries for each task.
 *
 * @param {array} item_ids
 * @param {object} taskParams
 * @param {array} taskParams.assignees
 * @param {array} taskParams.followers
 * @param {boolean} taskParams.duration_is_elapsed
 * @param {boolean} taskParams.remOldAssignees
 * @param {boolean} taskParams.remOldFollowers
 * @param {boolean} taskParams.remOldGroupFollowers
 * @param {number} taskParams.duration
 * @param {number} taskParams.points allows to set points on given tasks, is not intended to work in conjunction with adding/removing assignees.
 * @param {number} taskParams.userid
 * @param {string} taskParams.custom_type
 * @param {string} taskParams.due_date
 * @param {string} taskParams.due_date_time
 * @param {string} taskParams.priority
 * @param {string} taskParams.start_date
 * @param {string} taskParams.start_date_time
 * @param {boolean} taskParams.is_summary_task
 * @param {string} taskParams.status
 * @param {string} taskParams.time_estimate sets time estimate for a given task or per assignee if enabled, is not intended to work in conjunction with adding/removing assignees.
 * @param {string} taskParams.time_estimate_string a string representation of time estimate, must be provided along with time_estimate
 * @param {object} options
 * @param {boolean} [options.adjustSubtaskDates]
 * @param {boolean} [options.error_on_invalid_assignees]
 * @param {boolean} [options.group_assignees]
 * @param {boolean} [options.group_followers]
 * @param {boolean} [options.insertUnresolvedQueries]
 * @param {boolean} [options.mtt]
 * @param {boolean} [options.mute_notifications]
 * @param {boolean} [options.toolbar]
 * @param {boolean} [options.unapply_to_archived]
 * @param {string} [options.ws_key]
 * @param {string} [options.originating_service_override]
 * @param {boolean} [options.from_gantt]
 * @param {*} cb
 * @returns
 */
export async function editTasksService(item_ids, taskParams, options, cb) {
    console.log('i am here');
    const validationError = validateTaskParams(taskParams);
    if (validationError) {
        cb(validationError);
        return;
    }

    const {
        userid,
        remOldAssignees,
        remOldFollowers,
        remOldGroupFollowers,
        status,
        due_date,
        due_date_time,
        start_date,
        start_date_time,
        custom_type,
        duration_is_elapsed,
        points,
        time_estimate,
        time_estimate_string,
    } = taskParams;
    let { assignees, duration, followers, priority } = taskParams;

    // Large edits are failing without a trace, log additional info for debugging
    // https://app.clickup-stg.com/t/333/CLK-603179
    if (item_ids.length > 100 && shouldLogLargeTaskEdits()) {
        logger.info({
            msg: 'editTasksService: large edit',
            taskCount: item_ids.length,
            taskParams, // none of these fields are sensitive
            options,
        });
    }

    let add_group_assignees = [];
    let rem_group_assignees = [];
    let rem_assignees = [];
    let rem_followers = [];
    const rem_followers_by_task_id = {};
    const assignees_unaccessible_tasks = {};
    const add_group_followers = [];
    const rem_group_followers = [];
    const new_inbox_users = [];
    const permissions = [];
    const virtualTaskAssigneeHandlers = new Map();

    const {
        group_followers = {},
        group_assignees = {},
        error_on_invalid_assignees,
        originating_service_override,
    } = options;

    const task_verified_assignees = new Map();

    if (Array.isArray(group_followers.add)) {
        add_group_followers.push(...group_followers.add);
    }

    if (Array.isArray(group_followers.rem)) {
        rem_group_followers.push(...group_followers.rem);
    }

    if (Array.isArray(group_assignees.add)) {
        add_group_assignees = group_assignees.add;

        add_group_assignees.forEach(group_id => {
            if (!add_group_followers.includes(group_id)) {
                add_group_followers.push(group_id);
            }
        });
    }

    if (Array.isArray(group_assignees.rem)) {
        rem_group_assignees = group_assignees.rem;

        rem_group_assignees.forEach(group_id => {
            if (!rem_group_followers.includes(group_id)) {
                rem_group_followers.push(group_id);
            }
        });
    }

    if (options.toolbar) {
        writeAsync(
            'UPDATE task_mgmt.users SET used_multitask_toolbar = true WHERE id = $1',
            [userid],
            updateRequestForUser({ user: userid, operationType: OperationType.UPDATE }),
            undefined,
            {
                poolType: DbPool.GlobalWrite,
            }
        ).catch(err => {
            logger.error({
                msg: 'Failed to set user used multitask toolbar',
                err,
            });
        });
    }

    if (assignees.rem) {
        rem_assignees = uniq(assignees.rem);
        if (!assignees.add) {
            assignees = [];
        }
    }
    if (assignees.add) {
        assignees = uniq(assignees.add);
        new_inbox_users.push(...assignees);
    }

    if (!assignees) {
        assignees = [];
    }

    if (followers.rem) {
        rem_followers = uniq(followers.rem);
    }
    if (followers.add) {
        followers = uniq(followers.add);
    } else {
        followers = [];
    }

    if (followers.length > 0) {
        if (followers.length === 1 && followers[0] === userid) {
            permissions.push(config.permission_constants.add_self_follower);
        } else {
            permissions.push(config.permission_constants.add_followers);
        }
    }

    if (rem_followers.length > 0) {
        if (rem_followers.length === 1 && rem_followers[0] === userid) {
            permissions.push(config.permission_constants.remove_self_follower);
        } else {
            permissions.push(config.permission_constants.remove_followers);
        }
    }

    if (
        rem_assignees.length ||
        assignees.length ||
        remOldAssignees ||
        add_group_assignees.length ||
        rem_group_assignees.length
    ) {
        permissions.push(config.permission_constants.change_assignee);
    }

    if (add_group_followers.length || rem_group_followers.length) {
        permissions.push(config.permission_constants.add_followers);
    }

    if (status) {
        permissions.push(config.permission_constants.change_status);
    }

    if (due_date || due_date_time != null || start_date || start_date_time != null) {
        permissions.push(config.permission_constants.change_due_date);
    }

    if (priority) {
        permissions.push(config.permission_constants.change_priority);
    }

    if (points !== undefined) {
        permissions.push(config.permission_constants.change_points_estimate);
    }

    if (time_estimate) {
        permissions.push(config.permission_constants.change_time_estimate);
    }

    const customType = new CustomType(custom_type);

    if (customType.isCustomType()) {
        permissions.push(...customType.getExtraPermissions());
    } else if (!customType.isUnset()) {
        permissions.push(config.get('permission_constants.can_convert_item'));
    }

    const tasks = {};
    const item_statuses = {};
    const item_orderindex = {};
    const item_due_dates = {};
    const subtask_due_dates = {};
    const subtasks_to_resolve = {};
    const item_start_dates = {};
    const item_durations = {};
    const item_priorities = {};
    const status_objs = {};
    const old_status_objs = {};
    let removed_followers = [];
    const subcategories = [];
    const subcategoriesToInvalidate = [];

    let estimates_per_assignee;
    let points_per_assignee;
    let nested_subtasks;
    let pointsReassignedToGroupAssignee;
    let summary_task_behavior_map;

    const items_time_estimate_map = {};
    const items_points_estimate_map = {};
    const old_item_custom_types_map = {};
    let team_id;
    let ovm_field_changes_map = {};
    const click_apps = ['nested_subtasks', 'estimates_per_assignee', 'points_per_assignee'];

    try {
        ({ team_id, nested_subtasks, estimates_per_assignee, points_per_assignee } = await queryTeamClickApps(
            item_ids,
            click_apps
        ));
    } catch (err) {
        cb(new TaskError(err, 'ITEM_053'));
        return;
    }

    const useRemapDependenciesV2 = shouldUseRemapDependencyV2(team_id);
    const useRemapSubtasksV2 = shouldUseRemapSubtasksV2(team_id);
    const durationIsEnabled = isDurationEnabled(team_id);
    const isSummaryTaskV2Enabled = summaryTaskV2Enabled(team_id);
    // Note: adjustSubtaskDates is not compatiable with summary task v2
    // they must be mutually exclusive
    options.adjustSubtaskDates = !!(options.adjustSubtaskDates && !isSummaryTaskV2Enabled);

    const is_summary_task = isSummaryTaskV2Enabled ? taskParams.is_summary_task : null;
    if (!options.from_gantt && is_summary_task === true) {
        cb(new TaskError('Cannot mark as Summary Task outside of the gantt view', 'ITEM_220', 400));
        return;
    }

    async.parallel(
        {
            access(para_cb) {
                if (taskAsObjectConfig()?.monolithAccessChecks) {
                    checkAccessTaskOrTaskAsObjectBulkCb(userid, item_ids, { permissions }, para_cb);
                    return;
                }

                checkAccessTasks(userid, item_ids, { permissions }, para_cb);
            },
            items(para_cb) {
                readQuery(
                    `
                        SELECT 
                            items.id, 
                            status, 
                            items.due_date, 
                            items.due_date_time, 
                            orderindex, 
                            priority, 
                            items.start_date, 
                            items.start_date_time, 
                            subcategory, 
                            users.timezone,
                            items.points,
                            items.time_estimate,
                            items.custom_type,
                            items.date_closed,
                            items.date_done,
                            items.duration,
                            items.duration_is_elapsed,
                            COALESCE(items.subtask_parent, items.parent) as parent_task_id
                        FROM 
                            task_mgmt.items, 
                            task_mgmt.users 
                        WHERE 
                            items.id = ANY($1) AND 
                            users.id = $2
                    `,
                    [item_ids, userid],
                    (err, result) => {
                        if (err) {
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'ITEM_049',
                            });
                        } else {
                            result.rows.forEach(row => {
                                subcategories.push(row.subcategory);
                                subcategoriesToInvalidate.push(row.subcategory);
                                tasks[row.id] = row;
                                item_statuses[row.id] = row.status;
                                item_due_dates[row.id] = {
                                    due_date: maybeDateToInt(row.due_date),
                                    due_date_time: row.due_date_time,
                                    timezone: row.timezone,
                                };
                                item_orderindex[row.id] = row.orderindex;
                                item_priorities[row.id] = row.priority;
                                item_start_dates[row.id] = {
                                    start_date: maybeDateToInt(row.start_date),
                                    start_date_time: row.start_date_time,
                                    timezone: row.timezone,
                                };
                                item_durations[row.id] = {
                                    duration: row.duration,
                                    duration_is_elapsed: row.duration_is_elapsed,
                                };
                                items_time_estimate_map[row.id] = row.time_estimate || null;
                                items_points_estimate_map[row.id] = row.points || null;
                                old_item_custom_types_map[row.id] = { custom_type: row.custom_type };
                            });
                            para_cb();
                        }
                    }
                );
            },

            dueDateRemap(para_cb) {
                if (!nested_subtasks || !options.adjustSubtaskDates) {
                    para_cb();
                    return;
                }

                nestedHelpers.nestedSubtasksDueDateMW(item_ids, due_date, (err, result) => {
                    if (err) {
                        para_cb(new TaskError(err, 'ITEM_057'));
                        return;
                    }
                    const lookup = nestedHelpers.treeifyMap(result.rows);

                    item_ids.forEach(id => {
                        if (lookup[id]) {
                            const subtasks = [];
                            remapMW.dueDateSubtasks(lookup[id].subtasks, subtasks, item_ids);
                            subtask_due_dates[id] = subtasks;
                        } else {
                            subtask_due_dates[id] = [];
                        }
                    });
                    para_cb();
                });
            },

            resolve(para_cb) {
                if (!options.insertUnresolvedQueries) {
                    para_cb();
                    return;
                }

                nestedHelpers.nestedSubtasksUnresolvedMW(item_ids, status, (err, result) => {
                    if (err) {
                        para_cb(new TaskError(err, 'ITEM_037'));
                        return;
                    }

                    const lookup = nestedHelpers.treeifyMap(result.rows);

                    item_ids.forEach(id => {
                        if (lookup[id]) {
                            const subtasks = [];
                            unresolvedMW.getOpenSubtasks(lookup[id].subtasks, subtasks);
                            subtasks_to_resolve[id] = subtasks;
                        } else {
                            subtasks_to_resolve[id] = [];
                        }
                    });

                    para_cb();
                });
            },

            old_assignees(para_cb) {
                _getAssignees(item_ids, { replica: false }, para_cb);
            },
            old_virtual_assignees(para_cb) {
                getVirtualAssignees(item_ids, { replica: false }, para_cb);
            },
            old_group_assignees(para_cb) {
                groupMod.getUserGroupsByTaskIDs(userid, item_ids, { skipAccess: true }, para_cb);
            },
            old_followers(para_cb) {
                _getFollowers(item_ids, { replica: false }, para_cb);
            },
            validateAssignees(para_cb) {
                const _assignees = [];
                item_ids.forEach(task_id => task_verified_assignees.set(task_id, []));

                async.each(
                    assignees,
                    (assignee, each_cb) => {
                        checkAccessTasks(
                            assignee,
                            item_ids,
                            { permissions: [], checkJoined: false },
                            (err, { accessible_tasks, unaccessible_tasks }) => {
                                if (err && err.extra) {
                                    ({ accessible_tasks, unaccessible_tasks } = err.extra);
                                }

                                assignees_unaccessible_tasks[assignee] = unaccessible_tasks;

                                if (accessible_tasks.length) {
                                    _assignees.push(assignee);
                                }

                                item_ids.forEach(task_id => {
                                    if (accessible_tasks.includes(task_id)) {
                                        task_verified_assignees.get(task_id).push(assignee);
                                    }
                                });
                                each_cb();
                            }
                        );
                    },
                    () => {
                        if (error_on_invalid_assignees && assignees.length !== _assignees.length) {
                            para_cb(
                                new TaskError(
                                    'Invalid assignee(s)',
                                    'ITEM_107',
                                    400,
                                    { unaccessible_tasks: assignees_unaccessible_tasks },
                                    true
                                )
                            );
                            return;
                        }

                        assignees = _assignees;
                        para_cb();
                    }
                );
            },

            validateGroupFollowers(para_cb) {
                if (!add_group_followers.length && !add_group_assignees.length) {
                    para_cb();
                    return;
                }

                async.each(
                    [...add_group_followers, ...rem_group_followers],
                    (group_id, each_cb) => {
                        async.map(
                            item_ids,
                            (item_id, map_cb) => {
                                groupMod.checkGroupAvailableToLocation(
                                    group_id,
                                    item_id,
                                    parent_types_task,
                                    {},
                                    (err, result) => {
                                        if (err) {
                                            map_cb(err);
                                            return;
                                        }

                                        result.members.forEach(member => {
                                            if (new_inbox_users.includes(member)) {
                                                return;
                                            }

                                            new_inbox_users.push(member);
                                        });

                                        map_cb();
                                    }
                                );
                            },
                            each_cb
                        );
                    },
                    para_cb
                );
            },

            validateFollowers(para_cb) {
                const _followers = [];
                async.each(
                    followers,
                    (follower, each_cb) => {
                        checkAccessTasks(follower, item_ids, { permissions: [], checkJoined: false }, err => {
                            if (!err) {
                                _followers.push(follower);
                                each_cb();
                            } else {
                                each_cb(
                                    new AccessError(`Some members don't have access to the task`, 'ACCESS_201', 401)
                                );
                            }
                        });
                    },
                    err => {
                        followers = _followers;
                        para_cb(err);
                    }
                );
            },

            validateRemovedFollowers(para_cb) {
                if (!shouldCheckAccessForTaskFollowerRemove()) {
                    para_cb();
                    return;
                }

                async.each(
                    rem_followers || [],
                    (follower, each_cb) => {
                        checkAccessTasks(follower, item_ids, { permissions: [], checkJoined: false }, err => {
                            if (!err) {
                                each_cb();
                            } else {
                                each_cb(new AccessError(`Some users don't have access to the task`, 'ACCESS_203', 401));
                            }
                        });
                    },
                    err => {
                        para_cb(err);
                    }
                );
            },

            filterRemFollowers(para_cb) {
                if (!rem_followers?.length) {
                    para_cb();
                    return;
                }

                const query = `
                    SELECT task_id, userid
                    FROM task_mgmt.followers
                    WHERE task_id = ANY($1) AND userid = ANY($2)`;

                readQuery(query, [item_ids, rem_followers], (err, result) => {
                    if (err) {
                        logger.error({
                            msg: 'Failed to validate removed followers',
                            err,
                        });
                        para_cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'ITEM_088',
                        });
                        return;
                    }

                    const valid_rem_followers = new Set();
                    for (const row of result.rows) {
                        if (!rem_followers_by_task_id[row.task_id]) {
                            rem_followers_by_task_id[row.task_id] = [];
                        }

                        rem_followers_by_task_id[row.task_id].push(row.userid);
                        valid_rem_followers.add(row.userid);
                    }

                    rem_followers = [...valid_rem_followers];

                    para_cb();
                });
            },

            removed_followers(para_cb) {
                if (!remOldFollowers) {
                    para_cb();
                    return;
                }
                readQuery(
                    'SELECT * FROM task_mgmt.followers WHERE task_id = ANY($1) AND userid != ALL($2)',
                    [item_ids, followers || []],
                    (err, result) => {
                        if (err) {
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'ITEM_067',
                            });
                        } else {
                            removed_followers = result.rows;
                            para_cb();
                        }
                    }
                );
            },
            validateStatus(para_cb) {
                if (!status) {
                    para_cb();
                    return;
                }

                readQuery(
                    `
                    SELECT items.id, statuses.type 
                    FROM task_mgmt.statuses, task_mgmt.subcategories, task_mgmt.items
                    WHERE
                        items.id = ANY($1) 
                        AND items.subcategory = subcategories.id 
                        AND subcategories.status_group = statuses.status_group 
                        AND statuses.status = $2`,
                    [item_ids, status],
                    (err, result) => {
                        if (err) {
                            logger.error({
                                msg: 'Failed to validate statuses on edit items',
                                err,
                                status: 500,
                                ECODE: 'ITEM_415',
                            });
                            para_cb({ err: 'Internal server error', status: 500, ECODE: 'ITEM_116' });
                        } else if (result.rows.length < item_ids.length) {
                            const resultSet = new Set();
                            for (const row of result.rows) {
                                resultSet.add(row.id);
                            }
                            const tasksForWhichTheStatusIsInvalid = item_ids.filter(id => !resultSet.has(id));
                            logger.warn({
                                msg: 'Invalid status for tasks given',
                                userid,
                                item_ids,
                                status,
                                ids_no_status: tasksForWhichTheStatusIsInvalid,
                            });
                            para_cb({
                                err: 'Invalid status. Please refresh the page and try again.',
                                status: 400,
                                ECODE: 'ITEM_117',
                            });
                        } else {
                            result.rows.forEach(row => {
                                status_objs[row.id] = { type: row.type };
                            });
                            para_cb();
                        }
                    }
                );
            },

            /**
             * Select the new `status` object from the corresponding parent lists.
             *
             * *Note:* This is a naive approach where we're keying by task_id, for simplicity. Could be keyed by list_id
             * but requires more refactoring.
             * @param para_cb
             */
            newStatusesByTaskId(para_cb) {
                if (!status || (item_ids && item_ids.length === 0)) {
                    para_cb();
                    return;
                }

                readQuery(
                    `
                    SELECT tasks.id as task_id, statuses.*
                    FROM task_mgmt.statuses
                    INNER JOIN task_mgmt.subcategories
                        ON statuses.status_group = subcategories.status_group
                    INNER JOIN task_mgmt.items AS tasks
                        ON tasks.subcategory = subcategories.id
                    WHERE tasks.id = ANY($1)
                    AND statuses.status = $2`,
                    [item_ids, status],
                    (err, result) => {
                        if (err) {
                            logger.error({
                                msg: 'Failed to look up new status',
                                err,
                            });
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'ITEM_059',
                            });
                            return;
                        }
                        para_cb(null, keyBy(result.rows, 'task_id'));
                    }
                );
            },
            async getSubcategoriesForTIML(para_cb) {
                // Get all subcategories for TIML case
                if (!status) {
                    para_cb();
                    return;
                }
                try {
                    const subcatResult = await db.readQueryAsync(
                        `SELECT * FROM task_mgmt.task_subcategories WHERE task_id = ANY($1)`,
                        [item_ids]
                    );

                    subcatResult.rows.forEach(row => subcategoriesToInvalidate.push(row.subcategory));
                    para_cb();
                } catch (err) {
                    para_cb(err);
                }
            },
            async getDependencyChain(para_cb) {
                // Get Space remap dependencies settings
                const remapDependenciesEnabled = await areClickappsEnabledByTaskId(item_ids, ['remap_dependencies']);
                const enabledItemIds = item_ids.filter(
                    id => remapDependenciesEnabled[id] && remapDependenciesEnabled[id].remap_dependencies === true
                );
                if (enabledItemIds.length === 0) {
                    para_cb();
                    return;
                }

                const archivedTasksQuery = `
                    SELECT
                        items.id,
                        items.archived,
                        subcategories.archived as subcat_archived,
                        categories.archived as cat_archived,
                        projects.archived as proj_archived
                    FROM 
                        task_mgmt.items
                        JOIN task_mgmt.subcategories ON items.subcategory = subcategories.id
                        JOIN task_mgmt.categories ON subcategories.category = categories.id
                        JOIN task_mgmt.projects ON categories.project_id = projects.id
                    WHERE items.id = ANY($1)`;

                const tasksToGetDependencyChain = [];

                try {
                    const { rows } = await db.readQueryAsync(archivedTasksQuery, [enabledItemIds]);

                    rows.forEach(row => {
                        if (row.archived || row.subcat_archived || row.cat_archived || row.proj_archived) {
                            return;
                        }
                        tasksToGetDependencyChain.push(row.id);
                    });
                } catch (err) {
                    para_cb(err);
                    return;
                }

                if (tasksToGetDependencyChain.length === 0) {
                    para_cb();
                    return;
                }

                _getDependencyChain(tasksToGetDependencyChain, para_cb);
            },

            async checkCustomTypeEntitlement(para_cb) {
                if (!customType.isUsableCustomType()) {
                    para_cb();
                    return;
                }

                const customTypeEntitlement = await entitlementService.getEntitlement(
                    team_id,
                    EntitlementName.CustomItems
                );

                const canUseCustomType = item_ids.length <= customTypeEntitlement.usageLeft;

                if (!canUseCustomType) {
                    para_cb(new TaskError('Max usage for custom task types reached', 'ITEM_248', 400));
                    return;
                }

                para_cb();
            },

            async validateCUCustomItem(para_cb) {
                // needs to be in config
                if (!validateCustomTypeAllowedForTask({ customType, userId: userid })) {
                    para_cb(new TaskError('Invalid custom item type', 'ITEM_555', 400));
                    return;
                }

                para_cb();
            },

            async validate_custom_item(para_cb) {
                if (!customType.isWorkspaceCustomTypeRange()) {
                    para_cb();
                    return;
                }

                try {
                    await Promise.all([verifyCustomItemWithTask(item_ids[0], customType.value)]);
                    para_cb();
                } catch (e) {
                    para_cb(e);
                }
            },

            // changing dates, need to know if Tasks behave as Summary Tasks
            async summaryTasksBehavior(para_cb) {
                if (
                    !isSummaryTaskV2Enabled ||
                    (start_date == null && due_date == null && duration == null && duration_is_elapsed == null)
                ) {
                    para_cb();
                    return;
                }
                try {
                    // For a task to behave as a summary task:
                    // the task itself OR its parent must have the is_summary_task flag set to true
                    // AND the task must have children
                    // Note: This is a legacy requirement, and will be modified in the future
                    summary_task_behavior_map = await getSummaryTaskBehavior(item_ids);

                    para_cb(null, summary_task_behavior_map);
                } catch (err) {
                    para_cb(err);
                }
            },

            async summaryTasksState(para_cb) {
                if (!isSummaryTaskV2Enabled || is_summary_task == null) {
                    para_cb();
                    return;
                }
                try {
                    const summary_tasks_state = await getSummaryTasksState(item_ids, is_summary_task);

                    para_cb(null, summary_tasks_state);
                } catch (err) {
                    para_cb(err);
                }
            },

            // Check user follow preference
            async checkIfUserWantsToFollow(para_cb) {
                try {
                    const result = await doesUserWantToFollowTaskEdits(userid, team_id);
                    para_cb(null, result);
                } catch (err) {
                    para_cb(err);
                }
            },

            async oldStatuses(para_cb) {
                if (!status) {
                    para_cb();
                    return;
                }

                const res = await readClientQuery(
                    `
                        SELECT items.status, items.id, statuses.type
                        FROM task_mgmt.items
                                JOIN task_mgmt.statuses on status_id = statuses.id
                        WHERE items.id = ANY($1)
                    `,
                    [item_ids]
                );
                res.rows.forEach(row => {
                    old_status_objs[row.id] = row;
                });

                para_cb();
            },

            /**
             * Tasks can come from different spaces, which may have clickapps not enabled.
             * @param para_cb
             * @returns {Promise<void>}
             */
            async areClickappsEnabledByTaskId(para_cb) {
                try {
                    para_cb(null, await areClickappsEnabledByTaskId(item_ids, ['points', 'time_estimates']));
                } catch (err) {
                    para_cb(err);
                }
            },
        },
        async (err, result) => {
            if (err) {
                cb(err);
                return;
            }

            const queries = [];
            let hist_query =
                'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES (';
            const hist_params = [];
            const now = Date.now();
            const floatObjs = [];
            const priorityFloatObjs = [];
            const assigneeFloatObjs = [];

            const item_assignees = {};
            const item_group_assignees = {};
            const item_virtual_assignees = {};

            /**
             * @typedef {Object} AssigneeEstimateSettings
             * @property {boolean} splitTimeEstimateBetweenAssignees - Indicates if the time estimate should be split between assignees.
             * @property {boolean} assignPointsEstimateToFirstAssignee - Indicates if the points estimate should be assigned to the first assignee.
             * @property {boolean} allTaskAssigneesRemoved - Indicates if all task assignees have been removed.
             * @property {boolean} taskHadMoreThanOneAssignees - Indicates if task had more than 1 assigned user/group.
             * @property {boolean} taskHadAssignees - Indicates if task had any assignees.
             */

            /**
             * A map where the key is the task ID and the value is the estimate settings for the assignees of that task.
             * @type {Object.<string, AssigneeEstimateSettings>}
             */
            const item_assignee_estimate_settings = {};
            const allUserAssigneesRemoved = taskId =>
                item_assignees[taskId].every(assignee => rem_assignees.includes(assignee.id));
            const allGroupAssigneesRemoved = taskId =>
                item_group_assignees[taskId].every(assignee => rem_group_assignees.includes(assignee.id));

            const item_followers = {};
            const checkRecur = [];
            const parents_with_remapped_subtasks = [];
            const { newStatusesByTaskId } = result;
            const dependencyChain = result.getDependencyChain?.dependencies_to_change;
            const dependencyChainDetails = result.getDependencyChain?.dependencies_details;
            const taskSchedulingV2Info = result.getTaskSchedulingV2Info;
            const user_wants_to_follow_task_edits = result.checkIfUserWantsToFollow;
            const summary_tasks_state = result.summaryTasksState;
            const summary_task_behavior = result.summaryTasksBehavior;
            const otherTasksToUpdate = [];
            let taskIdsBeforeEdit = [...item_ids];
            let dateOfTasksBeforeEdit = [];
            try {
                if (options.adjustSubtaskDates) {
                    const subtaskIds = await getSubtaskIds(taskIdsBeforeEdit);
                    if (subtaskIds.length) {
                        taskIdsBeforeEdit = taskIdsBeforeEdit.concat(subtaskIds.map(subtask => subtask.id));
                    }
                }
                if (dependencyChain) {
                    const dependencyTaskIds = [];
                    item_ids.forEach(item_id => {
                        const deps = dependencyChain[item_id];
                        if (deps) {
                            deps.forEach(dep => {
                                dependencyTaskIds.push(dep);
                            });
                        }
                    });
                    if (dependencyTaskIds.length) {
                        taskIdsBeforeEdit = taskIdsBeforeEdit.concat(dependencyTaskIds);
                    }
                }
                // get date fields of item_ids, their subtasks and dependencies before edit
                dateOfTasksBeforeEdit = await getDateOfTasks(taskIdsBeforeEdit, false);
            } catch (errGetDateOfTasks) {
                cb(errGetDateOfTasks);
                return;
            }

            item_ids.forEach(item_id => {
                item_assignees[item_id] = result.old_assignees.assignees[item_id];
                item_group_assignees[item_id] = [];
                item_virtual_assignees[item_id] = result.old_virtual_assignees.virtualAssigneesByTaskId.get(item_id);
                item_followers[item_id] = result.old_followers.followers[item_id].map(follower => follower.id);
                virtualTaskAssigneeHandlers.set(item_id, new VirtualTaskAssigneeHandler(item_id));

                if (result.old_group_assignees && result.old_group_assignees[item_id]) {
                    item_group_assignees[item_id] = result.old_group_assignees[item_id];
                }

                const taskHadUserAssignees = item_assignees[item_id].length > 0;
                const taskHadGroupAssignees = item_group_assignees[item_id].length > 0;
                const taskHadAssignees = taskHadUserAssignees || taskHadGroupAssignees;
                const taskHadMoreThanOneAssignees =
                    item_assignees[item_id].length + item_group_assignees[item_id].length > 1;

                const allTaskAssigneesRemoved =
                    remOldAssignees || (allUserAssigneesRemoved(item_id) && allGroupAssigneesRemoved(item_id));

                const splitTimeEstimateBetweenAssignees =
                    estimates_per_assignee && items_time_estimate_map[item_id] && allTaskAssigneesRemoved;

                const assignPointsEstimateToFirstAssignee =
                    points_per_assignee && items_points_estimate_map[item_id] && allTaskAssigneesRemoved;

                item_assignee_estimate_settings[item_id] = {
                    splitTimeEstimateBetweenAssignees,
                    assignPointsEstimateToFirstAssignee,
                    allTaskAssigneesRemoved,
                    taskHadMoreThanOneAssignees,
                    taskHadAssignees,
                };
            });

            Object.values(tasks).forEach(task => {
                const { query, params, field_changes_map } = buildUpdateQuery(
                    task,
                    {
                        newStatus: status,
                        remOldAssignees,
                        priority,
                        customType,
                        newStatusesByTaskId,
                        points,
                        timeEstimate: time_estimate,
                        timeEstimateString: time_estimate_string,
                    },
                    status_objs,
                    points_per_assignee,
                    estimates_per_assignee,
                    result,
                    now
                );
                ovm_field_changes_map = mergeFieldChangesMaps(ovm_field_changes_map, field_changes_map);
                queries.push({ query, params });
            });

            const { durationMs: hiddenHistoryWindow } = hideRecentActivityHistoryWindow();

            if (status) {
                TaskSamplingService.getInstance().sampleSpan(tracer.getRootSpan(), {
                    workspace: team_id,
                    user: userid,
                });

                const taskIdsWithChangedStatus = [];
                item_ids.forEach(item_id => {
                    if (item_statuses[item_id] !== status) {
                        floatObjs.push({ id: item_id, new_status: status });
                        if (hiddenHistoryWindow > 0) {
                            queries.push({
                                query: `
                                    -- editTasksService
                                    UPDATE task_mgmt.task_history 
                                    SET hidden = true 
                                    WHERE id IN (
                                        SELECT id 
                                        FROM task_mgmt.task_history 
                                        WHERE task_id = $1 
                                          AND field = $2 
                                          AND userid = $3 
                                          AND date > $4 
                                          AND hidden IS NOT TRUE
                                    )`,
                                params: [item_id, 'status', userid, now - hiddenHistoryWindow],
                            });
                        }

                        queries.push({
                            query: `
                                    INSERT INTO task_mgmt.task_history
                                        (task_id, field, date, before, after, userid, data) 
                                    VALUES ($1, $2, $3, $4, $5, $6, $7) 
                                    RETURNING *`,
                            params: [
                                item_id,
                                'status',
                                now,
                                item_statuses[item_id],
                                status,
                                userid,
                                {
                                    status_type: newStatusesByTaskId[item_id].type,
                                    mute_notifications: options.mute_notifications,
                                },
                            ],
                        });

                        const isOldStatusClosed = config.closed_status_types.includes(old_status_objs[item_id]?.type);
                        const isOldStatusDone = config.done_status_types.includes(old_status_objs[item_id]?.type);
                        const isClosed = config.closed_status_types.includes(status_objs[item_id].type);
                        const isDone = config.done_status_types.includes(status_objs[item_id].type);

                        const hasClosedStatusChanged =
                            (isClosed && !isOldStatusClosed) || (!isClosed && isOldStatusClosed);
                        const hasDoneStatusChanged = (isDone && !isOldStatusDone) || (!isDone && isOldStatusDone);

                        if (hasClosedStatusChanged || hasDoneStatusChanged) {
                            taskIdsWithChangedStatus.push(item_id);
                        }

                        checkRecur.push({ id: item_id });
                    }
                });
                if (taskIdsWithChangedStatus.length <= MAX_COUNT_TO_CHECK_RELATIONS) {
                    try {
                        const dependsOnTasks = await db.readQueryAsync(getDependsOnTasksQuery, [
                            taskIdsWithChangedStatus,
                        ]);

                        const dependedByTasks = await db.readQueryAsync(getDependedByTasksQuery, [
                            taskIdsWithChangedStatus,
                        ]);

                        if (dependedByTasks.rows.length + dependsOnTasks.rows.length <= MAX_OTHER_TASKS_UPDATE_COUNT) {
                            otherTasksToUpdate.push(
                                ...map(dependedByTasks.rows, 'id'),
                                ...map(dependsOnTasks.rows, 'id')
                            );
                        }
                    } catch (e) {
                        logger.error({
                            msg: "Couldn't get relationships on status update",
                            err: e,
                            ECODE: 'ITEM_044',
                        });
                    }
                }
            }

            if (priority != null) {
                if (priority === 'none') {
                    priority = null;
                }

                try {
                    item_ids.forEach(item_id => {
                        priorityFloatObjs.push({ id: item_id, new_priority: priority });

                        const data = { mute_notifications: options.mute_notifications };
                        const priority_hist_queries = taskHelpers.historyQueries(
                            userid,
                            item_id,
                            'priority',
                            item_priorities[item_id],
                            priority,
                            data
                        );
                        queries.push(...priority_hist_queries);
                    });
                } catch (priority_hist_err) {
                    cb(new TaskError(priority_hist_err, 'ITEM_573'));
                    return;
                }
            }

            if (due_date != null || start_date != null) {
                const date_options = {
                    mtt: options.mtt,
                    mute_notifications: options.mute_notifications,
                    adjustSubtaskDates: options.adjustSubtaskDates,
                    summary_task_app: isSummaryTaskV2Enabled,
                    summary_task_behavior,
                    use_remap_dependency_v2: useRemapDependenciesV2,
                    use_remap_subtasks_v2: useRemapSubtasksV2,
                    is_duration_enabled: durationIsEnabled,
                };

                const {
                    queries: date_update_queries,
                    parents_with_remapped_subtasks: date_update_parents_with_remapped_subtasks,
                    field_changes_map,
                } = await updateDatesService(
                    team_id,
                    userid,
                    due_date,
                    due_date_time,
                    start_date,
                    start_date_time,
                    duration,
                    duration_is_elapsed,
                    item_ids,
                    item_due_dates,
                    item_start_dates,
                    item_durations,
                    subtask_due_dates,
                    dependencyChain,
                    dependencyChainDetails,
                    date_options
                );

                ovm_field_changes_map = mergeFieldChangesMaps(ovm_field_changes_map, field_changes_map);
                queries.push(...date_update_queries);
                parents_with_remapped_subtasks.push(...date_update_parents_with_remapped_subtasks);
            }

            /*
                TODO: Refactor duration and duration_is_elapsed section of this code
                For now we will just set the duration and duration_is_elapsed as set by the FE and not calculate it.
                We will need to the ability to calculate the duration based on
                the start and due dates AND non-working days based on the
                workspace/location/user (potentially all non-working days merged together
                from all locations).

                Note: Using similar pattern used to clear entries for due_due and start_date to keep things consistent in the FE and BE
                If duration = 'none', we will set it to null in the database
                If duration != null, we will set it to the duration provided by the FE
                If duration = null, we will not update the duration in the database
            */
            if (duration != null) {
                if (duration === 'none') {
                    duration = null;
                }

                if (duration != null && !isValidDateInterval(duration)) {
                    cb(new TaskError('Invalid duration', 'ITEM_575'));
                    return;
                }

                item_ids.forEach(item_id => {
                    // don't directly modify duration of summary tasks
                    const canUpdateDuration = !behaveAsSummaryTask(
                        item_id,
                        isSummaryTaskV2Enabled,
                        summary_task_behavior_map
                    );

                    if (canUpdateDuration && !compareDateIntervals(duration, item_durations[item_id]?.duration)) {
                        queries.push({
                            query: `UPDATE task_mgmt.items SET duration = $1 WHERE id = $2`,
                            params: [dateInterval(duration).asIntervalString, item_id],
                        });
                        if (!Object.prototype.hasOwnProperty.call(ovm_field_changes_map, item_id)) {
                            ovm_field_changes_map[item_id] = [];
                        }
                        ovm_field_changes_map[item_id].push({
                            field: 'duration',
                            before: dateInterval(item_durations[item_id]?.duration)?.asIntervalString,
                            after: dateInterval(duration).asIntervalString,
                        });

                        queries.push({
                            query: `
                                    INSERT INTO task_mgmt.task_history
                                        (task_id, field, date, before, after, userid, data) 
                                    VALUES ($1, $2, $3, $4, $5, $6, $7) 
                                    RETURNING *`,
                            params: [
                                item_id,
                                'duration',
                                now,
                                item_durations[item_id]?.duration,
                                dateInterval(duration).asIntervalString,
                                userid,
                                {
                                    mute_notifications: options.mute_notifications,
                                },
                            ],
                        });
                    }
                });
            }

            if (duration_is_elapsed != null) {
                item_ids.forEach(item_id => {
                    const durationIsElapsedChanged =
                        Boolean(item_durations[item_id]?.duration_is_elapsed) !== Boolean(duration_is_elapsed);

                    // don't directly modify duration of summary tasks
                    const canUpdateDurationIsElapsed = !behaveAsSummaryTask(
                        item_id,
                        isSummaryTaskV2Enabled,
                        summary_task_behavior_map
                    );

                    if (canUpdateDurationIsElapsed && durationIsElapsedChanged) {
                        queries.push({
                            query: `UPDATE task_mgmt.items SET duration_is_elapsed = $1 WHERE id = $2`,
                            params: [duration_is_elapsed, item_id],
                        });

                        if (!Object.prototype.hasOwnProperty.call(ovm_field_changes_map, item_id)) {
                            ovm_field_changes_map[item_id] = [];
                        }

                        ovm_field_changes_map[item_id].push({
                            field: 'duration_is_elapsed',
                            before: item_durations[item_id]?.duration_is_elapsed,
                            after: duration_is_elapsed,
                        });

                        queries.push({
                            query: `
                                    INSERT INTO task_mgmt.task_history
                                        (task_id, field, date, before, after, userid, data) 
                                    VALUES ($1, $2, $3, $4, $5, $6, $7) 
                                    RETURNING *`,
                            params: [
                                item_id,
                                'duration_is_elapsed',
                                now,
                                item_durations[item_id]?.duration_is_elapsed,
                                duration_is_elapsed,
                                userid,
                                {
                                    mute_notifications: options.mute_notifications,
                                },
                            ],
                        });
                    }
                });
            }

            if (add_group_assignees.length) {
                item_ids.forEach(item_id => {
                    add_group_assignees.forEach((group_id, index) => {
                        let points_value = null;
                        if (
                            item_assignee_estimate_settings[item_id].assignPointsEstimateToFirstAssignee &&
                            index === 0
                        ) {
                            points_value = items_points_estimate_map[item_id];

                            pointsReassignedToGroupAssignee = true;
                        }

                        const params = [item_id, group_id, Date.now(), team_id, points_value];

                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.group_assignees(task_id, group_id, date_assigned, workspace_id, points_float)
                                VALUES ($1, $2, $3, $4, $5)
                                ON CONFLICT (task_id, group_id) 
                                DO UPDATE SET 
                                    points_float = COALESCE(EXCLUDED.points_float, group_assignees.points_float)`,
                            params,
                        });

                        if (
                            !result.old_group_assignees[item_id] ||
                            !result.old_group_assignees[item_id].some(group => group.id === group_id)
                        ) {
                            // Add to history query
                            if (hist_params.length > 0) {
                                hist_query += '), (';
                            }

                            hist_query += `
                                $${hist_params.length + 1}, 
                                $${hist_params.length + 2}, 
                                $${hist_params.length + 3}, 
                                $${hist_params.length + 4}, 
                                $${hist_params.length + 5}, 
                                $${hist_params.length + 6}, 
                                $${hist_params.length + 7}`;
                            hist_params.push(item_id, 'group_assignee', now, null, group_id, userid, {
                                multitask_toolbar: options.toolbar,
                                mute_notifications: options.mute_notifications,
                            });
                        }
                    });
                });
            }

            if (rem_group_assignees.length) {
                item_ids.forEach(item_id => {
                    rem_group_assignees.forEach(group_id => {
                        queries.push({
                            query: `
                                DELETE FROM task_mgmt.group_assignees
                                WHERE task_id = $1
                                AND group_id = $2`,
                            params: [item_id, group_id],
                        });

                        if (
                            result.old_group_assignees[item_id] &&
                            result.old_group_assignees[item_id].some(group => group.id === group_id)
                        ) {
                            // Add to history query
                            if (hist_params.length > 0) {
                                hist_query += '), (';
                            }

                            hist_query += `
                                $${hist_params.length + 1}, 
                                $${hist_params.length + 2}, 
                                $${hist_params.length + 3}, 
                                $${hist_params.length + 4}, 
                                $${hist_params.length + 5}, 
                                $${hist_params.length + 6}, 
                                $${hist_params.length + 7}`;
                            hist_params.push(item_id, 'group_assignee', now, group_id, null, userid, {
                                multitask_toolbar: options.toolbar,
                                mute_notifications: options.mute_notifications,
                            });
                        }
                    });

                    if (points_per_assignee) {
                        accountForDeletedGroupAssignees({
                            virtualAssigneeHandler: virtualTaskAssigneeHandlers.get(item_id),
                            existingGroupAssignees: item_group_assignees[item_id],
                            item_group_assignees,
                            rem_group_assignees,
                        });
                    }
                });
            }

            if (add_group_followers.length) {
                item_ids.forEach(item_id => {
                    add_group_followers.forEach(group_id => {
                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.group_followers(task_id, group_id, date_added, workspace_id)
                                VALUES ($1, $2, $3, $4)
                                ON CONFLICT DO NOTHING`,
                            params: [item_id, group_id, new Date().getTime(), team_id],
                        });
                    });
                });
            }

            if (rem_group_followers.length) {
                item_ids.forEach(item_id => {
                    queries.push({
                        query: `DELETE FROM task_mgmt.group_followers WHERE group_id = ANY($1) AND task_id = $2`,
                        params: [rem_group_followers, item_id],
                    });
                });
            }

            if ((followers && followers.length) || (assignees && assignees.length)) {
                item_ids.forEach(item_id => {
                    followers.forEach(userToAdd => {
                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.followers
                                    (task_id, userid, added_from, workspace_id) 
                                    (
                                        SELECT $1, $2, $3, $4
                                        WHERE NOT EXISTS (
                                            SELECT * 
                                            FROM task_mgmt.followers
                                            WHERE
                                                task_id = $1
                                                AND userid = $2
                                        )
                                    )`,
                            params: [item_id, userToAdd, null, team_id],
                        });

                        // Set mute_notifications to true when self-added.
                        if (userid === userToAdd) {
                            options.mute_notifications = true;
                        }
                        if (item_followers[item_id].indexOf(userToAdd) < 0) {
                            // Add to history query
                            if (hist_params.length > 0) {
                                hist_query += '), (';
                            }
                            hist_query += `$${hist_params.length + 1}, $${hist_params.length + 2}, $${
                                hist_params.length + 3
                            }, $${hist_params.length + 4}, $${hist_params.length + 5}, $${hist_params.length + 6}, $${
                                hist_params.length + 7
                            }`;
                            hist_params.push(item_id, 'follower', now, null, userToAdd, userid, {
                                multitask_toolbar: options.toolbar,
                                mute_notifications: options.mute_notifications,
                            });
                        }
                    });

                    task_verified_assignees.get(item_id).forEach(userToAdd => {
                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.followers
                                    (task_id, userid, added_from, workspace_id)
                                    (
                                        SELECT $1, $2, $3, $4
                                        WHERE NOT EXISTS (
                                            SELECT *
                                            FROM Task_mgmt.followers
                                            WHERE
                                                task_id = $1
                                                AND userid = $2
                                            )
                                    )`,
                            params: [item_id, userToAdd, 'assignee_added', team_id],
                        });
                    });
                });

                const followerTaskIdPairs = [];
                followers.forEach(followerId => {
                    item_ids.forEach(taskId => {
                        followerTaskIdPairs.push([followerId, taskId]);
                    });
                });

                const unfollowerParams = [team_id, EntityType.TASK];
                const unfollowerQuery = `DELETE FROM task_mgmt.shared_entity_unfollowers
                        WHERE workspace_id = $1 AND entity_type = $2 AND (${
                    followerTaskIdPairs
                        .map(
                            ([followerId, taskId]) =>
                                `user_id = $${unfollowerParams.push(followerId)} AND entity_id = $${unfollowerParams.push(
                                    taskId
                                )}`
                        )
                        .join(' OR ')
                });`
                queries.push({
                    query: unfollowerQuery,
                    params: unfollowerParams,
                });
                console.log('unfollowerQuery', unfollowerQuery, unfollowerParams);
            }

            if (rem_followers && rem_followers.length > 0) {
                const query = 'DELETE FROM task_mgmt.followers WHERE userid = ANY($1) AND task_id = ANY($2)';
                const params = [rem_followers, item_ids];

                // add remove task hist
                item_ids.forEach(item_id => {
                    const task_rem_followers = rem_followers_by_task_id[item_id];
                    task_rem_followers?.forEach(rem_follower => {
                        if (hist_params.length > 0) {
                            hist_query += '), (';
                        }

                        hist_query += `$${hist_params.length + 1}, $${hist_params.length + 2}, $${
                            hist_params.length + 3
                        }, $${hist_params.length + 4}, $${hist_params.length + 5}, $${hist_params.length + 6}, $${
                            hist_params.length + 7
                        }`;
                        hist_params.push(item_id, 'follower', now, rem_follower, null, userid, {
                            multitask_toolbar: options.toolbar,
                            mute_notifications: options.mute_notifications,
                        });
                    });
                });

                const userTaskIdPairs = [];
                rem_followers.forEach(followerId => {
                    item_ids.forEach(taskId => {
                        userTaskIdPairs.push([followerId, taskId]);
                    });
                });

                const unfollowerParams = [team_id, EntityType.TASK];
                const unfollowerQuery = `INSERT INTO task_mgmt.shared_entity_unfollowers
                    (workspace_id, entity_type, user_id, entity_id)
                    VALUES ${
                        userTaskIdPairs
                            .map(
                                ([userId, taskId]) =>
                                    `($1, $2, $${unfollowerParams.push(userId)}, $${unfollowerParams.push(taskId)})`
                            )
                            .join(',')
                    };`

                console.log('unfollowerQuery', unfollowerQuery, unfollowerParams);
                queries.push({
                    query,
                    params,
                }, {
                    query: unfollowerQuery,
                    params: unfollowerParams,
                });
            }

            if (remOldFollowers) {
                queries.push({
                    query: 'DELETE FROM task_mgmt.followers WHERE task_id = ANY($1) AND userid != ALL($2) RETURNING *',
                    params: [item_ids, followers || []],
                });
            }

            if (remOldGroupFollowers) {
                queries.push({
                    query: `
                        DELETE
                        FROM task_mgmt.group_followers
                        WHERE
                            task_id = ANY($1)
                            AND group_id != ALL($2)
                        RETURNING *`,
                    params: [item_ids, add_group_followers || []],
                });
            }

            if (remOldAssignees) {
                queries.push({
                    query: `
                        WITH deletion AS (
                            DELETE FROM task_mgmt.assignees 
                            WHERE task_id = ANY($1) 
                                AND userid != ALL($2)
                            RETURNING *
                        )
                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                        SELECT task_id, 'assignee', $3, userid, null, $4, null
                        FROM deletion
                        WHERE deletion.userid != $5
                        RETURNING *`,
                    params: [item_ids, assignees || [], now, userid, unassignedTaskAssigneeId],
                });

                queries.push({
                    query: `
                        WITH deletion AS (
                            DELETE FROM task_mgmt.group_assignees 
                            WHERE task_id = ANY($1) 
                                AND group_id != ALL($2) 
                            RETURNING *
                        )
                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) 
                        SELECT task_id, 'group_assignee', $3, group_id, null, $4, null 
                        FROM deletion 
                        RETURNING *`,
                    params: [item_ids, add_group_assignees || [], now, userid],
                });
            }

            if (assignees && assignees.length) {
                item_ids.forEach(item_id => {
                    assigneeFloatObjs.push({ id: item_id, new_assignees: [] });
                    const newTaskAssignees = task_verified_assignees.get(item_id);

                    newTaskAssignees.forEach((userToAdd, index) => {
                        let estimate_value = null;
                        if (item_assignee_estimate_settings[item_id].splitTimeEstimateBetweenAssignees) {
                            estimate_value = items_time_estimate_map[item_id] / newTaskAssignees.length;
                        }

                        let points_value = null;
                        if (
                            item_assignee_estimate_settings[item_id].assignPointsEstimateToFirstAssignee &&
                            !pointsReassignedToGroupAssignee &&
                            index === 0
                        ) {
                            points_value = items_points_estimate_map[item_id];
                        }

                        queries.push({
                            query: `
                                INSERT INTO task_mgmt.assignees
                                    ( task_id, userid, date_assigned, workspace_id, time_estimate, points_float )
                                VALUES
                                    ($1, $2, $3, $4, $5, $6)
                                ON CONFLICT (task_id, userid)
                                DO UPDATE SET 
                                    time_estimate = COALESCE(EXCLUDED.time_estimate, assignees.time_estimate),
                                    points_float = COALESCE(EXCLUDED.points_float, assignees.points_float)
                               `,
                            params: [item_id, userToAdd, new Date().getTime(), team_id, estimate_value, points_value],
                        });

                        if (!item_assignees[item_id].some(assignee => assignee.id === userToAdd)) {
                            // Add to history query
                            if (hist_params.length > 0) {
                                hist_query += '), (';
                            }
                            hist_query += `$${hist_params.length + 1}, $${hist_params.length + 2}, $${
                                hist_params.length + 3
                            }, $${hist_params.length + 4}, $${hist_params.length + 5}, $${hist_params.length + 6}, $${
                                hist_params.length + 7
                            }`;
                            hist_params.push(item_id, 'assignee', now, null, userToAdd, userid, {
                                multitask_toolbar: options.toolbar,
                                mute_notifications: options.mute_notifications,
                            });

                            ovm_field_changes_map[item_id] ??= [];
                            ovm_field_changes_map[item_id].push({ field: 'assignee', before: null, after: userToAdd });
                        }
                    });
                });
            }

            if (rem_assignees && rem_assignees.length > 0) {
                const query = 'DELETE FROM task_mgmt.assignees WHERE userid = ANY($1) AND task_id = ANY($2)';
                const params = [rem_assignees, item_ids];

                queries.push({ query, params });
                if (hiddenHistoryWindow > 0) {
                    queries.push({
                        query: `
                        UPDATE task_mgmt.task_history
                        SET hidden = true 
                        WHERE id IN (
                            SELECT id 
                            FROM task_mgmt.task_history 
                            WHERE task_id = ANY($1) 
                              AND field = $2 
                              AND after = ANY($3) 
                              AND userid = $4 
                              AND date > $5
                        )`,
                        params: [item_ids, 'assignee', rem_assignees, userid, now - hiddenHistoryWindow],
                    });
                }
                queries.push({
                    query: `
                        DELETE FROM task_mgmt.task_notifs 
                        WHERE
                            hist_id IN (
                                SELECT id 
                                FROM task_mgmt.task_history
                                WHERE
                                    task_id = ANY($1)
                                    AND after = ANY($2)
                                    AND field = 'assignee'
                            )
                            AND archived = false`,
                    params: [item_ids, rem_assignees],
                });

                if (estimates_per_assignee || points_per_assignee) {
                    item_ids.forEach(item_id => {
                        accountForDeletedAssignees({
                            virtualAssigneeHandler: virtualTaskAssigneeHandlers.get(item_id),
                            existingAssignees: item_assignees[item_id],
                            rem_assignees,
                            estimates_per_assignee,
                            points_per_assignee,
                        });
                    });
                }
            }

            // unless User explicitly remove himself or removed all watchers.
            if (
                user_wants_to_follow_task_edits &&
                !rem_followers.includes(userid) &&
                !remOldGroupFollowers &&
                !remOldFollowers
            ) {
                // add User as a Follower to all edited Tasks
                const add_user_follower_query = addUserFollowerQuery(userid, item_ids, team_id);

                queries.push(add_user_follower_query);
            }

            if (!customType.isUnset()) {
                // create task history for custom type
                item_ids.forEach(item_id => {
                    const { custom_type: old_custom_type = null } = old_item_custom_types_map[item_id] || {};

                    if (old_custom_type === customType.value) {
                        return;
                    }

                    if (hist_params.length > 0) {
                        hist_query += '), (';
                    }

                    hist_query += `$${hist_params.length + 1}, $${hist_params.length + 2}, $${
                        hist_params.length + 3
                    }, $${hist_params.length + 4}, $${hist_params.length + 5}, $${hist_params.length + 6}, $${
                        hist_params.length + 7
                    }`;
                    hist_params.push(item_id, 'custom_type', now, old_custom_type, customType.value, userid, null);
                });
            }

            if (isSummaryTaskV2Enabled && is_summary_task != null && summary_tasks_state) {
                try {
                    const update_summary_state_options = {
                        dont_fail: true,
                        history_params_length: hist_params.length,
                    };

                    const { tasks_update_query, history_query } = prepareUpdateSummaryTasksQueries(
                        userid,
                        is_summary_task,
                        item_ids,
                        summary_tasks_state,
                        update_summary_state_options
                    );

                    if (tasks_update_query) {
                        queries.push(tasks_update_query);
                    }

                    if (history_query) {
                        const { query, params } = history_query;
                        hist_query += query;
                        hist_params.push(...params);
                    }

                    if (is_summary_task) {
                        const set_summary_tasks_dates_options = {
                            mute_notifications: options.mute_notifications,
                        };

                        const dates_update_queries = await prepareSetSummaryTasksDatesQueries(
                            CLICKBOT,
                            item_ids,
                            summary_tasks_state,
                            team_id,
                            set_summary_tasks_dates_options
                        );
                        queries.push(...dates_update_queries);
                    }
                } catch (err1) {
                    cb({
                        msg: err1,
                        status: 500,
                        ECODE: 'GANTT_038',
                    });
                    return;
                }
            }

            hist_query += `) RETURNING *`;

            const virtualAssigneeQueries = getVirtualAssigneeQueries(
                virtualTaskAssigneeHandlers,
                item_assignee_estimate_settings,
                item_virtual_assignees,
                team_id,
                now
            );
            if (virtualAssigneeQueries.length > 0) {
                queries.push(...virtualAssigneeQueries);
            }

            try {
                // If points per assignee mode is enabled, points need to be updated differently than on the task model.
                if (estimatesSupportInBulkActions() && points !== undefined && points_per_assignee) {
                    const pointQueries = getQueriesForPointsUpdate(
                        item_assignee_estimate_settings,
                        item_assignees,
                        item_group_assignees,
                        result.areClickappsEnabledByTaskId,
                        points,
                        now,
                        userid,
                        team_id
                    );
                    queries.push(...pointQueries);
                }

                if (estimatesSupportInBulkActions() && time_estimate !== undefined && estimates_per_assignee) {
                    const timeEstimateQueries = getQueriesForTimeEstimateUpdate(
                        item_assignee_estimate_settings,
                        item_assignees,
                        result.areClickappsEnabledByTaskId,
                        time_estimate,
                        now,
                        userid,
                        team_id
                    );
                    queries.push(...timeEstimateQueries);
                }
            } catch (err1) {
                cb({
                    msg: err1,
                    status: 500,
                    ECODE: 'ITEM_82',
                });
                return;
            }

            // create undo body
            /**
             * @type {{ assignees: { add: *[], rem: *[] }, followers: { add: *[], rem: *[] }, removed_followers: *[], versions?: (string|null)[] }}
             */
            const undo_body = {
                assignees: { add: [], rem: [] },
                followers: { add: [], rem: [] },
                removed_followers,
                task_scheduling_v2_info: taskSchedulingV2Info,
            };
            undo_body.item_ids = item_ids;
            if (assignees) {
                undo_body.assignees.rem = assignees;
            }
            if (rem_assignees) {
                undo_body.assignees.add = rem_assignees;
            }
            if (followers) {
                undo_body.followers.rem = followers;
            }
            if (rem_followers) {
                undo_body.followers.add = rem_followers;
            }
            if (status) {
                undo_body.orderindices = item_orderindex;
            }
            undo_body.new_indexes = item_orderindex;

            if (assignees_unaccessible_tasks) {
                undo_body.unaccessible_tasks = assignees_unaccessible_tasks;
            }

            getConn(cb, { label: 'edit items' }, (connErr, client, done) => {
                if (connErr) {
                    return;
                }
                client.query('BEGIN', beginErr => {
                    if (beginErr) {
                        rollback(client, done);
                        logger.error({
                            msg: 'Failed to begin',
                            err: beginErr,
                            status: 500,
                            ECODE: 'ITEM_041',
                        });
                        cb({
                            err: 'Internal server error',
                            status: 500,
                            ECODE: 'ITEM_041',
                        });
                        return;
                    }
                    const assignee_hist_items = [];
                    const hist_ids = {};
                    const before_hist_ids = {};
                    const remapped_due_dates = [];
                    const dependencies_adjusted = [];
                    const versionUpdates = [];
                    const historyRows = [];

                    async.parallel(
                        {
                            editMultiple(para_cb) {
                                async.eachSeries(
                                    queries,
                                    (query, each_cb) => {
                                        client.query(query.query, query.params, (queryErr, queryResult) => {
                                            if (queryErr) {
                                                const extra = {
                                                    query: query.query,
                                                    params: JSON.stringify(query.params),
                                                };

                                                each_cb(new TaskError(queryErr, 'ITEM_572', 500, extra));
                                            } else if (queryResult.rows.length > 0) {
                                                queryResult.rows.forEach(row => {
                                                    if (
                                                        row.operation === 'remap' ||
                                                        row.operation === 'remap_dependency'
                                                    ) {
                                                        remapped_due_dates.push(row);
                                                        dependencies_adjusted.push({
                                                            id: row.id,
                                                            diff: parseInt(row.due_date_diff, 10),
                                                            hist_id: row.hist_id,
                                                        });
                                                    } else if (row.id) {
                                                        assignee_hist_items.push(row);
                                                        if (!hist_ids[row.task_id]) {
                                                            hist_ids[row.task_id] = [];
                                                        }
                                                        hist_ids[row.task_id].push(row.id);
                                                    }

                                                    if (row.before_hist_id) {
                                                        if (!before_hist_ids[row.task_id]) {
                                                            before_hist_ids[row.task_id] = [];
                                                        }

                                                        before_hist_ids[row.task_id].push(row.before_hist_id);
                                                    }

                                                    if (row.field === 'assignee' && row.task_id) {
                                                        ovm_field_changes_map[row.task_id] ??= [];
                                                        ovm_field_changes_map[row.task_id].push({
                                                            field: 'assignee',
                                                            before: row.before,
                                                            after: row.after,
                                                            history_id: row.id,
                                                        });
                                                    }

                                                    if (row.updated_task_id) {
                                                        versionUpdates.push({
                                                            object_type: ObjectType.TASK,
                                                            object_id: row.updated_task_id,
                                                            workspace_id: team_id,
                                                            operation: OperationType.UPDATE,
                                                            data: {
                                                                context: { ws_key: options.ws_key },
                                                            },
                                                        });
                                                    }
                                                });
                                                each_cb();
                                            } else {
                                                each_cb();
                                            }
                                        });
                                    },
                                    async eachErr => {
                                        if (eachErr) {
                                            rollback(client, done);
                                            logger.error({
                                                msg: 'Failed to edit multiple items',
                                                err: eachErr,
                                                status: 500,
                                                ECODE: 'ITEM_042',
                                            });
                                            para_cb({
                                                err: 'Internal server error',
                                                status: 500,
                                                ECODE: 'ITEM_042',
                                            });
                                        } else {
                                            if (!customType.isUsableCustomType()) {
                                                para_cb();
                                                return;
                                            }

                                            try {
                                                await entitlementService.incrementEntitlement(
                                                    team_id,
                                                    EntitlementName.CustomItems,
                                                    {
                                                        increment: item_ids.length,
                                                    }
                                                );
                                            } catch (error) {
                                                para_cb({
                                                    err: 'Internal server error',
                                                    status: 500,
                                                    ECODE: 'ITEM_178',
                                                });
                                            }

                                            para_cb();
                                        }
                                    }
                                );
                            },

                            histQuery(para_cb) {
                                if (hist_params.length === 0) {
                                    para_cb(null, { rows: [] });
                                    return;
                                }
                                client.query(hist_query, hist_params, (histErr, histResult) => {
                                    if (histErr) {
                                        rollback(client, done);
                                        logger.error({
                                            msg: 'Failed to edit multiple items',
                                            err: histErr,
                                            status: 500,
                                            ECODE: 'ITEM_052',
                                        });
                                        para_cb({
                                            err: 'Internal server error',
                                            status: 500,
                                            ECODE: 'ITEM_052',
                                        });
                                    } else {
                                        histResult.rows.forEach(row => {
                                            if (!hist_ids[row.task_id]) {
                                                hist_ids[row.task_id] = [];
                                            }
                                            hist_ids[row.task_id].push(row.id);
                                        });
                                        historyRows.push(...histResult.rows);
                                        para_cb(null, histResult);
                                    }
                                });
                            },
                        },
                        async (paraErr, paraResult) => {
                            if (paraErr) {
                                logger.error({
                                    msg: 'Failed to edit multiple items',
                                    err: paraErr,
                                    status: 500,
                                });
                                cb(paraErr);
                                return;
                            }

                            let events;
                            const ovm = getObjectVersionManager();
                            try {
                                const txClient = new TransactionClientImpl(client, ovm);

                                for (const item_id of item_ids) {
                                    const data = {
                                        context: {
                                            ws_key: options.ws_key,
                                            originating_service: originating_service_override,
                                        },
                                    };

                                    if (Object.prototype.hasOwnProperty.call(ovm_field_changes_map, item_id)) {
                                        data.changes = filterOvmChangeValues({
                                            changes: ovm_field_changes_map[item_id],
                                            workspaceId: team_id,
                                        });
                                    }
                                    const item = tasks?.[item_id];
                                    if (item?.parent_task_id) {
                                        data.relationships = getParentTaskChangeEventRelationship({
                                            parent_task_ids: [item.parent_task_id],
                                            team_id,
                                            operation: OperationType.UPDATE,
                                        });
                                    }
                                    versionUpdates.push({
                                        object_type: ObjectType.TASK,
                                        object_id: item_id,
                                        workspace_id: team_id,
                                        operation: OperationType.UPDATE,
                                        data,
                                    });
                                }

                                otherTasksToUpdate.forEach(taskId => {
                                    versionUpdates.push({
                                        object_type: ObjectType.TASK,
                                        object_id: taskId,
                                        workspace_id: team_id,
                                        operation: OperationType.UPDATE,
                                        data: {
                                            context: { ws_key: options.ws_key },
                                        },
                                    });
                                });

                                updateObjectVersionUpdatesFromTaskHistoryRows(versionUpdates, historyRows);
                                events = await ovm.updateVersions(txClient, versionUpdates);
                            } catch (version_err) {
                                rollback(client, done);
                                cb(new TaskError(version_err, 'ITEM_244'));
                                return;
                            }

                            client.query('COMMIT', async commitErr => {
                                if (commitErr) {
                                    rollback(client, done);
                                    logger.error({
                                        msg: 'Failed to commit',
                                        err: commitErr,
                                        status: 500,
                                        ECODE: 'ITEM_043',
                                    });
                                    cb({
                                        err: 'Internal server error',
                                        status: 500,
                                        ECODE: 'ITEM_043',
                                    });
                                    return;
                                }
                                done();

                                try {
                                    await setDatesAndOrderindex({
                                        item_ids,
                                        team_id,
                                        new_inbox_users,
                                        dateOfTasksBeforeEdit,
                                        trigger_id: options.trigger_id,
                                        auto_id: options.auto_id,
                                    });
                                } catch (dateErr) {
                                    logger.error({
                                        msg: 'Failed to set task dates and orderindex',
                                        err: dateErr,
                                        status: 500,
                                    });
                                    cb(dateErr);
                                    return;
                                }

                                try {
                                    if (due_date) {
                                        const item_ids_with_changed_due_date = item_ids.filter(item_id => {
                                            const old_due_date = item_due_dates[item_id]?.due_date;
                                            return due_date !== old_due_date;
                                        });
                                        if (item_ids_with_changed_due_date.length) {
                                            await updateRecurringForDueDateChange(
                                                userid,
                                                item_ids_with_changed_due_date,
                                                due_date,
                                                due_date_time
                                            );
                                        }
                                    }
                                } catch (recurErr) {
                                    logger.error({
                                        msg: 'Failed to update recurring settings',
                                        err: recurErr,
                                        status: 500,
                                    });
                                    cb(recurErr);
                                    return;
                                }

                                // eslint-disable-next-line @typescript-eslint/no-empty-function
                                await ovm.notifyChanges(events).catch(() => {});

                                const eventMap =
                                    events?.reduce((acc, event) => {
                                        acc[event.object_id] = event;
                                        return acc;
                                    }, {}) || {};

                                undo_body.versions = [];
                                for (const item_id of undo_body.item_ids) {
                                    if (eventMap[item_id]?.object_type === ObjectType.TASK) {
                                        undo_body.versions.push(eventMap[item_id]);
                                    } else {
                                        undo_body.versions.push(null);
                                    }
                                }

                                undo_body.hist_ids = hist_ids;
                                undo_body.dependencies_adjusted = dependencies_adjusted;
                                undo_body.dependencies_adjusted.forEach(task_obj => {
                                    if (!undo_body.hist_ids[task_obj.id]) {
                                        undo_body.hist_ids[task_obj.id] = [];
                                    }
                                    undo_body.hist_ids[task_obj.id].push(task_obj.hist_id);

                                    if (!undo_body.item_ids.includes(task_obj.id)) {
                                        undo_body.item_ids.push(task_obj.id);
                                    }
                                });

                                const unresolved_queries = [];

                                async.series(
                                    [
                                        series_cb => {
                                            if (!options.insertUnresolvedQueries) {
                                                series_cb();
                                                return;
                                            }

                                            item_ids.forEach(id => {
                                                if (subtasks_to_resolve[id] && subtasks_to_resolve[id].length) {
                                                    unresolved_queries.push(
                                                        ...unresolvedMW._getNestedQueries(
                                                            userid,
                                                            id,
                                                            subtasks_to_resolve[id],
                                                            options.unapply_to_archived
                                                        )
                                                    );
                                                }
                                            });

                                            series_cb();
                                        },
                                        series_cb => {
                                            if (!options.insertUnresolvedQueries) {
                                                series_cb();
                                                return;
                                            }

                                            unresolved_queries.push(
                                                ...unresolvedMW._getQueries(
                                                    userid,
                                                    item_ids,
                                                    options.unapply_to_archived
                                                )
                                            );

                                            series_cb();
                                        },
                                        async series_cb => {
                                            if (unresolved_queries.length === 0) {
                                                series_cb();
                                                return;
                                            }

                                            let transactionClient;
                                            try {
                                                transactionClient = await getTransactionClientAsync(ovm);
                                                await transactionClient.beginAsync();

                                                const results = await batchQueriesSeriesWithClientAsync(
                                                    unresolved_queries,
                                                    transactionClient,
                                                    false
                                                );

                                                const uqVersionUpdates = results.reduce((acc, row) => {
                                                    if (row.updated_task_id) {
                                                        acc.push({
                                                            object_type: ObjectType.TASK,
                                                            object_id: row.updated_task_id,
                                                            workspace_id: team_id,
                                                            operation: OperationType.UPDATE,
                                                            data: {
                                                                context: { ws_key: options.ws_key },
                                                            },
                                                        });
                                                    }
                                                    if (row.updated_comment_id) {
                                                        acc.push({
                                                            object_type: ObjectType.COMMENT,
                                                            object_id: row.updated_comment_id,
                                                            workspace_id: team_id,
                                                            operation: OperationType.UPDATE,
                                                            data: {
                                                                context: { ws_key: options.ws_key },
                                                            },
                                                        });
                                                    }
                                                    if (row.updated_threaded_comment_id) {
                                                        acc.push({
                                                            object_type: ObjectType.COMMENT,
                                                            object_id: row.updated_threaded_comment_id,
                                                            workspace_id: team_id,
                                                            operation: OperationType.UPDATE,
                                                            data: {
                                                                context: { ws_key: options.ws_key },
                                                            },
                                                        });
                                                    }
                                                    return acc;
                                                }, []);

                                                let versionVectors;

                                                if (uqVersionUpdates.length) {
                                                    updateObjectVersionUpdatesFromTaskHistoryRows(
                                                        uqVersionUpdates,
                                                        results
                                                    );
                                                    const uqEvents = await ovm.updateVersions(
                                                        transactionClient,
                                                        uqVersionUpdates
                                                    );

                                                    versionVectors = uqEvents.map(
                                                        ({
                                                            workspace_id,
                                                            object_type,
                                                            object_id,
                                                            master_id,
                                                            version,
                                                        }) => ({
                                                            workspace_id,
                                                            object_type,
                                                            object_id,
                                                            vector: [{ version, master_id }],
                                                        })
                                                    );
                                                    transactionClient.recordEvents(uqEvents);
                                                }

                                                await transactionClient.commitAsync();

                                                const updatedCommentsIds = uqVersionUpdates
                                                    ?.filter(
                                                        update =>
                                                            update.object_type === ObjectType.COMMENT &&
                                                            update.operation === OperationType.UPDATE
                                                    )
                                                    .map(comment => comment.object_id);
                                                if (updatedCommentsIds?.length) {
                                                    try {
                                                        const resolvedComments = await getUpdatedResolvedComments({
                                                            userid,
                                                            updatedCommentsIds,
                                                            versionVectors,
                                                        });
                                                        if (resolvedComments?.length) {
                                                            undo_body.comments = resolvedComments;
                                                        }
                                                    } catch (e) {
                                                        series_cb();
                                                    }
                                                }
                                                series_cb();
                                            } catch (uqErr) {
                                                await transactionClient?.rollbackAsync(uqErr);
                                                series_cb(uqErr);
                                            }
                                        },
                                        async series_cb => {
                                            if (floatObjs.length === 0) {
                                                series_cb();
                                                return;
                                            }

                                            undo_body.new_indexes = {};

                                            try {
                                                for (let i = 0; i < floatObjs.length; i += 1) {
                                                    const { id } = floatObjs[i];

                                                    const { orderindex } = await setTaskAtMaxIndex(id, 'orderindex');

                                                    undo_body.new_indexes[id] = orderindex;
                                                }
                                                series_cb();
                                            } catch (e) {
                                                series_cb(e);
                                            }
                                        },
                                        async series_cb => {
                                            if (priorityFloatObjs.length === 0) {
                                                series_cb();
                                                return;
                                            }

                                            undo_body.new_indexes = {};

                                            try {
                                                for (let i = 0; i < priorityFloatObjs.length; i += 1) {
                                                    const { id } = priorityFloatObjs[i];

                                                    const { orderindex } = await setTaskAtMaxIndex(
                                                        id,
                                                        'priority_orderindex'
                                                    );

                                                    undo_body.new_indexes[id] = orderindex;
                                                }
                                                series_cb();
                                            } catch (e) {
                                                series_cb(e);
                                            }
                                        },
                                        async series_cb => {
                                            if (assigneeFloatObjs.length === 0) {
                                                cb(null, undo_body);
                                                series_cb();
                                                return;
                                            }

                                            undo_body.new_indexes = {};

                                            try {
                                                for (let i = 0; i < assigneeFloatObjs.length; i += 1) {
                                                    const { id } = assigneeFloatObjs[i];

                                                    const { orderindex } = await setTaskAtMaxIndex(
                                                        id,
                                                        'assignee_orderindex'
                                                    );

                                                    undo_body.new_indexes[id] = orderindex;
                                                }
                                                cb(null, undo_body);
                                                series_cb();
                                            } catch (e) {
                                                cb(e, undo_body);
                                                series_cb(e);
                                            }
                                        },
                                    ],
                                    () => {
                                        addTasksToES(userid, item_ids);

                                        if (checkRecur && checkRecur.length) {
                                            recurrence.checkShouldRecur(checkRecur, { userid });
                                        }

                                        if (!options.mute_notifications) {
                                            paraResult.histQuery.rows.forEach(row => {
                                                if (row.field === 'follower' && row.after) {
                                                    // add notif for follower added for only that follower
                                                    sqsNotif.sendNotifMessage('createTaskNotification', [
                                                        row.after,
                                                        row.task_id,
                                                        row.id,
                                                    ]);
                                                } else if (
                                                    (row.before && row.field === 'assignee') ||
                                                    row.field === 'follower'
                                                ) {
                                                    // do nothing
                                                } else {
                                                    sqsNotif.sendNotifMessage('createTaskNotifications', [
                                                        userid,
                                                        row.task_id,
                                                        row.id,
                                                        {
                                                            field: row.field,
                                                        },
                                                    ]);
                                                }
                                            });

                                            assignee_hist_items.forEach(row => {
                                                if (row.operation === 'deleted') {
                                                    // skip
                                                } else if (row.field === 'follower' && row.after) {
                                                    // add notif for follower added for only that follower
                                                    sqsNotif.sendNotifMessage('createTaskNotification', [
                                                        row.after,
                                                        row.task_id,
                                                        row.id,
                                                    ]);
                                                } else if (
                                                    (row.before && row.field === 'assignee') ||
                                                    row.field === 'follower'
                                                ) {
                                                    // do nothing
                                                } else {
                                                    sqsNotif.sendNotifMessage('createTaskNotifications', [
                                                        userid,
                                                        row.task_id,
                                                        row.id,
                                                        {
                                                            field: row.field,
                                                        },
                                                    ]);
                                                }
                                            });
                                        }

                                        if (rem_assignees.length > 0) {
                                            item_ids.forEach(item_id => {
                                                _removeOldAssigneeNotifications(item_id);
                                                _removeFollowerIfNecessary(
                                                    rem_assignees,
                                                    { workspace_id: team_id },
                                                    item_id
                                                );
                                            });
                                        }

                                        _unblockedNotificationIfNecessary(item_ids, {});
                                        _deleteUnblockedNotificationsIfNecessary(item_ids, {});
                                        CUTasksUpdated(item_ids);
                                        CUTasksUpdated(remapped_due_dates.map(task => task.id));
                                        remapped_due_dates.forEach(task => {
                                            item_ids.push(task.id);

                                            if (!hist_ids[task.id]) {
                                                hist_ids[task.id] = [];
                                            }

                                            if (!hist_ids[task.id].includes(task.hist_id)) {
                                                hist_ids[task.id].push(task.hist_id);
                                            }
                                        });

                                        if (status) {
                                            subcatHelper.invalidateCachedSubcatTaskCount(subcategoriesToInvalidate);
                                        }
                                        subcatHelper.invalidateCachedSubcatTaskCount(subcategories);

                                        const log_trail = shouldLogTrail(team_id);

                                        Object.keys(hist_ids).forEach(task_id => {
                                            const _hist_ids = hist_ids[task_id] || [];
                                            const _before_hist_ids = before_hist_ids[task_id] || [];
                                            const bulk_lane =
                                                (_hist_ids.length + _before_hist_ids.length) * item_ids.length >
                                                config.automation.lanes.bulk.hist_limit;

                                            webhook.sendWebhookMessage('taskUpdated', {
                                                task_id,
                                                hist_ids: _hist_ids,
                                                bulk_lane,
                                                log_trail,
                                                team_id,
                                                userid,
                                            });
                                        });

                                        Object.keys(before_hist_ids).forEach(task_id => {
                                            const _hist_ids = hist_ids[task_id] || [];

                                            const before_hists = before_hist_ids[task_id].filter(
                                                h_id => !_hist_ids.includes(h_id)
                                            );

                                            if (!before_hists.length) {
                                                return;
                                            }

                                            const bulk_lane =
                                                (_hist_ids.length + before_hists.length) * item_ids.length >
                                                config.automation.lanes.bulk.hist_limit;

                                            webhook.sendWebhookMessage('taskUpdated', {
                                                task_id,
                                                hist_ids: before_hists,
                                                bulk_lane,
                                                log_trail,
                                                team_id,
                                                userid,
                                            });
                                        });

                                        // Fire and forget instant Slapdash status update.
                                        sdAssetsHotUpdateNoWait(
                                            logger,
                                            userid,
                                            team_id,
                                            'task',
                                            item_ids.map(item_id => ({
                                                item_id,
                                                status_type: newStatusesByTaskId?.[item_id].type,
                                                updatedAt: new Date(now),
                                            }))
                                        );

                                        _getItems(item_ids, { userid }, (getErr, items) => {
                                            if (getErr) {
                                                return;
                                            }

                                            postTaskUpdatedNotification(
                                                userid,
                                                items,
                                                item_statuses,
                                                status,
                                                assignees,
                                                rem_assignees
                                            );

                                            if (status) {
                                                // I am not sure why here we process only unique parents, so I try to keep this logic
                                                const uniqueItemsByParent = [
                                                    ...new Map(items.map(item => [item.parent, item])).values(),
                                                ];
                                                uniqueItemsByParent.forEach(item => {
                                                    triggerSubtasksResolvedEvent(
                                                        item.parent,
                                                        options.auto_id,
                                                        options.trigger_id,
                                                        team_id,
                                                        item.subtask_parent
                                                    );
                                                });
                                            }

                                            items.forEach(item => {
                                                if (newStatusesByTaskId && newStatusesByTaskId[item.id]) {
                                                    item.status = newStatusesByTaskId[item.id];
                                                }

                                                item.project = {
                                                    id: item.project_id,
                                                    name: item.project_name,
                                                };
                                                if (due_date) {
                                                    createDueDateNotifs({
                                                        due_date,
                                                        due_date_time,
                                                        task_id: item.id,
                                                        team_id,
                                                    }).catch(notifErr => {
                                                        logger.error({
                                                            msg: 'Failed to create due date notifs',
                                                            err: notifErr,
                                                        });
                                                    });
                                                }
                                                if (start_date) {
                                                    createStartDateNotifs({
                                                        start_date,
                                                        start_date_time,
                                                        task_id: item.id,
                                                        team_id,
                                                    }).catch(notifErr => {
                                                        logger.error({
                                                            msg: 'Failed to create start date notifs',
                                                            err: notifErr,
                                                        });
                                                    });
                                                }
                                                if (shouldLogEditTaskDateConsistency()) {
                                                    logDateConsistency(taskParams, item, {
                                                        start_date: item_start_dates[item.id]?.start_date,
                                                        start_date_time: item_start_dates[item.id]?.start_date_time,
                                                        due_date: item_due_dates[item.id]?.due_date,
                                                        due_date_time: item_due_dates[item.id]?.due_date_time,
                                                        id: item.id,
                                                    });
                                                }
                                            });
                                        });
                                    }
                                );
                            });
                        }
                    );
                });
            });
        }
    );
}

function mergeFieldChangesMaps(map1, map2) {
    return Object.keys(map2).reduce(
        (mergedMap, key) => {
            if (Object.prototype.hasOwnProperty.call(mergedMap, key)) {
                mergedMap[key] = mergedMap[key].concat(map2[key]);
            } else {
                mergedMap[key] = map2[key];
            }
            return mergedMap;
        },
        { ...map1 }
    );
}

function getVirtualAssigneeQueries(
    virtualTaskAssigneeHandlers,
    item_assignee_estimate_settings,
    item_virtual_assignees,
    team_id,
    now
) {
    const taskVirtualAssignees = [];

    for (const [taskId, handler] of virtualTaskAssigneeHandlers.entries()) {
        if (item_assignee_estimate_settings[taskId].allTaskAssigneesRemoved) {
            handler.onAllAssigneesRemoved();
        }

        taskVirtualAssignees.push({
            handler,
            hadVirtualAssignee: () => item_virtual_assignees[taskId],
        });
    }

    return getVirtualTaskAssigneeQueries({
        taskVirtualAssignees,
        workspaceId: team_id,
        now,
    });
}

function accountForDeletedAssignees({
    virtualAssigneeHandler,
    existingAssignees,
    rem_assignees,
    estimates_per_assignee,
    points_per_assignee,
}) {
    rem_assignees.forEach(assigneeId => {
        const existingAssignee = existingAssignees.find(assignee => assignee.id === assigneeId);
        if (existingAssignee) {
            if (estimates_per_assignee) {
                virtualAssigneeHandler.onAssigneeRemoved(
                    existingAssignee.id,
                    EstimateType.Time,
                    Number(existingAssignee.time_estimate)
                );
            }

            if (points_per_assignee) {
                virtualAssigneeHandler.onAssigneeRemoved(
                    existingAssignee.id,
                    EstimateType.Points,
                    Number(existingAssignee.points)
                );
            }
        }
    });
}

function accountForDeletedGroupAssignees({ virtualAssigneeHandler, existingGroupAssignees, rem_group_assignees }) {
    rem_group_assignees.forEach(assigneeId => {
        const existingAssignee = existingGroupAssignees.find(assignee => assignee.id === assigneeId);
        if (existingAssignee) {
            virtualAssigneeHandler.onGroupAssigneeRemoved(EstimateType.Points, Number(existingAssignee.points));
        }
    });
}
