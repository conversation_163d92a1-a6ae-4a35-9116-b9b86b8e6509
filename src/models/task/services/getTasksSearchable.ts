import { compact } from 'lodash';
import { Object<PERSON>ey, ObjectVersionVector } from '@time-loop/ovm-object-version';

import { shouldUseObjectCacheManagerOnRead } from '@clickup/object-version-split-treatments';
import { ObjectCacheManagerSingleton } from '@clickup/object-version-manager-singleton';
import { ObjectKeyObjectFactory } from '@clickup/object-version-cache-common';

import { TaskErrorCodes } from '../../../utils/errors/constants';
import { metricsClient } from '../../../metrics/metricsClient';
import { TaskInternalProperties, TaskSearchableInfo } from '../interfaces/Task';
import { getTasksSearchableInfo as queryTasksSearchableInfo } from '../datastores/CRUD/getDataStore';
import { buildHierarchyInfoMap, HierarchyInfo, isArrayAllNull } from './getTasksInfo';
import { taskSearchableFallbackFn } from '../factories/cacheFallbackFactory';
import { ClickUpError } from '../../../utils/errors';

export enum MetricNames {
    CACHE_TRUE = 'getTasksSearchableInfo.use_cache.true',
    CACHE_FALSE = 'getTasksSearchableInfo.use_cache.false',
}

export type TaskSearchable = TaskSearchableInfo & HierarchyInfo;

export const TasksSearchableError = ClickUpError.makeNamedError('GetTasksSearchable');

const CACHE_NAME = 'Task-Searchable';

/**
 * Service function dedicated to getting task searchable info. Used for Search Indexing of tasks.
 * @returns Returns an array of tasks info. If a task was not found it will be null in the resulting array.
 */
// TODO: is it used anywhere?
export async function getTasksSearchable(
    taskIds: string[],
    keys?: ObjectKey[],
    versions?: ObjectVersionVector[],
    useReplica = false
): Promise<(TaskSearchable | null)[]> {
    try {
        let partialTasksInfo: Partial<TaskInternalProperties>[];
        const cache =
            shouldUseObjectCacheManagerOnRead(CACHE_NAME) && keys?.length
                ? ObjectCacheManagerSingleton.getCache(CACHE_NAME, new ObjectKeyObjectFactory(taskSearchableFallbackFn))
                : null;

        if (cache) {
            metricsClient.increment(MetricNames.CACHE_TRUE);
            const missingVectors = versions?.some(v => !v.vector.length);
            const taskObjects =
                versions?.length && !missingVectors
                    ? await cache.getObjectsForVectors(keys, versions)
                    : await cache.getObjects(keys);

            if (isArrayAllNull(taskObjects)) {
                return taskIds.map(_ => null);
            }

            partialTasksInfo = compact(taskObjects.map(t => t?.value));
        } else {
            metricsClient.increment(MetricNames.CACHE_FALSE);
            const taskObjectsResult = await queryTasksSearchableInfo(taskIds, useReplica);

            if (taskObjectsResult.rows.length === 0) {
                return taskIds.map(_ => null);
            }
            partialTasksInfo = taskObjectsResult.rows;
        }

        partialTasksInfo = partialTasksInfo.map(task => task ?? null);

        const taskIdToHierarchyMap = await buildHierarchyInfoMap(partialTasksInfo);
        const taskIdToInfoMap: Record<string, TaskSearchable> = {};
        partialTasksInfo.forEach(task => {
            if (task?.id) {
                taskIdToInfoMap[task.id] = {
                    ...task,
                    ...taskIdToHierarchyMap[task.id],
                } as TaskSearchable;
            }
        });
        // Ensure we return the result in the same order as the input array.
        return taskIds.map(id => taskIdToInfoMap[id] || null);
    } catch (err) {
        throw new TasksSearchableError(err, TaskErrorCodes.GetTasksSearchableError);
    }
}
