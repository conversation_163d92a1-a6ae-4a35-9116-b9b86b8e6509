/* eslint-disable max-len */
import config from 'config';

import moment from 'moment-timezone';
import { ObjectType, OperationType } from '@time-loop/ovm-object-version';
import { HierarchyPermissionLevel } from '@clickup/utils/authorization/models';
import { filterOvmChangeValues } from '@clickup-legacy/models/task/ovm/filter-ovm-change-values';
import { appendTaskAccessFanOutEvent } from '@clickup-legacy/models/task/ovm/append-task-access-fan-out-event';
import { getParentTaskChangeEventRelationship } from '@clickup-legacy/models/task/ovm/relationships';
import { toQuillDelta, sanitizeXssAttackForCommentParts } from '@time-loop/common-utils';
import { EntityType as EntityTypeConst } from '@clickup/utils/constants';

import {
    includeTaskParentsIntoUpdateEventsOnFieldValueChange,
    excludeAutomationDataFromTaskUpdateHistory,
} from '@clickup-legacy/models/integrations/split/squadTreatments/fieldTreatments';

import { objectIdsForParentTasks } from '@clickup-legacy/models/task/factories/ovmFanOutQueryFactory';
import { checkAccessTaskOrTaskAsObjectCb } from '@clickup-legacy/models/task_as_object/accessChecks';
import { taskAsObjectConfig } from '@clickup-legacy/models/integrations/split/squadTreatments/taskTreatments';
import { CustomType } from '@clickup/task/extensions';
import { dateInterval, isValidDateInterval, compareDateIntervals } from '@clickup/scheduler/common';
import { shouldSendDeleteTaskAccessMessage } from '@clickup/websocket/split-treatments';
import { validateCustomTypeAllowedForTask } from '@clickup-legacy/models/task/validation/custom-type-validation';
import { copyAttachmentsAsync } from '@clickup-legacy/models/attachment/attachmentCopy';
import { deleteAttachmentsByIdAsync } from '@clickup-legacy/models/attachment/services/updateAttachmentParent';
import { getAttachmentById } from '@clickup-legacy/models/attachment/attachment';
import { aiSkipAccessForAgentUser } from '@clickup-legacy/models/integrations/split/squadTreatments/aiTreatments';
import { isAgentUserRequest } from '@clickup-legacy/models/ai/agents';
import { getLogger } from '@clickup/shared/utils-logging';
import { updateRecurringForDueDateChange } from '@clickup-legacy/models/recurrence_2';
import { adjustAutomationsAfterEditTask } from '@clickup-legacy/models/task/helpers/editTasksHelper';
import { getDateOfTasks, getSubtaskIds } from '@clickup-legacy/models/task/datastores/getTaskDatastore';
import uniq from 'lodash/uniq';
import { updateDatesService } from '../../scheduling/datesUpdateServices';
import { createContentChangedNotif } from '../helpers/editTasksHelper';
import * as async from '../../../utils/asyncHelper';
import { metricsClient } from '../../../metrics/metricsClient';
import * as access from '../../../utils/access2';
import * as db from '../../../utils/db';
import * as dependencyMod from '../../dependencies/taskDependencies.service';
import * as delayedNotifs from '../../notifications/delayedNotification';
import * as dependencyRemapMod from '../../dependencies/remapDependencies.service';
import * as encrypt from '../../../utils/encrypt';
import * as getTask from './getTask';
import { getSummaryTaskBehavior } from '../../scheduling/summary_task/services/getSummaryInfo';
import { handleSummaryTaskDateUpdates } from '../../scheduling/summary_task/services/handleSummaryTaskDateUpdates';
import { checkForDurationChanges } from '../../scheduling/summary_task/services/summaryTaskUtils';
import * as floatOrder from '../../ordering/task/controllers';
import * as followersMod from '../../follower/follower';
import * as googleCal from '../../integrations/googleCalendar/taskNotifications';
import * as input from '../../../utils/input_validation';

import {
    hideRecentActivityHistoryWindow,
    shouldRemoveSubtasksFollowersAfterParentTaskPrivacyChange,
    shouldUseRecursiveParentValidation,
    shouldUseHiddenCoverImageCopy,
    shouldLogEditTaskDateConsistency,
} from '../../integrations/split/squadTreatments/taskTreatments';

import { shouldUseBreakEditTaskTransaction } from '../../integrations/split/squadTreatments/unclaimedTreatments';
import { isWriteToRecentTableDisabled } from '../../integrations/split/squadTreatments/userPlatformTreatments';
import { isDefaultLocationPermissionsEnabled } from '../../integrations/split/squadTreatments/accessManagementTreatments';

import {
    shouldUseRemapDependencyV2,
    shouldUseRemapSubtasksV2,
    summaryTaskV2Enabled,
    isDurationEnabled,
} from '../../integrations/split/squadTreatments/projectManagementTreatments';

import * as mentionMod from '../itemMentions';
import {
    postStatusChangedNotification,
    postAssigneesChangedNotification,
} from '../../integrations/notifications/notifications';
import * as privacy from '../../privacy';
import * as remapMW from '../../due_dates/remapMW';
import * as quill from '../../../utils/quill';
import * as recurrence from '../../recurrence';
import * as sqsNotif from '../../../utils/sqs-notif';
import * as sqsWs from '../../../utils/v1-ws-message-sending';
import { toBoolean } from '../../helpers';
import { coeditorClientInstance } from '../../../clients/CoeditorClient';
import { EntityType } from '../../coeditor/constants';
import * as entitiesService from '../../../utils/entities/services/entitiesService';
import * as createTask from './createTask';
import * as subcatHelper from '../../subcategory/helpers';
import * as elasticProducer from '../../elastic/producer';
import * as webhook from '../../../utils/webhook';
import { generateToken } from '../../public_api/oauth';
import * as viewHelpers from '../../views/helpers';
import * as userGroupMod from '../../groups';
import { getRolledUpTimeEstimate } from './getTask/get-rolled-up-estimates';
import * as pointsMod from '../../pointsEstimate';
import * as nestedHelpers from '../nestedHelpers';
import * as recent from '../../profile/recent';
import * as relationships from '../relationships';
import * as orderindexMod from '../../ordering/task/helpers';
import * as customItemsMod from '../../custom_items/customItems';
import { PublicSharingCodes, StatusErrorCodes } from '../../../utils/errors/constants';
import { ClickUpError } from '../../../utils/errors';
import { triggerSubtasksResolvedEvent } from '../../../automation/resources/task/utils/triggerSubtasksResolvedEvent';
import { getObjectVersionManager } from '../../../utils/version/ObjectVersionManagerFactory';
import { TransactionClientImpl } from '../../../utils/transaction/TransactionClientImpl';
import { prepareUpdateOfTaskCoverImage } from '../factories/coverImageQueryFactory';
import { queryInsertContentHistory } from '../datastores/taskHistoryDataStore';
import { logDateConsistency } from '../dateHelpers';
import { sdAssetsHotUpdateNoWait } from '../../sd/api/sdAssetsHotUpdate';
import { throwIfReadOnlyGuestRole } from '../../custom_roles/services/readOnlyRole';
import { prepareBinaryYdoc } from '../../../utils/content/contentQueryFactory';
import { entitlementService, EntitlementName } from '../../entitlements/entitlementService';
import { getVersionUpdateForTask } from '../helpers/versionUpdateHelper';
import { getRollupChangeEventRelationship } from '../../rolled_up_field_value/ovm/changeEventRelationships';
import { SubcategoryDatastore } from '../../subcategory/datastores/subcategoryDatastore';
import { shouldLogTrail } from '../../integrations/split/squadTreatments/automationTreatments';
import { unassignedTaskAssigneeId } from '../../assignee';
import { VirtualTaskAssigneeHandler, EstimateType } from '../services/assigneeEstimates/virtualTaskAssigneeHandler';
import { getVirtualTaskAssigneeQueries } from '../services/assigneeEstimates/virtualTaskAssigneeQueryFactory';
import { subtaskLimitChecker } from '../utils/limits';
import { upsertObjectAccessInfoQueryObject } from '../../../utils/access/datastores/objectAccessInfoDatastore';
import {
    addSelfToAclWhenSettingDefaultPermissionLevel,
    isValidDefaultPermissionLevel,
} from '../../../utils/access/services/defaultPermissionLevel';

const logger = getLogger('item');

const TaskError = ClickUpError.makeNamedError('task');
const subcategoryDatastore = new SubcategoryDatastore();
const {
    views: { parent_types },
} = config;

const attachment_types = config.attachments.types;

const maxNestingLevel = Number(config.max_nesting_level);

const task_access_fields = new Set([
    'subcategory',
    'parent',
    'archived',
    'private',
    'owner',
    'assignees',
    'keep_creator',
    'default_permission_level',
]);

function shouldUpdateTaskAccess(params) {
    return Object.keys(params).some(key => task_access_fields.has(key));
}

export { shouldUpdateTaskAccess };

async function runNonTransactionalFunctions(item_id, new_inbox_users, team_id, params, points_ca) {
    return new Promise((res, rej) => {
        async.parallel(
            {
                addOrderIndexes(para_cb) {
                    if (!new_inbox_users.length) {
                        para_cb();
                        return;
                    }

                    async.each(
                        new_inbox_users,
                        async (assignee, each_cb) => {
                            try {
                                const type = `inbox:${assignee}`;
                                await orderindexMod.setTaskAtMaxIndex(item_id, type, {
                                    team_id,
                                    extra: {
                                        from: 'editItemNewInboxUsers',
                                        team_id,
                                        item_id,
                                        assignee,
                                    },
                                });
                                each_cb();
                            } catch (e) {
                                each_cb(e);
                            }
                        },
                        para_cb
                    );
                },
                async pointsIncrease(para_cb) {
                    if (!params || !params.points || !points_ca) {
                        para_cb();
                        return;
                    }

                    try {
                        await pointsMod.pointsIncreaseTeamLimit(team_id);
                        para_cb();
                    } catch (e) {
                        para_cb(e);
                    }
                },
            },
            e => {
                if (e) {
                    rej(e);
                } else {
                    res();
                }
            }
        );
    });
}

function _removeOldAssigneeNotifications(item_id) {
    const query = `
        SELECT task_notifs.userid, task_notifs.hist_id
        FROM task_mgmt.task_notifs, task_mgmt.task_history
        WHERE task_mgmt.task_notifs.hist_id = task_history.id
          AND task_history.field = 'assignee'
          AND task_history.task_id = $1
          AND task_notifs.archived = false
          AND task_history.after NOT IN (
              SELECT userid::text
              FROM task_mgmt.assignees
              WHERE task_id = $1
          )`;
    const params = [item_id];

    db.readQuery(query, params, (err, result) => {
        if (err) {
            logger.error({
                msg: 'Failed to select old assignee notifs to delete',
                err,
                item_id,
                ECODE: 'ITEM_015',
            });
        } else {
            result.rows.forEach(row => {
                db.writeQuery(
                    'DELETE FROM task_mgmt.task_notifs WHERE userid = $1 AND hist_id = $2',
                    [row.userid, row.hist_id],
                    deleteErr => {
                        if (deleteErr) {
                            logger.error({
                                msg: 'failed to delete old assignee notif',
                                err: deleteErr,
                                row,
                                item_id,
                                ECODE: 'ITEM_016',
                            });
                        }
                    }
                );
            });
        }
    });
}
export { _removeOldAssigneeNotifications };

function checkLastUpdated(item_id, lastUpdated, force, cb) {
    if (force || !lastUpdated) {
        cb();
        return;
    }

    db.readQuery('SELECT date_updated FROM task_mgmt.items WHERE id = $1', [item_id], (err, result) => {
        if (err) {
            logger.error({
                msg: 'Failed to get date updated',
                err,
                status: 500,
                ECODE: 'ITEM_026',
            });
            cb({
                err: 'Internal server error',
                status: 500,
                ECODE: 'ITEM_026',
            });
        } else if (result.rows.length === 0) {
            logger.error({
                msg: 'Task not found',
                status: 404,
                ECODE: 'ITEM_027',
            });
            cb({ err: 'Task not found', status: 404, ECODE: 'ITEM_027' });
        } else if (result.rows[0].date_updated > lastUpdated) {
            logger.error({
                msg: 'Task updated since last fetched',
                status: 400,
                ECODE: 'ITEM_028',
            });
            cb({
                err: 'Task updated since last fetched',
                status: 400,
                ECODE: 'ITEM_028',
            });
        } else {
            cb();
        }
    });
}

function _validateNoSubtasks(item_id, cb) {
    db.readQuery('SELECT id FROM task_mgmt.items WHERE parent = $1 AND deleted = false', [item_id], (err, result) => {
        if (err) {
            cb({
                err: 'Internal server error',
                status: 500,
                ECODE: 'ITEM_065',
            });
        } else if (result.rows.length > 0) {
            cb({
                err: 'Cannot make subtasks of subtasks',
                status: 400,
                ECODE: 'ITEM_066',
            });
        } else {
            cb();
        }
    });
}

function _editItem(userid, subcategory_id, item_id, params, revision, mobile, options, cb) {
    const hist_ids = [];
    const subtasks_history = [];
    const follower_notifs = [];
    let status_type = null;
    const subtask_due_dates = [];
    const virtualTaskAssigneeHandler = new VirtualTaskAssigneeHandler(item_id);

    const should_update_task_access = shouldUpdateTaskAccess(params);

    if (config.automation_server) {
        logger.trace({
            msg: 'edit item refactored request',
            userid,
            subcategory_id,
            item_id,
            revision,
            mobile,
            options,
        });
    }

    if (params.name) {
        if (!input.validateTaskName(params.name)) {
            cb({
                err: 'Task name invalid',
                status: 400,
                ECODE: 'ITEM_064',
            });
            return;
        }
    }

    if (params.name == null) {
        // don't let name be null in db
        delete params.name;
    }

    if (params.parent && params.private) {
        cb(new TaskError('Cannot make a subtask private', 'ITEM_125', 400));
        return;
    }

    if (
        params.assignees &&
        Array.isArray(params.assignees.add) &&
        params.assignees.add.some(assignee => {
            const id = Number(assignee);

            if (Number.isNaN(id)) {
                return true;
            }

            return id === 0;
        })
    ) {
        cb({
            err: 'Assignees list invalid',
            status: 400,
            ECODE: 'ITEM_119',
        });
        return;
    }

    if (params.parent === item_id) {
        cb({
            err: 'Cannot set a task to be a subtask of itself',
            status: 400,
            ECODE: 'ITEM_069',
        });
        return;
    }

    if (!input.validatePriority(params.priority)) {
        cb({
            err: 'Task priority invalid',
            status: 400,
            ECODE: 'ITEM_070',
        });
        return;
    }

    if ('status' in params && !input.validateStatus(params.status)) {
        cb({
            err: 'Task status invalid',
            status: 400,
            ECODE: 'ITEM_070',
        });
        return;
    }

    if (params.status && params.status !== 'Closed' && params.status !== 'Open') {
        params.status = params.status.toLowerCase();
        if (params.status === 'open') {
            params.status = 'Open';
        } else if (params.status === 'closed') {
            params.status = 'Closed';
        }
    }

    if (params.time_estimate && !params.time_estimate_string) {
        const duration = moment.duration(params.time_estimate);

        params.time_estimate_string = '';

        if (duration.years()) {
            params.time_estimate_string += ` ${duration.years()} years`;
        }

        if (duration.months()) {
            params.time_estimate_string += ` ${duration.months()} months`;
        }

        if (duration.weeks()) {
            params.time_estimate_string += ` ${duration.weeks()} weeks`;
        }

        if (duration.days()) {
            params.time_estimate_string += ` ${duration.days()} days`;
        }

        if (duration.hours()) {
            params.time_estimate_string += ` ${duration.hours()} hours`;
        }

        if (duration.minutes()) {
            params.time_estimate_string += ` ${duration.minutes()} minutes`;
        }
    }

    const permissions = [];

    const param_to_permission = {
        content: config.permission_constants.change_description,
        change_priority: config.permission_constants.change_priority,
        status: config.permission_constants.change_status,
        assignees: config.permission_constants.change_assignee,
        group_assignees: config.permission_constants.change_assignee,
        due_date: config.permission_constants.change_due_date,
        date_created: config.permission_constants.change_due_date,
        start_date: config.permission_constants.change_due_date,
        due_date_time: config.permission_constants.change_due_date,
        start_date_time: config.permission_constants.change_due_date,
        name: config.permission_constants.change_title,
        parent: config.permission_constants.move_task,
        private: config.permission_constants.can_edit_privacy,
        time_estimate: config.permission_constants.change_time_estimate,
        points: config.permission_constants.change_points_estimate,
        coverimage: config.permission_constants.change_description,
        public: config.permission_constants.can_make_tasks_public,
        relationships: config.permission_constants.can_create_relationships,
        default_permission_level: config.permission_constants.can_set_default_permission_level,
    };

    const customType = new CustomType(params.custom_type);

    Object.keys(params).forEach(param => {
        if (param_to_permission[param]) {
            permissions.push(param_to_permission[param]);
        }

        if (param === 'custom_type' && customType.isCustomType()) {
            permissions.push(...customType.getExtraPermissions());
        }
    });

    params.group_assignees = params.group_assignees || {};
    params.group_assignees.add = params.group_assignees.add || [];
    params.group_assignees.rem = params.group_assignees.rem || [];

    params.group_followers = params.group_followers || {};
    params.group_followers.add = params.group_followers.add || [];
    params.group_followers.rem = params.group_followers.rem || [];

    if (params.followers) {
        if (params.followers.add && params.followers.add.length > 0) {
            if (params.followers.add.length === 1 && userid === params.followers.add[0]) {
                permissions.push(config.permission_constants.add_self_follower);
            } else {
                permissions.push(config.permission_constants.add_followers);
            }
        }

        if (params.followers.rem && params.followers.rem.length > 0) {
            if (params.followers.rem.length === 1 && userid === params.followers.rem[0]) {
                permissions.push(config.permission_constants.remove_self_follower);
            } else {
                permissions.push(config.permission_constants.remove_followers);
            }
        }
    }

    let rem_assignees;
    let rem_group_assignees;
    let before_assignees_count;
    let before_user_assignees_count;
    let before_group_assignees_count;
    let nested_subtasks;
    let nested_subtasks_level;
    let { skip_remap_dependencies } = options;
    let team_id;
    let points_scale;
    const new_inbox_users = [];
    const ids_to_update = [];
    const first_level_ids = [];
    let events;
    let userQueryClause = '';
    const queryParams = [item_id];
    if (userid !== config.clickbot_assignee && !(aiSkipAccessForAgentUser() && isAgentUserRequest())) {
        userQueryClause = ' AND team_members.userid = $2';
        queryParams.push(userid);
    }

    const common_history_data = {
        trigger_id: params.trigger_id ?? undefined,
        is_ai: params.is_ai ? true : undefined,
    };

    const include_automation_data_to_history = !excludeAutomationDataFromTaskUpdateHistory();
    const getCommonHistoryData = () => (include_automation_data_to_history ? common_history_data : null);

    db.replicaQuery(
        `
            SELECT
                teams.id as team_id,
                teams.nested_subtasks,
                teams.nested_subtasks_level,
                teams.disable_public_sharing,
                teams.admin_public_share_override,
                teams.points_scale,
                team_members.role,
                team_members.follow_on_task_edit,
                projects.id as project_id
            FROM
                task_mgmt.items
                JOIN task_mgmt.subcategories
                    ON subcategories.id = items.subcategory
                JOIN task_mgmt.categories
                    ON categories.id = subcategories.category
                JOIN task_mgmt.projects
                    ON projects.id = categories.project_id
                JOIN task_mgmt.teams
                    ON teams.id = projects.team
                JOIN task_mgmt.team_members
                    ON team_members.team_id = teams.id ${userQueryClause}
            WHERE
                items.id = $1`,
        queryParams,
        async (itemErr, itemResult) => {
            if (itemErr) {
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'ITEM_048',
                });
                logger.error({
                    msg: 'Failed to read clickapps in edit task',
                    err: itemErr,
                    ECODE: 'ITEM_048',
                });
                return;
            }

            let disable_public_sharing;
            let admin_public_share_override;
            let role;
            let follow_on_task_edit;
            let useRemapDependenciesV2 = false;
            let useRemapSubtasksV2 = false;
            let isSummaryTaskV2Enabled = false;
            let task_is_summary_task = false;
            let summary_task_behavior = new Map();
            let has_date_changes = false;
            let durationIsEnabled = false;
            let remappedDependencySubtaskDates = [];
            const taskFieldLatestHistoryMap = new Map();

            // ensure we always have a team_id available
            if (!team_id) {
                team_id = await entitiesService.getTeamId({ task_ids: [item_id] });
            }
            if (!team_id) {
                cb(new TaskError('Workspace not found for this task', 'ITEM_330', 404));
                return;
            }

            if (itemResult.rows.length) {
                [
                    {
                        nested_subtasks,
                        nested_subtasks_level,
                        disable_public_sharing,
                        role,
                        follow_on_task_edit,
                        admin_public_share_override,
                        team_id,
                        points_scale,
                    },
                ] = itemResult.rows;
                params.dont_follow ||= !follow_on_task_edit;

                try {
                    useRemapDependenciesV2 = shouldUseRemapDependencyV2(team_id);
                    useRemapSubtasksV2 = shouldUseRemapSubtasksV2(team_id);
                    isSummaryTaskV2Enabled = summaryTaskV2Enabled(team_id) && !!options.from_gantt; // Enabled only if from_gantt is set AND feature flag is enabled
                    durationIsEnabled = isDurationEnabled(team_id);
                    // Note: adjustSubtaskDates is not compatiable with summary task v2
                    // they must be mutually exclusive
                    options.adjustSubtaskDates = !!(options.adjustSubtaskDates && !isSummaryTaskV2Enabled);

                    // This code ensures that the is_summary_task parameter is
                    // processed only if the feature flag is enabled and the parameter is not null
                    if (isSummaryTaskV2Enabled && params.is_summary_task != null) {
                        params.is_summary_task = toBoolean(params.is_summary_task);
                    } else {
                        delete params.is_summary_task;
                    }
                } catch (err) {
                    logger.error({
                        msg: 'Failed to read clickapp/workspace setting in edit task',
                        err,
                        ECODE: 'ITEM_333',
                    });
                    cb(new TaskError(err, 'ITEM_333'));
                    return;
                }
            }
            if (
                params.public === true &&
                disable_public_sharing === true &&
                (!admin_public_share_override ||
                    (Number(role) !== config.get('team_roles.owner') &&
                        Number(role) !== config.get('team_roles.admin')))
            ) {
                cb(
                    new TaskError(
                        'Cannot make task public',
                        PublicSharingCodes.DisabledPublicSharingError,
                        StatusErrorCodes.Unauthorized
                    )
                );
                return;
            }

            async.parallel(
                {
                    async checkAssigneesForReadOnlyGuest(para_cb) {
                        if (!params.assignees?.add?.length) {
                            para_cb();
                            return;
                        }

                        try {
                            await throwIfReadOnlyGuestRole(params.assignees.add, { task_ids: [item_id] });
                            para_cb();
                        } catch (error) {
                            para_cb(error);
                        }
                    },

                    async item(para_cb) {
                        const query = `
                            SELECT
                                items.*,
                                subcategories.category,
                                categories.project_id,
                                subcategories.archived as subcat_archived,
                                categories.archived as cat_archived,
                                projects.archived as proj_archived
                            FROM 
                                task_mgmt.items
                                JOIN task_mgmt.subcategories ON items.subcategory = subcategories.id
                                JOIN task_mgmt.categories ON subcategories.category = categories.id
                                JOIN task_mgmt.projects ON categories.project_id = projects.id
                            WHERE items.id = $1`;

                        try {
                            const [task] = (await db.promiseReadQuery(query, [item_id])).rows;

                            if (task) {
                                para_cb(null, task);
                            } else {
                                para_cb({
                                    msg: 'Task not found',
                                    status: 404,
                                    ECODE: 'ITEM_173',
                                    item_id,
                                    userid,
                                });
                            }
                        } catch (err) {
                            para_cb({
                                err: 'Internal server error',
                                status: 500,
                                ECODE: 'ITEM_046',
                            });
                            logger.error({
                                msg: 'Failed to get before item in edit item',
                                status: 500,
                                ECODE: 'ITEM_046',
                                err,
                                userid,
                                item_id,
                            });
                        }
                    },
                    useGantt(para_cb) {
                        if (!options.use_gantt) {
                            para_cb();
                            return;
                        }

                        viewHelpers.incrementGanttUsage({ view_id: options.use_gantt }, para_cb);
                    },

                    checkGantt(para_cb) {
                        if (!options.use_gantt) {
                            para_cb();
                            return;
                        }

                        viewHelpers.checkGanttPaywall({ view_id: options.use_gantt }, para_cb);
                    },

                    async checkViewLimit(para_cb) {
                        if (!options.timeline && !options.workload) {
                            para_cb();
                            return;
                        }

                        try {
                            if (options.timeline) {
                                await viewHelpers.timelineLimitCheck(team_id);
                            } else if (options.workload) {
                                await viewHelpers.workloadLimitCheck(team_id);
                            }
                        } catch (e) {
                            para_cb(e);
                            return;
                        }

                        para_cb();
                    },

                    assignees(para_cb) {
                        db.readQuery(
                            'SELECT userid FROM task_mgmt.assignees WHERE task_id = $1',
                            [item_id],
                            (err, result) => {
                                if (err) {
                                    para_cb({
                                        err: 'Internal server error',
                                        status: 500,
                                        ECODE: 'ITEM_047',
                                    });
                                    logger.error({
                                        msg: "Failed to get before item's assignees in edit item",
                                        status: 500,
                                        ECODE: 'ITEM_047',
                                        err,
                                        userid,
                                        item_id,
                                    });
                                    return;
                                }

                                if (options.reassign_assignees) {
                                    params.assignees = params.assignees || {};
                                    params.assignees.rem = params.assignees.rem || [];
                                    params.assignees.add = params.assignees.add || [];

                                    result.rows.forEach(({ userid: assignee }) => {
                                        if (!params.assignees.add.map(String).includes(String(assignee))) {
                                            params.assignees.rem.push(assignee);
                                        }
                                    });
                                }

                                before_assignees_count = result.rows.length;
                                before_user_assignees_count = result.rows.filter(
                                    x => x.userid !== unassignedTaskAssigneeId
                                ).length;

                                para_cb(null, result.rows);
                            }
                        );
                    },

                    groupAssignees(para_cb) {
                        const query = `SELECT * FROM task_mgmt.group_assignees WHERE task_id = $1`;
                        const query_params = [item_id];

                        db.readQuery(query, query_params, (err, result) => {
                            if (err) {
                                para_cb(new TaskError(err, 'ITEM_159'));
                                return;
                            }

                            if (options.reassign_assignees) {
                                result.rows.forEach(({ group_id }) => {
                                    if (!params.group_assignees.add.includes(group_id)) {
                                        params.group_assignees.rem.push(group_id);
                                    }
                                });
                            }

                            before_group_assignees_count = result.rows ? result.rows.length : 0;

                            para_cb(null, result.rows);
                        });
                    },

                    validateGroupAssignees(para_cb) {
                        if (!params.group_assignees.add.length) {
                            para_cb();
                            return;
                        }

                        async.each(
                            params.group_assignees.add,
                            (group_id, each_cb) => {
                                userGroupMod.checkGroupAvailableToLocation(
                                    group_id,
                                    item_id,
                                    parent_types.task,
                                    {},
                                    (err, result) => {
                                        if (err) {
                                            each_cb(err);
                                            return;
                                        }

                                        result.members.forEach(member => {
                                            if (new_inbox_users.includes(member)) {
                                                return;
                                            }

                                            new_inbox_users.push(member);
                                        });

                                        each_cb();
                                    }
                                );
                            },
                            para_cb
                        );
                    },

                    validateGroupFollowers(para_cb) {
                        if (!params.group_followers.add.length) {
                            para_cb();
                            return;
                        }

                        async.each(
                            params.group_followers.add,
                            (group_id, each_cb) => {
                                userGroupMod.checkGroupAvailableToLocation(
                                    group_id,
                                    item_id,
                                    parent_types.task,
                                    {},
                                    each_cb
                                );
                            },
                            para_cb
                        );
                    },

                    validateAssignees(para_cb) {
                        if (!options.validate_assignees || !params || !params.assignees || !params.assignees.add) {
                            para_cb();
                            return;
                        }

                        checkAssigneesAccess(item_id, params, para_cb);
                    },

                    // user custom items
                    async validateCustomItem(para_cb) {
                        if (!customType.isWorkspaceCustomTypeRange()) {
                            para_cb();
                            return;
                        }

                        try {
                            await Promise.all([customItemsMod.verifyCustomItemWithTask(item_id, customType.value)]);
                            para_cb();
                        } catch (e) {
                            para_cb(e);
                        }
                    },

                    async validateCUCustomItem(para_cb) {
                        // needs to be in config
                        if (!validateCustomTypeAllowedForTask({ customType, userId: userid })) {
                            para_cb(new TaskError('Invalid custom item type', 'ITEM_555', 400));
                            return;
                        }

                        para_cb();
                    },

                    validateFollowers(para_cb) {
                        if (!options.validate_assignees || !params || !params.followers || !params.followers.add) {
                            para_cb();
                            return;
                        }

                        checkFollowersAccess(item_id, params, para_cb);
                    },
                    validateDuration(para_cb) {
                        if (!params.duration) {
                            para_cb();
                            return;
                        }
                        if (!isValidDateInterval(params.duration)) {
                            para_cb({
                                err: 'Invalid date interval',
                                status: 400,
                                ECODE: 'ITEM_251',
                            });
                            return;
                        }
                        if (Object.keys(params.duration).length === 0) {
                            delete params.duration;
                        }
                        para_cb();
                    },
                    projectSettings(para_cb) {
                        db.readQuery(
                            `SELECT
                                multiple_assignees,
                                coalesce(projects.points, false) AS points,
                                projects.id
                            FROM
                                task_mgmt.projects,
                                task_mgmt.categories,
                                task_mgmt.subcategories,
                                task_mgmt.items
                            WHERE
                                items.id = $1 AND
                                items.subcategory = subcategories.id AND
                                subcategories.category = categories.id AND
                                categories.project_id = projects.id`,
                            [item_id],
                            (err, result) => {
                                if (err) {
                                    para_cb({
                                        err: 'Internal server error',
                                        status: 500,
                                        ECODE: 'ITEM_055',
                                    });
                                    logger.error({
                                        msg: 'Failed to look up project settings in task edit',
                                        status: 500,
                                        ECODE: 'ITEM_055',
                                        err,
                                    });
                                } else {
                                    para_cb(null, result.rows[0]);
                                }
                            }
                        );
                    },
                    access(para_cb) {
                        if (
                            userid === config.clickbot_assignee ||
                            (aiSkipAccessForAgentUser() && isAgentUserRequest())
                        ) {
                            const query = `
                                SELECT coalesce(subcategories.team_id, categories.team_id, projects.team) AS team_id
                                FROM task_mgmt.items
                                LEFT JOIN task_mgmt.subcategories
                                    ON subcategories.id = items.subcategory
                                LEFT JOIN task_mgmt.categories
                                    ON categories.id = subcategories.category
                                LEFT JOIN task_mgmt.projects
                                    ON projects.id = categories.project_id
                                WHERE items.id = $1`;

                            db.readQuery(query, [item_id], (err, result) => {
                                if (err) {
                                    para_cb(err);
                                } else {
                                    para_cb(null, result.rows[0] || {});
                                }
                            });

                            return;
                        }

                        if (taskAsObjectConfig()?.monolithAccessChecks) {
                            checkAccessTaskOrTaskAsObjectCb(userid, item_id, { permissions }, para_cb);
                            return;
                        }

                        access.checkAccessTask(userid, item_id, { permissions }, para_cb);
                    },
                    accessConvertToTask(para_cb) {
                        if (!params.parent) {
                            para_cb();
                            return;
                        }

                        const query = 'SELECT subcategory, parent FROM task_mgmt.items WHERE id = $1';

                        db.readQuery(query, [item_id], (err, result) => {
                            if (err) {
                                para_cb(err);
                            } else if (!result.rows.length) {
                                para_cb(new TaskError('Task Not Found', 'ITEM_127', 400));
                            } else {
                                const { subcategory, parent } = result.rows[0];
                                options.old_parent = parent;
                                access.checkAccessSubcategory(
                                    userid,
                                    subcategory,
                                    { permissions: [config.permission_constants.move_task] },
                                    para_cb
                                );
                            }
                        });
                    },
                    checkLastUpdated(para_cb) {
                        checkLastUpdated(item_id, params.date_updated, params.force, para_cb);
                    },
                    parentOfnewParent(para_cb) {
                        if (params.parent && params.parent !== 'none' && !nested_subtasks) {
                            createTask._validateNoParent([params.parent], para_cb);
                        } else {
                            para_cb();
                        }
                    },
                    noSubtasks(para_cb) {
                        // if we're making this task a subtask, validate it has no subtasks
                        if (options.convert_subtasks) {
                            para_cb();
                            return;
                        }
                        if (params.parent && params.parent !== 'none' && !nested_subtasks) {
                            _validateNoSubtasks(item_id, para_cb);
                        } else {
                            para_cb();
                        }
                    },
                    coverImageAccess(para_cb) {
                        if (!params.coverimage) {
                            para_cb();
                            return;
                        }

                        access.checkAccessAttachment(userid, params.coverimage, { permissions: [] }, para_cb);
                    },
                    validateStatus(para_cb) {
                        if (!params.status) {
                            para_cb();
                            return;
                        }

                        db.readQuery(
                            'SELECT statuses.status, statuses.type FROM Task_mgmt.statuses, task_mgmt.items, task_mgmt.subcategories WHERE subcategories.status_group = statuses.status_group AND subcategories.id = items.subcategory AND statuses.status = $1 AND items.id = $2',
                            [params.status, item_id],
                            (err, result) => {
                                if (err) {
                                    para_cb({
                                        err: 'Internal server error',
                                        status: 500,
                                        ECODE: 'ITEM_113',
                                    });
                                } else if (result.rows.length > 0) {
                                    status_type = result.rows[0].type;
                                    para_cb();
                                } else {
                                    para_cb({
                                        err: 'Status does not exist',
                                        status: 400,
                                        ECODE: 'ITEM_114',
                                    });
                                }
                            }
                        );
                    },
                    getDependencyChain(para_cb) {
                        dependencyRemapMod._getDependencyChain([item_id], (err, result) => {
                            para_cb(err, result);
                        });
                    },
                    teamPlan(para_cb) {
                        const query = `
                            SELECT
                                coalesce(teams.time_estimate_rollup, false) AS time_estimate_rollup,
                                coalesce(teams.estimates_per_assignee, false) AS estimates_per_assignee,
                                coalesce(teams.points_per_assignee, false) AS points_per_assignee,
                                coalesce(teams.points_estimate_rollup, false) AS points_rollup,
                                teams.disable_never_expire_pub_links,
                                teams.pub_links_max_year
                            FROM task_mgmt.items
                                LEFT JOIN task_mgmt.subcategories ON items.subcategory = subcategories.id
                                LEFT JOIN task_mgmt.categories ON subcategories.category = categories.id
                                LEFT JOIN task_mgmt.projects ON categories.project_id = projects.id
                                LEFT JOIN task_mgmt.teams ON projects.team = teams.id
                            WHERE items.id = $1`;
                        const dbParams = [item_id];

                        db.replicaQuery(query, dbParams, para_cb);
                    },
                    getRemAssignees(para_cb) {
                        if (!params || !params.assignees || !params.assignees.rem || !params.assignees.rem.length) {
                            para_cb();
                            return;
                        }

                        const query = `
                            SELECT
                                assignees.userid,
                                assignees.time_estimate,
                                assignees.points_float as points
                            FROM
                                task_mgmt.assignees
                            WHERE
                                task_id = $1 AND
                                userid = ANY($2)
                                `;

                        db.replicaQuery(query, [item_id, params.assignees.rem], (err, result) => {
                            if (err) {
                                para_cb(err);
                                return;
                            }

                            rem_assignees = result.rows || [];
                            para_cb();
                        });
                    },
                    getRemGroupAssignees(para_cb) {
                        if (
                            !params ||
                            !params.group_assignees ||
                            !params.group_assignees.rem ||
                            !params.group_assignees.rem.length
                        ) {
                            para_cb();
                            return;
                        }

                        const query = `
                            SELECT
                                group_assignees.group_id,
                                group_assignees.points_float as points
                            FROM
                                task_mgmt.group_assignees
                            WHERE
                                task_id = $1 AND
                                group_id = ANY($2)
                        `;

                        db.replicaQuery(query, [item_id, params.group_assignees.rem], (err, result) => {
                            if (err) {
                                para_cb(err);
                                return;
                            }

                            rem_group_assignees = result.rows || [];
                            para_cb();
                        });
                    },
                    getRemFollowers(para_cb) {
                        if (!params || !params.followers || !params.followers.rem || !params.followers.rem.length) {
                            para_cb();
                            return;
                        }

                        followersMod._getFollowers([item_id], { replica: true }, para_cb);
                    },
                    async checkPointsLimitValidation(para_cb) {
                        if (!params || !params.points) {
                            para_cb();
                            return;
                        }

                        const query = `
                            SELECT
                                coalesce(projects.points, false) AS points_ca
                            FROM
                                task_mgmt.items
                                JOIN task_mgmt.subcategories ON subcategories.id = items.subcategory
                                JOIN task_mgmt.categories ON categories.id = subcategories.category
                                JOIN task_mgmt.projects ON projects.id = categories.project_id
                            WHERE
                                items.id = $1
                        `;

                        let result;
                        try {
                            result = await db.promiseReplicaQuery(query, [item_id], {});
                        } catch (e) {
                            para_cb(e);
                            return;
                        }

                        if (!result || !result.rows || !result.rows.length) {
                            para_cb();
                            return;
                        }

                        const [{ points_ca }] = result.rows;

                        if (!points_scale) {
                            points_scale = config.features.points.points_scale;
                        }

                        if (points_ca) {
                            try {
                                await pointsMod.pointsLimitCheck(team_id);

                                // check points against scale
                                if (params.points && !points_scale.includes(params.points)) {
                                    throw new TaskError('not a valid points selection', 'ITEM_223', 400);
                                }

                                para_cb();
                            } catch (e) {
                                para_cb(e);
                            }
                        } else if (params.points && options.from_public_api) {
                            para_cb(new TaskError('The Sprint Points ClickApp is not enabled.', 'ITEM_225', 400));
                        } else {
                            delete params.points;
                            para_cb();
                        }
                    },

                    remapNestedSubtasksDueDates(para_cb) {
                        if (!options.adjustSubtaskDates || !nested_subtasks) {
                            para_cb();
                            return;
                        }

                        nestedHelpers.nestedSubtasksDueDateMW([item_id], params.due_date, (err, result) => {
                            if (err) {
                                para_cb(new TaskError(err, 'ITEM_167'));
                            } else {
                                const tree = nestedHelpers.treeifyMap(result.rows);
                                // get remapped due dates
                                if (tree[item_id]) {
                                    remapMW.dueDateSubtasks(tree[item_id].subtasks, subtask_due_dates);
                                }

                                para_cb();
                            }
                        });
                    },

                    async setSubTaskParentCheckLimits(para_cb) {
                        if (!params.parent) {
                            para_cb();
                            return;
                        }

                        let tree = {};
                        try {
                            tree = await nestedHelpers.buildTreeFromDBAsync([item_id, params.parent]);
                        } catch (err) {
                            para_cb(new TaskError(err, 'ITEM_167'));
                            return;
                        }

                        // For parity with old behavior, this silent tree existence check is maintained
                        // However, this would only occur in exceptional circumstances where the task
                        // being edited either doesn't exist or is deleted.
                        if (!tree[item_id]) {
                            para_cb();
                            return;
                        }

                        // Example DB format for a task hierarchy of A->B->C:
                        // ID | PARENT | SUBTASK_PARENT
                        // A  | null   | null
                        // B  | A      | null
                        // C  | A      | B

                        // params.parent is the desired immediate parent of the task
                        // To convert to the DB format, we need to know if the desired immediate parent is a root:
                        // - if so, then the subtask_parent should be null and the parent is already correct
                        // - if not, then the subtask_parent should be the desired immediate parent and the parent is the associated root

                        nestedHelpers.findBranchIDs(tree[item_id].subtasks, ids_to_update);
                        first_level_ids.push(...tree[item_id].subtasks.map(_task => _task.id));

                        // No need to rewrite the parent parameter to db format if it is none
                        if (params.parent === 'none') {
                            para_cb();
                            return;
                        }

                        // find and set true new parent (root)
                        const current_item = tree[item_id];
                        const future_parent = tree[params.parent];

                        if (future_parent.parent) {
                            params.subtask_parent = future_parent.id;
                            params.parent = future_parent.parent;
                        } else {
                            params.subtask_parent = 'none';
                        }

                        // check if the new subtask_parent is actually a subtask of the current task
                        if (
                            params.subtask_parent !== 'none' &&
                            shouldUseRecursiveParentValidation() &&
                            !validateNoRecursiveParent(current_item, params.subtask_parent)
                        ) {
                            para_cb(
                                new TaskError(
                                    'Cannot set a task to be a subtask of one of its own subtasks',
                                    'ITEM_071',
                                    400
                                )
                            );
                            return;
                        }

                        // if the task hierarchy has not changed, we can skip subtask limit checks
                        if (
                            params.parent === current_item.parent &&
                            params.subtask_parent === (current_item.subtask_parent ?? 'none')
                        ) {
                            para_cb();
                            return;
                        }

                        // check if nested subtask depth limit is violated
                        if (nested_subtasks) {
                            const future_length = nestedHelpers.findLevel(
                                tree[future_parent.parent || future_parent.id],
                                params.parent
                            );
                            // current node
                            const current_length = nestedHelpers.longestPath(tree, tree[item_id]);
                            // add 1 to account for the connection of two trees
                            if (future_length + current_length + 1 > nested_subtasks_level) {
                                para_cb(
                                    new TaskError(
                                        `Level of nested subtasks is limited to ${nested_subtasks_level}`,
                                        'ITEM_224',
                                        400
                                    )
                                );
                                return;
                            }
                        }
                        // check subtask limit only if task is changing root parent - otherwise the subtask count doesn't change
                        if (params.parent !== current_item.parent) {
                            // check if total subtask count limit violated
                            const currentTree = Object.values(tree).filter(task => task.parent === item_id);
                            const parentTree = Object.values(tree).filter(task => task.parent === params.parent);
                            const isArchived = params.archived ?? current_item.archived;

                            try {
                                await subtaskLimitChecker.checkSubtaskLimitForRootTask(
                                    team_id,
                                    params.parent,
                                    // add 1 to account for input task no longer being the root of the tree
                                    {
                                        newSubtasks:
                                            subtaskLimitChecker.countArchivedStatus(currentTree, false) +
                                            (isArchived ? 0 : 1),
                                        newArchivedSubtasks:
                                            subtaskLimitChecker.countArchivedStatus(currentTree, true) +
                                            (isArchived ? 1 : 0),
                                    },
                                    {
                                        currentSubtaskCount: subtaskLimitChecker.countArchivedStatus(parentTree, false),
                                        currentArchivedSubtaskCount: subtaskLimitChecker.countArchivedStatus(
                                            parentTree,
                                            true
                                        ),
                                    }
                                );
                            } catch (e) {
                                para_cb(e);
                                return;
                            }
                        }
                        para_cb();
                    },
                    getMultipleSubcategories(para_cb) {
                        if (!params || !Object.keys(params).includes('points')) {
                            para_cb();
                            return;
                        }

                        const query = `
                            SELECT array_agg(subcategory) AS subcategories
                            FROM task_mgmt.task_subcategories
                            WHERE task_id = $1
                        `;

                        db.replicaQuery(query, [item_id], para_cb);
                    },
                    /**
                     * Used to get all parent tasks inherited from the task hierarchy
                     * @param para_cb
                     */
                    taskParentChain(para_cb) {
                        const isEnabled = includeTaskParentsIntoUpdateEventsOnFieldValueChange(team_id);
                        if (!isEnabled) {
                            para_cb();
                            return;
                        }

                        const queryObject = objectIdsForParentTasks(item_id);
                        db.replicaQuery(queryObject.query, queryObject.params, para_cb);
                    },

                    /**
                     * Get the last history entries for a specific task
                     * @param para_cb
                     */
                    lastHistory(para_cb) {
                        const query = `
                            SELECT DISTINCT ON (field) field, data
                            FROM task_mgmt.task_history
                            WHERE task_id = $1
                            ORDER BY field, date DESC, id DESC
                        `;
                        db.replicaQuery(query, [item_id], (err, result) => {
                            if (err) {
                                para_cb(err);
                                return;
                            }

                            for (const row of result.rows) {
                                const { field, data } = row;
                                const { is_ai, trigger_id } = data ?? {};
                                taskFieldLatestHistoryMap.set(field, { is_ai, trigger_id });
                            }
                            para_cb();
                        });
                    },
                },
                async (err, result) => {
                    if (err) {
                        cb(err);
                        return;
                    }

                    try {
                        const before = result.item;
                        const { creator } = before;
                        const before_subcategory_id = before.subcategory;
                        const undo_ids = params.hist_ids;
                        const new_parent = params.parent;
                        const in_mentions = params.mentions;
                        const dependencyChain = result.getDependencyChain?.dependencies_to_change[item_id];
                        const dependencyDetails = result.getDependencyChain?.dependencies_details[item_id];

                        let taskIdsBeforeEdit = [item_id];
                        let dateOfTasksBeforeEdit = [];
                        try {
                            if (options.adjustSubtaskDates) {
                                const subtaskIds = await getSubtaskIds(taskIdsBeforeEdit);
                                if (subtaskIds.length) {
                                    taskIdsBeforeEdit = taskIdsBeforeEdit.concat(subtaskIds.map(subtask => subtask.id));
                                }
                            }
                            if (dependencyChain && dependencyChain.length) {
                                taskIdsBeforeEdit = taskIdsBeforeEdit.concat(dependencyChain);
                            }
                            // get date fields of item_id, its subtasks and dependencies before edit
                            dateOfTasksBeforeEdit = await getDateOfTasks(taskIdsBeforeEdit, false);
                        } catch (errGetDateOfTasks) {
                            cb(errGetDateOfTasks);
                            return;
                        }
                        /**
                         * @type {Array<string>}
                         */
                        const taskIdsFromParentChain = Array.from(
                            new Set(result.taskParentChain?.rows.map(row => row.object_id))
                        );
                        delete params.date_updated;
                        delete params.force;
                        delete params.hist_ids;
                        delete params.parent;
                        delete params.mentions;
                        let estimates_per_assignee;
                        let points_per_assignee;
                        let time_estimate_rollup;
                        let disable_never_expire_pub_links;
                        let pub_links_max_year;
                        let points_rollup;
                        let subcats_to_invalidate = [before_subcategory_id];
                        if (result.teamPlan.rows[0]) {
                            disable_never_expire_pub_links = result.teamPlan.rows[0].disable_never_expire_pub_links;
                            pub_links_max_year = result.teamPlan.rows[0].pub_links_max_year;
                            estimates_per_assignee = result.teamPlan.rows[0].estimates_per_assignee;
                            points_per_assignee = result.teamPlan.rows[0].points_per_assignee;
                            time_estimate_rollup = result.teamPlan.rows[0].time_estimate_rollup;
                            points_rollup = result.teamPlan.rows[0].points_rollup;
                        }
                        if (
                            Object.keys(params).includes('points') &&
                            result.getMultipleSubcategories &&
                            result.getMultipleSubcategories.rows &&
                            result.getMultipleSubcategories.rows.length
                        ) {
                            const [{ subcategories }] = result.getMultipleSubcategories.rows;
                            if (subcategories) {
                                subcategories.push(before_subcategory_id);
                                subcats_to_invalidate = subcategories;
                            }
                        }

                        const { multiple_assignees, points: points_ca } = result.projectSettings;

                        const now = new Date().getTime();
                        const { group_assignees } = params;
                        const assignees = { add: [], rem: [] };
                        let followers = { add: [], rem: [] };
                        let hist_query =
                            'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES (';
                        const hist_params = [];
                        const before_assignees = [];
                        const before_group_assignees = [];
                        const before_followers = result.getRemFollowers?.followers[item_id].map(user => user?.id) || [];
                        const addedAssignees = [];
                        const removedAssignees = [];

                        delete params.public_token;

                        if (before.archived || before.subcat_archived || before.cat_archived || before.proj_archived) {
                            skip_remap_dependencies = true;
                        }

                        if (params.public && !before.public) {
                            params.public_token = generateToken(15);
                        }

                        if (params.public) {
                            params.public_permission_level = 1; // eslint-disable-line prefer-destructuring
                        }

                        const total_before_assignees_count = before_user_assignees_count + before_group_assignees_count;

                        if (rem_assignees && estimates_per_assignee) {
                            rem_assignees.forEach(assignee => {
                                virtualTaskAssigneeHandler.onAssigneeRemoved(
                                    assignee.userid,
                                    EstimateType.Time,
                                    Number(assignee.time_estimate)
                                );
                            });

                            const isSingleUserAssigneeRemoved =
                                before_user_assignees_count === 1 && rem_assignees.length === 1;

                            if (isSingleUserAssigneeRemoved) {
                                delete params.time_estimate;
                            }
                        }

                        if ((rem_assignees || rem_group_assignees) && points_per_assignee) {
                            const old_total_points_estimate = before.points || 0;
                            const new_total_points_estimate = old_total_points_estimate;

                            if (rem_assignees) {
                                rem_assignees.forEach(assignee => {
                                    virtualTaskAssigneeHandler.onAssigneeRemoved(
                                        assignee.userid,
                                        EstimateType.Points,
                                        Number(assignee.points)
                                    );
                                });
                            }

                            if (rem_group_assignees) {
                                rem_group_assignees.forEach(assignee => {
                                    virtualTaskAssigneeHandler.onGroupAssigneeRemoved(
                                        EstimateType.Points,
                                        Number(assignee.points)
                                    );
                                });
                            }

                            if (new_total_points_estimate !== old_total_points_estimate) {
                                params.points = new_total_points_estimate;
                            }

                            const isSingleAssigneeRemoved =
                                total_before_assignees_count === 1 &&
                                ((rem_assignees && rem_assignees.length === 1) ||
                                    (rem_group_assignees && rem_group_assignees.length === 1));

                            if (isSingleAssigneeRemoved) {
                                delete params.points;
                            }
                        }

                        const areAllAssigneesRemoved =
                            total_before_assignees_count <=
                            (rem_assignees?.length ?? 0) + (rem_group_assignees?.length ?? 0);

                        if (areAllAssigneesRemoved) {
                            virtualTaskAssigneeHandler.onAllAssigneesRemoved();
                        }

                        if (customType.isUsableCustomType()) {
                            const entitled = await entitlementService.checkEntitlement(
                                team_id,
                                EntitlementName.CustomItems
                            );
                            if (!entitled) {
                                cb(new TaskError('Max usage for custom task types reached', 'ITEM_247', 400));
                                return;
                            }
                        }

                        const public_link_restrictions_available = await entitlementService.checkEntitlement(
                            team_id,
                            EntitlementName.PublicLinkRestrictions
                        );

                        if (
                            public_link_restrictions_available &&
                            disable_never_expire_pub_links &&
                            params.public_share_expires_on === 0
                        ) {
                            cb({
                                err: 'Cannot set never expires on public link',
                                ECODE: 'ITEM_202',
                                status: 400,
                            });
                            return;
                        }

                        if (
                            isValidDefaultPermissionLevel(params.default_permission_level) &&
                            isDefaultLocationPermissionsEnabled(team_id) &&
                            params.default_permission_level !== HierarchyPermissionLevel.CAN_CREATE_AND_EDIT
                        ) {
                            const entitled = await entitlementService.checkEntitlement(
                                team_id,
                                EntitlementName.GranularDefaultPermissionLevel
                            );

                            if (!entitled) {
                                cb({
                                    err: 'Cannot set granular default permission level',
                                    ECODE: 'ITEM_832',
                                    status: 400,
                                });
                                return;
                            }
                        }

                        if (
                            public_link_restrictions_available &&
                            pub_links_max_year &&
                            new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime() <
                                params.public_share_expires_on
                        ) {
                            cb({
                                err: 'Cannot set expires date on public link past one year',
                                ECODE: 'ITEM_201',
                                status: 400,
                            });
                            return;
                        }

                        if (params.public_fields) {
                            if (Array.isArray(params.public_fields)) {
                                params.public_fields = params.public_fields.filter(field =>
                                    config.sharing.public_fields.includes(field)
                                );
                            } else if (params.public_fields !== 'everything') {
                                delete params.public_fields;
                            }
                        }

                        if (params.assignees && Array.isArray(params.assignees.add)) {
                            assignees.add = uniq(params.assignees.add);

                            new_inbox_users.push(...assignees.add);
                        }

                        if (params.assignees && Array.isArray(params.assignees.rem)) {
                            assignees.rem = uniq(params.assignees.rem);
                        }

                        delete params.assignees;
                        delete params.group_assignees;

                        if (params.name) {
                            params.lower_name = params.name.toLowerCase();
                        }

                        result.assignees.forEach(user_obj => {
                            before_assignees.push(user_obj.userid);
                        });

                        result.groupAssignees.forEach(({ group_id }) => {
                            before_group_assignees.push(group_id);
                        });

                        if (params.followers) {
                            followers = params.followers;
                            delete params.followers;
                        }

                        if (
                            assignees &&
                            assignees.add &&
                            (assignees.add.length && group_assignees.add.length) > 1 &&
                            multiple_assignees === false
                        ) {
                            cb({
                                err: 'Cannot add multiple assignees to items in this space',
                                status: 400,
                                ECODE: 'ITEM_056',
                            });
                            return;
                        }

                        if (!followers) {
                            followers = { add: [], rem: [] };
                        }

                        if (!followers.add) {
                            followers.add = [];
                        }

                        if (!followers.rem) {
                            followers.rem = [];
                        }

                        let query = 'UPDATE task_mgmt.items SET date_updated = $1';
                        const query_params = [now];

                        let individualQueries = [];

                        if (undo_ids && undo_ids.length > 0) {
                            individualQueries.push(
                                {
                                    query: 'DELETE FROM task_mgmt.task_history WHERE id = ANY($1) AND task_id = $2',
                                    params: [undo_ids, item_id],
                                },
                                {
                                    query: 'DELETE FROM task_mgmt.task_notifs WHERE hist_id IN (SELECT id FROM task_mgmt.task_history WHERE id = ANY($1) AND task_id = $2)',
                                    params: [undo_ids, item_id],
                                }
                            );
                        }

                        // subtasks cannot be private
                        if (new_parent) {
                            query += ', private = FALSE';
                        }

                        if (params.private) {
                            query_params.push(params.keep_creator ? creator ?? userid : userid);
                            query += `, owner = $${query_params.length}`;

                            individualQueries.push(
                                {
                                    query: `
                                    INSERT INTO task_mgmt.task_members(task_id, userid, date_added, workspace_id) (
                                        SELECT $1, $2, $3, $4 WHERE NOT EXISTS (
                                            SELECT userid FROM task_mgmt.task_members WHERE task_id = $1 AND userid = $2
                                        )
                                    )`,
                                    params: [item_id, userid, new Date().getTime(), team_id],
                                },
                                {
                                    query: `
                                    UPDATE task_mgmt.task_members
                                    SET permission_level = $3
                                    WHERE task_id = $1 AND userid = $2`,
                                    params: [item_id, userid, 5],
                                }
                            );

                            if (shouldRemoveSubtasksFollowersAfterParentTaskPrivacyChange()) {
                                individualQueries.push(
                                    {
                                        query: `
                                        INSERT INTO task_mgmt.task_members(task_id, userid, date_added, workspace_id) (
                                            SELECT items.id, $2, $3, $4
                                            FROM task_mgmt.items
                                            WHERE items.parent = $1 AND NOT EXISTS (
                                                SELECT userid FROM task_mgmt.task_members WHERE task_id = items.id AND userid = $2
                                            )
                                        )`,
                                        params: [item_id, userid, new Date().getTime(), team_id],
                                    },
                                    {
                                        query: `
                                        UPDATE task_mgmt.task_members
                                        SET permission_level = $3
                                        WHERE task_id in (
                                            SELECT id FROM task_mgmt.items where items.parent = $1
                                        ) AND task_members.userid = $2`,
                                        params: [item_id, userid, 5],
                                    }
                                );
                            }

                            if (params.keep_creator && creator) {
                                individualQueries.push({
                                    query: `
                                        INSERT INTO task_mgmt.task_members(task_id, userid, date_added, permission_level, workspace_id)
                                        VALUES ($1, $2, $3, $4, $5)
                                        ON CONFLICT DO NOTHING
                                    `,
                                    params: [item_id, creator, new Date().getTime(), 5, team_id],
                                });
                            } else if (params.keep_creator === false && creator) {
                                individualQueries.push({
                                    query: `
                                        DELETE FROM task_mgmt.task_members
                                        WHERE task_members.task_id = $1
                                          AND task_members.userid = $2
                                    `,
                                    params: [item_id, creator],
                                });

                                if (shouldRemoveSubtasksFollowersAfterParentTaskPrivacyChange()) {
                                    individualQueries.push({
                                        query: `
                                            DELETE FROM task_mgmt.task_members
                                            WHERE task_members.task_id in (
                                                SELECT id FROM task_mgmt.items where items.parent = $1
                                            )
                                              AND task_members.userid = $2
                                        `,
                                        params: [item_id, creator],
                                    });
                                }
                            }

                            individualQueries.push({
                                query: `
                                    DELETE FROM task_mgmt.followers
                                    WHERE followers.task_id = $1
                                        AND followers.userid NOT IN (
                                            SELECT userid
                                            FROM task_mgmt.task_members
                                            WHERE task_id = $1
                                        )`,
                                params: [item_id],
                            });

                            if (shouldRemoveSubtasksFollowersAfterParentTaskPrivacyChange()) {
                                individualQueries.push({
                                    query: `
                                        DELETE FROM task_mgmt.followers
                                        WHERE followers.task_id in (
                                            SELECT id FROM task_mgmt.items where items.parent = $1
                                        )
                                            AND followers.userid NOT IN (
                                                SELECT userid
                                                FROM task_mgmt.task_members
                                                WHERE task_id = $1
                                            )
                                        RETURNING followers.task_id AS updated_task_id`,
                                    params: [item_id],
                                });
                            }
                        }

                        if (params.text_content) {
                            // keep the text content
                        } else if (params.content) {
                            if (params.content.ops) {
                                try {
                                    params.text_content = await quill.toPlainTextAsync(params.content.ops);
                                } catch (e) {
                                    //
                                }
                            } else {
                                params.text_content = params.content;
                                params.content = toQuillDelta(params.content, [], false, true);
                            }
                        }

                        const text_content_limit = config.tasks.limits.text_content_size;
                        if (params.text_content && params.text_content.length > text_content_limit) {
                            logger.info({
                                msg: 'Task description is too long during edit',
                                task_id: item_id,
                                text_content_limit,
                                text_content_length: params.text_content.length,
                                ECODE: 'ITEM_182',
                            });
                            cb({
                                err: 'Task description is too long',
                                status: 413,
                                ECODE: 'ITEM_182',
                            });
                            return;
                        }

                        if (params.text_content) {
                            params.lower_text_content = params.text_content.toLowerCase();
                        }

                        let new_due_date_time = null;
                        if (!before.due_date_time && params.due_date_time) {
                            new_due_date_time = true;
                        }

                        if (isSummaryTaskV2Enabled) {
                            has_date_changes = checkForDateChanges(params, before);
                            if (has_date_changes) {
                                // Check if the trigger task is a summary task
                                summary_task_behavior = await getSummaryTaskBehavior([item_id]);
                                const taskBehavior = summary_task_behavior.get(item_id);
                                if (taskBehavior && taskBehavior.is_summary_task) {
                                    task_is_summary_task = true;
                                    if (!checkForDurationChanges(params, before)) {
                                        summary_task_behavior = new Map(); // Summary tasks durations cannot be changed
                                    }
                                }
                            }
                        }

                        if (!skip_remap_dependencies || options.adjustSubtaskDates) {
                            if (!useRemapDependenciesV2) {
                                individualQueries = individualQueries.concat(
                                    dependencyRemapMod._getAdjustDependencyQueries(
                                        dependencyChain,
                                        parseInt(params.due_date, 10) - parseInt(before.due_date, 10),
                                        new_due_date_time
                                    )
                                );
                            } else if (
                                (params.due_date || params.due_date_time) &&
                                (params.due_date !== before.due_date || params.due_date_time !== before.due_date_time)
                            ) {
                                const updateDateResult = await updateTaskDates({
                                    team_id,
                                    userid,
                                    item_id,
                                    params,
                                    before,
                                    options,
                                    dependencyChain,
                                    dependencyDetails,
                                    subtask_due_dates,
                                    useRemapDependenciesV2,
                                    useRemapSubtasksV2,
                                    durationIsEnabled,
                                    isSummaryTaskV2Enabled,
                                    summary_task_behavior,
                                });

                                if (updateDateResult && updateDateResult.queries?.length > 0) {
                                    individualQueries = individualQueries.concat(updateDateResult.queries);
                                    remappedDependencySubtaskDates = updateDateResult.remappedDependencySubtaskDates;
                                }
                            }
                        }

                        if (isSummaryTaskV2Enabled) {
                            try {
                                const { new_date_info, old_date_info } = createDateInfoObjects(params, before);

                                const { summaryTaskQueries, is_summary_task_updated } =
                                    await handleSummaryTaskDateUpdates({
                                        new_date_info,
                                        old_date_info,
                                        team_id,
                                        item_id,
                                        userid,
                                        subtask_due_dates,
                                        remappedDependencySubtaskDates,
                                        task_is_summary_task,
                                        has_date_changes,
                                        is_summary_task_param: params.is_summary_task,
                                        mute_notifications: params.mute_notifications,
                                    });

                                // Handle summary task queries
                                if (summaryTaskQueries?.length > 0) {
                                    individualQueries = individualQueries.concat(summaryTaskQueries);
                                }

                                if (task_is_summary_task && checkForDurationChanges(params, before)) {
                                    delete new_date_info.start_date;
                                    delete new_date_info.start_date_time;
                                    delete new_date_info.due_date;
                                    delete new_date_info.due_date_time;
                                    delete new_date_info.duration;
                                    delete new_date_info.duration_is_elapsed;
                                }

                                // There are 3 possible values for is_summary_task_updated
                                // 1. true/false -> should set the is_summary_task parameter to the value of is_summary_task_updated (differs from the what is stored in the database)
                                // 2. null -> means that the is_sumamry_task parameter was equal to the what is stored in the database.
                                if (is_summary_task_updated != null) {
                                    params.is_summary_task = is_summary_task_updated;
                                } else {
                                    delete params.is_summary_task;
                                }
                            } catch (summary_task_err) {
                                logger.error({
                                    msg: 'Failed to prepare summary task dates update queries',
                                    status: 500,
                                    ECODE: 'ITEM_216',
                                    err: summary_task_err,
                                    userid,
                                    item_id,
                                    team_id,
                                    task_is_summary_task,
                                });
                                cb(new TaskError(summary_task_err, 'ITEM_216'));
                                return;
                            }
                        }

                        if (shouldUseHiddenCoverImageCopy(team_id)) {
                            if (
                                params.coverimage !== undefined ||
                                params.cover_image_url ||
                                params.cover_image_color ||
                                params.cover_position_x ||
                                params.cover_position_y
                            ) {
                                // copy the cover image as concealed attachment
                                if (params.coverimage !== null && params.coverimage !== 'none') {
                                    try {
                                        const copyResponse = await copyAttachmentsAsync(item_id, item_id, null, {
                                            attachment_ids: [params.coverimage],
                                            copy_attachments_from_s3: true,
                                            team_id,
                                            old_type: attachment_types.task,
                                            type: attachment_types.task,
                                            // wait for really copied then FE is able to show it
                                            wait_for_copy: true,
                                            // conceal the attachment from user
                                            force_hidden: true,
                                        });

                                        // a new coverimage is set
                                        if (copyResponse?.ids?.length > 0) {
                                            const newCoverId = copyResponse.ids[0];
                                            params.coverimage = newCoverId;
                                            params.cover_image_url = newCoverId;
                                            params.cover_image_copied = true;
                                        }
                                    } catch (copyError) {
                                        logger.warn({
                                            msg: 'Could not copy cover image attachment',
                                            coverimage: params.coverimage,
                                            err: copyError,
                                            ECODE: 'ITEM_187',
                                        });
                                    }
                                }
                            }
                        }

                        const valid_params = [
                            'name',
                            'is_summary_task',
                            'lower_name',
                            'content',
                            'creator',
                            'date_created',
                            'date_updated',
                            'date_closed',
                            'type',
                            'status',
                            'time_spent',
                            'subcategory',
                            'priority',
                            'orderindex',
                            'parent',
                            'subtask_parent',
                            'coverimage',
                            'html_content',
                            'text_content',
                            'due_date',
                            'due_date_time',
                            'sent_due_date_notif',
                            'start_date_time',
                            'start_date',
                            'points',
                            'time_estimate',
                            'archived',
                            'template_name',
                            'show_completed',
                            'encrypted',
                            'lower_text_content',
                            'time_estimate_string',
                            'email_token',
                            'private',
                            'via',
                            'status_id',
                            'delegator',
                            'date_done',
                            'title_ts',
                            'content_ts',
                            'date_delegated',
                            'draft_uuid',
                            'deleted_by',
                            'merged_to',
                            'default_category_id',
                            'editor_token',
                            'form_id',
                            'public',
                            'public_share_expires_on',
                            'public_token',
                            'public_permission_level',
                            'seo_optimized',
                            'public_fields',
                            'subtask_sort',
                            'subtask_sort_dir',
                            'custom_type',
                            'duration',
                            'duration_is_elapsed',
                        ];

                        Object.keys(params).forEach(param => {
                            if (param === 'userid' || param === 'subcategory_id' || param === 'id') {
                                return;
                            }

                            if (!valid_params.includes(param)) {
                                return;
                            }

                            if (query_params.length > 0) {
                                query += ', ';
                            }
                            if (param === 'status') {
                                if (config.closed_status_types.includes(status_type)) {
                                    if (!isWriteToRecentTableDisabled('recently_closed')) {
                                        individualQueries.push({
                                            query: `
                                                INSERT INTO task_mgmt.recently_closed(userid, team_id, task_id, date)
                                                VALUES ($1, $2, $3, $4)
                                                ON CONFLICT (team_id, userid, task_id)
                                                DO UPDATE SET task_id = excluded.task_id, date = excluded.date`,
                                            params: [userid, team_id, item_id, now],
                                        });
                                    }

                                    individualQueries.push({
                                        query: `
                                            UPDATE tasK_mgmt.items
                                            SET date_closed = $2
                                            WHERE id = $1 AND date_closed IS NULL`,
                                        params: [item_id, now],
                                    });
                                } else {
                                    query += 'date_closed = null, ';
                                }

                                if (config.done_status_types.includes(status_type)) {
                                    if (!isWriteToRecentTableDisabled('recently_done')) {
                                        individualQueries.push({
                                            query: `
                                                INSERT INTO task_mgmt.recently_done(userid, team_id, task_id, date)
                                                VALUES ($1, $2, $3, $4)
                                                ON CONFLICT (team_id, userid, task_id)
                                                DO UPDATE SET task_id = excluded.task_id, date = excluded.date`,
                                            params: [userid, team_id, item_id, now],
                                        });
                                    }

                                    individualQueries.push({
                                        query: `
                                            UPDATE tasK_mgmt.items
                                            SET date_done = $2
                                            WHERE id = $1 AND date_done IS NULL`,
                                        params: [item_id, now],
                                    });
                                    recent.invalidateCachedRecentTasks(userid, team_id, 'task_mgmt.recently_done');
                                } else {
                                    query += 'date_done = null, ';
                                }

                                if (config.active_status_types.includes(status_type)) {
                                    individualQueries.push({
                                        query: `
                                            UPDATE tasK_mgmt.items
                                            SET date_active = $2
                                            WHERE id = $1 AND date_active IS NULL`,
                                        params: [item_id, now],
                                    });
                                }

                                if (config.unstarted_status_types.includes(status_type)) {
                                    individualQueries.push({
                                        query: `
                                            UPDATE tasK_mgmt.items
                                            SET date_unstarted = coalesce(date_unstarted, $2), date_active = null
                                            WHERE id = $1`,
                                        params: [item_id, now],
                                    });
                                }

                                query += ` status_id = (SELECT statuses.id FROM task_mgmt.statuses, task_mgmt.subcategories WHERE subcategories.id = items.subcategory AND statuses.status = $${
                                    query_params.length + 1
                                }::text AND statuses.status_group = subcategories.status_group), `;
                            } else if (
                                ['due_date', 'priority', 'start_date', 'time_estimate', 'subtask_parent'].indexOf(
                                    param
                                ) >= 0 &&
                                params[param] === 'none'
                            ) {
                                params[param] = null;
                            } else if (param === 'due_date') {
                                let sent_due_date_notif = false;
                                const twenty_hours = 20 * 60 * 60 * 1000;

                                try {
                                    if (
                                        parseInt(params[param], 10) < new Date().getTime() &&
                                        params.due_date_time === true
                                    ) {
                                        sent_due_date_notif = true;
                                    } else if (
                                        parseInt(params[param], 10) + twenty_hours < new Date().getTime() &&
                                        !params.due_date_time
                                    ) {
                                        sent_due_date_notif = true;
                                    }
                                    query_params.push(sent_due_date_notif);
                                    query += `sent_due_date_notif = $${query_params.length}, `;
                                } catch (e) {
                                    // catch
                                }
                            } else if (
                                ['name', 'html_content', 'text_content'].indexOf(param) >= 0 &&
                                before.encrypted
                            ) {
                                params[param] = encrypt.encrypt(params[param]);
                            } else if (param === 'content' && before.encrypted) {
                                if (params[param]?.ops) {
                                    params[param].ops = sanitizeXssAttackForCommentParts(params[param].ops);
                                }

                                params[param] = encrypt.encryptContent(params[param]);
                            } else if (param === 'public_fields' && params[param] === 'everything') {
                                params[param] = null;
                            } else if (
                                param === 'subtask_sort' &&
                                !config.subtask_sort_options.includes(params[param])
                            ) {
                                params[param] = 'manual';
                            } else if (param === 'subtask_sort_dir' && params[param] !== 'asc') {
                                params[param] = 'desc';
                            }

                            if (param === 'public_share_expires_on') {
                                if (
                                    params.public &&
                                    params[param] &&
                                    params[param] !== 0 &&
                                    param === 'public_share_expires_on' &&
                                    public_link_restrictions_available
                                ) {
                                    query_params.push(params[param]);
                                    query += `public_share_expires_on = $${query_params.length} `;
                                } else if (
                                    params.public &&
                                    params[param] === 0 &&
                                    param === 'public_share_expires_on' &&
                                    public_link_restrictions_available
                                ) {
                                    query += `public_share_expires_on = NULL `;
                                } else {
                                    query = query.substring(0, query.length - 2);
                                }

                                return;
                            }

                            if (param === 'custom_type') {
                                params[param] = customType.value;
                            }

                            if (param === 'public') {
                                query_params.push(params[param] ? userid : null);
                                query += `made_public_by = $${query_params.length} ,`;

                                query_params.push(params[param] ? now : null);
                                query += `made_public_time = $${query_params.length} ,`;
                            }

                            //
                            if (valid_params.includes(param)) {
                                query_params.push(params[param]);
                                query += `${param} = $${query_params.length}`;
                            }

                            if (param === 'status') {
                                query += `::text`;
                            }

                            const param_data = {};

                            if (Object.keys(params).includes('time_estimate')) {
                                param_data.time_estimate = {
                                    time_estimate_string: params.time_estimate_string || null,
                                    old_time_estimate_string: before.time_estimate_string,
                                };
                            }

                            if (Object.keys(params).includes('status')) {
                                param_data.status = {
                                    status_type,
                                };
                            }

                            if (param === 'duration') {
                                query_params[query_params.length - 1] = dateInterval(params[param]).asIntervalString;
                            }

                            const historyData = taskFieldLatestHistoryMap.get(param);
                            // the field is considered changed if:
                            // 1. the value is different from the previous value
                            // 2. the previous value is not set by the same actor as current value (ai vs user)
                            // 3. the trigger_id is different
                            const hasChanged =
                                before[param] !== params[param] ||
                                historyData?.is_ai !== common_history_data.is_ai ||
                                historyData?.trigger_id !== common_history_data.trigger_id;

                            const { durationMs: hiddenHistoryWindow } = hideRecentActivityHistoryWindow();

                            if (
                                hasChanged &&
                                param !== 'text_content' &&
                                param !== 'lower_text_content' &&
                                param !== 'lower_name' &&
                                param !== 'title_ts' &&
                                param !== 'content_ts' &&
                                param !== 'time_estimate_string' &&
                                param !== 'custom_type'
                            ) {
                                if (
                                    param !== 'content' &&
                                    param !== 'html_content' &&
                                    param !== 'name' &&
                                    param !== 'due_date_time' &&
                                    param !== 'start_date_time' &&
                                    param !== 'due_date' &&
                                    param !== 'start_date' &&
                                    param !== 'show_completed' &&
                                    param !== 'time_estimate' &&
                                    param !== 'duration' &&
                                    param !== 'duration_is_elapsed'
                                ) {
                                    if (hist_params.length > 0) {
                                        hist_query += '), (';
                                    }
                                    hist_query += `
                                    $${hist_params.length + 1},
                                    $${hist_params.length + 2},
                                    $${hist_params.length + 3},
                                    $${hist_params.length + 4},
                                    $${hist_params.length + 5},
                                    $${hist_params.length + 6},
                                    $${hist_params.length + 7}`;

                                    const field = param === 'is_summary_task' ? 'summary_task' : param;

                                    hist_params.push(item_id, field, now, before[param], params[param], userid, {
                                        ...param_data[param],
                                        ...(getCommonHistoryData() || {}),
                                    });
                                    if (param === 'due_date') {
                                        individualQueries.push({
                                            query: "DELETE FROM task_mgmt.task_notifs WHERE hist_id IN (SELECT id FROM task_mgmt.task_history WHERE task_id = $1 AND field = 'due_date_missed') AND archived = false",
                                            params: [item_id],
                                        });
                                    }
                                } else if (
                                    param === 'name' &&
                                    params[param] &&
                                    before[param] &&
                                    params[param].replace(/\s/g, '').toLowerCase() ===
                                        before[param].replace(/\s/g, '').toLowerCase()
                                ) {
                                    // dont make a history item for case or white space changes
                                } else if (
                                    param === 'name' ||
                                    param === 'content' ||
                                    param === 'html_content' ||
                                    param === 'coverimage'
                                ) {
                                    if (!(param === 'content' || param === 'html_content')) {
                                        if (hiddenHistoryWindow > 0) {
                                            individualQueries.push({
                                                query: `
                                                -- editItem
                                                UPDATE task_mgmt.task_history
                                                SET hidden = true
                                                WHERE id IN (
                                                    SELECT id
                                                FROM task_mgmt.task_history
                                                WHERE task_id = $1
                                                AND field = $2
                                                    AND userid = $3
                                                    AND date > $4
                                                    AND hidden IS NOT TRUE
                                                    )`,
                                                params: [item_id, param, userid, now - hiddenHistoryWindow],
                                            });
                                        }

                                        const beforeValue = before[param];
                                        const afterValue = params[param];
                                        individualQueries.push({
                                            query: `
                                            INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                                            VALUES ($1, $2, $3, $4, $5, $6, $7)
                                            RETURNING id, field, data`,
                                            params: [
                                                item_id,
                                                param,
                                                now,
                                                beforeValue,
                                                afterValue,
                                                userid,
                                                param_data[param],
                                            ],
                                        });
                                    }
                                } else if (param === 'time_estimate') {
                                    individualQueries.push({
                                        query: `
                                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                                        VALUES($1, $2, $3, $4, $5, $6, $7)
                                        RETURNING id, field`,
                                        params: [
                                            item_id,
                                            param,
                                            now,
                                            before[param],
                                            params[param],
                                            userid,
                                            param_data[param],
                                        ],
                                    });
                                } else if (param === 'points') {
                                    individualQueries.push({
                                        query: `
                                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                                        VALUES($1, $2, $3, $4, $5, $6, $7)
                                        RETURNING id, field`,
                                        params: [item_id, param, now, before[param], params[param], userid, null],
                                    });
                                } else if (param === 'due_date') {
                                    const data = {
                                        due_date_time: params.due_date_time,
                                        old_due_date_time: before.due_date_time,
                                    };
                                    if (include_automation_data_to_history) {
                                        Object.assign(data, common_history_data);
                                    } else {
                                        data.trigger_id = params.trigger_id ?? undefined;
                                    }

                                    if (hiddenHistoryWindow > 0) {
                                        individualQueries.push({
                                            query: `
                                            UPDATE task_mgmt.task_history
                                            SET hidden = true
                                            WHERE id IN (
                                                SELECT id
                                                FROM task_mgmt.task_history
                                                WHERE task_id = $1
                                                    AND field = $2
                                                    AND userid = $3
                                                    AND date > $4
                                            )`,
                                            params: [item_id, param, userid, now - hiddenHistoryWindow],
                                        });
                                    }

                                    individualQueries.push({
                                        query: `
                                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                                        VALUES ($1, $2, $3, $4, $5, $6, $7)
                                        RETURNING id, field`,
                                        params: [item_id, param, now, before[param], params[param], userid, data],
                                    });
                                } else if (param === 'start_date') {
                                    const data = {
                                        start_date_time: params.start_date_time,
                                        old_start_date_time: before.start_date_time,
                                    };
                                    if (include_automation_data_to_history) {
                                        Object.assign(data, common_history_data);
                                    } else {
                                        data.trigger_id = params.trigger_id ?? undefined;
                                    }

                                    if (hiddenHistoryWindow > 0) {
                                        individualQueries.push({
                                            query: `
                                            UPDATE task_mgmt.task_history
                                            SET hidden = true
                                            WHERE id IN (
                                                SELECT id
                                                FROM task_mgmt.task_history
                                                WHERE task_id = $1
                                                    AND field = $2
                                                    AND userid = $3
                                                    AND date > $4
                                            )`,
                                            params: [item_id, param, userid, now - hiddenHistoryWindow],
                                        });
                                    }

                                    individualQueries.push({
                                        query: `
                                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                                        VALUES ($1, $2, $3, $4, $5, $6, $7)
                                        RETURNING id, field`,
                                        params: [item_id, param, now, before[param], params[param], userid, data],
                                    });
                                } else if (param === 'duration') {
                                    const data = {
                                        old: before.duration,
                                        new: params.duration,
                                    };
                                    if (compareDateIntervals(data.new, data.old)) {
                                        return;
                                    }

                                    if (hiddenHistoryWindow > 0) {
                                        individualQueries.push({
                                            query: `
                                            UPDATE task_mgmt.task_history
                                            SET hidden = true
                                            WHERE id IN (
                                                SELECT id
                                                FROM task_mgmt.task_history
                                                WHERE task_id = $1
                                                    AND field = $2
                                                    AND userid = $3
                                                    AND date > $4
                                            )`,
                                            params: [item_id, param, userid, now - hiddenHistoryWindow],
                                        });
                                    }
                                    individualQueries.push({
                                        query: `
                                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                                        VALUES ($1, $2, $3, $4, $5, $6, $7)
                                        RETURNING id, field`,
                                        params: [item_id, param, now, data.old, data.new, userid, data],
                                    });
                                } else if (param === 'duration_is_elapsed') {
                                    const data = {
                                        old: before.duration_is_elapsed,
                                        new: params.duration_is_elapsed,
                                    };

                                    if (Boolean(data.new) === Boolean(data.old)) {
                                        return;
                                    }

                                    if (hiddenHistoryWindow > 0) {
                                        individualQueries.push({
                                            query: `
                                            UPDATE task_mgmt.task_history
                                            SET hidden = true
                                            WHERE id IN (
                                                SELECT id
                                                FROM task_mgmt.task_history
                                                WHERE task_id = $1
                                                    AND field = $2
                                                    AND userid = $3
                                                    AND date > $4
                                            )`,
                                            params: [item_id, param, userid, now - hiddenHistoryWindow],
                                        });
                                    }
                                    individualQueries.push({
                                        query: `
                                        INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data)
                                        VALUES ($1, $2, $3, $4, $5, $6, $7)
                                        RETURNING id, field`,
                                        params: [item_id, param, now, data.old, data.new, userid, data],
                                    });
                                }
                            }

                            if (param === 'name') {
                                individualQueries.push({
                                    query: `UPDATE task_mgmt.mind_map_nodes SET title = $1 WHERE parent_id = $2`,
                                    params: [params[param], item_id],
                                });
                            }
                        });

                        if (new_parent && new_parent !== 'none') {
                            individualQueries.push({
                                query: 'DELETE FROM task_mgmt.task_members WHERE task_id = $1',
                                params: [item_id],
                            });
                            if (nested_subtasks && first_level_ids.length) {
                                individualQueries.push(
                                    {
                                        query: `UPDATE task_mgmt.items SET subtask_parent = $1 WHERE id = ANY($2)`,
                                        params: [item_id, first_level_ids],
                                    },
                                    {
                                        query: `UPDATE task_mgmt.items SET parent = $1 WHERE id = ANY($2)`,
                                        params: [new_parent, ids_to_update],
                                    },
                                    {
                                        query: `UPDATE task_mgmt.items SET subcategory = (SELECT subcategory FROM task_mgmt.items WHERE id = $1), status_id = (
                                            SELECT statuses.id
                                            FROM task_mgmt.statuses, task_mgmt.subcategories
                                            WHERE subcategories.id = (SELECT subcategory FROM task_mgmt.items WHERE id = $1)
                                                AND statuses.status = items.status
                                              AND statuses.status_group = subcategories.status_group
                                        )  WHERE id = ANY($2)`,
                                        params: [new_parent, ids_to_update],
                                    }
                                );
                            }
                        }

                        if (new_parent && new_parent === 'none') {
                            individualQueries.push(
                                {
                                    query: 'UPDATE task_mgmt.items SET owner = $1 WHERE id = $2',
                                    params: [userid, item_id],
                                },
                                {
                                    query: `
                                        INSERT INTO task_mgmt.task_members(task_id, userid, permission_level, date_added, workspace_id)
                                        VALUES ($1, $2, $3, $4, $5) ON CONFLICT (task_id, userid) DO NOTHING`,
                                    params: [item_id, userid, 5, new Date().getTime(), team_id],
                                }
                            );
                            if (nested_subtasks && first_level_ids.length) {
                                individualQueries.push(
                                    {
                                        query: `UPDATE task_mgmt.items SET subtask_parent = null WHERE id = ANY($1)`,
                                        params: [first_level_ids],
                                    },
                                    {
                                        query: `UPDATE task_mgmt.items SET parent = $1 WHERE id = ANY($2)`,
                                        params: [item_id, ids_to_update],
                                    }
                                );
                            }
                        }

                        if (params.relationships) {
                            individualQueries.push(
                                ...(await relationships.editTaskRelationshipView(userid, item_id, params.relationships))
                            );
                        }

                        if (
                            params.coverimage !== undefined ||
                            params.cover_image_url ||
                            params.cover_image_color ||
                            params.cover_position_x ||
                            params.cover_position_y
                        ) {
                            individualQueries.push(
                                ...prepareUpdateOfTaskCoverImage(userid, item_id, team_id, {
                                    cover_image_url: params.coverimage || params.cover_image_url,
                                    cover_image_color: params.cover_image_color,
                                    cover_position_y: params.cover_position_y,
                                    cover_position_x: params.cover_position_x,
                                })
                            );
                        }

                        if (
                            isValidDefaultPermissionLevel(params.default_permission_level) &&
                            isDefaultLocationPermissionsEnabled(team_id)
                        ) {
                            individualQueries.push(
                                upsertObjectAccessInfoQueryObject({
                                    objectId: item_id,
                                    objectType: ObjectType.TASK,
                                    workspaceId: team_id,
                                    defaultPermissionLevel: params.default_permission_level,
                                })
                            );

                            if (params.default_permission_level !== null) {
                                individualQueries.push(
                                    ...addSelfToAclWhenSettingDefaultPermissionLevel(
                                        userid,
                                        item_id,
                                        ObjectType.TASK,
                                        team_id
                                    )
                                );
                            }
                        }

                        const virtualAssigneeQueries = getVirtualTaskAssigneeQueries({
                            taskVirtualAssignees: [
                                {
                                    handler: virtualTaskAssigneeHandler,
                                    hadVirtualAssignee: () => before_assignees.includes(unassignedTaskAssigneeId),
                                },
                            ],
                            workspaceId: team_id,
                            now,
                        });

                        if (virtualAssigneeQueries.length > 0) {
                            individualQueries.push(...virtualAssigneeQueries);
                        }

                        if (params.content) {
                            if (params.content.ops) {
                                params.content.ops = sanitizeXssAttackForCommentParts(params.content.ops);
                            }

                            query_params.push(
                                prepareBinaryYdoc(before.id, before.ydoc, params.content, params.ydoc, team_id)
                            );
                            query += `, ydoc = $${query_params.length}`;
                        }

                        query_params.push(item_id);
                        query += ` WHERE id = $${query_params.length}`;

                        const dependencies_adjusted = [];
                        const due_dates_adjusted = [];
                        const before_hist_ids = [];
                        const success_funcs = [];
                        const versionUpdates = [];
                        const versionUpdateChanges = [];
                        if (params.content) {
                            versionUpdateChanges.push({ field: 'content' });
                        }

                        try {
                            await runNonTransactionalFunctions(item_id, new_inbox_users, team_id, params, points_ca);
                        } catch (e) {
                            cb(e);
                            return;
                        }

                        let rollUpTimeEstimate;
                        db.getConn(cb, { label: 'edit item refactored' }, (connErr, client, done) => {
                            if (connErr) {
                                return;
                            }
                            const breakEditTaskTransaction = shouldUseBreakEditTaskTransaction();
                            async.waterfall(
                                [
                                    wf_cb => {
                                        if (breakEditTaskTransaction) {
                                            wf_cb();
                                            return;
                                        }
                                        client.query('BEGIN', beginErr => {
                                            if (beginErr) {
                                                db.rollback(client, done);
                                                logger.error({
                                                    msg: 'Error beginning',
                                                    err: beginErr,
                                                    status: 500,
                                                    ECODE: 'ITEM_019',
                                                });
                                                wf_cb({
                                                    err: 'Internal server error',
                                                    status: 500,
                                                    ECODE: 'ITEM_019',
                                                });
                                            } else {
                                                wf_cb();
                                            }
                                        });
                                    },
                                    wf_cb => {
                                        // manage assignees and followers and boardindex
                                        const queries = [];
                                        async.parallel(
                                            {
                                                edit(para_cb) {
                                                    if (options.log_query) {
                                                        logger.debug({
                                                            msg: 'EDITING ITEM FROM AUTOMATION',
                                                            query,
                                                            query_params,
                                                            item_id,
                                                        });
                                                    }

                                                    queries.push({
                                                        query,
                                                        params: query_params,
                                                    });
                                                    para_cb();
                                                },

                                                add(para_cb) {
                                                    if (assignees.add.length + group_assignees.add.length === 0) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    group_assignees.add.forEach(group_id => {
                                                        if (before_group_assignees.includes(group_id)) {
                                                            return;
                                                        }

                                                        const insert_group_assignee = [item_id, group_id, now, team_id];

                                                        let points_insert = ``;
                                                        let points_column = ``;
                                                        if (
                                                            points_per_assignee &&
                                                            total_before_assignees_count === 0 &&
                                                            before.points
                                                        ) {
                                                            insert_group_assignee.push(before.points);
                                                            points_insert = `, $${insert_group_assignee.length}`;
                                                            points_column = ', points_float';
                                                        } else if (points_per_assignee) {
                                                            insert_group_assignee.push(0);
                                                            points_insert = `, $${insert_group_assignee.length}`;
                                                            points_column = ', points_float';
                                                        }

                                                        addedAssignees.push(group_id);

                                                        queries.push({
                                                            query: `
                                                            INSERT INTO task_mgmt.group_assignees(task_id, group_id, date_assigned, workspace_id ${points_column})
                                                            VALUES ($1, $2, $3, $4 ${points_insert})
                                                            ON CONFLICT (task_id, group_id)
                                                            DO UPDATE SET date_assigned = $3`,
                                                            params: insert_group_assignee,
                                                        });

                                                        if (hist_params.length > 0) {
                                                            hist_query += '), (';
                                                        }

                                                        hist_query += `
                                                            $${hist_params.length + 1},
                                                            $${hist_params.length + 2},
                                                            $${hist_params.length + 3},
                                                            $${hist_params.length + 4},
                                                            $${hist_params.length + 5},
                                                            $${hist_params.length + 6},
                                                            $${hist_params.length + 7}`;

                                                        hist_params.push(
                                                            item_id,
                                                            'group_assignee',
                                                            now,
                                                            null,
                                                            group_id,
                                                            userid,
                                                            getCommonHistoryData()
                                                        );
                                                    });

                                                    assignees.add.forEach(userToAdd => {
                                                        if (before_assignees.indexOf(userToAdd) < 0) {
                                                            addedAssignees.push(userToAdd);

                                                            const insert_assignee = [
                                                                item_id,
                                                                userToAdd,
                                                                new Date().getTime(),
                                                                team_id,
                                                            ];
                                                            let estimate_insert = ``;
                                                            let estimate_column = ``;
                                                            let points_insert = ``;
                                                            let points_column = ``;
                                                            const noAssignees =
                                                                total_before_assignees_count === 0 ||
                                                                total_before_assignees_count -
                                                                    (rem_assignees?.length || 0) ===
                                                                    0;

                                                            if (
                                                                estimates_per_assignee &&
                                                                noAssignees &&
                                                                before.time_estimate
                                                            ) {
                                                                insert_assignee.push(
                                                                    assignees.add.length > 0 && before.time_estimate
                                                                        ? Math.round(
                                                                              before.time_estimate /
                                                                                  assignees.add.length
                                                                          )
                                                                        : 0
                                                                );
                                                                estimate_insert = `, $${insert_assignee.length}`;
                                                                estimate_column = ', time_estimate';
                                                            } else if (estimates_per_assignee) {
                                                                insert_assignee.push(0);
                                                                estimate_insert = `, $${insert_assignee.length}`;
                                                                estimate_column = ', time_estimate';
                                                            }

                                                            if (points_per_assignee && noAssignees && before.points) {
                                                                insert_assignee.push(before.points);
                                                                points_insert = `, $${insert_assignee.length}`;
                                                                points_column = ', points_float';
                                                            } else if (points_per_assignee) {
                                                                insert_assignee.push(0);
                                                                points_insert = `, $${insert_assignee.length}`;
                                                                points_column = ', points_float';
                                                            }

                                                            queries.push({
                                                                query: `INSERT INTO task_mgmt.assignees(task_id, userid, date_assigned, workspace_id ${estimate_column} ${points_column}) (SELECT $1, $2, $3, $4 ${estimate_insert} ${points_insert}
                                                                WHERE NOT EXISTS (SELECT * FROM task_mgmt.assignees WHERE task_id = $1 AND userid = $2))`,
                                                                params: insert_assignee,
                                                            });

                                                            queries.push({
                                                                query: `
                                                                    INSERT INTO task_mgmt.assignees(task_id, userid, date_assigned, workspace_id)
                                                                    VALUES ($1, $2, $3, $4)
                                                                    ON CONFLICT (task_id, userid)
                                                                    DO UPDATE SET date_assigned = $3`,
                                                                params: [
                                                                    item_id,
                                                                    userToAdd,
                                                                    new Date().getTime(),
                                                                    team_id,
                                                                ],
                                                            });

                                                            if (hist_params.length > 0) {
                                                                hist_query += '), (';
                                                            }

                                                            hist_query += `
                                                                $${hist_params.length + 1},
                                                                $${hist_params.length + 2},
                                                                $${hist_params.length + 3},
                                                                $${hist_params.length + 4},
                                                                $${hist_params.length + 5},
                                                                $${hist_params.length + 6},
                                                                $${hist_params.length + 7}`;
                                                            hist_params.push(
                                                                item_id,
                                                                'assignee',
                                                                now,
                                                                null,
                                                                userToAdd,
                                                                userid,
                                                                getCommonHistoryData()
                                                            );
                                                        }
                                                    });

                                                    const users_assigned_without_self_before = assignees.add.filter(
                                                        t => t !== userid && !before_assignees.includes(t)
                                                    );

                                                    if (!users_assigned_without_self_before.length) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    const weight_params = [];
                                                    let weight_query = `
                                                    INSERT INTO task_mgmt.team_user_weights
                                                        (team_id, userid, assigned_userid, type, date) VALUES
                                                `;
                                                    users_assigned_without_self_before.forEach(uid => {
                                                        weight_query += `(
                                                        $${weight_params.push(team_id)},
                                                        $${weight_params.push(userid)},
                                                        $${weight_params.push(uid)},
                                                        $${weight_params.push(config.user_weight_type.assign)},
                                                        $${weight_params.push(now)}
                                                    ), `;
                                                    });
                                                    weight_query = weight_query.slice(0, -2);

                                                    weight_query += ` ON CONFLICT DO NOTHING`;

                                                    queries.push({
                                                        query: weight_query,
                                                        params: weight_params,
                                                    });

                                                    para_cb();
                                                },

                                                rem(para_cb) {
                                                    if (assignees.rem.length + group_assignees.rem.length === 0) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    group_assignees.rem.forEach(group_id => {
                                                        if (!before_group_assignees.includes(group_id)) {
                                                            return;
                                                        }

                                                        removedAssignees.push(group_id);

                                                        queries.push(
                                                            {
                                                                query: `
                                                                UPDATE task_mgmt.task_history
                                                                SET hidden = true
                                                                WHERE id IN (
                                                                    SELECT id
                                                                    FROM task_mgmt.task_history
                                                                    WHERE task_id = $1
                                                                        AND field = $2
                                                                        AND userid = $3
                                                                        AND date >= $4
                                                                )`,
                                                                params: [item_id, 'group_assignee', userid, now],
                                                            },
                                                            {
                                                                query: `
                                                                INSERT INTO task_mgmt.task_history(task_id, field, before, userid, date, data)
                                                                VALUES ($1, $2, $3, $4, $5, $6)
                                                                RETURNING id, field, before, after`,
                                                                params: [
                                                                    item_id,
                                                                    'group_assignee',
                                                                    group_id,
                                                                    userid,
                                                                    now,
                                                                    getCommonHistoryData(),
                                                                ],
                                                            }
                                                        );
                                                    });

                                                    assignees.rem.forEach(userToRem => {
                                                        removedAssignees.push(userToRem);

                                                        if (!before_assignees.includes(userToRem)) {
                                                            return;
                                                        }

                                                        queries.push(
                                                            {
                                                                query: `
                                                                INSERT INTO task_mgmt.task_history(task_id, field, before, userid, date, data)
                                                                VALUES ($1, $2, $3, $4, $5, $6)
                                                                RETURNING id, field, before, after`,
                                                                params: [
                                                                    item_id,
                                                                    'assignee',
                                                                    userToRem,
                                                                    userid,
                                                                    now,
                                                                    getCommonHistoryData(),
                                                                ],
                                                            },
                                                            {
                                                                query: `
                                                                UPDATE task_mgmt.task_history
                                                                SET hidden = true
                                                                WHERE id IN (
                                                                    SELECT id
                                                                    FROM task_mgmt.task_history
                                                                    WHERE task_id = (
                                                                      SELECT DISTINCT task_id
                                                                      FROM task_mgmt.task_history
                                                                      WHERE task_id = $1
                                                                      AND field = $2
                                                                      AND userid = $3
                                                                      AND date >= $4
                                                                      AND after = $5
                                                                    )
                                                                      AND field = $2
                                                                      AND userid = $3
                                                                      AND date >= $4
                                                                      AND (before = $5 or after = $5)
                                                                )`,
                                                                params: [item_id, 'assignee', userid, now, userToRem],
                                                            }
                                                        );
                                                    });

                                                    if (assignees.rem.length) {
                                                        queries.push(
                                                            {
                                                                query: `DELETE FROM task_mgmt.assignees WHERE task_id = $1 and userid = ANY($2)`,
                                                                params: [item_id, assignees.rem],
                                                            },
                                                            {
                                                                query: `
                                                                DELETE FROM task_mgmt.task_notifs
                                                                WHERE hist_id IN (
                                                                    SELECT id
                                                                    FROM task_mgmt.task_history
                                                                    WHERE task_id = $1
                                                                        AND after = ANY($2)
                                                                        AND field = 'assignee'
                                                                )
                                                                    AND archived = false`,
                                                                params: [item_id, assignees.rem],
                                                            }
                                                        );
                                                    }

                                                    if (group_assignees.rem.length) {
                                                        queries.push(
                                                            {
                                                                query: `
                                                                DELETE FROM task_mgmt.group_assignees
                                                                WHERE task_id = $1
                                                                    AND group_id = ANY($2)`,
                                                                params: [item_id, group_assignees.rem],
                                                            },
                                                            {
                                                                query: `
                                                                DELETE FROM task_mgmt.task_notifs
                                                                WHERE hist_id IN (
                                                                    SELECT id
                                                                    FROM task_mgmt.task_history
                                                                    WHERE task_id = $1
                                                                        AND after = ANY($2)
                                                                        AND field = 'group_assignee'
                                                                )
                                                                    AND archived = false`,
                                                                params: [item_id, group_assignees.rem],
                                                            }
                                                        );
                                                    }

                                                    para_cb();
                                                },

                                                addGroupFollowers(para_cb) {
                                                    if (
                                                        !params.group_followers.add.length &&
                                                        !group_assignees.add.length
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    const groups_to_add = [
                                                        ...params.group_followers.add,
                                                        ...group_assignees.add,
                                                    ];

                                                    groups_to_add.forEach(group_id => {
                                                        const gf_query = `
                                                            INSERT INTO task_mgmt.group_followers(task_id, group_id, date_added, workspace_id)
                                                            VALUES($1, $2, $3, $4)
                                                            ON CONFLICT DO NOTHING`;
                                                        const gf_params = [
                                                            item_id,
                                                            group_id,
                                                            new Date().getTime(),
                                                            team_id,
                                                        ];

                                                        queries.push({ query: gf_query, params: gf_params });
                                                    });

                                                    para_cb();
                                                },

                                                remGroupFollowers(para_cb) {
                                                    if (!params.group_followers.rem.length) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    queries.push({
                                                        query: `DELETE FROM task_mgmt.group_followers WHERE task_id = $1 and group_id = ANY($2)`,
                                                        params: [item_id, params.group_followers.rem],
                                                    });
                                                    para_cb();
                                                },

                                                splitTimeEstimate(para_cb) {
                                                    if (
                                                        !estimates_per_assignee ||
                                                        !params ||
                                                        params.time_estimate === undefined ||
                                                        !before_assignees_count ||
                                                        (rem_assignees && rem_assignees.length)
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    let timesplit = Math.floor(
                                                        Number(params.time_estimate) / before_assignees_count
                                                    );

                                                    if (!params.time_estimate) {
                                                        timesplit = null;
                                                    }

                                                    queries.push({
                                                        query: `UPDATE task_mgmt.assignees SET time_estimate = $1 WHERE task_id = $2`,
                                                        params: [timesplit, item_id],
                                                    });

                                                    para_cb();
                                                },

                                                addFollowers(para_cb) {
                                                    if (
                                                        (!followers.add || followers.add.length === 0) &&
                                                        (!assignees.add || assignees.add.length === 0)
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    let usersToAdd = [];
                                                    if (followers.add) {
                                                        usersToAdd = usersToAdd.concat(
                                                            followers.add.map(userToAdd => ({
                                                                id: userToAdd,
                                                                source: null,
                                                            }))
                                                        );
                                                    }

                                                    if (assignees.add && Array.isArray(assignees.add)) {
                                                        usersToAdd = usersToAdd.concat(
                                                            assignees.add.map(userToAdd => ({
                                                                id: userToAdd,
                                                                source: 'assignee_added',
                                                            }))
                                                        );
                                                    }
                                                    const followers_added = [];
                                                    usersToAdd.forEach(userToAdd => {
                                                        if (followers_added.indexOf(userToAdd.id) < 0) {
                                                            followers_added.push(userToAdd.id);

                                                            queries.push({
                                                                query: 'INSERT INTO task_mgmt.followers(task_id, userid, added_from, workspace_id) VALUES ($1, $2, $3, $4) ON CONFLICT (task_id, userid) DO NOTHING',
                                                                params: [
                                                                    item_id,
                                                                    userToAdd.id,
                                                                    userToAdd.source,
                                                                    team_id,
                                                                ],
                                                            }, {
                                                                query: `DELETE FROM task_mgmt.shared_entity_unfollowers WHERE workspace_id = $1 AND entity_id = $2 AND entity_type = $3 AND user_id = $4`,
                                                                params: [team_id, item_id, EntityTypeConst.TASK, userToAdd.id],
                                                            });

                                                            if (hist_params.length > 0) {
                                                                hist_query += '), (';
                                                            }

                                                            hist_query += `$${hist_params.length + 1}, $${
                                                                hist_params.length + 2
                                                            }, $${hist_params.length + 3}, $${
                                                                hist_params.length + 4
                                                            }, $${hist_params.length + 5}, $${
                                                                hist_params.length + 6
                                                            }, $${hist_params.length + 7}`;

                                                            hist_params.push(
                                                                item_id,
                                                                'follower',
                                                                now,
                                                                null,
                                                                userToAdd.id,
                                                                userid,
                                                                getCommonHistoryData()
                                                            );
                                                        }
                                                    });
                                                    para_cb();
                                                },
                                                remFollowers(para_cb) {
                                                    if (!followers.rem || followers.rem.length === 0) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    const deleteQuery =
                                                        'DELETE FROM task_mgmt.followers WHERE task_id = $1 and userid = ANY($2)';

                                                    const unfollowersParam = [team_id, EntityTypeConst.TASK, item_id];
                                                    queries.push({
                                                        query: deleteQuery,
                                                        params: [item_id, followers.rem],
                                                    }, {
                                                        query: `INSERT INTO task_mgmt.shared_entity_unfollowers(workspace_id, entity_type, entity_id, user_id) VALUES
                                                            ${followers.rem
                                                                .map(
                                                                    (userId) =>
                                                                        `($1, $2, $3, $${unfollowersParam.push(
                                                                            userId
                                                                        )})`
                                                                )
                                                                .join(',')}`,
                                                        params: unfollowersParam,
                                                    });

                                                    followers.rem.forEach(rem => {
                                                        if (before_followers.includes(rem)) {
                                                            if (hist_params.length > 0) {
                                                                hist_query += '), (';
                                                            }

                                                            hist_query += `$${hist_params.length + 1}, $${
                                                                hist_params.length + 2
                                                            }, $${hist_params.length + 3}, $${
                                                                hist_params.length + 4
                                                            }, $${hist_params.length + 5}, $${
                                                                hist_params.length + 6
                                                            }, $${hist_params.length + 7}`;

                                                            hist_params.push(
                                                                item_id,
                                                                'follower',
                                                                now,
                                                                rem,
                                                                null,
                                                                userid,
                                                                getCommonHistoryData()
                                                            );
                                                        }
                                                    });

                                                    para_cb();
                                                },
                                                individualQueries(para_cb) {
                                                    async.eachSeries(
                                                        individualQueries,
                                                        (queryObj, each_cb) => {
                                                            client.query(
                                                                queryObj.query,
                                                                queryObj.params,
                                                                (queryErr, queryResult) => {
                                                                    if (queryErr) {
                                                                        logger.error({
                                                                            msg: 'Failed to edit item',
                                                                            err: queryErr,
                                                                            ECODE: 'ITEM_217',
                                                                        });
                                                                        each_cb(queryErr);
                                                                        return;
                                                                    }

                                                                    const queryResultRow = queryResult?.rows?.[0];
                                                                    if (
                                                                        queryResultRow &&
                                                                        queryResultRow.id &&
                                                                        queryResultRow.field !== 'html_content' &&
                                                                        queryResultRow.operation !== 'remap' &&
                                                                        queryResultRow.operation !== 'remap_dependency'
                                                                    ) {
                                                                        hist_ids.push(queryResultRow.id);

                                                                        const versionUpdateChange = {
                                                                            history_id: queryResultRow.id,
                                                                            field: queryResultRow.field,
                                                                        };

                                                                        if (
                                                                            shouldIncludeBeforeAndAfterInVersionUpdate(
                                                                                queryResultRow.field
                                                                            )
                                                                        ) {
                                                                            versionUpdateChange.before =
                                                                                queryResultRow.before;
                                                                            versionUpdateChange.after =
                                                                                queryResultRow.after;
                                                                        }

                                                                        versionUpdateChanges.push(versionUpdateChange);
                                                                    }

                                                                    if (
                                                                        queryResult &&
                                                                        queryResult.rows[0] &&
                                                                        queryResult.rows[0].before_hist_id
                                                                    ) {
                                                                        before_hist_ids.push(
                                                                            queryResult.rows[0].before_hist_id
                                                                        );
                                                                    }

                                                                    if (queryResult && queryResult.rows) {
                                                                        queryResult.rows.forEach(row => {
                                                                            if (
                                                                                row.operation === 'remap_dependency' ||
                                                                                row.operation === 'remap'
                                                                            ) {
                                                                                dependencies_adjusted.push({
                                                                                    id: row.id,
                                                                                    diff: parseInt(
                                                                                        row.due_date_diff,
                                                                                        10
                                                                                    ),
                                                                                    hist_id: row.hist_id,
                                                                                });
                                                                                due_dates_adjusted.push({
                                                                                    task_id: row.id,
                                                                                    hist_id: row.hist_id,
                                                                                });
                                                                            }

                                                                            if (row.updated_task_id) {
                                                                                versionUpdates.push({
                                                                                    object_type: ObjectType.TASK,
                                                                                    object_id: row.updated_task_id,
                                                                                    workspace_id: team_id,
                                                                                    operation: OperationType.UPDATE,
                                                                                    data: {
                                                                                        changes: versionUpdateChanges,
                                                                                        context: {
                                                                                            ws_key:
                                                                                                options.ws_key ??
                                                                                                params.ws_key,
                                                                                            originating_service:
                                                                                                options.originating_service_override,
                                                                                            audit_context: {
                                                                                                userid,
                                                                                            },
                                                                                        },
                                                                                        relationships:
                                                                                            getParentTaskChangeEventRelationship(
                                                                                                {
                                                                                                    operation:
                                                                                                        OperationType.UPDATE,
                                                                                                    parent_task_ids:
                                                                                                        taskIdsFromParentChain,
                                                                                                    team_id,
                                                                                                }
                                                                                            ),
                                                                                    },
                                                                                });
                                                                                if (should_update_task_access) {
                                                                                    versionUpdates.push({
                                                                                        object_type:
                                                                                            ObjectType.TASK_ACCESS,
                                                                                        object_id: row.updated_task_id,
                                                                                        workspace_id: team_id,
                                                                                        operation: OperationType.UPDATE,
                                                                                    });
                                                                                }
                                                                            }
                                                                        });
                                                                    }

                                                                    if (queryResult?.rows?.length) {
                                                                        subtasks_history.push(
                                                                            ...queryResult.rows
                                                                                .filter(
                                                                                    ({ task_id }) =>
                                                                                        task_id && task_id !== item_id
                                                                                )
                                                                                .map(({ id, task_id }) => ({
                                                                                    hist_id: id,
                                                                                    task_id,
                                                                                }))
                                                                        );
                                                                    }
                                                                    each_cb();
                                                                }
                                                            );
                                                        },
                                                        seriesErr => {
                                                            if (shouldUseHiddenCoverImageCopy(team_id)) {
                                                                clearUnusedCoverImage(
                                                                    before,
                                                                    params,
                                                                    team_id,
                                                                    seriesErr
                                                                );
                                                            }
                                                            para_cb(seriesErr);
                                                        }
                                                    );
                                                },
                                                customItemsHistory(para_cb) {
                                                    if (
                                                        customType.isUnset() ||
                                                        customType.value === before.custom_type
                                                    ) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    const cust_query = `INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid, data) VALUES($1, $2, $3, $4, $5, $6, $7) RETURNING id`;
                                                    const cust_params = [
                                                        item_id,
                                                        'custom_type',
                                                        now,
                                                        before.custom_type,
                                                        customType.value,
                                                        userid,
                                                        getCommonHistoryData(),
                                                    ];

                                                    queries.push({ query: cust_query, params: cust_params });
                                                    para_cb();
                                                },
                                                timelineIncrease(para_cb) {
                                                    if (!options.timeline) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    queries.push({
                                                        query: `UPDATE task_mgmt.team_limits SET timelines = COALESCE(timelines, 0) + 1 WHERE team_id = $1`,
                                                        params: [team_id],
                                                    });
                                                    para_cb();
                                                },
                                                workloadIncrease(para_cb) {
                                                    if (!options.workload) {
                                                        para_cb();
                                                        return;
                                                    }

                                                    queries.push({
                                                        query: `UPDATE task_mgmt.team_limits SET workloads = COALESCE(workloads, 0) + 1 WHERE team_id = $1`,
                                                        params: [team_id],
                                                    });
                                                    para_cb();
                                                },
                                                mentions(para_cb) {
                                                    if (!params.content) {
                                                        para_cb();
                                                        return;
                                                    }
                                                    mentionMod._setDescriptionMentions(
                                                        item_id,
                                                        params.content,
                                                        {
                                                            client,
                                                            mentions: in_mentions,
                                                        },
                                                        para_cb
                                                    );
                                                },
                                                async incrementCustomTaskTypeUsage(series_cb) {
                                                    if (!customType.isUsableCustomType()) {
                                                        series_cb();
                                                        return;
                                                    }

                                                    try {
                                                        const ovm = getObjectVersionManager();
                                                        const txClient = new TransactionClientImpl(client, ovm);
                                                        await entitlementService.incrementEntitlement(
                                                            team_id,
                                                            EntitlementName.CustomItems,
                                                            {},
                                                            txClient
                                                        );
                                                        series_cb();
                                                    } catch (series_err) {
                                                        series_cb(series_err);
                                                    }
                                                },
                                            },
                                            (paraErr, paraResult) => {
                                                if (paraErr) {
                                                    logger.error({
                                                        msg: 'Error editting item',
                                                        err: paraErr,
                                                        status: 500,
                                                        ECODE: 'ITEM_021',
                                                    });
                                                    db.rollback(client, done);
                                                    wf_cb({
                                                        err: 'Internal server error',
                                                        status: 500,
                                                        ECODE: 'ITEM_021',
                                                    });
                                                } else {
                                                    if (options.middleware_queries) {
                                                        // merge middleware queries into the current queries
                                                        queries.push(...options.middleware_queries);
                                                        // merge middleware version update requests into the current requests
                                                        const mqVersionUpdates = options.middleware_queries.reduce(
                                                            (acc, mq) =>
                                                                mq.versionUpdates ? acc.concat(mq.versionUpdates) : acc,
                                                            []
                                                        );
                                                        versionUpdates.push(...mqVersionUpdates);
                                                    }

                                                    if (
                                                        paraResult.updateOrderindex &&
                                                        paraResult.updateOrderindex.success_funcs
                                                    ) {
                                                        success_funcs.push(
                                                            ...paraResult.updateOrderindex.success_funcs
                                                        );
                                                    }

                                                    async.eachSeries(
                                                        queries,
                                                        (queryObj, each_cb) => {
                                                            client.query(
                                                                queryObj.query,
                                                                queryObj.params,
                                                                (queryErr, queryResult) => {
                                                                    const queryResultRow = queryResult?.rows?.[0];

                                                                    if (queryResultRow) {
                                                                        if (queryResultRow.id) {
                                                                            hist_ids.push(queryResultRow.id);
                                                                        }

                                                                        if (queryResultRow.before_hist_id) {
                                                                            before_hist_ids.push(
                                                                                queryResultRow.before_hist_id
                                                                            );
                                                                        }

                                                                        if (queryResultRow.id && queryResultRow.field) {
                                                                            const versionUpdateChange = {
                                                                                history_id: queryResultRow.id,
                                                                                field: queryResultRow.field,
                                                                            };

                                                                            if (
                                                                                shouldIncludeBeforeAndAfterInVersionUpdate(
                                                                                    queryResultRow.field
                                                                                )
                                                                            ) {
                                                                                versionUpdateChange.before =
                                                                                    queryResultRow.before;
                                                                                versionUpdateChange.after =
                                                                                    queryResultRow.after;
                                                                            }

                                                                            versionUpdateChanges.push(
                                                                                versionUpdateChange
                                                                            );
                                                                        }

                                                                        if (queryResult?.rows) {
                                                                            for (const row of queryResult.rows) {
                                                                                if (row.updated_task_id) {
                                                                                    versionUpdates.push({
                                                                                        object_type: ObjectType.TASK,
                                                                                        object_id: row.updated_task_id,
                                                                                        workspace_id: team_id,
                                                                                        operation: OperationType.UPDATE,
                                                                                        data: {
                                                                                            changes:
                                                                                                filterOvmChangeValues({
                                                                                                    changes:
                                                                                                        versionUpdateChanges,
                                                                                                    workspaceId:
                                                                                                        team_id,
                                                                                                }),
                                                                                            context: {
                                                                                                ws_key:
                                                                                                    options.ws_key ??
                                                                                                    params.ws_key,
                                                                                                originating_service:
                                                                                                    options.originating_service_override,
                                                                                                audit_context: {
                                                                                                    userid,
                                                                                                },
                                                                                            },
                                                                                            relationships:
                                                                                                getParentTaskChangeEventRelationship(
                                                                                                    {
                                                                                                        operation:
                                                                                                            OperationType.UPDATE,
                                                                                                        parent_task_ids:
                                                                                                            taskIdsFromParentChain,
                                                                                                        team_id,
                                                                                                    }
                                                                                                ),
                                                                                        },
                                                                                    });
                                                                                    if (should_update_task_access) {
                                                                                        versionUpdates.push({
                                                                                            object_type:
                                                                                                ObjectType.TASK_ACCESS,
                                                                                            object_id:
                                                                                                row.updated_task_id,
                                                                                            workspace_id: team_id,
                                                                                            operation:
                                                                                                OperationType.UPDATE,
                                                                                        });
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                    each_cb(queryErr);
                                                                }
                                                            );
                                                        },
                                                        eachErr => {
                                                            if (eachErr) {
                                                                logger.error({
                                                                    msg: 'Error editting item',
                                                                    err: eachErr,
                                                                    status: 500,
                                                                    ECODE: 'ITEM_025',
                                                                });
                                                                db.rollback(client, done);
                                                                wf_cb({
                                                                    err: 'Internal server error',
                                                                    status: 500,
                                                                    ECODE: 'ITEM_025',
                                                                });
                                                            } else {
                                                                wf_cb();
                                                            }
                                                        }
                                                    );
                                                }
                                            }
                                        );
                                    },
                                    wf_cb => {
                                        if (!breakEditTaskTransaction) {
                                            wf_cb();
                                            return;
                                        }

                                        // when edit task transaction is broken up into several
                                        // individual database transactions, we will only create a
                                        // transaction for task history + ovm version + ovm history
                                        // updates into a single transaction.  this is more efficient
                                        // and the transaction won't have any long waits in the
                                        // application layer
                                        client.query('BEGIN', beginErr => {
                                            if (beginErr) {
                                                db.rollback(client, done);
                                                logger.error({
                                                    msg: 'Error beginning',
                                                    err: beginErr,
                                                    status: 500,
                                                    ECODE: 'ITEM_019',
                                                });
                                                wf_cb({
                                                    err: 'Internal server error',
                                                    status: 500,
                                                    ECODE: 'ITEM_019',
                                                });
                                            } else {
                                                wf_cb();
                                            }
                                        });
                                    },
                                    wf_cb => {
                                        if (hist_params.length === 0) {
                                            wf_cb();
                                            return;
                                        }
                                        // add history
                                        hist_query += ') RETURNING *';
                                        if (!revision) {
                                            if (!undo_ids || undo_ids.length === 0) {
                                                client.query(hist_query, hist_params, (histErr, histResult) => {
                                                    if (histErr) {
                                                        logger.error({
                                                            msg: 'Error inserting into the history',
                                                            err: histErr,
                                                            status: 500,
                                                            ECODE: 'ITEM_022',
                                                        });
                                                        db.rollback(client, done);
                                                        wf_cb({
                                                            err: 'Internal server error',
                                                            status: 500,
                                                            ECODE: 'ITEM_022',
                                                        });
                                                    } else {
                                                        histResult.rows.forEach(row => {
                                                            const versionUpdateChange = {
                                                                history_id: row.id,
                                                                field: row.field,
                                                            };

                                                            if (shouldIncludeBeforeAndAfterInVersionUpdate(row.field)) {
                                                                versionUpdateChange.before = row.before;
                                                                versionUpdateChange.after = row.after;
                                                            }

                                                            versionUpdateChanges.push(versionUpdateChange);

                                                            if (
                                                                // Only create follower notifs if the actor is not the new follower
                                                                row.field === 'follower' &&
                                                                row.after &&
                                                                row.after !== String(userid) &&
                                                                // And only create follower notifs if this task edit is simply adding watchers
                                                                followers &&
                                                                followers.add &&
                                                                followers.add.length > 0
                                                            ) {
                                                                follower_notifs.push(row);
                                                                hist_ids.push(row.id);
                                                            } else if (
                                                                ![
                                                                    'follower',
                                                                    'public',
                                                                    'public_fields',
                                                                    'public_token',
                                                                    'seo_optimized',
                                                                    'subtask_sort',
                                                                    'subtask_sort_dir',
                                                                    'public_permission_level',
                                                                    'public_share_expires_on',
                                                                ].includes(row.field) &&
                                                                !(row.field === 'gh_commit' && row.before) &&
                                                                !(row.field === 'assignee' && row.before)
                                                            ) {
                                                                hist_ids.push(row.id);
                                                            }
                                                        });
                                                        wf_cb();
                                                    }
                                                });
                                            } else {
                                                wf_cb();
                                            }
                                        } else {
                                            const rev_query =
                                                'INSERT INTO task_mgmt.task_history(task_id, field, date, before, after, userid) VALUES ($1, $2, $3, $4, $5, $6)';
                                            const rev_params = [item_id, 'revision', now, null, revision, userid];
                                            client.query(rev_query, rev_params, histErr => {
                                                if (histErr) {
                                                    logger.error({
                                                        msg: 'Error inserting into the history',
                                                        err: histErr,
                                                        status: 500,
                                                        ECODE: 'ITEM_023',
                                                    });
                                                    db.rollback(client, done);
                                                    wf_cb({
                                                        err: 'Internal server error',
                                                        status: 500,
                                                        ECODE: 'ITEM_023',
                                                    });
                                                } else {
                                                    wf_cb();
                                                }
                                            });
                                        }
                                    },
                                    async wf_cb => {
                                        const ovm = getObjectVersionManager();
                                        try {
                                            const txClient = new TransactionClientImpl(client, ovm);
                                            if (
                                                !versionUpdates.some(
                                                    vu => vu.object_type === ObjectType.TASK && vu.object_id === item_id
                                                )
                                            ) {
                                                versionUpdates.push({
                                                    object_type: ObjectType.TASK,
                                                    object_id: item_id,
                                                    workspace_id: team_id,
                                                    operation: OperationType.UPDATE,
                                                    data: {
                                                        changes: versionUpdateChanges,
                                                        relationships: [
                                                            ...getRollupChangeEventRelationships({
                                                                item_id,
                                                                team_id,
                                                                time_estimate_rollup,
                                                                points_rollup,
                                                                versionUpdateChanges,
                                                            }),
                                                            ...getParentTaskChangeEventRelationship({
                                                                operation: OperationType.UPDATE,
                                                                parent_task_ids: taskIdsFromParentChain,
                                                                team_id,
                                                            }),
                                                        ],
                                                        context: {
                                                            ws_key: options.ws_key ?? params.ws_key,
                                                            originating_service: options.originating_service_override,
                                                            audit_context: {
                                                                userid,
                                                            },
                                                        },
                                                    },
                                                });
                                                if (should_update_task_access) {
                                                    versionUpdates.push({
                                                        object_type: ObjectType.TASK_ACCESS,
                                                        object_id: item_id,
                                                        workspace_id: team_id,
                                                        operation: OperationType.UPDATE,
                                                        data: {
                                                            changes: versionUpdateChanges,
                                                            relationships: appendTaskAccessFanOutEvent({
                                                                task_id: item_id,
                                                                team_id,
                                                                operation: OperationType.UPDATE,
                                                            }),
                                                        },
                                                    });
                                                }
                                            }
                                            events = await ovm.updateVersions(txClient, versionUpdates);
                                        } catch (versionerr) {
                                            db.rollback(client, done);
                                            wf_cb(versionerr);
                                            return;
                                        }

                                        // this COMMIT ends either the uber transaction from beginning or
                                        // the transaction just before task history, depending on the Split flag
                                        // shouldUseBreakEditTaskTransaction()
                                        client.query('COMMIT', async commitErr => {
                                            if (commitErr) {
                                                db.rollback(client, done);
                                                logger.error({
                                                    msg: 'Error committing',
                                                    err: commitErr,
                                                    status: 500,
                                                    ECODE: 'ITEM_024',
                                                });
                                                wf_cb({
                                                    err: 'Internal server error',
                                                    status: 500,
                                                    ECODE: 'ITEM_024',
                                                });
                                            } else {
                                                done();
                                                wf_cb();
                                                // eslint-disable-next-line @typescript-eslint/no-empty-function
                                                await ovm.notifyChanges(events).catch(() => {});
                                            }
                                        });
                                    },
                                    async wf_cb => {
                                        if (!params.content || params.skip_content_broadcast) {
                                            wf_cb();
                                            return;
                                        }

                                        coeditorClientInstance
                                            .postContentUpdate(item_id, EntityType.TASK, params.content, team_id)
                                            .catch(() => {});

                                        wf_cb();
                                    },
                                    async wf_cb => {
                                        try {
                                            await adjustAutomationsAfterEditTask(dateOfTasksBeforeEdit, {
                                                scheduled_from: 'edit_task_refactored',
                                                trigger_id: options.trigger_id,
                                                auto_id: options.auto_id,
                                            });
                                            wf_cb();
                                        } catch (e) {
                                            wf_cb(e);
                                        }
                                    },
                                    async wf_cb => {
                                        try {
                                            if (params.due_date && params.due_date !== before.due_date) {
                                                await updateRecurringForDueDateChange(
                                                    userid,
                                                    [item_id],
                                                    params.due_date,
                                                    params.due_date_time
                                                );
                                            }
                                            wf_cb();
                                        } catch (e) {
                                            wf_cb(e);
                                        }
                                    },
                                    wf_cb => {
                                        if (!time_estimate_rollup) {
                                            wf_cb();
                                            return;
                                        }

                                        rollUpTimeEstimate = getRolledUpTimeEstimate(
                                            [item_id],
                                            undefined,
                                            (rollUpErr, rollupResult) => {
                                                if (rollUpErr) {
                                                    wf_cb(rollUpErr);
                                                    return;
                                                }

                                                rollUpTimeEstimate = rollupResult[item_id] || null;

                                                if (rollUpTimeEstimate && params.time_estimate) {
                                                    rollUpTimeEstimate =
                                                        (rollUpTimeEstimate - before.time_estimate || 0) +
                                                        params.time_estimate;
                                                }

                                                wf_cb();
                                            }
                                        );
                                    },
                                    wf_cb => {
                                        if (followers && followers.rem && followers.rem.indexOf(userid) >= 0) {
                                            wf_cb();
                                            return;
                                        }

                                        if (params.dont_follow) {
                                            wf_cb();
                                            return;
                                        }

                                        if (
                                            userid === config.clickbot_assignee ||
                                            (aiSkipAccessForAgentUser() && isAgentUserRequest())
                                        ) {
                                            wf_cb();
                                            return;
                                        }

                                        db.writeQuery(
                                            'INSERT INTO task_mgmt.followers(task_id, userid, added_from, workspace_id)  (SELECT $1, $2, $3, $4 WHERE NOT EXISTS (SELECT * FROM task_mgmt.followers WHERE task_id = $1 AND userid = $2))',
                                            [item_id, userid, null, team_id],
                                            () => {
                                                wf_cb();
                                            }
                                        );
                                    },
                                    async wf_cb => {
                                        if (!addedAssignees.length && !removedAssignees.length) {
                                            wf_cb();
                                            return;
                                        }

                                        try {
                                            await orderindexMod.setTaskAtMaxIndex(item_id, 'assignee_orderindex');
                                            wf_cb();
                                        } catch (e) {
                                            wf_cb();
                                        }
                                    },

                                    async wf_cb => {
                                        if (!params.priority) {
                                            wf_cb();
                                            return;
                                        }

                                        try {
                                            await orderindexMod.setTaskAtMaxIndex(item_id, 'priority_orderindex');
                                            wf_cb();
                                        } catch (e) {
                                            wf_cb();
                                        }
                                    },

                                    async wf_cb => {
                                        if (!params.status) {
                                            wf_cb();
                                            return;
                                        }

                                        try {
                                            await orderindexMod.setTaskAtMaxIndex(item_id, 'orderindex');
                                            wf_cb();
                                        } catch (e) {
                                            wf_cb();
                                        }
                                    },

                                    wf_cb => {
                                        if (!new_parent && !params.subtask_parent) {
                                            wf_cb();
                                            return;
                                        }

                                        const task_obj = { id: item_id };
                                        if (new_parent) {
                                            task_obj.new_parent = new_parent;
                                        }
                                        if (params.subtask_parent) {
                                            task_obj.new_subtask_parent = params.subtask_parent;
                                        }

                                        floatOrder._setItemOrder(
                                            userid,
                                            [task_obj],
                                            {
                                                ws_key: params.ws_key,
                                                convert_subtasks: options.convert_subtasks,
                                                skip_slack_update: true,
                                                skipAccess: true,
                                                skip_nested_checks: true,
                                                insertUnresolvedQueries: options.insertUnresolvedQueries,
                                                skip_check_recur: true,
                                                skip_notif: true,
                                                skip_triggers: !!params.status,
                                            },
                                            () => wf_cb()
                                        );
                                    },
                                ],
                                async wf_err => {
                                    done();
                                    if (wf_err) {
                                        cb(wf_err);
                                    } else {
                                        if (!isWriteToRecentTableDisabled('recently_updated')) {
                                            db.batchQueries(
                                                [
                                                    {
                                                        query: `
                                                        INSERT INTO task_mgmt.recently_updated(
                                                            userid,
                                                            team_id,
                                                            task_id,
                                                            date
                                                        ) (SELECT $1, $2, $3, $4 WHERE NOT EXISTS
                                                            (
                                                                SELECT 1
                                                                FROM tasK_mgmt.recently_updated
                                                                WHERE
                                                                    userid = $1
                                                                    AND team_id = $2
                                                                    AND task_id = $3
                                                            )
                                                        )  ON CONFLICT (team_id, userid, task_id) DO UPDATE SET date = $4`,
                                                        params: [userid, team_id, item_id, now],
                                                    },
                                                ],
                                                () => {}
                                            );
                                            recent.invalidateCachedRecentTasks(
                                                userid,
                                                team_id,
                                                'task_mgmt.recently_updated'
                                            );
                                        }

                                        dependencyMod._unblockedNotificationIfNecessary([item_id], {});
                                        dependencyMod._deleteUnblockedNotificationsIfNecessary([item_id], {});

                                        if (params.due_date) {
                                            delayedNotifs
                                                .createDueDateNotifs({
                                                    due_date: params.due_date,
                                                    due_date_time: params.due_date_time,
                                                    task_id: item_id,
                                                    team_id,
                                                })
                                                .catch(notifErr => {
                                                    logger.error({
                                                        msg: 'Failed to create due date notifs',
                                                        task_id: item_id,
                                                        team_id,
                                                        err: notifErr,
                                                    });
                                                });
                                        }
                                        if (params.start_date) {
                                            delayedNotifs
                                                .createStartDateNotifs({
                                                    start_date: params.start_date,
                                                    start_date_time: params.start_date_time,
                                                    task_id: item_id,
                                                    team_id,
                                                })
                                                .catch(notifErr => {
                                                    logger.error({
                                                        msg: 'Failed to create start date notifs',
                                                        task_id: item_id,
                                                        team_id,
                                                        err: notifErr,
                                                    });
                                                });
                                        }
                                        if (params.time_estimate || Object.keys(params).includes('time_estimate')) {
                                            subcategoryDatastore
                                                .getTaskSubcategoryIds(item_id, before_subcategory_id)
                                                .then(val => {
                                                    if (val?.subcategory_ids) {
                                                        const { subcategory_ids } = val;
                                                        subcatHelper.invalidateCachedSubcatTimeEstimate(
                                                            subcategory_ids
                                                        );
                                                    } else {
                                                        logger.warn({
                                                            msg: `subcategory_ids not found for task ${item_id}`,
                                                            stack: new Error().stack,
                                                        });
                                                    }
                                                });
                                        }

                                        if (params.points || Object.keys(params).includes('points')) {
                                            subcatHelper.invalidateCachedSubcatPointCount(subcats_to_invalidate);
                                        }

                                        const log_trail = shouldLogTrail(team_id);

                                        webhook.sendWebhookMessage('taskUpdated', {
                                            task_id: item_id,
                                            hist_ids,
                                            auto_id: options.auto_id,
                                            trigger_id: options.trigger_id,
                                            log_trail,
                                            team_id,
                                            userid,
                                        });

                                        const before_hists = before_hist_ids.filter(h_id => !hist_ids.includes(h_id));
                                        if (before_hists.length) {
                                            webhook.sendWebhookMessage('taskUpdated', {
                                                task_id: item_id,
                                                hist_ids: before_hists,
                                                auto_id: options.auto_id,
                                                trigger_id: options.trigger_id,
                                                log_trail,
                                                team_id,
                                                userid,
                                            });
                                        }

                                        subcatHelper.invalidateCachedSubcatTaskCount([before_subcategory_id]);
                                        elasticProducer.addTasksToES(userid, [item_id], {
                                            description_changed: Object.keys(params).includes('content'),
                                        });

                                        if (params.content) {
                                            queryInsertContentHistory(
                                                userid,
                                                item_id,
                                                before.content,
                                                params.content,
                                                team_id
                                            )
                                                .then(contentHistoryId => {
                                                    webhook.sendWebhookMessage('taskUpdated', {
                                                        task_id: item_id,
                                                        hist_ids: [],
                                                        content_history_id: contentHistoryId,
                                                        auto_id: options.auto_id,
                                                        trigger_id: options.trigger_id,
                                                        log_trail,
                                                        team_id,
                                                        userid,
                                                    });
                                                })
                                                .catch(errContHist =>
                                                    logger.error({
                                                        err: errContHist,
                                                        ECODE: 'HIST_CONTENT_001',
                                                        msg: 'failed to insert content history',
                                                    })
                                                );
                                            if (params.text_content !== before.text_content) {
                                                createContentChangedNotif(userid, item_id).catch(errContentNotif => {
                                                    logger.error({
                                                        err: errContentNotif,
                                                        ECODE: 'HIST_CONTENT_002',
                                                        msg: 'failed to create content changed notif',
                                                    });
                                                });
                                            }
                                        }

                                        const fields = [];

                                        if (time_estimate_rollup && 'time_estimate' in (params ?? {})) {
                                            fields.push('rolledUpTimeEstimate');
                                        }

                                        if (estimates_per_assignee && 'time_estimate' in (params ?? {})) {
                                            fields.push('assignees');
                                        }

                                        if (points_rollup && 'points' in (params ?? {})) {
                                            fields.push('rolledUpPointsEstimate');
                                        }

                                        if (params.relationships) {
                                            fields.push('relationships');
                                        }

                                        if (params?.default_permission_level) {
                                            fields.push('defaultPermissionLevel');
                                        }

                                        let version;
                                        if (events && Array.isArray(events)) {
                                            version = getVersionUpdateForTask(item_id, team_id, events);
                                        }

                                        if (!version) {
                                            logger.warn({
                                                msg: 'missing version in update task',
                                                item_id,
                                                events,
                                                ECODE: 'MISS_VER_U_001',
                                            });
                                        }

                                        // TODO: in this context, since we know the new version of the task that we just
                                        // updated, we should have a way to get that task with the specific version
                                        getTask._getItem(
                                            userid,
                                            item_id,
                                            {
                                                mobile,
                                                skipAccess: true,
                                                fields,
                                                split_backlinks: options.split_backlinks,
                                                field_backlinks: options.field_backlinks,
                                                getRolledUpTimeEstimateForSubtask:
                                                    time_estimate_rollup && 'time_estimate' in (params ?? {}),
                                                replica: false,
                                            },
                                            (getErr, getResult) => {
                                                if (getErr) {
                                                    metricsClient.increment(
                                                        'task.edit_task_refactored.get_item_failure_count',
                                                        1,
                                                        {
                                                            called_by: params.trigger_id ? 'AUTOMATIONS' : null,
                                                        }
                                                    );
                                                    cb(getErr);
                                                } else {
                                                    const ret_val = {
                                                        task: getResult,
                                                        newHistIds: hist_ids,
                                                        undo_order: before.orderindex,
                                                        dependencies_adjusted,
                                                        version,
                                                    };
                                                    if (params.status) {
                                                        recurrence.checkShouldRecur(
                                                            [
                                                                {
                                                                    id: item_id,
                                                                    old_orderindex: before.orderindex,
                                                                    old_status: before.status,
                                                                },
                                                            ],
                                                            { userid }
                                                        );
                                                    }
                                                    cb(null, ret_val);

                                                    if (shouldLogEditTaskDateConsistency()) {
                                                        logDateConsistency(params, ret_val.task, before);
                                                    }

                                                    if (params.private != null) {
                                                        sqsWs.sendWSMessage('sendTaskPrivacyChange', [
                                                            item_id,
                                                            params.private,
                                                            params.ws_key,
                                                        ]);

                                                        if (shouldSendDeleteTaskAccessMessage()) {
                                                            sqsWs.sendWSMessage('sendDeleteTaskAccess', [
                                                                item_id,
                                                                params.private,
                                                                params.ws_key,
                                                            ]);
                                                        }

                                                        privacy.insertTaskPrivacy('privacy_changed', userid, item_id, {
                                                            private: getResult.private,
                                                        });
                                                    }

                                                    if (params.status || new_parent != null) {
                                                        triggerSubtasksResolvedEvent(
                                                            before.parent,
                                                            options.auto_id,
                                                            options.trigger_id,
                                                            team_id
                                                        );

                                                        triggerSubtasksResolvedEvent(
                                                            new_parent,
                                                            options.auto_id,
                                                            options.trigger_id,
                                                            team_id
                                                        );
                                                    }

                                                    let time_estimate_options = {};
                                                    if (time_estimate_rollup) {
                                                        time_estimate_options = {
                                                            time_estimate_rollup,
                                                            rollUpTimeEstimate,
                                                        };
                                                    }

                                                    googleCal.CUTaskUpdated(getResult);
                                                    googleCal.CUTasksUpdated(due_dates_adjusted);
                                                    follower_notifs.forEach(hist_row => {
                                                        if (
                                                            userid !== Number(hist_row.after) &&
                                                            !options.mute_notifications
                                                        ) {
                                                            sqsNotif.sendNotifMessage('createTaskNotification', [
                                                                hist_row.after,
                                                                item_id,
                                                                hist_row.id,
                                                                { created_by: userid },
                                                            ]);
                                                        }
                                                    });
                                                    hist_ids.forEach(hist_id => {
                                                        if (!options.mute_notifications) {
                                                            if (!Object.keys(params).includes('coverimage')) {
                                                                sqsNotif.sendNotifMessage('createTaskNotifications', [
                                                                    userid,
                                                                    item_id,
                                                                    hist_id,
                                                                    {},
                                                                ]);
                                                            }
                                                        }
                                                    });

                                                    subtasks_history.forEach(({ hist_id, task_id }) => {
                                                        if (!options.mute_notifications) {
                                                            if (!Object.keys(params).includes('coverimage')) {
                                                                sqsNotif.sendNotifMessage('createTaskNotifications', [
                                                                    userid,
                                                                    item_id,
                                                                    hist_id,
                                                                    {},
                                                                ]);
                                                            }
                                                        }
                                                    });

                                                    if (
                                                        params.status ||
                                                        addedAssignees.length > 0 ||
                                                        removedAssignees.length > 0
                                                    ) {
                                                        if (params.status) {
                                                            postStatusChangedNotification(
                                                                getResult,
                                                                userid,
                                                                before.status,
                                                                params.status
                                                            );
                                                        }
                                                        if (addedAssignees.length > 0 || removedAssignees.length > 0) {
                                                            postAssigneesChangedNotification(
                                                                getResult,
                                                                userid,
                                                                addedAssignees,
                                                                removedAssignees
                                                            );
                                                        }
                                                    }

                                                    if (options.old_parent) {
                                                        sqsWs.sendWSMessage('sendSubtaskRemoved', [
                                                            options.old_parent,
                                                            item_id,
                                                            params.ws_key,
                                                        ]);

                                                        if (new_parent) {
                                                            sqsWs.sendWSMessage('sendSubtaskAdded', [
                                                                item_id,
                                                                new_parent,
                                                                options.old_parent,
                                                                params.ws_key,
                                                            ]);
                                                        }
                                                    }

                                                    if (removedAssignees.length > 0) {
                                                        _removeOldAssigneeNotifications(item_id);
                                                        followersMod._removeFollowerIfNecessary(
                                                            removedAssignees,
                                                            { workspace_id: team_id },
                                                            item_id
                                                        );
                                                    }

                                                    success_funcs.forEach(func => {
                                                        func();
                                                    });
                                                }
                                            }
                                        );

                                        // Fire and forget instant Slapdash update.
                                        // Updates title/name and assignees.
                                        sdAssetsHotUpdateNoWait(logger, userid, team_id, 'task', [
                                            {
                                                item_id,
                                                ...params,
                                                assignees,
                                                updatedAt: new Date(now),
                                            },
                                        ]);
                                    }
                                }
                            );
                        });
                    } catch (e) {
                        cb(new TaskError(e, 'ITEM_238'));
                    }
                }
            );
        }
    );
}

export { _editItem };

function getRollupChangeEventRelationships({
    item_id,
    team_id,
    time_estimate_rollup,
    points_rollup,
    versionUpdateChanges,
}) {
    const rolledUpValueChanged =
        (time_estimate_rollup && versionUpdateChanges.find(change => change.field === 'time_estimate')) ||
        (points_rollup && versionUpdateChanges.find(change => change.field === 'points'));

    if (rolledUpValueChanged) {
        return [getRollupChangeEventRelationship({ task_id: item_id, team_id })];
    }

    return [];
}

export { getRollupChangeEventRelationships as _getRollupChangeEventRelationships };

function shouldIncludeBeforeAndAfterInVersionUpdate(field) {
    return ['assignee', 'priority', 'status', 'start_date', 'due_date'].includes(field);
}

function checkFollowersAccess(item_id, params, para_cb) {
    async.each(
        params.followers.add,
        (follower, each_cb) => {
            access.checkAccessTask(follower, item_id, { permissions: [], checkJoined: false }, each_cb);
        },
        err => {
            if (err) {
                if (err.status === 500) {
                    logger.error({
                        msg: 'Failed to look up team members',
                        status: 500,
                        ECODE: 'ITEM_094',
                        err,
                    });
                    para_cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'ITEM_095',
                    });
                } else {
                    para_cb({
                        err: 'All followers must be in the workspace and have access to all lists of the task',
                        status: 400,
                        ECODE: 'ITEM_096',
                    });
                }
            } else {
                para_cb();
            }
        }
    );
}

export { checkFollowersAccess as _checkFollowersAccess };

function checkAssigneesAccess(item_id, params, para_cb) {
    const taskAsObjectConfigMonolithAccessChecks = taskAsObjectConfig()?.monolithAccessChecks;

    async.each(
        params.assignees.add,
        (assignee, each_cb) => {
            if (taskAsObjectConfigMonolithAccessChecks) {
                checkAccessTaskOrTaskAsObjectCb(assignee, item_id, { permissions: [], checkJoined: false }, each_cb);
                return;
            }

            access.checkAccessTask(assignee, item_id, { permissions: [], checkJoined: false }, each_cb);
        },
        err => {
            if (err) {
                if (err.status === 500) {
                    logger.error({
                        msg: 'Failed to look up team members',
                        status: 500,
                        ECODE: 'ITEM_086',
                        err,
                    });
                    para_cb({
                        err: 'Internal server error',
                        status: 500,
                        ECODE: 'ITEM_086',
                    });
                } else {
                    para_cb({
                        err: 'All assignees must have access to this task',
                        status: 400,
                        ECODE: 'ITEM_087',
                    });
                }
            } else {
                para_cb();
            }
        }
    );
}

export { checkAssigneesAccess as _checkAssigneesAccess };

function validateNoRecursiveParent(treeItem, newSubtaskParent, currentDepth = 0) {
    if (!treeItem.subtasks?.length) {
        return true;
    }
    // protect against circular references in the subtask tree
    // _shouldn't_ be necessary, but just in case
    if (currentDepth > maxNestingLevel + 1) {
        return true;
    }
    return treeItem.subtasks.every(
        subtask =>
            subtask.id !== newSubtaskParent && validateNoRecursiveParent(subtask, newSubtaskParent, currentDepth + 1)
    );
}

async function clearUnusedCoverImage(before, params, team_id, seriesErr) {
    if (seriesErr) {
        // delete copied image if there was an error
        if (params?.cover_image_copied) {
            await deleteAttachmentsByIdAsync(params.coverimage, {
                workspaceId: team_id,
            });
        }
    } else if (
        before?.coverimage &&
        before.coverimage !== 'none' &&
        (params?.coverimage || params?.cover_image_url || params?.cover_image_color)
    ) {
        // delete the old hidden cover image
        try {
            const oldAttachment = (await getAttachmentById(before.coverimage))?.rows?.[0];
            // delete the old cover image if it was a copy of a real attachment
            if (oldAttachment?.hidden === true) {
                await deleteAttachmentsByIdAsync(before.coverimage, {
                    workspaceId: team_id,
                });
            }
        } catch (delError) {
            logger.warn({
                msg: 'Could not delete cover image attachment',
                coverimage: before.coverimage,
                err: delError,
                ECODE: 'ITEM_188',
            });
        }
    }
}

/**
 * Checks if there are any date-related changes in the task update parameters
 * @param {Object} params - The parameters containing potential updates
 * @param {Object} before - The current state of the task before updates
 * @returns {boolean} - Returns true if there are any date-related changes
 */
function checkForDateChanges(params, before) {
    return !!(
        (params.start_date != null && params.start_date !== before.start_date) ||
        (params.due_date != null && params.due_date !== before.due_date) ||
        (params.start_date_time != null && params.start_date_time !== before.start_date_time) ||
        (params.due_date_time != null && params.due_date_time !== before.due_date_time) ||
        (params.duration != null && !compareDateIntervals(params.duration, before.duration)) ||
        (params.duration_is_elapsed != null && params.duration_is_elapsed !== before.duration_is_elapsed)
    );
}

async function updateTaskDates({
    team_id,
    userid,
    item_id,
    params,
    before,
    options,
    dependencyChain,
    dependencyDetails,
    subtask_due_dates,
    useRemapDependenciesV2,
    useRemapSubtasksV2,
    durationIsEnabled,
    isSummaryTaskV2Enabled,
    summary_task_behavior,
}) {
    const date_options = {
        mtt: options.mtt,
        mute_notifications: options.mute_notifications,
        adjustSubtaskDates: options.adjustSubtaskDates,
        summary_task_app: isSummaryTaskV2Enabled,
        summary_task_behavior,
        use_remap_dependency_v2: useRemapDependenciesV2,
        use_remap_subtasks_v2: useRemapSubtasksV2,
        is_duration_enabled: durationIsEnabled,
    };

    const item_due_dates = {
        [item_id]: {
            due_date: before.due_date,
            due_date_time: before.due_date_time,
        },
    };

    const item_start_dates = {
        [item_id]: {
            start_date: before.start_date,
            start_date_time: before.start_date_time,
        },
    };

    const item_durations = {
        [item_id]: {
            duration: before.duration,
            duration_is_elapsed: before.duration_is_elapsed,
        },
    };

    const chain = {
        [item_id]: dependencyChain,
    };

    const chainDetails = {
        [item_id]: dependencyDetails,
    };

    // This will do both V2 dependency rescheduling and V2 subtask remapping if required
    const { queries, remappedDependencySubtaskDates } = await updateDatesService(
        team_id,
        userid,
        params.due_date,
        params.due_date_time,
        params.start_date,
        params.start_date_time,
        params.duration,
        params.duration_is_elapsed,
        [item_id],
        item_due_dates,
        item_start_dates,
        item_durations,
        { [item_id]: subtask_due_dates },
        options.skip_remap_dependencies ? null : chain,
        options.skip_remap_dependencies ? null : chainDetails,
        date_options
    );

    return { queries, remappedDependencySubtaskDates };
}

function createDateInfoObjects(params, before) {
    const createDateInfo = source => ({
        due_date: source.due_date,
        start_date: source.start_date,
        due_date_time: source.due_date_time,
        start_date_time: source.start_date_time,
        duration: source.duration,
        duration_is_elapsed: source.duration_is_elapsed,
    });

    return {
        new_date_info: createDateInfo(params),
        old_date_info: createDateInfo(before),
    };
}
