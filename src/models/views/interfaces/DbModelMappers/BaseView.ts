export interface BaseView {
    date_created: string;
    creator: number;
    name: string;
    type: number;
    parent_id_bigint?: number;
    orderindex: number;
    visibility: number;
    me_view: boolean;
    locked: boolean;
    team_id: string;
    deleted: boolean;
    public_duplication_enabled: boolean;
    public: boolean;
    seo_optimized: boolean;
    standard: boolean;
    visible_to: string;
    template_visibility: boolean;
    template_visible_to: string;
    date_updated: string;
    date_deleted: string;
    share_tasks: boolean;
    share_task_fields: [string];
    pinned: boolean;
    template_public: boolean;
    auto_save: boolean;
    hours_per_day: number;
    time_tracking_display_hours: boolean;
    time_estimate_display_hours: boolean;
    view_access: boolean;
    id: string;
    view_id: string;
    threaded_comment?: boolean;
    parent_id?: string;
    parent_type?: number;
    parent: ParentInfo;
    dashboard: DashboardInfo;
    permissions: Permissions;
    creator_user: User;
    members: [
        {
            user: User;
            role: number;
            permission_level: number;
            permissions: Permissions;
        }
    ];
    settings: BaseViewSettings;
    sorting: unknown;
    filters: unknown;
    columns: { fields: [Field] };
    team_sidebar: {
        assignees: [];
        group_assignees: [];
        assigned_comments: boolean;
        unassigned_tasks: boolean;
    };
    viewing: [];
    commenting: [];
    followers: [];
    avatar: unknown;
    branding: unknown;
    hierarchy_members: [
        {
            user: User;
            role: number;
            permission_level: number;
        }
    ];
    hierarchy_group_members: [];
    can_unfreeze_count: 1;
    group_members: [];
    mentions: {
        mentions: [];
        unaccessible_ids: [];
    };
    view_dashboard?: { id: string };
    extra: Record<string, unknown>;
    context: Record<string, unknown>;
}

interface ParentInfo {
    id: string;
    type: number;
}

interface DashboardInfo {
    id: string;
    name: string;
}

interface Permissions {
    can_unprotect?: boolean;
    edit_view?: boolean;
    comment?: boolean;
    delete_view?: boolean;
    permission_level?: number;
}

interface User {
    id: number;
    email: string;
    color: string;
    initials: string;
    profilePicture: string;
    username: string;
    date_joined: string;
}

interface BaseViewSettings {
    show_task_locations: boolean;
    show_subtasks: 1;
    show_subtask_parent_names: boolean;
    show_closed_subtasks: boolean;
    show_assignees: boolean;
    show_images: boolean;
    show_timer: boolean;
    collapse_empty_columns: boolean;
    me_comments: boolean;
    me_subtasks: boolean;
    me_checklists: boolean;
    show_empty_statuses: boolean;
    auto_wrap: boolean;
    time_in_status_view: number;
}

interface Field {
    field: string;
    idx: number;
    width: string;
    hidden: boolean;
    name: string;
    display: string;
}
