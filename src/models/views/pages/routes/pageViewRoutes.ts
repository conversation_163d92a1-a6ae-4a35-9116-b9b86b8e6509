import express from 'express';
import * as pageController from '../pageController';
import { authChecksMiddleware } from '../../../../utils/access/services/authChecksService';

const pageViewRouter = express.Router();

pageViewRouter
    .get('/view/:view_id/page', authChecksMiddleware, pageController.handleGetViewPagesRequest)
    .get('/view/:view_id/page/count', authChecksMiddleware, pageController.handleGetViewPagesCountRequest)
    .post('/view/:view_id/page', authChecksMiddleware, pageController.handlePostPageRequest)
    .post('/view/:view_id/pages', authChecksMiddleware, pageController.handlePostMultiPageRequest)
    .get('/view/:view_id/page/:page_id', authChecksMiddleware, pageController.handleGetPageRequest)
    .put('/view/:view_id/page/:page_id', authChecksMiddleware, pageController.handlePutPageRequest)
    .put('/view/:view_id/page/:page_id/view', authChecksMiddleware, pageController.handlePutPageViewRequest)
    .put('/view/:view_id/pages', authChecksMiddleware, pageController.handlePutMultiPageRequest)
    .delete('/view/:view_id/page/:page_id', authChecksMiddleware, pageController.handleDeletePageRequest);

export default pageViewRouter;
