/**
 * Factory for all queries needed during a page update
 */
import config from 'config';
import uuid from 'node-uuid';
import { pick } from 'lodash';

import { OperationType } from '@time-loop/ovm-object-version';
import { getHistoryMetadataJson } from '../helpers/historyMetadata';
import { encrypt } from '../../../../utils/encrypt';
import { queryPageIdsForView } from '../datastores/pageDataStore';
import { updateDocSettingsQuery } from '../../documents/datastores/documentSettingsDatastore';

import { QueryObject, QueryParams } from '../../../../utils/interfaces/QueryObject';
import { Page } from '../interfaces/Page';
import { PageUpdateOptions } from '../interfaces/PageOptions';
import { toBoolean } from '../../../helpers';
import { NumericId } from '../../../../interfaces/UtilInterfaces';
import { prepareBinaryYdoc } from '../../../../utils/content/contentQueryFactory';
import { docsSeparateContentUpdateQueries } from '../../../integrations/split/squadTreatments/docTreatments';

/**
 * to be used to convert percent value coming in as decimal into an INT4 in the DB
 */
const POSITION_CONVERSION_CONSTANT = config.get<number>('position_conversion_constant');
const DOC_TYPE = config.get<number>('views.view_types.doc');

export const SINGLE_PAGE_VIEW_NAME = 'SINGLE_PAGE_VIEW_NAME';

/**
 * Prepare queries for content-related updates
 */
export function prepareContentUpdateQueries(page: Page, options: PageUpdateOptions): QueryObject[] {
    const queries: QueryObject[] = [];

    if (options.content != null) {
        queries.push({
            query: `UPDATE task_mgmt.view_docs SET ydoc = $1 WHERE id = $2`,
            params: [prepareBinaryYdoc(page.id, page.ydoc as Uint8Array, options.content, options.ydoc), page.id],
        });

        // Update encrypted content
        const encryptedContent = encrypt(options.content);
        queries.push({
            query: `UPDATE task_mgmt.view_docs SET content = $1 WHERE id = $2`,
            params: [encryptedContent, page.id],
        });

        // Update text content if provided
        if (options.text_content != null) {
            queries.push({
                query: `UPDATE task_mgmt.view_docs SET text_content = $1 WHERE id = $2`,
                params: [options.text_content, page.id],
            });
        }
    }

    return queries;
}

/**
 * Prepare Query for the update of the page core data
 */
export function prepareCoreDataUpdateQuery(
    userId: number,
    page: Page,
    orderindex: number,
    contentChanged: boolean,
    options: PageUpdateOptions
): QueryObject | Record<string, never> {
    let query = `UPDATE task_mgmt.view_docs SET `;
    const setElements: string[] = [];
    const params: QueryParams = [];
    const now = Date.now();

    if (options.name != null) {
        setElements.push(`name = $${params.push(options.name)}`);
    }

    if (options.subtitle != null) {
        setElements.push(`subtitle = $${params.push(options.subtitle)}`);
    }

    if (options.content != null && !docsSeparateContentUpdateQueries()) {
        const encryptedContent = encrypt(options.content);
        setElements.push(`content = $${params.push(encryptedContent)}`);

        if (options.text_content != null) {
            setElements.push(`text_content = $${params.push(options.text_content)}`);
        }

        setElements.push(
            `ydoc = $${params.push(prepareBinaryYdoc(page.id, page.ydoc as Uint8Array, options.content, options.ydoc))}`
        );
    }

    if (options.color) {
        setElements.push(`color = $${params.push(options.color)}`);
    }

    if (options.parent) {
        const value = options.parent === 'none' ? null : options.parent;
        setElements.push(`parent = $${params.push(value)}`);
    }

    if (orderindex != null) {
        setElements.push(`orderindex = $${params.push(orderindex)}`);
    }

    if (options.public != null) {
        setElements.push(`public = $${params.push(options.public)}`);
    }

    if (options.seo_optimized != null) {
        setElements.push(`seo_optimized = $${params.push(options.seo_optimized)}`);
    }

    if (options.public_duplication_enabled != null) {
        setElements.push(`public_duplication_enabled = $${params.push(options.public_duplication_enabled)}`);
    }

    if (options.full_width != null) {
        setElements.push(`full_width = $${params.push(options.full_width)}`);
    }

    if (options.protected != null) {
        setElements.push(`protected = $${params.push(options.protected)}`);
        setElements.push(`protected_by = $${params.push(options.protected ? userId : null)}`);
        setElements.push(`protected_note = $${params.push(options.protected_note || null)}`);
    }

    if (options.unset_avatar) {
        const index = params.push(null);
        setElements.push(`avatar_source = $${index}`);
        setElements.push(`avatar_value = $${index}`);
        setElements.push(`avatar_color = $${index}`);
    } else {
        if (options.avatar?.color) {
            setElements.push(`avatar_color = $${params.push(options.avatar.color)}`);
        }

        if (options.avatar?.value) {
            setElements.push(`avatar_value = $${params.push(options.avatar.value)}`);
        }

        if (options.avatar?.attachment_id) {
            const value = options.avatar.attachment_id === 'none' ? null : options.avatar.attachment_id;
            setElements.push(`avatar_source = $${params.push(value)}`);
        }
    }

    if (options.cover_image_url) {
        const value = options.cover_image_url === 'none' ? null : options.cover_image_url;
        setElements.push(`cover_image_url = $${params.push(value)}`);
    }

    if (options.cover_image_color) {
        const value = options.cover_image_color === 'none' ? null : options.cover_image_color;
        setElements.push(`cover_image_color = $${params.push(value)}`);
    }

    if (options.cover_position_x != null) {
        const posX = Math.round(options.cover_position_x * POSITION_CONVERSION_CONSTANT);
        setElements.push(`cover_position_x = $${params.push(posX)}`);
    }

    if (options.cover_position_y != null) {
        const posY = Math.round(options.cover_position_y * POSITION_CONVERSION_CONSTANT);
        setElements.push(`cover_position_y = $${params.push(posY)}`);
    }

    if (options.relationships_display_setting) {
        const value = options.relationships_display_setting === 'none' ? null : options.relationships_display_setting;
        setElements.push(`relationships_display_setting = $${params.push(value)}`);
    }

    if (contentChanged) {
        setElements.push(`date_edited = $${params.push(now)}`);
        setElements.push(`edited_by = $${params.push(userId)}`);
    }

    if (setElements.length > 0) {
        setElements.push(`date_updated = $${params.push(now)}`);
    }

    query += setElements.join(', ');
    query += ` WHERE id = $${params.push(page.id)}`;

    return setElements.length > 0 ? { query, params } : {};
}

/**
 * Prepare Query for updating mind map node with the new page name
 */
export function prepareMindMapUpdateQuery(
    pageId: string,
    options: PageUpdateOptions
): QueryObject | Record<string, never> {
    if (options.name) {
        return {
            query: `UPDATE task_mgmt.mind_map_nodes SET title = $1 WHERE parent_id = $2`,
            params: [options.name, pageId],
        };
    }
    return {};
}

/**
 * Prepare all possible queries related to avatar information update
 */
export function prepareAvatarUpdateQueries(
    userId: number,
    page: Page,
    options: PageUpdateOptions
): { queries: QueryObject[]; attachmentVersionTypeMap: { [key: string]: OperationType } } {
    const queries: QueryObject[] = [];
    const attachmentVersionTypeMap: { [key: string]: OperationType } = {};

    if (
        (options.unset_avatar && page.avatar?.attachment_id) ||
        (page.avatar?.attachment_id &&
            options.avatar?.attachment_id &&
            page.avatar.attachment_id !== options.avatar?.attachment_id)
    ) {
        queries.push({
            query: `
                UPDATE task_mgmt.attachments
                SET deleted = true, deleted_by = $2, date_deleted = $3
                WHERE id = $1`,
            params: [page.avatar.attachment_id, userId, new Date().getTime()],
        });
        attachmentVersionTypeMap[page.avatar.attachment_id] = OperationType.DELETE;
    }

    if (!options.unset_avatar && options.avatar?.attachment_id && options.avatar.attachment_id !== 'none') {
        queries.push({
            query: `
                UPDATE task_mgmt.attachments
                SET hidden = TRUE, permanent = TRUE, parent_id = $1, type = $2
                WHERE id = $3`,
            params: [page.id, config.get<number>('attachments.types.doc'), options.avatar.attachment_id],
        });
        attachmentVersionTypeMap[options.avatar.attachment_id] = OperationType.UPDATE;
    }

    return {
        queries,
        attachmentVersionTypeMap,
    };
}

/**
 * Prepare the query that will update orderindex values for all pages affected
 */
export function prepareOrderindexUpdateQuery(
    viewId: string,
    pageId: string,
    parent: string | null,
    orderindex: number | null
): QueryObject | Record<string, never> {
    if (orderindex != null) {
        let query = `
            UPDATE task_mgmt.view_docs
            SET orderindex = orderindex + 1
            WHERE view_docs.view_id = $1
                AND view_docs.id != $2
                AND view_docs.orderindex >= $3`;
        const params: QueryParams = [viewId, pageId, orderindex];

        if (!parent || parent === 'none') {
            query += ` AND view_docs.parent IS NULL`;
        } else {
            params.push(parent);
            query += ` AND view_docs.parent = $${params.length}`;
        }

        return { query, params };
    }

    return {};
}

/**
 * Prepare the queries used to update doc settings
 */
export function prepareDocSettingsUpdateQueries(
    userId: number,
    viewId: string,
    pageId: string,
    workspaceId: NumericId,
    collapsed: string[],
    options: PageUpdateOptions
): QueryObject[] {
    const queries: QueryObject[] = [];

    // options.collapsed is a boolean value when defined
    if (options.collapsed != null) {
        if (options.collapsed) {
            collapsed = collapsed.includes(pageId) ? collapsed : [pageId, ...collapsed];
        } else {
            collapsed = collapsed.filter(p => p !== pageId);
        }
    }

    queries.push(
        updateDocSettingsQuery(userId, viewId, {
            ...options,
            collapsed: options.collapsed != null ? collapsed : null,
            workspace_id: workspaceId,
        })
    );

    return queries;
}

/**
 * Prepare query to insert a new doc content history entry
 */
export function prepareInsertDocContentHistoryQuery(
    userId: number,
    page: Page,
    options: PageUpdateOptions
): QueryObject {
    return {
        query: `
                INSERT INTO task_mgmt.doc_history(id, page_id, field, date, before, after, userid, deleted, data, workspace_id)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        params: [
            uuid.v4(),
            page.id,
            'content',
            Date.now(),
            encrypt(page.content),
            encrypt(options.content),
            userId,
            false,
            getHistoryMetadataJson(options),
            page.team_id,
        ],
    };
}

/**
 * Prepare the queries used to update doc content history
 */
export function prepareUpdateDocContentHistoryQuery(
    userId: number,
    page: Page,
    options: PageUpdateOptions
): QueryObject {
    let before: string = page.content;
    let after: string | boolean = options.content;

    after = encrypt(after);
    before = before ? encrypt(before) : null;

    const right_now = new Date().getTime();
    const thirty_seconds_ago = right_now - 60000;

    return {
        query: `
                WITH lastUpdatedRecordByUser AS (
                    -- in the last 60 seconds
                    SELECT id
                    FROM task_mgmt.doc_history
                    WHERE page_id = $2
                        AND field = $3
                        AND userid = $7
                        AND date > $11
                    ORDER BY date DESC
                    LIMIT 1
                ),
                insertDocHistory AS (
                    INSERT INTO task_mgmt.doc_history(id, page_id, field, date, before, after, userid, deleted, data, workspace_id) (
                        SELECT $1, $2, $3, $4, $5, $6, $7, $8, $9, $10 WHERE NOT EXISTS (
                            SELECT 1 FROM lastUpdatedRecordByUser
                        )
                    )
                )
                UPDATE task_mgmt.doc_history
                SET after = $6, date = $4, data = $9
                WHERE id IN (
                    SELECT id
                    FROM lastUpdatedRecordByUser
                )
            `,
        params: [
            uuid.v4(),
            page.id,
            'content',
            right_now,
            before,
            after,
            userId,
            false,
            getHistoryMetadataJson(options),
            page.team_id,
            thirty_seconds_ago,
        ],
    };
}

/**
 * Prepare the queries used to update doc history
 */
export function prepareDocHistoryUpdateQueries(userId: number, page: Page, options: PageUpdateOptions): QueryObject[] {
    const queries: QueryObject[] = [];

    interface FieldTypes {
        content?: string;
        name?: string;
        public?: boolean;
        seo_optimized?: boolean;
    }

    const teamId = page.team_id ?? null;
    const fields: FieldTypes = pick(options, ['name', 'subtitle', 'public', 'seo_optimized', 'protected']);

    Object.keys(fields).forEach(field => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const before: string | boolean = (page as any)[field];

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const after: string | boolean = (options as any)[field];

        if (after == null) {
            return;
        }

        let protectionData: { note?: string } = {};
        if (field === 'protected' && options.protected_note) {
            protectionData = { note: options.protected_note };
        }

        const right_now = new Date().getTime();
        const thirty_seconds_ago = right_now - 60000;

        queries.push(
            {
                query: `
                    INSERT INTO task_mgmt.doc_history(id, page_id, field, date, before, after, userid, deleted, data, workspace_id) (
                        SELECT $1, $2, $3, $4, $5, $6, $7, $8, $9, $11 WHERE NOT EXISTS(
                            SELECT 1
                            FROM task_mgmt.doc_history
                            WHERE page_id = $2
                                AND field = $3
                                AND userid = $7
                                AND date > $10
                        )
                    )`,
                params: [
                    uuid.v4(),
                    page.id,
                    field,
                    right_now,
                    before,
                    after,
                    userId,
                    false,
                    getHistoryMetadataJson(options, protectionData),
                    thirty_seconds_ago,
                    teamId,
                ],
            },
            {
                query: `
                    UPDATE task_mgmt.doc_history
                    SET after = $6, date = $7
                    WHERE id IN (
                        SELECT id
                        FROM task_mgmt.doc_history
                        WHERE page_id = $1
                            AND field = $2
                            AND userid = $3
                            AND date > $4
                    )
                    AND NOT EXISTS(
                        SELECT 1
                        FROM task_mgmt.doc_history
                        WHERE page_id = $1
                            AND field = $2
                            AND userid = $3
                            AND date > $4
                            AND before = $5
                    )`,
                params: [page.id, field, userId, thirty_seconds_ago, before, after, right_now],
            },
            {
                query: `
                    DELETE FROM task_mgmt.doc_history
                    WHERE id IN (
                        SELECT id
                        FROM task_mgmt.doc_history
                        WHERE page_id = $1
                            AND field = $2
                            AND userid = $3
                            AND date > $4
                    )
                    AND EXISTS (
                        SELECT 1
                        FROM task_mgmt.doc_history
                        WHERE page_id = $1
                            AND field = $2
                            AND userid = $3
                            AND date > $4
                            AND before = $5
                    )`,
                params: [page.id, field, userId, thirty_seconds_ago, after],
            }
        );
    });

    return queries;
}

/**
 * Prepare the query to update Doc date_updated
 */
export const prepareDocDateUpdatedUpdateQuery = (viewId: string): QueryObject => ({
    query: 'UPDATE task_mgmt.views SET date_updated = $1 WHERE view_id = $2',
    params: [Date.now(), viewId],
});

/**
 * Prepare the query to update Page date_updated
 */
export const preparePageDateUpdatedUpdateQuery = (viewId: string, pageId: string): QueryObject => ({
    query: 'UPDATE task_mgmt.view_docs SET date_updated = $1 WHERE view_id = $2 AND id = $3',
    params: [Date.now(), viewId, pageId],
});

export function prepareSinglePresentationDetailsUpdateQuery(
    pageId: string,
    workspaceId: NumericId,
    options: PageUpdateOptions
): QueryObject {
    if (options.presentation_details) {
        return {
            query: `
                    INSERT INTO task_mgmt.page_details
                        (page_id, font, font_size, line_height, paragraph_spacing, page_width, show_author_header, show_contributor_header, show_cover_header, show_date_header, show_page_outline, show_subpages, show_subtitle_header, show_title_icon_header, subpage_size, workspace_id)
                    VALUES
                        ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
                    ON CONFLICT (page_id) DO UPDATE SET font = $2
                        ,font_size = $3
                        ,line_height = $4
                        ,paragraph_spacing = $5
                        ,page_width  = $6
                        ,show_author_header = $7
                        ,show_contributor_header = $8
                        ,show_cover_header = $9
                        ,show_date_header = $10
                        ,show_page_outline = $11
                        ,show_subpages = $12
                        ,show_subtitle_header = $13
                        ,show_title_icon_header = $14
                        ,subpage_size = $15
                `,
            params: [
                pageId,
                options.presentation_details.font,
                options.presentation_details.font_size,
                options.presentation_details.line_height,
                options.presentation_details.paragraph_spacing,
                options.presentation_details.page_width,
                options.presentation_details.show_author_header,
                options.presentation_details.show_contributor_header,
                options.presentation_details.show_cover_header,
                options.presentation_details.show_date_header,
                options.presentation_details.show_page_outline,
                options.presentation_details.show_subpages,
                options.presentation_details.show_subtitle_header,
                options.presentation_details.show_title_icon_header,
                options.presentation_details.subpage_size,
                workspaceId,
            ],
        };
    }
    return null;
}

/**
 * Prepare the upsert query for presentation details
 */
export async function preparePresentationDetailsUpdateQuery(
    page: Page,
    options: PageUpdateOptions
): Promise<QueryObject[]> {
    const queries: QueryObject[] = [];
    if (options.presentation_details) {
        queries.push(prepareSinglePresentationDetailsUpdateQuery(page.id, page.team_id, options));

        // if all pages was specified on the query string of the originating request
        // all font settings are to be applied to all pages of the document
        // all other presentation details are possibly going to be left NULL

        if (options.all_pages) {
            const { rows } = await queryPageIdsForView(page.view_id);
            const otherIds = rows.map(r => r.page_id).filter(id => id !== page.id);

            if (otherIds.length === 0) {
                return queries;
            }

            const values: string[] = [];
            // eslint-disable-next-line no-plusplus
            for (let i = 0; i < otherIds.length; i++) {
                values.push(`($${i + 7}, $1, $2, $3, $4, $5, $6)`);
            }
            queries.push({
                query: `
                        INSERT INTO task_mgmt.page_details
                            (page_id, font, font_size, line_height, paragraph_spacing, page_width, workspace_id)
                        VALUES
                            ${values.join(',')}
                        ON CONFLICT (page_id) DO UPDATE SET font = $1
                            ,font_size = $2
                            ,line_height = $3
                            ,paragraph_spacing = $4
                            ,page_width  = $5
                    `,
                params: [
                    options.presentation_details.font,
                    options.presentation_details.font_size,
                    options.presentation_details.line_height,
                    options.presentation_details.paragraph_spacing,
                    options.presentation_details.page_width,
                    page.team_id,
                    ...otherIds,
                ],
            });
        }
    }

    return queries;
}

/**
 * Prepare the series of queries to update authors and contributors
 */
export function prepareAuthorsUpdateQueries(
    pageId: string,
    workspaceId: NumericId,
    options: PageUpdateOptions
): QueryObject[] {
    const queries: QueryObject[] = [];
    let whereClause = '';
    let editorIds: number[] = [];
    let authorsQuery: QueryObject = null;
    let contributorsQuery: QueryObject = null;

    if (options.authors?.length > 0) {
        const { values, ids } = options.authors.reduce(
            (acc, author, idx) => {
                acc.values.push(`($1, $${idx + 3}, FALSE, $2)`);
                acc.ids.push(author.id);
                return acc;
            },
            { values: [] as string[], ids: [] as number[] }
        );

        editorIds = editorIds.concat(ids);

        authorsQuery = {
            query: `
                INSERT INTO task_mgmt.page_authors
                    (page_id, user_id, contributor, workspace_id)
                VALUES
                    ${values.join(',')}
                ON CONFLICT (page_id, user_id) DO UPDATE SET contributor = FALSE
            `,
            params: [pageId, workspaceId, ...ids],
        };
    }

    if (options.contributors?.length > 0) {
        const { values, ids } = options.contributors.reduce(
            (acc, contributor, idx) => {
                acc.values.push(`($1, $${idx + 3}, TRUE, $2)`);
                acc.ids.push(contributor.id);
                return acc;
            },
            { values: [] as string[], ids: [] as number[] }
        );

        editorIds = editorIds.concat(ids);

        contributorsQuery = {
            query: `
                INSERT INTO task_mgmt.page_authors
                    (page_id, user_id, contributor, workspace_id)
                VALUES
                    ${values.join(',')}
                ON CONFLICT (page_id, user_id) DO UPDATE SET contributor = TRUE
            `,
            params: [pageId, workspaceId, ...ids],
        };
    }

    if (options.authors != null && options.contributors != null) {
        whereClause = `WHERE page_id = $1 AND NOT user_id = ANY($2)`;
    } else if (options.authors != null) {
        whereClause = `WHERE page_id = $1 AND NOT user_id = ANY($2) AND contributor IS NOT TRUE`;
    } else if (options.contributors != null) {
        whereClause = `WHERE page_id = $1 AND NOT user_id = ANY($2) AND contributor IS TRUE`;
    }

    if (options.authors != null || options.contributors != null) {
        queries.push({
            query: `
                DELETE FROM task_mgmt.page_authors
                ${whereClause}`,
            params: [pageId, editorIds],
        });
    }

    if (authorsQuery != null) {
        queries.push(authorsQuery);
    }

    if (contributorsQuery != null) {
        queries.push(contributorsQuery);
    }

    return queries;
}

/**
 * Prepare the query that will record the fact that a view is now being viewed
 */
export function prepareRecentlyViewedViewQuery(userId: number, viewId: string, workspaceId: NumericId): QueryObject {
    return {
        query: `
            INSERT INTO task_mgmt.recently_viewed_views(
                userid,
                view_id,
                date,
                workspace_id
            ) VALUES ($1, $2, $3, $4)
            ON CONFLICT(userid, view_id) DO UPDATE SET date = $3
        `,
        params: [userId, viewId, Date.now(), workspaceId],
    };
}

/**
 * Prepare the query that will record the fact that a view is now being viewed
 */
export function prepareRecentlyUpdatedViewQuery(userId: number, viewId: string, workspaceId: NumericId): QueryObject {
    return {
        query: `
            INSERT INTO task_mgmt.recently_updated_views(
                userid,
                view_id,
                date,
                workspace_id
            ) VALUES ($1, $2, $3, $4)
            ON CONFLICT(userid, view_id) DO UPDATE SET date = $3
        `,
        params: [userId, viewId, Date.now(), workspaceId],
    };
}

/**
 * Prepare the query that will record the fact that a page is now being viewed
 */
export function prepareRecentlyViewedPageQuery(userId: number, pageId: string, workspaceId: NumericId): QueryObject {
    return {
        query: `
            INSERT INTO task_mgmt.recently_viewed_pages(
                userid,
                page_id,
                date,
                workspace_id
            ) VALUES ($1, $2, $3, $4)
            ON CONFLICT(userid, page_id) DO UPDATE SET date = $3
        `,
        params: [userId, pageId, Date.now(), workspaceId],
    };
}

export function prepareUpdateParentQuery(pageId: string, parent: string): QueryObject {
    return {
        query: `
            UPDATE task_mgmt.view_docs
            SET parent = $2, orderindex = (
                SELECT coalesce(max(orderindex), 0) + 1 AS count
                FROM task_mgmt.view_docs
                WHERE view_docs.parent = $2
            )
            WHERE id = $1`,
        params: [pageId, parent],
    };
}

export function prepareBulkUpdateCollapsedQuery(
    userId: number,
    viewId: string,
    pageIds: string[],
    collapsed: boolean
): QueryObject {
    if (toBoolean(collapsed)) {
        return {
            query: `UPDATE task_mgmt.doc_settings SET collapsed = ARRAY(SELECT DISTINCT unnest(coalesce(doc_settings.collapsed, ARRAY[]::text[]) || $3::text[])) WHERE userid = $1 AND view_id = $2`,
            params: [userId, viewId, pageIds],
        };
    }
    return {
        query: `UPDATE task_mgmt.doc_settings SET collapsed = (SELECT ARRAY(SELECT unnest(coalesce(doc_settings.collapsed, ARRAY[]::text[])) EXCEPT SELECT unnest($3::text[]))) WHERE userid = $1 AND view_id = $2`,
        params: [userId, viewId, pageIds],
    };
}

export function prepareUpdateViewParentQueries(view_id: string, page_ids: string[], page_id: string): QueryObject[] {
    return [
        {
            query: `UPDATE task_mgmt.view_docs SET view_id = $1 WHERE id = ANY($2) AND (SELECT type FROM task_mgmt.views WHERE view_id = $1) = ${DOC_TYPE} AND id != $3`,
            params: [view_id, page_ids, page_id],
        },
        {
            query: `UPDATE task_mgmt.view_docs SET view_id = $1, parent = NULL, orderindex = (SELECT max(orderindex) + 1 FROM tasK_mgmt.view_docs WHERE view_id = $1) WHERE id = $2 AND (SELECT type FROM task_mgmt.views WHERE view_id = $1) = ${DOC_TYPE}`,
            params: [view_id, page_id],
        },
    ];
}

export function prepareSinglePageViewNameUpdateQuery(viewId: string, name: string): QueryObject {
    return name
        ? {
              query: `
            WITH page_count_subquery AS (
                SELECT COUNT(*) as page_count
                FROM task_mgmt.view_docs
                WHERE view_id = $1 AND deleted = FALSE
            )
            UPDATE task_mgmt.views
            SET name = $2
            FROM page_count_subquery
            WHERE view_id = $1 AND page_count_subquery.page_count=1
            RETURNING '${SINGLE_PAGE_VIEW_NAME}' AS id, views.parent_type, views.parent_id
        `,
              params: [viewId, name],
          }
        : null;
}
