import { FormField, FormRule } from '../../interfaces/DbModelMappers/FormField';
import { FormFieldModel } from './form-field.model';

export class FormFieldRuleModel extends FormFieldModel {
    rules: FormRule[] = [];

    rules_enabled = false;

    constructor(formField: FormField) {
        super(formField);

        Object.assign(this, {
            rules: formField.rules,
            rules_enabled: formField.rules_enabled,
        });
    }
}
