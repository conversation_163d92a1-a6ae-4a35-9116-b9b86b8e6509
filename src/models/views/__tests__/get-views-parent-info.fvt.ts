/**
 * @group functional/models/views
 */

import {
    useMigratedGetParentInfo,
    shouldUseReplicasGetViewsParent,
} from '@clickup-legacy/models/integrations/split/squadTreatments/viewsTreatments';
import { ViewParentInfo } from '@clickup-legacy/models/views/interfaces/ViewParentInfo';
import { queryViewsParentInfo } from '@clickup-legacy/models/views/datastores/CRUD/viewsParentInfo';

jest.mock('@clickup-legacy/models/integrations/split/squadTreatments/viewsTreatments', () => ({
    useMigratedGetParentInfo: jest.fn(),
    shouldUseReplicasGetViewsParent: jest.fn(),
}));

jest.mock('@clickup-legacy/libs/entitlements/helpers/disableGlobalQueryEntitlementsRepositoryForTests', () => ({
    disableGlobalQueryEntitlementsRepositoryForTests: jest.fn(() => Promise.resolve(true)),
}));

// Helper function to run tests with both implementations and compare results
async function testWithBothImplementations(
    testName: string,
    userId: number,
    viewIds: string[],
    options: { team_only?: boolean } = {},
    expectedLength: number,
    customExpectations?: (rows: ViewParentInfo[]) => void
) {
    describe(testName, () => {
        let originalResult: ViewParentInfo[];
        let egressResult: ViewParentInfo[];

        beforeAll(async () => {
            jest.mocked(shouldUseReplicasGetViewsParent).mockReturnValue(false);

            // Get results from original implementation
            jest.mocked(useMigratedGetParentInfo).mockReturnValue(false);
            originalResult = await queryViewsParentInfo(userId, viewIds, options);

            // Get results from egress implementation
            jest.mocked(useMigratedGetParentInfo).mockReturnValue(true);
            egressResult = await queryViewsParentInfo(userId, viewIds, options);
        });

        it('should return the expected number of results', async () => {
            expect(originalResult.length).toBe(expectedLength);
            expect(egressResult.length).toBe(expectedLength);
        });

        it('should return matching results between implementations', async () => {
            // Sort both arrays by view_id to ensure consistent comparison
            const sortedOriginal = [...originalResult].sort((a, b) => a.view_id.localeCompare(b.view_id));
            const sortedEgress = [...egressResult].sort((a, b) => a.view_id.localeCompare(b.view_id));

            // Compare each view's properties
            sortedOriginal.forEach((originalView, index) => {
                const egressView = sortedEgress[index];
                expect(egressView.view_id).toBe(originalView.view_id);
                expect(egressView.id).toBe(originalView.id);
                expect(egressView.name).toBe(originalView.name);
                expect(egressView.category).toBe(originalView.category);
                expect(egressView.project).toBe(originalView.project);
                expect(egressView.task_id).toBe(originalView.task_id);
                expect(egressView.is_template).toBe(originalView.is_template);
                expect(egressView.role).toBe(originalView.role);
                expect(egressView.task_access).toBe(originalView.task_access);
                expect(egressView.subcategory_access).toBe(originalView.subcategory_access);
                expect(egressView.category_access).toBe(originalView.category_access);
                expect(egressView.project_access).toBe(originalView.project_access);
            });
        });

        if (customExpectations) {
            it('should satisfy custom expectations', async () => {
                customExpectations(originalResult);
                customExpectations(egressResult);
            });
        }
    });
}

describe('queryViewsParentInfo', () => {
    jest.setTimeout(240000);

    const userId = 2000;

    describe('Task parent views', () => {
        testWithBothImplementations('public task view info', userId, ['8000'], {}, 1);

        testWithBothImplementations('private task view info when user has direct access', userId, ['8001'], {}, 1);

        testWithBothImplementations('template task view info', userId, ['8002'], {}, 1, rows => {
            expect(rows[0].is_template).toBe(true);
        });
    });

    describe('List parent views', () => {
        testWithBothImplementations('public list view info', userId, ['8010'], {}, 1);

        testWithBothImplementations('private list view info when user has direct access', userId, ['8011'], {}, 1);

        testWithBothImplementations('personal list view info for owner', userId, ['8013'], {}, 1, rows => {
            expect(rows[0].personal_list).toBe(true);
        });
    });

    describe('Folder parent views', () => {
        testWithBothImplementations('public folder view info', userId, ['8020'], {}, 1);

        testWithBothImplementations('private folder view info when user has direct access', userId, ['8021'], {}, 1);
    });

    describe('Space parent views', () => {
        testWithBothImplementations('public space view info', userId, ['8030'], {}, 1);

        testWithBothImplementations('private space view info when user has direct access', userId, ['8031'], {}, 1);
    });

    describe('Team parent views', () => {
        testWithBothImplementations('public team view info', userId, ['8040'], {}, 1);

        testWithBothImplementations('private team view info when user has direct access', userId, ['8041'], {}, 1);

        testWithBothImplementations('team view info from different team when user has access', userId, ['8042'], {}, 1);
    });

    describe('Multiple views', () => {
        testWithBothImplementations(
            'info for multiple views of different types',
            userId,
            ['8000', '8010', '8020', '8030', '8040'],
            {},
            5
        );

        testWithBothImplementations(
            'info for multiple private views with access',
            userId,
            ['8001', '8011', '8021', '8031', '8041'],
            {},
            5
        );
    });

    describe('Group access', () => {
        testWithBothImplementations(
            'view info when user has access through group membership',
            2001, // User with only group access
            ['8001'],
            {},
            1
        );
    });

    describe('Team only views', () => {
        testWithBothImplementations(
            'only team level info when team_only option is true',
            userId,
            ['8040', '8041'],
            { team_only: true },
            2
        );
    });
});
