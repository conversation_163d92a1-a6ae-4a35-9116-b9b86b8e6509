import express from 'express';

import { createMulterUploadsMiddleware } from '@clickup-legacy/models/attachment/middlewares/multer-uploads';
import * as attachment from '../../../attachment/attachment';
import { authChecksMiddleware } from '../../../../utils/access/services/authChecksService';
import { jwtCheck } from '../../../../utils/auth/jwt/jwtCheckMiddlewares';

const uploads = createMulterUploadsMiddleware();
const uploadsWithThumbnails = createMulterUploadsMiddleware({
    fields: [
        {
            name: 'attachment',
            maxCount: 1,
        },
        {
            name: 'thumbnail',
            maxCount: 1,
        },
    ],
});

const taskAttachmentRouter = express.Router();

taskAttachmentRouter
    // v1
    .get('/v1/attachments', jwtCheck, authChecksMiddleware, attachment.getAttachmentsByIDReq)
    .get('/v1/attachment', jwtCheck, attachment.getAttachmentsOfType)
    .put('/v1/attachment', jwtCheck, authChecksMiddleware, attachment.editAttachmentsV1)
    .post('/v1/attachment', jwtCheck, uploads, authChecksMiddleware, attachment.postAttachment)
    .delete('/v1/attachment', jwtCheck, authChecksMiddleware, attachment.deleteAttachments)
    .put('/v1/attachment/:attachment_id', jwtCheck, authChecksMiddleware, attachment.editAttachment)
    .delete('/v1/attachment/:attachment_id', jwtCheck, authChecksMiddleware, attachment.deleteAttachment)
    .put('/v1/attachmentUndo', jwtCheck, authChecksMiddleware, attachment.undoDeleteAttachments)
    .put('/v1/attachment/:attachment_id/undo', jwtCheck, authChecksMiddleware, attachment.undoDeleteAttachment)
    .get('/v1/attachment/:attachment_id/version/:version', jwtCheck, authChecksMiddleware, attachment.attachmentProxy)

    // v1 project
    .post('/v1/project/:project_id/attachment', jwtCheck, authChecksMiddleware, uploads, attachment.postAttachment)

    // v1 task
    .post(
        '/v1/task/:task_id/attachment',
        jwtCheck,
        authChecksMiddleware,
        uploadsWithThumbnails,
        attachment.postAttachment
    )
    .delete('/v1/task/:task_id/attachment', jwtCheck, authChecksMiddleware, attachment.deleteAttachments)
    .delete('/v1/task/:task_id/attachment/:attachment_id', jwtCheck, authChecksMiddleware, attachment.deleteAttachment)

    // v1 team
    .put('/v1/team/:team_id/attachment', jwtCheck, authChecksMiddleware, attachment.getTeamAttachmentId)

    // v2
    .put('/v2/attachment', jwtCheck, authChecksMiddleware, attachment.editAttachments);

export default taskAttachmentRouter;
