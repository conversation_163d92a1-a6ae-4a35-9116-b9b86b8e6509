import IORedis from 'ioredis';
import zlib from 'zlib';
import { promisify } from 'util';

import { safeGetConfig } from '@clickup-legacy/utils/configUtil';
import { getLogger } from '@clickup/shared/utils-logging';
import { createRedisConnection, client } from '@clickup-legacy/utils/redis';

const logger = getLogger('redis');

const gvRedisEnabled = safeGetConfig<boolean>('views.redis.enabled');
const gvRedisHost = safeGetConfig<string>('views.redis.host');
const gvRedisPort = safeGetConfig<number>('views.redis.port');
const redisHostAndPort = { host: gvRedisHost, port: gvRedisPort };
const useTls = safeGetConfig<boolean>('views.redis.useTls');
const clustered = safeGetConfig<boolean>('views.redis.cluster');

const MIN_COMPRESS_SIZE = 1000;
const MAX_SYNC_SIZE = 1000_000;
const HEADER_COMPRESSED_ASYNC = '!CUA';
const HEADER_COMPRESSED_SYNC = '!CUS';

const deflateAsync = promisify(zlib.deflate);
const inflateAsync = promisify(zlib.inflate);

/**
 * A specialized redis client for use in generic view caching.
 * For larger values, we (de)compress them using zlib.
 * Especially large values get (de)compressed using the worker pool.
 */

export const compress = async (value: IORedis.ValueType): Promise<IORedis.ValueType> => {
    if (typeof value === 'number' || Array.isArray(value) || value.length < MIN_COMPRESS_SIZE) {
        return value;
    }
    if (value.length > MAX_SYNC_SIZE) {
        // Do not block the event loop compressing large values
        try {
            const compressedValue = await deflateAsync(value, { level: 1 });
            return HEADER_COMPRESSED_ASYNC + compressedValue.toString('base64');
        } catch (err) {
            return value.toString('base64');
        }
    }
    const compressedValue = zlib.deflateSync(value, { level: 1 });
    return HEADER_COMPRESSED_SYNC + compressedValue.toString('base64');
};

export const decompress = async (value: string | null): Promise<string | null> => {
    if (!value) {
        return null;
    }
    if (value.startsWith(HEADER_COMPRESSED_ASYNC)) {
        // Decompress large values using the worker pool
        try {
            const compressedValue = Buffer.from(value.slice(HEADER_COMPRESSED_ASYNC.length), 'base64');
            const decompressedValue = await inflateAsync(compressedValue);
            return decompressedValue.toString();
        } catch (err) {
            return null;
        }
    }
    if (value.startsWith(HEADER_COMPRESSED_SYNC)) {
        const compressedValue = Buffer.from(value.slice(HEADER_COMPRESSED_SYNC.length), 'base64');
        const decompressedValue = zlib.inflateSync(compressedValue);
        return decompressedValue.toString();
    }
    return value;
};

let compressingRedisClient: IORedis.Redis | IORedis.Cluster;
if (gvRedisEnabled && gvRedisHost) {
    logger.info('Using dedicated redis for generic view caching.');

    compressingRedisClient = createRedisConnection({
        connectionName: 'gvRedis',
        useTls,
        ...(clustered ? { cluster: [redisHostAndPort] } : redisHostAndPort),
    }).getClient();

    const originalGet = compressingRedisClient.get.bind(compressingRedisClient);
    const originalSet = compressingRedisClient.set.bind(compressingRedisClient);

    compressingRedisClient.get = async (key: string): Promise<string | null> => {
        const result = await originalGet(key);
        return decompress(result);
    };

    compressingRedisClient.set = async (
        key: IORedis.KeyType,
        value: IORedis.ValueType,
        ...args: any[]
    ): Promise<IORedis.Ok | null> => {
        const compressedValue = await compress(value);
        return originalSet(key, compressedValue, ...args);
    };
}

export const gvRedisClient: IORedis.Redis | IORedis.Cluster = compressingRedisClient ?? client;
