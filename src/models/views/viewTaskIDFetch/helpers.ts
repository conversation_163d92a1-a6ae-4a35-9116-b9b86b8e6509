import config from 'config';
import { promiseMapLimit } from '../../../utils/promises';
import { batchLongArray } from '../../helpers_v2';
import { getViewsBatchingConfig } from '../../integrations/split/squadTreatments/viewsTreatments';

const QUERY_BATCH_SIZE = Number(config.get('views.batching.query_limit'));

const { field_types } = config;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function batchTaskQueries<A extends any[], B>(
    getTasks: (ids: string[], ...a: A) => Promise<B[]>,
    taskIds: string[],
    ...additionalArgs: A
): Promise<B[]> {
    const { task_batch_size } = getViewsBatchingConfig();

    const batches = batchLongArray(taskIds, task_batch_size);
    const result = await promiseMapLimit(batches, QUERY_BATCH_SIZE, async (batchTaskIds: Array<string>) =>
        getTasks(batchTaskIds, ...additionalArgs)
    );

    return result.flat();
}

// Takes forms like {"dateCreated": {"min": 111, "max": 111}} or {"cf_xx": {"sum": "111"}}
interface MetricColumnLabel {
    [key: string]: { [key: string]: number | string };
}

export interface DivisionResponse {
    id?: string;
    groups: { id: string; group_calculations: MetricColumnLabel; task_ids: string[] }[];
}

export function getTagsQueryInTeamScope(parentType: number, include_archived: boolean): string | undefined {
    const parent_types: Record<string, number> = config.get('views.parent_types');
    let additional_join: string;
    let specific_condition: string;
    const archived_condition = include_archived ? '' : 'AND projects.archived = FALSE';

    switch (parentType) {
        case parent_types.team:
        case parent_types.shared:
        case parent_types.widget:
        case parent_types.template:
            additional_join = '';
            specific_condition = 'AND projects.team = $1';
            break;
        case parent_types.project:
            additional_join = 'INNER JOIN task_mgmt.projects as projects2 ON projects2.team = projects.team';
            specific_condition = 'AND projects2.id = $1';
            break;
        case parent_types.category:
            additional_join = 'INNER JOIN task_mgmt.categories ON categories.workspace_id = projects.team';
            specific_condition = 'AND categories.id = $1';
            break;
        case parent_types.subcategory:
            additional_join = 'INNER JOIN task_mgmt.subcategories ON subcategories.workspace_id = projects.team';
            specific_condition = 'AND subcategories.id = $1';
            break;
        default:
            return undefined;
    }

    const tag_query = `
        SELECT project_tags.*
        FROM task_mgmt.project_tags
        INNER JOIN task_mgmt.projects
            ON projects.id = project_tags.project_id
        ${additional_join}
        LEFT OUTER JOIN task_mgmt.project_indexes
            ON project_indexes.userid = $2
            AND project_indexes.project_id = projects.id
        WHERE project_tags.deleted = FALSE
            AND projects.deleted = FALSE
            AND projects.template = FALSE
            ${specific_condition}
            ${archived_condition}
        ORDER BY coalesce(project_indexes.orderindex, projects.orderindex)
    `;

    return tag_query;
}

export const field_type_to_sql_type = {
    [field_types.url]: 'text',
    [field_types.drop_down]: 'text',
    [field_types.email]: 'text',
    [field_types.phone]: 'text',
    [field_types.date]: 'numeric',
    [field_types.text]: 'text',
    [field_types.short_text]: 'text',
    [field_types.checkbox]: 'boolean',
    [field_types.number]: 'numeric',
    [field_types.currency]: 'numeric',
    [field_types.tasks]: 'text[]',
    [field_types.users]: 'numeric[]',
    [field_types.votes]: 'numeric[]',
    [field_types.emoji]: 'numeric',
    [field_types.labels]: 'uuid[]',
    [field_types.manual_progress]: 'numeric',
    [field_types.attachment]: 'text[]',
};
