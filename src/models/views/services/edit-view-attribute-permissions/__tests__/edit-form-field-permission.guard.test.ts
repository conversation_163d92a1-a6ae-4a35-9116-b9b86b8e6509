import { ViewAttributeWithField } from '@clickup-legacy/models/views/services/edit-view-attribute-permissions/view-attribute-change';
import type { ObjectKey } from '@time-loop/ovm-object-version';
import { ObjectKeyMap } from '@clickup/object-version-cache-common';
import { UserId } from '@clickup-legacy/libs/user/interfaces/Ids';
import { getCustomFieldsByIds } from '@clickup-legacy/models/field/datastores/customFieldsDatastore';
import { EditFormFieldPermissionGuard } from '../edit-form-field-permission.guard';
import {
    EditViewAttributePermissionGuardContext,
    ViewAttributeProvider,
} from '../abstractions/edit-view-attribute-permission.guard';
import { FieldId } from '../../../../field/interfaces/types';

const authServiceMock = {
    filterAccess: jest.fn(),
};

jest.mock('@clickup-legacy/utils/access/services/authorization/instances', () => ({
    getAuthorizationService: () => authServiceMock,
}));

jest.mock('@clickup-legacy/models/field/datastores/customFieldsDatastore', () => ({
    getCustomFieldsByIds: jest.fn(),
}));

describe('EditFormFieldPermissionGuard unit tests', () => {
    const userId = 42;
    const userId2 = 1234;
    const workspaceId = 333;
    const defaultOrderIndex = 1;
    const viewIds = ['view-id'];

    let subject: EditFormFieldPermissionGuard;
    let repositoryMock: ViewAttributeProvider;

    let updatedFormFields: ViewAttributeWithField[];
    let originalFormFields: ViewAttributeWithField[];
    let defaultFieldNameByIds: Map<FieldId, string>;
    let fieldAccessUserIds: Map<FieldId, UserId[]>;
    let privateFieldIds: Set<FieldId>;

    beforeEach(() => {
        jest.resetAllMocks();

        resetFields();

        repositoryMock = {
            getItemsByIds: jest.fn(() => Promise.resolve(originalFormFields)),
        };

        jest.spyOn(authServiceMock, 'filterAccess').mockImplementation(
            ({ userIds, objects }: { userIds: UserId[]; objects: ObjectKey[] }) => {
                const authorizedObjects = new ObjectKeyMap<ObjectKey, Set<number>>();

                for (const object of objects ?? []) {
                    const userIdsWithAccess = fieldAccessUserIds.get(object.object_id) ?? [];

                    authorizedObjects.set(
                        object,
                        new Set(userIds.filter(currentUserId => userIdsWithAccess.includes(currentUserId)))
                    );
                }

                return { authorizedObjects };
            }
        );

        jest.mocked(getCustomFieldsByIds).mockResolvedValue([]);

        subject = new EditFormFieldPermissionGuard(repositoryMock);
    });

    describe('form field addition is requested', () => {
        beforeEach(() => {
            customFieldsExist('added_field', 'public_added_field');

            fieldsAreAddedToForm('added_field', 'public_added_field');
        });

        afterEach(() => {
            resetFields();
        });

        describe('user has edit view permission', () => {
            beforeEach(() => {
                customField('added_field').isPrivate().hasAccess([userId]);
            });

            it('field is added', async () => {
                const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                customField('added_field').in(attributes).isExpectedToExist();
            });

            it('private field is added', async () => {
                const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                customField('public_added_field').in(attributes).isExpectedToExist();
            });
        });

        describe('user has edit permission', () => {
            beforeEach(() => {
                customField('added_field').isPrivate().hasAccess([userId]);
            });

            it('field is added', async () => {
                const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                customField('added_field').in(attributes).isExpectedToExist();
            });

            describe('custom field name is specified', () => {
                const updatedFieldName = 'Custom Field 2 Name';

                beforeEach(() => {
                    customField('added_field')
                        .isPrivate()
                        .hasUpdate(field => {
                            field.display_name = updatedFieldName;
                        });
                });

                it('custom name is saved', async () => {
                    const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                    customField('added_field')
                        .in(attributes)
                        .for(field => field.display_name)
                        .expect(() => updatedFieldName);
                });
            });
        });

        describe('user has no permission', () => {
            beforeEach(() => {
                customField('added_field').isPrivate().hasNoAccess();
            });

            it('error is thrown', async () => {
                await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
            });

            it('error still should be thrown if authz returned access for a different user', async () => {
                customField('added_field').hasAccess([userId2]);

                await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
            });
        });

        describe('user has no permissions but error is not thrown because the field is public', () => {
            const updatedFieldName = 'Custom Field 3 Name';

            beforeEach(() => {
                customField('added_field').isPrivate().hasAccess([userId]);
                customField('public_added_field').hasUpdate(field => {
                    field.display_name = updatedFieldName;
                });
            });

            it('error is not thrown', async () => {
                const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                customField('public_added_field')
                    .in(attributes)
                    .for(field => field.display_name)
                    .expect(() => updatedFieldName);
            });
        });
    });

    describe('form field update is requested', () => {
        beforeEach(() => {
            customFieldsExist('updated_field', 'public_updated_field');

            formFieldsAre('updated_field', 'public_updated_field');
        });

        afterEach(() => {
            resetFields();
        });

        describe('form field index is updated', () => {
            const updatedOrderIndex = 42;

            beforeEach(() => {
                customField('updated_field')
                    .isPrivate()
                    .hasUpdate(field => {
                        field.orderindex = updatedOrderIndex;
                    });
            });

            describe('user has edit view permission', () => {
                beforeEach(() => {
                    customField('updated_field').hasAccess([userId]);
                });

                it('field index is updated', async () => {
                    const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                    customField('updated_field')
                        .in(attributes)
                        .for(field => field.orderindex)
                        .expect(() => updatedOrderIndex);
                });
            });

            describe('user has no permission', () => {
                beforeEach(() => {
                    customField('updated_field').isPrivate().hasNoAccess();
                });

                it('error is thrown', async () => {
                    await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
                });

                it('error still should be thrown if authz returned access for a different user', async () => {
                    customField('updated_field').hasAccess([userId2]);

                    await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
                });
            });
        });

        describe('form field dependencies are updated', () => {
            const addedRuleId = 'added-rule-id';

            beforeEach(() => {
                customField('updated_field')
                    .isPrivate()
                    .hasUpdate(field => {
                        field.dependencies = field.dependencies.filter(dep => dep !== 'existing-rule-id');
                        field.dependencies.push(addedRuleId);
                    });
            });

            describe('user has read permission', () => {
                beforeEach(() => {
                    customField('updated_field').isPrivate().hasAccess([userId]);
                });

                it('field dependency is updated', async () => {
                    const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                    customField('updated_field')
                        .in(attributes)
                        .for(field => field.dependencies)
                        .expect(() => [addedRuleId]);
                });
            });

            describe('user has no permission', () => {
                beforeEach(() => {
                    customField('updated_field').isPrivate().hasNoAccess();
                });

                it('error is thrown', async () => {
                    await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
                });

                it('error still should be thrown if authz returned access for a different user', async () => {
                    customField('updated_field').hasAccess([userId2]);

                    await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
                });
            });

            describe('user has no permissions but error is not thrown because the field is public', () => {
                beforeEach(() => {
                    customField('updated_field').isPrivate().hasAccess([userId]);
                    customField('public_updated_field').hasUpdate(field => {
                        field.dependencies = field.dependencies.filter(dep => dep !== 'existing-rule-id');
                        field.dependencies.push(addedRuleId);
                    });
                });

                it('error is not thrown', async () => {
                    const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                    customField('public_updated_field')
                        .in(attributes)
                        .for(field => field.dependencies)
                        .expect(() => [addedRuleId]);
                });
            });
        });
    });

    describe('form field deletion is requested', () => {
        beforeEach(() => {
            customFieldsExist('deleted_field', 'public_deleted_field');

            formFieldsAre('deleted_field', 'public_deleted_field');

            fieldsAreDeletedFromForm('deleted_field', 'public_deleted_field');
        });

        afterEach(() => {
            resetFields();
        });

        describe('user has edit view permission', () => {
            beforeEach(() => {
                customField('deleted_field').isPrivate().hasAccess([userId]);
            });

            it('field is deleted', async () => {
                const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                customField('deleted_field').in(attributes).isExpectedNotToExist();
            });
        });

        describe('user has no permission', () => {
            beforeEach(() => {
                customField('deleted_field').isPrivate().hasNoAccess();
            });

            it('error is thrown', async () => {
                await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
            });

            it('error still should be thrown if authz returned access for a different user', async () => {
                customField('deleted_field').hasAccess([userId2]);

                await expect(subject.revertChangesWithoutAccess(getContext())).rejects.toThrow();
            });
        });

        describe('user has no permissions but error is not thrown because the field is public', () => {
            it('error is not thrown', async () => {
                customField('deleted_field').hasAccess([userId]);

                const { attributes } = await subject.revertChangesWithoutAccess(getContext());

                customField('public_deleted_field').in(attributes).isExpectedNotToExist();
            });
        });
    });

    function customFieldsExist(...fieldNames: string[]) {
        for (const fieldName of fieldNames) {
            defaultFieldNameByIds.set(fieldIdByName(fieldName), fieldName);
        }
    }

    function formFieldsAre(...fieldNames: string[]) {
        for (const fieldName of fieldNames) {
            const field: ViewAttributeWithField = {
                display_name: fieldName,
                orderindex: defaultOrderIndex,
                field: `cf_${fieldIdByName(fieldName)}`,
                dependencies: ['existing-rule-id'],
            };
            originalFormFields.push(field);
        }

        fieldsAreAddedToForm(...fieldNames);
    }

    function fieldsAreAddedToForm(...fieldNames: string[]) {
        for (const fieldName of fieldNames) {
            const field: ViewAttributeWithField = {
                display_name: fieldName,
                orderindex: defaultOrderIndex,
                field: `cf_${fieldIdByName(fieldName)}`,
                dependencies: ['existing-rule-id'],
            };
            updatedFormFields.push(field);
        }
    }

    function fieldsAreDeletedFromForm(...fieldNames: string[]) {
        updatedFormFields = updatedFormFields.filter(field => !fieldNames.includes(field.display_name));
    }

    function customField(name: string) {
        const fieldId = fieldIdByName(name);

        return {
            hasAccess(userIds: UserId[]) {
                fieldAccessUserIds.set(fieldId, userIds);
            },
            isPrivate() {
                privateFieldIds.add(fieldId);

                (getCustomFieldsByIds as jest.Mock)
                    .mockReset()
                    .mockResolvedValue(Array.from(privateFieldIds).map(id => ({ id })));

                return this;
            },
            hasNoAccess() {
                fieldAccessUserIds.set(fieldId, []);
            },
            hasUpdate(update: (field: ViewAttributeWithField) => void) {
                const field = getFieldById(fieldId, updatedFormFields);

                update(field);
            },
            in: (fields: ViewAttributeWithField[]) => ({
                isExpectedToRollback: () => {
                    const field = getFieldById(fieldId, fields);
                    expect(field).toBeDefined();

                    const originalFormField = getFieldById(fieldId, originalFormFields);

                    expect(field).toMatchObject(originalFormField);
                },
                isExpectedNotToExist: () => {
                    expect(getFieldById(fieldId, fields)).toBeFalsy();
                },
                isExpectedToExist: () => {
                    expect(getFieldById(fieldId, fields)).toBeTruthy();
                },
                for: (actual: (field: ViewAttributeWithField) => unknown) => ({
                    expect: (expected: (field: ViewAttributeWithField) => unknown) => {
                        const field = getFieldById(fieldId, fields);

                        expect(field).toBeDefined();

                        expect(actual(field)).toEqual(expected(field));
                    },
                }),
            }),
        };
    }

    function getContext(): EditViewAttributePermissionGuardContext {
        return {
            userId,
            workspaceId,
            updatedAttributes: updatedFormFields,
            viewIds,
        };
    }

    function resetFields() {
        updatedFormFields = [];
        originalFormFields = [];
        defaultFieldNameByIds = new Map<FieldId, string>();
        fieldAccessUserIds = new Map<FieldId, UserId[]>();
        privateFieldIds = new Set<FieldId>();
    }

    function fieldIdByName(name: string): FieldId {
        return `id-of-${name}`.replaceAll('_', '-');
    }

    function getFieldById(fieldId: FieldId, fields: ViewAttributeWithField[]): ViewAttributeWithField {
        return fields.find(field => field.field === `cf_${fieldId}`);
    }
});
