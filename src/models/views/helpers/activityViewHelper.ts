import { QueryParams } from '@clickup-legacy/utils/interfaces/QueryObject';

/**
 * Build a condition for custom item and mutates the `task_params` array
 * @param op
 * @param values
 * @param task_params
 */
export function buildCustomItemCondition(op: string, values: number[], task_params: QueryParams): string {
    // Remove `0` from the values, it represents "task" type
    const copyValues = Array.from(values);
    const indexOfTask = copyValues.findIndex(value => value === 0);
    if (indexOfTask !== -1) {
        copyValues.splice(indexOfTask, 1);
    }

    // Not including "task" type needs special handing, because NULLs will be skipped
    // e.g. "Task type" is not "X1"
    let specialNull = '';
    if (op === 'NOT' && indexOfTask === -1) {
        specialNull = 'items.custom_type IS NULL OR ';
    }

    // Construct the condition, because for "task" type custom_type is null
    const customTypeConditions = [];

    if (copyValues.length) {
        task_params.push(copyValues);
        customTypeConditions.push(`items.custom_type ${op === 'EQ' ? '= ANY' : '!= ALL'}($${task_params.length})`);
    }
    if (indexOfTask !== -1) {
        customTypeConditions.push(`items.custom_type ${op === 'EQ' ? 'IS' : 'IS NOT'} NULL`);
    }

    return `(${specialNull}${customTypeConditions.join(op === 'EQ' ? ' OR ' : ' AND ')})`;
}
