/**
 * Declare routes for doc services here
 *
 * TODO: This legacy router is being created to prevent adding new routes for
 *  the docs service under the unclaimed /v1 route. If you need to create a new
 *  docs service API, be sure to create it in the appropriate router (one that
 *  is not prefixed with legacy). If the only router available is not prefixed
 *  with legacy, please create a duplicate version of the router, prefix it with
 *  legacy, replace the non prefixed one here with the prefixed one, and add
 *  your new route to the non-prefixed version. We should clean this up once we
 *  have updated the FE to no longer use any docs APIs in the /v1 space.
 */

import express from 'express';
import docRouter from '../documents/routes/docRoutes';
import docContentRouter from '../documents/routes/docContentRoutes';
import docAIRouter from '../documents/routes/docAIRoutes';
import docExportRouter from '../documents/routes/docExportRoutes';
import docImportRouter from '../documents/routes/docImportRoutes';
import documentTaggingRouter from '../documents/routes/documentTaggingRoutes';
import documentTagRouter from '../documents/routes/documentTagRoutes';
import legacyPageRouter from '../pages/routes/legacy/legacyPageRoutes';
import teamDocumentRouter from '../documents/routes/teamDocumentRoutes';
import legacyPageViewRouter from '../pages/routes/legacy/legacyPageViewRoutes';
import publicPagesRouter from '../pages/routes/publicPageRoutes';
import notepadRouter from '../../notepad/notepadRoutes';
import codoxRouter from '../../integrations/codox/codoxRoutes';
import { jwtCheck } from '../../../utils/auth/jwt/jwtCheckMiddlewares';

const legacyDocsServiceRouter = express.Router();

legacyDocsServiceRouter.use(
    '/',
    [publicPagesRouter, docExportRouter, docContentRouter, notepadRouter, codoxRouter],
    jwtCheck,
    [
        docImportRouter,
        docRouter,
        docAIRouter,
        teamDocumentRouter,
        documentTagRouter,
        documentTaggingRouter,
        legacyPageRouter,
        legacyPageViewRouter,
    ]
);

export default legacyDocsServiceRouter;
