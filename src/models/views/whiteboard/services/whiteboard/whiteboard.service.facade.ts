import {
    DeleteWhiteboardArgs,
    RawDeleteWhiteboardArgs,
    RawGetWhiteboardArgs,
    RawReplaceWhiteboardArgs,
} from '@clickup-legacy/models/views/whiteboard/services/whiteboard/common/types';
import { WhiteboardsServiceV3GetWhiteboardResponse } from '@clickup-legacy/models/views/whiteboard/integrations/whiteboards-service-v3.api-client.types';
import { fixTypes } from './common/fix-types.function';
import { WhiteboardsServiceV2GetWhiteboardResponseData } from '../../integrations/whiteboards-service-v2.api-client.types';
import * as WhiteboardV2Client from './v2/whiteboard-v2-client';
import * as WhiteboardV3Client from './v3/whiteboard-v3-client';
import { getWhiteboardViewData } from './common/get-whiteboard-view-data.function';

export const deleteWhiteboard = async (args: RawDeleteWhiteboardArgs): Promise<void> => {
    const fixedArgs: DeleteWhiteboardArgs = fixTypes(args);
    if (fixedArgs.whiteboardViewData.whiteboardVersion === 'v3') {
        await WhiteboardV3Client.deleteWhiteboard(fixedArgs);
    } else {
        await WhiteboardV2Client.deleteWhiteboard(fixedArgs);
    }
};

/**
 * If the whiteboard is not found, it will be created.
 * If the whiteboard is found, it will be replaced.
 *
 * Think of it as a "upsert" operation.
 */
export const replaceWhiteboard = async (args: RawReplaceWhiteboardArgs): Promise<void> => {
    const whiteboardViewData = getWhiteboardViewData(args.whiteboardViewData);
    if (whiteboardViewData.whiteboardVersion === 'v3') {
        await WhiteboardV3Client.replaceWhiteboard(args);
    } else {
        await WhiteboardV2Client.replaceWhiteboard(args);
    }
};

export const getWhiteboard = async (
    args: RawGetWhiteboardArgs
): Promise<WhiteboardsServiceV2GetWhiteboardResponseData | WhiteboardsServiceV3GetWhiteboardResponse | null> => {
    const fixedArgs = fixTypes(args);
    if (fixedArgs.whiteboardViewData.whiteboardVersion === 'v3') {
        return WhiteboardV3Client.getWhiteboard(args);
    }
    return WhiteboardV2Client.getWhiteboard(args);
};
