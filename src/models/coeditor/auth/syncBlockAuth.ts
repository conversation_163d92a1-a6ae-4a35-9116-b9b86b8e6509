import { getBlockWorkspaceId, getSyncBlockParent } from '../../sync_blocks/syncBlockService';
import { Auth } from '../Auth';
import { PermissionLevels } from '../constants';
import { checkPermission } from './genericAuth';

export class SyncBlockAuth implements Auth {
    async authenticate(userId: number, entityId: string): Promise<PermissionLevels> {
        const { parent_id, parent_type } = await getSyncBlockParent(entityId);
        const permissionLevel = await checkPermission(userId, parent_id, parent_type);
        return permissionLevel;
    }

    async getTeam(entityId: string): Promise<string> {
        return getBlockWorkspaceId(entityId);
    }
}
