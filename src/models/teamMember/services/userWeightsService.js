import config from 'config';
import { ClickUpError } from '../../../utils/errors';
import * as db from '../../../utils/db';

const UserError = ClickUpError.makeNamedError('userWeights');

export async function _getUserWeights(team_id, userid, valid_members) {
    const params = [];

    const valid_members_query = !valid_members
        ? ''
        : `
        AND team_user_weights.assigned_userid = ANY($${params.push(valid_members)})
        `;
    const query = `
        SELECT
            team_user_weights.assigned_userid,
        team_user_weights.type,
        count(*) as count
        FROM
            task_mgmt.team_user_weights
        JOIN
            task_mgmt.team_members
            ON team_members.userid = team_user_weights.assigned_userid
                AND team_members.team_id = team_user_weights.team_id
        WHERE
            team_user_weights.team_id = $${params.push(team_id)}
            AND team_user_weights.userid = $${params.push(userid)}
            ${valid_members_query}
        GROUP BY
            team_user_weights.assigned_userid, team_user_weights.type
        `;
    let weights;
    try {
        weights = await db.promiseReplicaQuery(query, params);
    } catch (err) {
        throw new UserError(err, 'USR_110', 500);
    }

    const user_map = {};
    weights.rows.forEach(row => {
        const uid = row.assigned_userid;
        if (!user_map[uid]) {
            user_map[uid] = {};
        }
        const type_keys = Object.keys(config.user_weight_type);
        if (!Number.isNaN(parseInt(row.count, 10)) && Object.values(config.user_weight_type).includes(row.type)) {
            const field = type_keys.find(k => config.user_weight_type[k] === row.type);
            user_map[uid][field] = parseInt(row.count, 10);
        }
    });
    Object.values(user_map).forEach(map => {
        const sum = Object.values(map).reduce((acc, v) => acc + v, 0);
        map.total = sum;
    });

    return user_map;
}
