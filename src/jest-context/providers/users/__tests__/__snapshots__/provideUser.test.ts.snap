// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`should create a user 1`] = `
Object {
  "android_onboarding_done": false,
  "bl": false,
  "block_tz_change_modal": null,
  "bouncing": false,
  "box_enabled": true,
  "client": Any<RestClient>,
  "clientToken": Any<String>,
  "color": Any<String>,
  "contrast": 0,
  "country_iso_code": null,
  "created_from_v2": false,
  "dark_theme": null,
  "dashboard": 6,
  "dashboard_size": null,
  "date_format": null,
  "date_joined": Any<String>,
  "default_category": null,
  "default_project": null,
  "default_subcategory": null,
  "default_team": null,
  "demo_data_done": false,
  "density": 1,
  "description": null,
  "dont_add_harvest_task_ids": null,
  "drive_enabled": true,
  "dropbox_enabled": true,
  "email": Any<String>,
  "extended_logging": false,
  "features": Object {
    "harvest": null,
    "toggl_check": null,
  },
  "gh_authed": false,
  "global_font_support": false,
  "has_seen_mobile_onboarding": false,
  "hide_breadcrumbs_when_sorting": null,
  "hmac": Any<String>,
  "hotkeys": false,
  "id": Any<Number>,
  "imports_in_progress": Array [],
  "inbox_breadcrumbs": null,
  "initials": "E",
  "joined": true,
  "last_2fa_prompt": null,
  "markdown_shortcuts": true,
  "onboarding_bonus_claimed": false,
  "onboarding_data": null,
  "onboarding_incentive_accepted": false,
  "onboarding_opt_out": false,
  "onboarding_step": null,
  "onboarding_video_steps": null,
  "one_drive_enabled": true,
  "password": Any<String>,
  "phone": null,
  "pop_up_preference": 1,
  "post_with_cmd": null,
  "profileInfo": Object {},
  "profilePicture": null,
  "require_2fa_count": 0,
  "reverse_statuses": null,
  "rtl_mode": false,
  "scratchpad": true,
  "seen_time_estimate_warning": false,
  "segmentation_questions": Object {},
  "show_celebrations": true,
  "show_coverimages": true,
  "sidebar_theme": null,
  "skipCaptcha": false,
  "sso": Object {
    "policies": Array [],
    "sso_requirement_violated": false,
  },
  "theme_color": null,
  "timezone": null,
  "timezone_offset": null,
  "tour_cards": Object {
    "category_tour_completed": null,
    "comment_tour_completed": null,
    "create_task_tour_completed": false,
    "list_select_tour_completed": null,
    "minimize_tour_completed": null,
    "multitask_tour_completed": false,
    "notifications_tour_completed": false,
    "used_slash_commands": null,
    "view_task_tour_completed": false,
  },
  "tour_cards_data": null,
  "tour_cards_done": false,
  "twenty_four_hr_setting": null,
  "twofa_enabled": "0",
  "twofa_options": Object {
    "text_enabled": false,
    "totp_enabled": false,
  },
  "twofa_required": false,
  "user_data": null,
  "user_settings": Array [
    Object {
      "name": "pinned_rec",
      "value": null,
    },
    Object {
      "name": "pinned_time_tracking",
      "value": null,
    },
    Object {
      "name": "pinned_reminder",
      "value": null,
    },
    Object {
      "name": "pinned_note",
      "value": null,
    },
    Object {
      "name": "pinned_doc",
      "value": null,
    },
    Object {
      "name": "pinned_tray",
      "value": true,
    },
    Object {
      "name": "country_holidays",
      "value": Array [],
    },
    Object {
      "name": "pinned_calendar",
      "value": null,
    },
    Object {
      "name": "show_stats_while_typing",
      "value": null,
    },
    Object {
      "name": "stats_target",
      "value": null,
    },
    Object {
      "name": "focus_mode",
      "value": null,
    },
    Object {
      "name": "start_date_only_tasks",
      "value": true,
    },
    Object {
      "name": "calendar_settings",
      "value": Object {},
    },
    Object {
      "name": "search_name",
      "value": null,
    },
    Object {
      "name": "search_custom_fields",
      "value": null,
    },
    Object {
      "name": "search_description",
      "value": null,
    },
    Object {
      "name": "show_stats_details",
      "value": null,
    },
    Object {
      "name": "stats_active_detail",
      "value": null,
    },
    Object {
      "name": "task_view_v3",
      "value": null,
    },
    Object {
      "name": "show_quotes",
      "value": null,
    },
    Object {
      "name": "docs_focus_mode",
      "value": null,
    },
    Object {
      "name": "plain_urls",
      "value": null,
    },
    Object {
      "name": "custom_fields_manager",
      "value": null,
    },
    Object {
      "name": "task_view_mode",
      "value": null,
    },
    Object {
      "name": "layout_v3",
      "value": true,
    },
    Object {
      "name": "toolbar_theme",
      "value": null,
    },
    Object {
      "name": "pinned_ai",
      "value": null,
    },
    Object {
      "name": "pinned_chat",
      "value": null,
    },
    Object {
      "name": "pinned_my_tasks",
      "value": null,
    },
    Object {
      "name": "pinned_whiteboard",
      "value": null,
    },
    Object {
      "name": "pinned_people",
      "value": null,
    },
    Object {
      "name": "pinned_sidebar_items",
      "value": Array [
        "ai",
        "calendar",
        "chat",
        "clips",
        "dashboards",
        "docs",
        "forms",
        "goals",
        "home",
        "home-spaces",
        "home-chat",
        "inbox",
        "my-work",
        "pulse",
        "teams",
        "time-hub",
        "whiteboards",
        "meetings",
      ],
    },
    Object {
      "name": "inbox_v3",
      "value": null,
    },
    Object {
      "name": "docs_comment_style",
      "value": "minimal",
    },
    Object {
      "name": "home_v3_enabled",
      "value": null,
    },
    Object {
      "name": "details_target",
      "value": null,
    },
    Object {
      "name": "pinned_dashboard",
      "value": null,
    },
    Object {
      "name": "docs_block_select_all",
      "value": true,
    },
  ],
  "username": null,
  "v2_beta": null,
  "week_start_day": null,
}
`;
