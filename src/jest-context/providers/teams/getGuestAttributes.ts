import type { RestClient } from '@clickup/rest-client';
import { boolean, number, string, optional, record, type } from 'superstruct';

import type { ClientProvider } from '../../ClientProvider';

interface Params {
    teamId: string;
    userids: number[];
}

export async function getGuestAttributesAsPost(this: ClientProvider, params: Params, options: { client: RestClient }) {
    const { client } = options ?? (await this.provideUser());
    return client
        .writeJson(
            '/team/v1/team/:teamId/guestAttributes',
            {
                teamId: params.teamId,
                userids: params.userids.map(String),
            },
            'POST'
        )
        .json(
            record(
                string(),
                type({
                    read_only_guest: optional(boolean()),
                    internal: optional(boolean()),
                    guest_access: optional(
                        type({
                            userid: number(),
                            task_count: number(),
                            project_count: optional(number()),
                            category_count: number(),
                            subcategory_count: number(),
                            team_count: number(),
                        })
                    ),
                })
            )
        );
}
