// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`should create a project 1`] = `
Object {
  "activity_view_settings": null,
  "activity_view_template": null,
  "activity_view_update_views": null,
  "admin_can_manage": true,
  "all_statuses": Array [
    Object {
      "colors": Array [
        "#d3d3d3",
      ],
      "orderindex": 0,
      "status": "to do",
      "type": "open",
    },
    Object {
      "colors": Array [
        "#008844",
      ],
      "orderindex": 1,
      "status": "complete",
      "type": "closed",
    },
  ],
  "archived": false,
  "assignee": null,
  "assignees": Array [],
  "automation_count": 0,
  "avatar": null,
  "avatar_source": null,
  "avatar_value": null,
  "board_view_settings": null,
  "board_view_template": null,
  "board_view_update_views": null,
  "box_view_settings": null,
  "box_view_template": null,
  "box_view_update_views": null,
  "calendar_view_settings": null,
  "calendar_view_template": null,
  "calendar_view_update_views": null,
  "color": null,
  "content": "{\\"ops\\":[]}",
  "creator": Any<Number>,
  "custom_items": false,
  "custom_task_ids_display": null,
  "custom_task_ids_start": null,
  "custom_task_ids_start_100": null,
  "date_created": Any<String>,
  "date_deleted": null,
  "date_updated": Any<String>,
  "default_category": null,
  "default_preset_view": 1,
  "deleted": false,
  "deleted_by": null,
  "due_date": null,
  "due_date_time": false,
  "editor_token": Any<String>,
  "emails_as_replies": null,
  "emails_clickapp": false,
  "features": Object {
    "check_unresolved": Object {
      "checklists": null,
      "comments": null,
      "enabled": true,
      "subtasks": null,
    },
    "custom_fields": Object {
      "enabled": true,
    },
    "custom_items": Object {
      "enabled": false,
    },
    "dependency_warning": Object {
      "enabled": true,
    },
    "due_dates": Object {
      "enabled": true,
      "remap_closed_due_date": false,
      "remap_due_dates": false,
      "start_date": true,
    },
    "milestones": Object {
      "enabled": false,
    },
    "multiple_assignees": Object {
      "enabled": true,
    },
    "points": Object {
      "enabled": false,
    },
    "priorities": Object {
      "enabled": true,
      "priorities": Array [
        Object {
          "color": "#f50000",
          "id": "1",
          "orderindex": "1",
          "priority": "urgent",
        },
        Object {
          "color": "#f8ae00",
          "id": "2",
          "orderindex": "2",
          "priority": "high",
        },
        Object {
          "color": "#6fddff",
          "id": "3",
          "orderindex": "3",
          "priority": "normal",
        },
        Object {
          "color": "#d8d8d8",
          "id": "4",
          "orderindex": "4",
          "priority": "low",
        },
      ],
    },
    "scheduler_enabled": false,
    "sprints": Object {
      "enabled": false,
    },
    "status_pies": Object {
      "enabled": false,
    },
    "tags": Object {
      "enabled": true,
    },
    "time_tracking": Object {
      "default_to_billable": 2,
      "enabled": true,
      "harvest": false,
      "rollup": false,
    },
  },
  "gantt_view_settings": null,
  "gantt_view_template": null,
  "gantt_view_update_views": null,
  "group_assignees": Array [],
  "group_members": Any<Array>,
  "hidden": false,
  "hide_project": false,
  "id": Any<String>,
  "import_id": null,
  "import_uuid": null,
  "importing": null,
  "listViewSettings": Object {
    "sorting": Array [],
    "visible": Object {
      "assignees": true,
      "date_created": false,
      "date_updated": true,
      "due_date": true,
      "priority": true,
      "start_date": false,
      "task_id": false,
      "time_spent": false,
    },
  },
  "list_view_settings": null,
  "list_view_template": null,
  "list_view_update_views": null,
  "map_view_template": null,
  "map_view_update_views": null,
  "members": Any<Array>,
  "mind_map_view_settings": null,
  "mind_map_view_template": null,
  "mind_map_view_update_views": null,
  "multiple_assignees": true,
  "name": StringMatching /Test Project/,
  "orderindex": null,
  "owner": Any<Object>,
  "permanent_template_id": null,
  "permission_level": 5,
  "permissions": Any<Object>,
  "personal_list": false,
  "points": false,
  "points_estimate_rollup": null,
  "preset_views": Array [
    1,
    2,
  ],
  "priority": null,
  "private": false,
  "project_orderindex": null,
  "project_prefix": null,
  "project_type": null,
  "public_sharing": null,
  "slack_channel": null,
  "start_date": null,
  "start_date_time": false,
  "status": null,
  "statuses": Any<Array>,
  "storage_used": "0",
  "subcategoryId": null,
  "table_view_settings": null,
  "table_view_template": null,
  "table_view_update_views": null,
  "taskcount": "0",
  "team": Object {
    "date_created": Any<String>,
    "id": Any<String>,
    "name": Any<String>,
    "using_bitbucket": null,
    "using_github": false,
    "using_gitlab": null,
  },
  "template": false,
  "template_field_ids": null,
  "time_in_status": null,
  "timeline_view_settings": null,
  "timeline_view_template": null,
  "timeline_view_update_views": null,
  "workload_view_settings": null,
  "workload_view_template": null,
  "workload_view_update_views": null,
  "zoom": null,
}
`;

exports[`should create a project with subcategory 1`] = `
Object {
  "activity_view_settings": null,
  "activity_view_template": null,
  "activity_view_update_views": null,
  "admin_can_manage": true,
  "all_statuses": Array [
    Object {
      "colors": Array [
        "#d3d3d3",
      ],
      "orderindex": 0,
      "status": "to do",
      "type": "open",
    },
    Object {
      "colors": Array [
        "#008844",
      ],
      "orderindex": 1,
      "status": "complete",
      "type": "closed",
    },
  ],
  "archived": false,
  "assignee": null,
  "assignees": Array [],
  "automation_count": 0,
  "avatar": null,
  "avatar_source": null,
  "avatar_value": null,
  "board_view_settings": null,
  "board_view_template": null,
  "board_view_update_views": null,
  "box_view_settings": null,
  "box_view_template": null,
  "box_view_update_views": null,
  "calendar_view_settings": null,
  "calendar_view_template": null,
  "calendar_view_update_views": null,
  "color": null,
  "content": "{\\"ops\\":[]}",
  "creator": Any<Number>,
  "custom_items": false,
  "custom_task_ids_display": null,
  "custom_task_ids_start": null,
  "custom_task_ids_start_100": null,
  "date_created": Any<String>,
  "date_deleted": null,
  "date_updated": Any<String>,
  "default_category": null,
  "default_preset_view": 1,
  "deleted": false,
  "deleted_by": null,
  "due_date": null,
  "due_date_time": false,
  "editor_token": Any<String>,
  "emails_as_replies": null,
  "emails_clickapp": false,
  "features": Object {
    "check_unresolved": Object {
      "checklists": null,
      "comments": null,
      "enabled": true,
      "subtasks": null,
    },
    "custom_fields": Object {
      "enabled": true,
    },
    "custom_items": Object {
      "enabled": false,
    },
    "dependency_warning": Object {
      "enabled": true,
    },
    "due_dates": Object {
      "enabled": true,
      "remap_closed_due_date": false,
      "remap_due_dates": false,
      "start_date": true,
    },
    "milestones": Object {
      "enabled": false,
    },
    "multiple_assignees": Object {
      "enabled": true,
    },
    "points": Object {
      "enabled": false,
    },
    "priorities": Object {
      "enabled": true,
      "priorities": Array [
        Object {
          "color": "#f50000",
          "id": "1",
          "orderindex": "1",
          "priority": "urgent",
        },
        Object {
          "color": "#f8ae00",
          "id": "2",
          "orderindex": "2",
          "priority": "high",
        },
        Object {
          "color": "#6fddff",
          "id": "3",
          "orderindex": "3",
          "priority": "normal",
        },
        Object {
          "color": "#d8d8d8",
          "id": "4",
          "orderindex": "4",
          "priority": "low",
        },
      ],
    },
    "scheduler_enabled": false,
    "sprints": Object {
      "enabled": false,
    },
    "status_pies": Object {
      "enabled": false,
    },
    "tags": Object {
      "enabled": true,
    },
    "time_tracking": Object {
      "default_to_billable": 2,
      "enabled": true,
      "harvest": false,
      "rollup": false,
    },
  },
  "gantt_view_settings": null,
  "gantt_view_template": null,
  "gantt_view_update_views": null,
  "group_assignees": Array [],
  "group_members": Any<Array>,
  "hidden": false,
  "hide_project": false,
  "id": Any<String>,
  "import_id": null,
  "import_uuid": null,
  "importing": null,
  "listViewSettings": Object {
    "sorting": Array [],
    "visible": Object {
      "assignees": true,
      "date_created": false,
      "date_updated": true,
      "due_date": true,
      "priority": true,
      "start_date": false,
      "task_id": false,
      "time_spent": false,
    },
  },
  "list_view_settings": null,
  "list_view_template": null,
  "list_view_update_views": null,
  "map_view_template": null,
  "map_view_update_views": null,
  "members": Any<Array>,
  "mind_map_view_settings": null,
  "mind_map_view_template": null,
  "mind_map_view_update_views": null,
  "multiple_assignees": true,
  "name": StringMatching /Test Project/,
  "orderindex": null,
  "owner": Any<Object>,
  "permanent_template_id": null,
  "permission_level": 5,
  "permissions": Any<Object>,
  "personal_list": false,
  "points": false,
  "points_estimate_rollup": null,
  "preset_views": Array [
    1,
    2,
  ],
  "priority": null,
  "private": false,
  "project_orderindex": null,
  "project_prefix": null,
  "project_type": null,
  "public_sharing": null,
  "slack_channel": null,
  "start_date": null,
  "start_date_time": false,
  "status": null,
  "statuses": Any<Array>,
  "storage_used": "0",
  "subcategoryId": Any<String>,
  "table_view_settings": null,
  "table_view_template": null,
  "table_view_update_views": null,
  "taskcount": "0",
  "team": Object {
    "date_created": Any<String>,
    "id": Any<String>,
    "name": Any<String>,
    "using_bitbucket": null,
    "using_github": false,
    "using_gitlab": null,
  },
  "template": false,
  "template_field_ids": null,
  "time_in_status": null,
  "timeline_view_settings": null,
  "timeline_view_template": null,
  "timeline_view_update_views": null,
  "workload_view_settings": null,
  "workload_view_template": null,
  "workload_view_update_views": null,
  "zoom": null,
}
`;
