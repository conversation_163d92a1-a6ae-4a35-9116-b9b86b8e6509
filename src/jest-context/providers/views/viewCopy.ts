import type { RestClient } from '@clickup/rest-client';
import type { ClientProvider } from '../../ClientProvider';
import { getViewResponseType } from './utils';

interface Params {
    viewId: string;
    locked?: boolean;
}

export default async function viewCopy(this: ClientProvider, args: Params, options?: { client?: RestClient }) {
    const client: RestClient = options?.client ? options.client : (await this.provideUser()).client;

    const route = `/viz/v1/view/${args.viewId}/copy`;

    return client.writeJson(route, { locked: args.locked }, 'POST').json(getViewResponseType);
}
