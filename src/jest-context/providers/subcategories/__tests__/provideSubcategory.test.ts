/** @group functional/jest-context */
import ClickUpContext from '../../../ClickUpContext';

describe('provideSubcategory tests', () => {
    it('should create a subcategory', async () => {
        const context = new ClickUpContext();

        const { client } = await context.provideUser();
        const team = await context.provideTeam(
            {},
            {
                client,
            }
        );
        const subcategory = await context.provideSubcategory({}, { client });

        expect(subcategory).toMatchObject({
            ...subcategory,
            team_id: team.id,
        });
    });

    it('should be able to customize subcategory name', async () => {
        const context = new ClickUpContext();
        const subcategory = await context.provideSubcategory({ name: 'Custom Name' });
        expect(subcategory.name).toEqual('Custom Name');
    });
});
