import { object, optional, type } from 'superstruct';

import type { ClientProvider } from '../../ClientProvider';
import type ClickUpContextBaseOptions from '../../baseOptions';

export default async function commentDelete(
    this: ClientProvider,
    commentId: string,
    options?: ClickUpContextBaseOptions
) {
    const client = options?.client ?? (await this.provideUser()).client;
    return client.writeDelete(`/comments/v2/comment/${commentId}`).json(type({ version: optional(object()) }));
}
