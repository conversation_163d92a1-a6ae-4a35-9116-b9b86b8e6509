import { type, string, array } from 'superstruct';

import type { RestClient } from '@clickup/rest-client';
import type { ClientProvider } from '../../ClientProvider';
import type { HierarchyProvider } from '../../HierarchyProvider';

interface CopyTaskParams {
    sourceTaskId: string;
    name?: string;
    subcategoryId?: string;
    copyUnassignedComments?: boolean;
    relationships?: boolean;
    old_status?: boolean;
    old_checklist_item_status?: boolean;
}

interface Options {
    client?: RestClient;
}

export default async function copyTasks(
    this: ClientProvider & HierarchyProvider,
    tasks_params: CopyTaskParams[],
    options?: Options
) {
    const client = options?.client || (await this.provideUser()).client;
    const { ids: taskIds } = await client
        .writeJson('/templates/v1/taskCopy', {
            tasks: await Promise.all(
                tasks_params.map(async (params, idx) => {
                    const subcategoryId =
                        'subcategoryId' in params ? params.subcategoryId : (await this.provideSubcategory()).id;

                    return {
                        id: params.sourceTaskId,
                        subcategory_id: subcategoryId,
                        name: params.name || `Task ${idx}`,
                        assigned_comment: params.copyUnassignedComments !== true,
                        attachments: true,
                        comment: true,
                        comment_attachments: true,
                        custom_fields: true,
                        custom_type: true,
                        external_dependencies: true,
                        internal_dependencies: true,
                        old_assignees: true,
                        old_checklists: true,
                        old_checklist_item_status: params?.old_checklist_item_status ?? undefined, // Backwards compatible is "undefined"
                        old_due_date: true,
                        old_start_date: true,
                        old_followers: true,
                        old_status: params.old_status ?? true,
                        old_subtask_assignees: true,
                        old_tags: true,
                        recur_settings: true,
                        return_immediately: false,
                        time_estimate: true,
                        subtasks: true,
                        relationships: params.relationships || false,
                    };
                })
            ),
        })
        .json(type({ ids: array(string()) }));

    return taskIds;
}
