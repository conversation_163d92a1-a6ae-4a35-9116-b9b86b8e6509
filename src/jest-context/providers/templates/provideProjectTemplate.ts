import { array, nullable, number, optional, string, type } from 'superstruct';
import type { RestClient } from '@clickup/rest-client';
import type { ClientProvider } from '../../ClientProvider';
import type { HierarchyProvider } from '../../HierarchyProvider';
import type { UserId } from '../../../libs/user/interfaces/Ids';

interface Options {
    client: RestClient;
    id: UserId;
}

interface Params {
    projectId?: string;
    teamId?: string;
    templateId?: string;
    name?: string;
    public?: boolean;
    visibility?: number;
    template_members?: number[];
    template_group_members?: string[];
    old_checklist_item_status?: boolean;
}

export default async function provideProjectTemplate(
    this: ClientProvider & HierarchyProvider,
    params?: Params,
    options?: Options
) {
    const { id: userId, client } = options ?? (await this.provideUser());

    const projectId = params?.projectId ?? (await this.provideProject()).id;
    const teamId = params?.teamId ?? (await this.provideTeam()).id;

    const { template_id } = await client
        .writeJson(
            '/templates/v2/project/:projectId/template',
            // Copy-paste from UI:
            {
                projectId,
                team: teamId,
                name: params?.name ?? 'Test Project Template',
                template_id: params?.templateId ?? null,
                old_due_date: true,
                old_start_date: true,
                old_assignees: true,
                old_subtask_assignees: true,
                old_followers: true,
                old_statuses: true,
                old_status: true,
                old_tags: true,
                attachments: true,
                comment: true,
                comment_attachments: true,
                custom_fields: true,
                custom_type: true,
                subtasks: true,
                content: true,
                old_checklists: true,
                old_checklist_item_status: params?.old_checklist_item_status ?? false,
                recur_settings: true,
                priority: true,
                internal_dependencies: true,
                external_dependencies: true,
                return_immediately: false,
                time_estimate: true,
                include_views: true,
                automation: true,
                include_tasks: true,
                template_members: params?.template_members ?? [userId],
                template_group_members: params?.template_group_members ?? [],
                template_visibility: params?.visibility ?? 4,
                public_sharing: params?.public ?? false,
            }
        )
        .json(type({ template_id: number() }));

    const templates = await client.get('/v1/team/:teamId/templates?name=&paging=true', { teamId }).json(
        type({
            project: type({
                templates: array(
                    type({
                        id: string(),
                        name: string(),
                        description: nullable(string()),
                        original_id: string(),
                        public_key: optional(nullable(string())),
                        counts: type({
                            tags: number(),
                            views: number(),
                            private: type({}),
                            sprints: number(),
                            statuses: number(),
                            click_apps: number(),
                            automations: number(),
                            custom_fields: number(),
                        }),
                        options: type({
                            count_details: type({
                                statuses: array(
                                    type({
                                        id: string(),
                                        type: string(),
                                        color: string(),
                                        status: string(),
                                        status_group: string(),
                                    })
                                ),
                                views: array(
                                    type({
                                        type: number(),
                                    })
                                ),
                            }),
                        }),
                    })
                ),
            }),
        })
    );

    for (const t of templates.project.templates) {
        if (t.original_id === String(template_id)) {
            return t;
        }
    }
    return null;
}
