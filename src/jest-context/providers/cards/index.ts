import type { RestClient } from '@clickup/rest-client';
import {
    DEFAULT_WIDGET_V3_CREATE_BODY,
    DEFAULT_WIDGET_V1_CREATE_BODY,
    WidgetV1Response,
    WidgetV3Response,
} from '@clickup-legacy/jest-context/providers/widgets';
import type { WidgetV3UpsertRequest } from '@clickup-legacy/models/dashboard/widgets/api-contracts/WidgetRequests';
import * as ss from 'superstruct';
import type { WidgetV1UpsertRequest } from '@clickup-legacy/models/dashboard/widgets/api-contracts/Widget';
import type { EditCardAsyncOptions } from '@clickup-legacy/models/dashboard/cards/CardsRequests';
import { Parented } from '@clickup-legacy/models/dashboard/cards/CardsRequests';
import type { ViewProviderParams } from '@clickup-legacy/jest-context/types/ViewProviderParams';
import { buildViewRequestParams } from '@clickup-legacy/jest-context/providers/views/viewProviderParams';
import type { ProvideViewResponse } from '@clickup-legacy/jest-context/providers/views/provideView';
import { getViewResponseType } from '@clickup-legacy/jest-context/providers/views/utils';
import type {
    DrilldownHistoricalGroupResponse,
    DrilldownHistoricalXValueResponse,
} from '@clickup-legacy/models/dashboard/widgets/api-contracts/Drilldown';
import type { ClientProvider } from '../../ClientProvider';
import type { HierarchyProvider } from '../../HierarchyProvider';

export const ParentedCardV1Response = ss.intersection([WidgetV1Response, Parented]);
export const ParentedCardV3Response = ss.intersection([WidgetV3Response, Parented]);

export type ParentedCardV1Response = ss.Infer<typeof ParentedCardV1Response>;
export type ParentedCardV3Response = ss.Infer<typeof ParentedCardV3Response>;

export const ParentedCardResponse = ss.union([ParentedCardV1Response, ParentedCardV3Response]);
export type ParentedCardResponse = ParentedCardV1Response | ParentedCardV3Response;

export const helpers = {
    async cardV3Create(client: RestClient, workspaceId: string, params?: { widget?: Partial<WidgetV3UpsertRequest> }) {
        const body = {
            ...DEFAULT_WIDGET_V3_CREATE_BODY,
            ...(params?.widget || {}),
        };

        return client.writeJson(`/widget/workspaces/${workspaceId}/cards`, body).json(WidgetV3Response);
    },
    async cardV1Create(client: RestClient, workspaceId: string, params?: { widget?: Partial<WidgetV1UpsertRequest> }) {
        const body = {
            ...DEFAULT_WIDGET_V1_CREATE_BODY,
            ...(params?.widget || {}),
        };

        return client.writeJson(`/widget/workspaces/${workspaceId}/cards`, body).json(
            ss.type({
                widget: WidgetV1Response,
            })
        );
    },
    async cardGet<GetCardType>(client: RestClient, workspaceId: number, id: string): Promise<GetCardType> {
        return client.get(`/widget/workspaces/${workspaceId}/cards/${id}`).json(ss.any()) as GetCardType;
    },
    async cardUpdate(client: RestClient, workspaceId: number, id: string, params: EditCardAsyncOptions) {
        const response = await client
            .writeJson(`/widget/workspaces/${workspaceId}/cards/${id}`, params, 'PUT')
            .json(ss.any());

        return response.widget;
    },
    async cardDelete(client: RestClient, workspaceId: number, id: string) {
        return client.writeDelete(`/widget/workspaces/${workspaceId}/cards/${id}`).response();
    },
    async cardDrilldownViewCreate(
        this: ClientProvider & HierarchyProvider,
        client: RestClient,
        workspaceId: string,
        params: { cardId: string; view?: ViewProviderParams }
    ): Promise<ProvideViewResponse> {
        const { cardId, view } = params;

        const response = await client
            .writeJson(
                `/widget/workspaces/${workspaceId}/cards/${cardId}/drilldown-view`,
                await buildViewRequestParams(this, view),
                'POST'
            )
            .json(ss.type({ view: getViewResponseType }));

        return {
            ...response.view,
            date_created: Number(response.view.date_created),
            date_updated: Number(response.view.date_updated),
        };
    },
    async cardDrilldownHistoricalGet(
        client: RestClient,
        workspaceId: string,
        params: { cardId: string; xvalue: string; group?: string }
    ): Promise<DrilldownHistoricalGroupResponse | DrilldownHistoricalXValueResponse> {
        const { cardId, xvalue, group } = params;

        return client
            .get(
                `/widget/workspaces/${workspaceId}/cards/${cardId}/drilldown-historical/${xvalue}${
                    group ? `/${group}` : ''
                }`
            )
            .json(ss.any());
    },
};
