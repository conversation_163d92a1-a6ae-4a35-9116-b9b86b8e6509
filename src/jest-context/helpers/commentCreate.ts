import { object, optional, type } from 'superstruct';
import type { Client<PERSON>rovider } from '../ClientProvider';
import type ClickUpContextBaseOptions from '../baseOptions';

interface Params {
    payload: {
        parent: string;
        comment: Record<string, any>[] | string;
        type: number;
        comment_type?: number;
        comment_type_data?: {
            subtype_id?: number;
            title?: string;
            cover_image?: string;
        };
    };
}

export default async function taskCommentEdit(
    this: ClientProvider,
    params: Params,
    options?: ClickUpContextBaseOptions
) {
    const client = options?.client ?? (await this.provideUser()).client;
    return client.writeJson(`/comments/v2/comment`, params.payload, 'POST').json(type({ version: optional(object()) }));
}
