import { getAgentPermissions, getPermissionsToSaveAgent } from '../getAgentPermissions';
import ParentTypes from '../../../../../lib/types/ParentTypes';
import { getChannelLocation } from '../getChannelLocation';
import { AgentActionInput } from '../types';

jest.mock('../getChannelLocation');

describe('getAgentPermissions', () => {
    const mockGetChannelLocation = getChannelLocation as jest.Mock;

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return view and subcategory parent', async () => {
        mockGetChannelLocation.mockResolvedValue({ type: ParentTypes.SubcategoryType, id: '123' });

        const input = {
            workspaceKnowledge: {
                currentLocation: true,
            },
        } as any;
        const options = { parent_type: ParentTypes.ViewType, parent_id: '456', team_id: 1 } as any;

        const { can_read } = await getAgentPermissions(input, options);

        expect(can_read).toContain('view:456');
        expect(can_read).toContain('subcategory:123');
    });

    it('should return view and category parent', async () => {
        mockGetChannelLocation.mockResolvedValue({ type: ParentTypes.CategoryType, id: '123' });

        const input = {
            workspaceKnowledge: {
                currentLocation: true,
            },
        } as any;
        const options = { parent_type: ParentTypes.ViewType, parent_id: '456', team_id: 1 } as any;

        const { can_read } = await getAgentPermissions(input, options);

        expect(can_read).toContain('view:456');
        expect(can_read).toContain('category:123');
    });

    it('should return view and project parent', async () => {
        mockGetChannelLocation.mockResolvedValue({ type: ParentTypes.ProjectType, id: '123' });

        const input = {
            workspaceKnowledge: {
                currentLocation: true,
            },
        } as any;
        const options = { parent_type: ParentTypes.ViewType, parent_id: '456', team_id: 1 } as any;

        const { can_read } = await getAgentPermissions(input, options);

        expect(can_read).toContain('view:456');
        expect(can_read).toContain('project:123');
    });

    it('should return only the view when locationless', async () => {
        mockGetChannelLocation.mockResolvedValue(null);

        const input = {
            workspaceKnowledge: {
                currentLocation: true,
            },
        } as any;
        const options = { parent_type: ParentTypes.ViewType, parent_id: '456', team_id: 1 } as any;

        const { can_read } = await getAgentPermissions(input, options);

        expect(can_read).toContain('view:456');
        expect(can_read?.length).toBe(1);
    });

    it('should include clickupWorkspaceContextLocationPicker (v0 agent)', async () => {
        const input = {
            clickupWorkspaceContextDropdown: 'custom',
            clickupWorkspaceContextLocationPicker: ['123', '234', '345'],
        } as AgentActionInput;

        const { can_read } = await getAgentPermissions(input, {} as any);

        expect(can_read).toStrictEqual(['subcategory:123', 'subcategory:234', 'subcategory:345']);
    });

    it('should use the doc view if no page ID', async () => {
        const input = {
            workspaceKnowledge: {
                extraAssets: {
                    docs: [
                        { viewId: 'view-123', pageId: 'page-123' },
                        { viewId: 'view-456' },
                        { viewId: 'view-789', pageId: 'page-789' },
                    ],
                },
            },
        } as AgentActionInput;

        const { can_read } = await getAgentPermissions(input, {} as any);

        expect(can_read).toStrictEqual(['doc:page-123', 'view:view-456', 'doc:page-789']);
    });

    it('should tolerate currentLocation on a workspace agent', async () => {
        const input = {
            workspaceKnowledge: {
                currentLocation: true,
                extraAssets: {
                    tasks: ['task-123'],
                },
            },
        } as AgentActionInput;

        const { can_read } = await getAgentPermissions(input, {
            parent_type: ParentTypes.TeamType,
            parent_id: 'workspace-123',
            team_id: 123,
        });

        expect(can_read).toStrictEqual(['task:task-123']);
    });
});

describe('getPermissionsToSaveAgent', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should require "share" for any assets which the agent has "can_read"', async () => {
        const input = {
            workspaceKnowledge: {
                extraAssets: {
                    tasks: ['123'],
                    lists: ['456'],
                },
            },
        } as AgentActionInput;
        const options = { parent_type: 1, parent_id: '123', team_id: 456 };

        const result = await getPermissionsToSaveAgent(input, options);

        expect(result.share?.sort()).toEqual(['subcategory:456', 'task:123']);
        expect(result.can_read).toBeUndefined();
    });
});
