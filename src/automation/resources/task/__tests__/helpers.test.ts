import momentTZ from 'moment-timezone';
import * as automationService from '@clickup-legacy/automation/services/automationService';
import * as getTasksService from '@clickup-legacy/models/task/services/getTasksService';
import * as db from '../../../../utils/db';
import * as getTeamData from '../../../datastores/getTeamData';
import * as groupDatastore from '../../../datastores/groupDatastore';
import * as fieldDatastore from '../../../datastores/fieldDatastore';
import * as fieldMiddleware from '../../../../models/field/fieldMiddleware';
import * as taskDatastore from '../../../datastores/subtaskDatastore';

import {
    _getTeamId,
    handleStatusAction,
    validateName,
    getUserIdsFromMessageAndInput,
    validateSubcategory,
    getFieldMiddlewareQueriesAsync,
    getAllActiveTriggers,
} from '../helpers';
import { SUBCATEGORY_BY_ID } from '../../../datastores/subcategoryDatastore';

jest.mock('@clickup-legacy/models/task/services/getTasksService');
jest.mock('@clickup-legacy/automation/services/automationService');
jest.mock('@clickup-legacy/automation/datastores/subtaskDatastore');

describe('task getTeamId', () => {
    beforeEach(() => {
        // Making sure that the counters for mock calls are reset
        // and there's no interference between what should be return in previous test vs current test.
        jest.clearAllMocks();
    });
    describe('when message has team_id already', () => {
        it('should return the team_id from the message object', async () => {
            const msg = { team_id: 123 };
            const team_id = await _getTeamId(msg);

            expect(team_id).toEqual(msg.team_id);
        });

        it('should not lookup the team_id from the task', async () => {
            const msg = { team_id: 123 };
            jest.spyOn(getTeamData, 'getTeamIdByTaskId').mockResolvedValue(7999);
            const team_id = await _getTeamId(msg);

            expect(getTeamData.getTeamIdByTaskId).toHaveBeenCalledTimes(0);
            expect(team_id).not.toEqual(getTeamData.getTeamIdByTaskId(msg));
        });
        it('should not lookup the team_id via hierarchy', async () => {
            const msg = { team_id: 123 };
            jest.spyOn(getTeamData, 'getTeamIdByHierarchy').mockResolvedValue(1999);
            const team_id = await _getTeamId(msg);

            expect(getTeamData.getTeamIdByHierarchy).toHaveBeenCalledTimes(0);
            expect(team_id).not.toEqual(await getTeamData.getTeamIdByHierarchy(msg));
        });
    });
    describe('when message is missing team_id', () => {
        it('should lookup the team_id from the hierarchy', async () => {
            const msg = { task_id: 'abc' };
            const team_id = await _getTeamId(msg);
            jest.spyOn(getTeamData, 'getTeamIdByHierarchy').mockResolvedValue(1999);

            expect(getTeamData.getTeamIdByHierarchy).toHaveBeenCalledTimes(1);
            expect(team_id).toEqual(await getTeamData.getTeamIdByHierarchy(msg));
        });

        it('should not lookup the team_id from the task', async () => {
            const msg = { task_id: 'abc' };
            jest.spyOn(getTeamData, 'getTeamIdByHierarchy').mockResolvedValue(1999);
            jest.spyOn(getTeamData, 'getTeamIdByTaskId').mockResolvedValue(7999);
            const team_id = await _getTeamId(msg);

            expect(getTeamData.getTeamIdByTaskId).toHaveBeenCalledTimes(0);
            expect(team_id).not.toEqual(await getTeamData.getTeamIdByTaskId(msg));
        });
    });

    describe('when message is missing team_id and lookup from hierarchy fails', () => {
        it('should lookup the team_id from the task', async () => {
            const msg = { task_id: 'abc' };
            jest.spyOn(getTeamData, 'getTeamIdByHierarchy').mockResolvedValue(undefined);
            jest.spyOn(getTeamData, 'getTeamIdByTaskId').mockResolvedValue(7999);
            const team_id = await _getTeamId(msg);

            expect(getTeamData.getTeamIdByTaskId).toHaveBeenCalledTimes(1);
            expect(team_id).toEqual(await getTeamData.getTeamIdByTaskId(msg));
            expect(getTeamData.getTeamIdByHierarchy).toHaveBeenCalledTimes(1);
        });
    });

    describe('when message is missing team_id and all lookups fail', () => {
        it('should throw an exception', async () => {
            const msg = { task_id: 'abc' };
            jest.spyOn(getTeamData, 'getTeamIdByHierarchy').mockResolvedValue(undefined);
            jest.spyOn(getTeamData, 'getTeamIdByTaskId').mockResolvedValue(undefined);

            try {
                await _getTeamId(msg);
            } catch (e) {
                expect(e).toMatchInlineSnapshot(`[AutomationnonretryableClickUpError: Workspace not found]`);
            }
            expect(getTeamData.getTeamIdByHierarchy).toHaveBeenCalledTimes(1);
            expect(getTeamData.getTeamIdByHierarchy).toHaveBeenCalledTimes(1);
        });
    });
});

describe('handleStatusAction', () => {
    beforeEach(() => {
        // Making sure that the counters for mock calls are reset
        // and there's no interference between what should be return in previous test vs current test.
        jest.clearAllMocks();
    });
    it('should fail the action when status is not found', async () => {
        jest.spyOn(db, 'replicaQuery');
        (db.replicaQuery as jest.Mock).mockImplementation((arg1, arg2, cb) => {
            cb(null, {
                rows: [],
            });
        });
        try {
            handleStatusAction('111', 'todo', 1, { status_type: 'task' }, (err: any, result: any) => {});
        } catch (e) {
            expect(e).toMatchInlineSnapshot(`[AutomationClickUpError: Status todo does not exist for the list.]`);
        }
        expect(db.replicaQuery).toHaveBeenCalledTimes(1);
        expect(db.replicaQuery).toHaveBeenCalledWith(expect.anything(), expect.anything(), expect.anything());
    });
});

describe('validateName', () => {
    const date = new Date();
    const max_len = 2048;
    // Thu Feb 01 2024 00:00:00 GMT-0800 (Pacific Standard Time)
    date.setTime(1706774400000);
    const original_name = 'original task name';
    const default_name = 'Task (created from automation) - #2024-02-01T08:00:00+00:00';
    it('should return original task name', async () => {
        const ret = validateName(
            original_name,
            max_len,
            'Task (created from automation) - #'.concat(momentTZ(date.getTime()).format())
        );
        expect(ret).toBe(original_name);
    });
    it('should return default task name', async () => {
        let ret = validateName(
            '',
            max_len,
            'Task (created from automation) - #'.concat(momentTZ(date.getTime()).format())
        );
        expect(ret).toBe(default_name);
        ret = validateName(
            null,
            max_len,
            'Task (created from automation) - #'.concat(momentTZ(date.getTime()).format())
        );
        expect(ret).toBe(default_name);
        ret = validateName(
            undefined,
            max_len,
            'Task (created from automation) - #'.concat(momentTZ(date.getTime()).format())
        );
        expect(ret).toBe(default_name);
    });
    it('should return trimmed task name', async () => {
        const ret = validateName('a'.repeat(10000), max_len, default_name);
        expect(ret).toBe('a'.repeat(2048));
    });
});

describe('getUserIdsFromMessageAndInput', () => {
    it('should return creator, triggered_by, watchers, followers, group followers and regular assignee', async () => {
        const input = {
            assignee: ['creator', 'triggered_by', 'watchers', 'people_cf_1001', 'group_1001', 1000],
            resource_instance: {
                creator: {
                    id: 1001,
                },
                followers: [
                    {
                        id: 1002,
                    },
                    {
                        id: 1003,
                    },
                    {
                        id: 1004,
                    },
                ],
                group_followers: [
                    {
                        members: [
                            {
                                id: 1003,
                            },
                            {
                                id: 1005,
                            },
                        ],
                    },
                ],
            },
        };
        const message = {
            trigger_user_id: 1006,
        };
        jest.spyOn(fieldDatastore, 'getFieldValues').mockResolvedValue({
            rows: [{ value: { value: [1007, 'group_1001'] } }],
        });
        jest.spyOn(groupDatastore, 'getUsersFromGroups').mockResolvedValue({
            rows: [{ userid: 1008 }, { userid: 1009 }],
        });
        const user_ids = await getUserIdsFromMessageAndInput(message, input);
        expect(user_ids.sort()).toStrictEqual([1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009]);
    });
});

describe('validateSubcategory', () => {
    const subcategory_id = 1000;
    it('should throw error', async () => {
        jest.spyOn(db, 'promiseReplicaQuery');
        (db.promiseReplicaQuery as jest.Mock).mockResolvedValueOnce({
            rows: [],
        });
        try {
            await validateSubcategory(subcategory_id);
        } catch (e) {
            expect(e).toMatchInlineSnapshot(`[AutomationClickUpError: List not found]`);
        }
        expect(db.promiseReplicaQuery).toHaveBeenCalledTimes(1);
        expect(db.promiseReplicaQuery).toHaveBeenCalledWith(SUBCATEGORY_BY_ID, [subcategory_id]);
    });
});

describe('getFieldMiddlewareQueries', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return the correct middleware queries', async () => {
        jest.spyOn(fieldMiddleware, 'checkTaskMove').mockImplementation(async (req, _resp, next) => {
            expect(req.query).toEqual(expect.objectContaining({ skip_add: 'true' }));

            req.middleware_queries = [];
            next();
        });

        const middlewareQueries = await getFieldMiddlewareQueriesAsync(1, { subcategory_id: 1 });
        const { middleware_queries } = middlewareQueries;

        expect(middleware_queries).toBeDefined();
    });
});

describe('getAllActiveTriggers', () => {
    const mockTaskId = 'task1';
    const mockHierarchy = {
        task1: { project_id: 'project1', category: 'category1', subcategory: 'subcategory1' },
    };
    const mockTriggers = [{ id: 'trigger1' }, { id: 'trigger2' }];
    const mockTaskMetadata = {
        id: mockTaskId,
        archived: false,
        deleted: false,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return active triggers for valid task IDs', async () => {
        (taskDatastore.getTaskMetadata as jest.Mock).mockResolvedValueOnce(mockTaskMetadata);
        (getTasksService.getTasksHierarchy as jest.Mock).mockResolvedValueOnce(mockHierarchy);
        (automationService.getTriggersByParents as jest.Mock).mockResolvedValueOnce(mockTriggers);
        (automationService.getTriggersByParents as jest.Mock).mockResolvedValueOnce([]);
        (automationService.getTriggersByParents as jest.Mock).mockResolvedValueOnce([]);
        (automationService.getTriggersByParents as jest.Mock).mockResolvedValueOnce([]);

        const result = await getAllActiveTriggers(mockTaskId, 'type');
        expect(result).toEqual(mockTriggers);
        expect(getTasksService.getTasksHierarchy).toHaveBeenCalledWith(expect.anything(), [mockTaskId], false, false);
        expect(automationService.getTriggersByParents).toHaveBeenCalledTimes(4); // For project, category, subcategory, and task
    });

    it('should handle errors from getTasksHierarchy', async () => {
        (taskDatastore.getTaskMetadata as jest.Mock).mockResolvedValueOnce(mockTaskMetadata);
        (getTasksService.getTasksHierarchy as jest.Mock).mockRejectedValueOnce(new Error('Service error'));

        await expect(getAllActiveTriggers(mockTaskId, 'type')).rejects.toThrow('Service error');
        expect(getTasksService.getTasksHierarchy).toHaveBeenCalledWith(expect.anything(), [mockTaskId], false, false);
    });

    it('should handle errors from getTriggersByParents', async () => {
        (taskDatastore.getTaskMetadata as jest.Mock).mockResolvedValueOnce(mockTaskMetadata);
        (getTasksService.getTasksHierarchy as jest.Mock).mockResolvedValueOnce(mockHierarchy);
        (automationService.getTriggersByParents as jest.Mock).mockRejectedValue(new Error('Service error'));

        await expect(getAllActiveTriggers(mockTaskId, 'type')).rejects.toThrow('Service error');
        expect(automationService.getTriggersByParents).toHaveBeenCalled();
    });
});
