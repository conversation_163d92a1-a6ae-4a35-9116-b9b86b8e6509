import { itrigger } from '../../lib/types/Trigger';
import { TriggerOnType } from '../../lib/types/TriggerOnType';

export async function evaluateTriggerOnForTask(trigger: itrigger, is_subtask: boolean) {
    const trigger_on: TriggerOnType = (trigger.input as { trigger_on: TriggerOnType })?.trigger_on || TriggerOnType.ALL;

    switch (trigger_on) {
        case TriggerOnType.TASK:
            return !is_subtask;
        case TriggerOnType.SUBTASK:
            return is_subtask;
        case TriggerOnType.ALL:
            return true;
        default:
            return true;
    }
}
