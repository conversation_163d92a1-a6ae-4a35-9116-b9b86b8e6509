import config from 'config';
import Logger from '@clickup/shared/utils-logging';
import { TFunction } from 'i18next';
import { resolveSpecial } from '@clickup-legacy/automation/resources/task/utils/dynamicUsers';
import { UsersDynamicTypes } from '@clickup-legacy/automation/lib/types/Action';
import { getUserTimezoneAsync } from '@clickup-legacy/automation/services/getUserTimezone';
import moment from 'moment-timezone';
import { validateMessage } from '@clickup-legacy/automation/resources/hubspot/actions/validation/hubspotActionValidators';
import { entitlementService } from '@clickup-legacy/models/entitlements/entitlementService';
import { EntitlementNameCounter } from '@clickup-legacy/libs/entitlements/types';
import { getFieldValues } from '@clickup-legacy/automation/datastores/fieldDatastore';
import { isUUID } from '@clickup-legacy/models/helpers';
import { getCustomFieldsByIds } from '@clickup-legacy/models/field/datastores/customFieldsDatastore';
import { Knex } from 'knex';
import { improvedAiCfAutomationActionHandling } from '@clickup-legacy/models/integrations/split/squadTreatments/fieldTreatments';
import { updateAiCustomField } from '@clickup-legacy/automation/resources/task/utils/aiApiFacade';
import FieldTypes from '../../../lib/types/FieldTypes';
import Action from '../../../lib/Action';
import { EventMessage } from '../../../lib/types/EventMessage';
import { Task } from '../../../lib/types/Task';
import { AutoErrorCodes } from '../../../lib/errors/ErrorCodes';
import { AutomationError } from '../../../AutomationError';
import { fieldClientInstance } from '../../../../clients/FieldClient';
import { getDateFromDelta } from '../utils/dateUtils';
import { promiseAccessField } from '../../../../utils/access2';
import * as automationService from '../../../services/automationService';
import { shouldSkipAiAction } from '../utils/aiFieldHistoryUtils';
import { ClickUpError } from '../../../../utils/errors';

interface CustomFieldActionInput {
    field_id: string;
    field_type: FieldTypes;
    days_from_now?: number;
    use_now?: boolean;
    resource_instance?: Task;
    value: any;
    value_options: any;
    add_people_cf_ids?: string;
    rem_people_cf_ids?: string;
    isAiCustomField?: boolean;
}

const logger = Logger.getLogger('customFieldAction');

export class CustomFieldAction extends Action<CustomFieldActionInput, void> {
    readonly type = 'custom field';

    readonly i18nKey = 'automation_task-resource--set-cf';

    readonly verb = 'set';

    readonly noun = 'custom field';

    readonly search_keywords = ['custom', 'field', 'property', 'set', 'assign'];

    buildSchema(textResolvers: Record<string, TFunction>, lang: string): any {
        return {
            type: 'object',
            required: ['field_id', 'value'],
            properties: {
                field_id: {
                    type: 'string',
                    title: textResolvers[lang]('common_field'),
                },

                value: {
                    type: ['number', 'string', 'boolean', 'object', 'array'],
                    title: textResolvers[lang]('common_value'),
                },

                days_from_now: {
                    type: 'number',
                    title: textResolvers[lang]('automation_task-resource--days-from-now'),
                    description: textResolvers[lang]('automation_task-resource--days-from-now-desc'),
                    field_types: [FieldTypes.Date],
                },

                add_people_cf_ids: {
                    type: 'array',
                    title: textResolvers[lang]('automation_add-people-custom-fields'),
                    items: {
                        type: 'string',
                    },
                    raw: true,
                    pretty: false,
                },
                rem_people_cf_ids: {
                    type: 'array',
                    title: textResolvers[lang]('automation_remove-people-custom-fields'),
                    items: {
                        type: 'string',
                    },
                    raw: true,
                    pretty: false,
                },

                value_options: {
                    type: 'object',
                    properties: {
                        time: {
                            type: 'boolean',
                            title: textResolvers[lang]('automation_task-resource--dt'),
                        },
                    },
                },
            },
        };
    }

    async validate(userId: number, input: CustomFieldActionInput): Promise<any> {
        if (!input.field_id) {
            throw new AutomationError(
                'field_id is a required input',
                AutoErrorCodes.ChangeCustomFieldValidationFailed,
                400
            );
        }

        const result = await promiseAccessField(userId, input.field_id, { permissions: [] });
        const field_type = Number(result.field_type);

        if (field_type === FieldTypes.Date && input.days_from_now != null) {
            return { assign: { field_type } };
        }

        if (input.value === null) {
            throw new AutomationError(
                'value is a required input',
                AutoErrorCodes.ChangeCustomFieldValidationFailed,
                400
            );
        }

        return { assign: { field_type } };
    }

    shouldRemovePeopleField(value: any): boolean {
        return (Array.isArray(value) && value.includes('unassigned')) || value?.unassign;
    }

    async getFieldsIdsByTeamAsync(teamId: number, fieldType: number, filterByIds: string[]): Promise<string[]> {
        const filters = [
            (queryBuilder: Knex.QueryBuilder) => {
                queryBuilder.where('fields.type', fieldType);
                queryBuilder.where('fields.team_id', teamId);
                queryBuilder.where('fields.deleted', false);
            },
        ];

        const result = await getCustomFieldsByIds({
            ids: filterByIds,
            selectors: ['id'],
            filters,
        });

        return result.map(row => row.id);
    }

    async processDynamicFieldValues(
        values: string[],
        taskId: string,
        workspaceId: string,
        availableFields: string[]
    ): Promise<string[]> {
        const relevantFieldIds = values.filter(value => availableFields.includes(value));
        const fieldValues = await getFieldValues(taskId, relevantFieldIds, workspaceId);
        const extractedValues = fieldValues.rows.flatMap((row: any) => row.value.value);
        return [...values, ...extractedValues].filter(value => !availableFields.includes(value));
    }

    async processCustomFields(
        input: { value: Record<string, string[]> },
        message: EventMessage,
        availableCustomFields: string[]
    ): Promise<void> {
        const operationTypes: ('add' | 'rem')[] = ['add', 'rem'];

        for (const operationType of operationTypes) {
            const values = input.value[operationType];
            if (values?.some((value: string) => availableCustomFields.includes(value))) {
                input.value[operationType] = await this.processDynamicFieldValues(
                    values,
                    message.task_id,
                    String(message.workspace_id),
                    availableCustomFields
                );
            }
        }
    }

    async perform(
        message: EventMessage,
        autoId: string,
        triggerId: string,
        input: CustomFieldActionInput,
        actionId: string
    ): Promise<any | { skip_increment: boolean; action_skipped?: boolean }> {
        validateMessage(message);

        if (input.value === null) {
            return { skip_increment: true };
        }

        if (!message.task_id) {
            logger.warn({
                msg: 'Skipping as no task_id found',
                message,
                autoId,
                triggerId,
            });

            return { skip_increment: true };
        }

        if (input.field_type === FieldTypes.Date && input.value !== undefined) {
            const timezone = await getUserTimezoneAsync(message.trigger_user_id);
            // Days from now
            if (Number.isInteger(input.value.days_from_now)) {
                input.days_from_now = input.value.days_from_now;
            }
            // Trigger date
            if (input.value.date) {
                input.value = input.value.date;
            }
            const timestamp = parseInt(input.value, 10);
            if (!Number.isNaN(timestamp)) {
                const date = moment(timestamp).tz(timezone);
                const hour = date.hour();
                // since we do not have the flag date_time from input
                // so we guess, if it is 4 am, then we probably should not show the time
                if (hour !== 4 && !input.value_options) {
                    input.value_options = { time: true };
                }
            }
            if (input.use_now) {
                input.value = Date.now();
            } else if (input.days_from_now != null) {
                const date = getDateFromDelta(timezone, input.days_from_now);
                if (date) {
                    input.value = date.date;
                }
            }
            // Trigger remove date
            if (input.value.remove) {
                input.value = null;
            }
        }
        // Determine the field type for the given field ID
        let fieldType;
        let field;

        try {
            field = await fieldClientInstance.getField(input.field_id);
            fieldType = field.type_id;
        } catch (err) {
            throw new AutomationError('Custom field not found', AutoErrorCodes.ChangeCustomFieldActionFailed, 400);
        }

        if (field.type_config?.ai) {
            if (!input.isAiCustomField) {
                await automationService.backfillIsAiCustomField(triggerId, actionId);
            }
            const isEntitled = await entitlementService.checkEntitlement(
                Number(message.workspace_id),
                EntitlementNameCounter.CustomFieldAutomatedAiUsages
            );

            if (!isEntitled) {
                logger.warn({
                    msg: 'Skipping AI Field update. Workspace is not entitled.',
                    message,
                    autoId,
                    triggerId,
                });
                return { skip_increment: true, action_skipped: true };
            }

            if (field.deleted) {
                logger.warn({
                    msg: 'Skipping AI Field update. Field is soft-deleted.',
                    message,
                    autoId,
                    triggerId,
                });
                return { skip_increment: true, action_skipped: true };
            }

            if (
                await shouldSkipAiAction(message.task_id, `cf_${input.field_id}`, skipConfig => skipConfig.customFields)
            ) {
                return { skip_increment: true, action_skipped: true };
            }

            const actionConfig = improvedAiCfAutomationActionHandling(String(message.workspace_id));
            const userId =
                (actionConfig?.useFieldCreatorAsActingUser ? Number(field.userid) : message.userid) ??
                config.clickbot_assignee;

            try {
                await updateAiCustomField({
                    workspaceId: Number(message.workspace_id),
                    taskId: message.task_id,
                    fieldId: input.field_id,
                    userId,
                    triggerId,
                    autoId,
                });
            } catch (err: unknown) {
                logger.error({
                    msg: 'Failed to update AI custom field',
                    error: err,
                    message,
                    autoId,
                    triggerId,
                });
                if (
                    err instanceof ClickUpError &&
                    err?.ECODE &&
                    actionConfig?.errorCodesToSwallow?.includes(err.ECODE)
                ) {
                    return { skip_increment: true };
                }

                throw new AutomationError(
                    'Failed to update AI custom field',
                    AutoErrorCodes.AiCustomFieldUpdateFailed,
                    400
                );
            }

            return { skip_increment: true };
        }

        // Tasks or List
        if ([FieldTypes.Tasks, FieldTypes.ListRelationship].includes(fieldType)) {
            input.value = Array.isArray(input.value) ? { add: input.value.map(row => row.id || row) } : input.value;
        }

        // Users
        if (fieldType === FieldTypes.Users) {
            // Value can be empty if only interpolated input was added
            if (!input.value) {
                input.value = {
                    add: [],
                    rem: [],
                };
            }

            if (input.value.add?.length || input.value.rem?.length) {
                const potentialCfIds = [...(input.value.add || []), ...(input.value.rem || [])].filter(id =>
                    isUUID(id)
                );

                const availableCustomFields: string[] = await this.getFieldsIdsByTeamAsync(
                    Number(message.workspace_id),
                    fieldType,
                    potentialCfIds
                );
                await this.processCustomFields(input, message, availableCustomFields);
            }
            // The "unassigned" value is a sentinel value that is passed when
            // the People Custom Field value is set to "None". When this value
            // is set, we want to unassign users from the Custom Field.
            if (this.shouldRemovePeopleField(input.value)) {
                await fieldClientInstance.removeFieldValue(message.task_id, input.field_id, {
                    autoId,
                    triggerId,
                    valueOptions: input.value_options,
                });

                if (input.value.unassign) {
                    return null;
                }

                // Remove the sentinel "unassigned" value from the values list
                // and continue to assign any other additional users
                input.value = input.value.filter((value: string) => value !== 'unassigned');
            }

            input.value = Array.isArray(input.value) ? { add: input.value.map(row => row.id || row) } : input.value;

            const special_add = input.value.add?.filter((addValue: UsersDynamicTypes) =>
                Object.values(UsersDynamicTypes).includes(addValue)
            );
            const special_rem = input.value.rem?.filter((remValue: UsersDynamicTypes) =>
                Object.values(UsersDynamicTypes).includes(remValue)
            );
            if (special_add) {
                const add_special_processed = resolveSpecial(special_add, message, input.resource_instance);
                input.value.add = input.value.add
                    .concat(add_special_processed.users, add_special_processed.groups)
                    .filter((value: UsersDynamicTypes) => !Object.values(UsersDynamicTypes).includes(value));
            }

            if (special_rem) {
                const rem_special_processed = resolveSpecial(special_rem, message, input.resource_instance);
                input.value.rem = input.value.rem
                    .concat(rem_special_processed.users, rem_special_processed.groups)
                    .filter((value: UsersDynamicTypes) => !Object.values(UsersDynamicTypes).includes(value));
            }

            if (input.add_people_cf_ids) {
                const add_people_cf_ids = input.add_people_cf_ids.split(', ').map(id => {
                    if (!Number.isNaN(Number(id))) {
                        return Number.parseInt(id);
                    }
                    return id;
                });
                input.value.add = [...input.value.add, ...add_people_cf_ids];
            }

            if (input.rem_people_cf_ids) {
                const rem_people_cf_ids = input.rem_people_cf_ids.split(', ').map(id => {
                    if (!Number.isNaN(Number(id))) {
                        return Number.parseInt(id);
                    }
                    return id;
                });
                input.value.rem = [...input.value.rem, ...rem_people_cf_ids];
            }
        }

        // Attachment
        if (fieldType === FieldTypes.Attachment) {
            input.value = Array.isArray(input.value) ? { add: input.value.map(row => row.id || row) } : input.value;
        }

        // Checkbox
        if (fieldType === FieldTypes.Checkbox && input.value === false) {
            await fieldClientInstance.removeFieldValue(message.task_id, input.field_id, {
                autoId,
                triggerId,
                valueOptions: input.value_options,
            });
            return null;
        }

        // Remove
        if (input.value === undefined) {
            await fieldClientInstance.removeFieldValue(message.task_id, input.field_id, {
                autoId,
                triggerId,
                valueOptions: input.value_options,
            });
            return null;
        }

        // Updates the given field
        await fieldClientInstance.addFieldValue(message.task_id, input.field_id, input.value, {
            autoId,
            triggerId,
            valueOptions: input.value_options,
        });
        return null;
    }
}

export default new CustomFieldAction();
