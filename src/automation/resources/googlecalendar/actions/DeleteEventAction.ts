import DeleteEventIO from '@time-loop/slapdash-io/dist/automations/actions/DeleteEventIO';
import { TFunction } from 'i18next';
import { JsonSchema } from '../../../lib/types/JsonSchema';
import { ActionValidationOptions } from '../../../lib/Action';
import validateCalendar from '../helpers/validateCalendar';
import AbstractSdAutomationAction from '../helpers/AbstractSdAutomationAction';

interface DeleteEventInput {
    cred: string;
    calendar: string;
    keywords: string;
    skipNotFound?: boolean | null;
    skipNotifications?: boolean | null;
}

export default class DeleteEventAction extends AbstractSdAutomationAction<DeleteEventInput, typeof DeleteEventIO> {
    readonly type = 'delete_event';

    readonly i18nKey = 'automation_googlecalendar--delete-event';

    readonly verb = 'delete';

    readonly noun = 'event';

    buildSchema(textResolvers: Record<string, TFunction>, lang: string): JsonSchema {
        return {
            type: 'object',
            required: ['cred', 'calendar', 'keywords'],
            properties: {
                cred: {
                    title: textResolvers[lang]('automation_googlecalendar--account'),
                    type: 'string',
                    format: 'cred',
                    app: 'googlecalendar',
                },
                calendar: {
                    title: textResolvers[lang]('automation_googlecalendar--calendar'),
                    type: 'string',
                    dynamic: 'calendar',
                    depends_on: ['cred'],
                },
                keywords: {
                    title: textResolvers[lang]('automation_googlecalendar--keywords'),
                    type: 'string',
                    description: textResolvers[lang]('automation_googlecalendar--keywords-description'),
                },
                skipNotFound: {
                    title: textResolvers[lang]('automation_googlecalendar--skip-if-event-not-found'),
                    type: 'boolean',
                },
                skipNotifications: {
                    title: textResolvers[lang]('automation_googlecalendar--do-not-send-notifications'),
                    type: 'boolean',
                },
            },
        };
    }

    async validate(userId: number, input: DeleteEventInput, options: ActionValidationOptions): Promise<void> {
        await validateCalendar(userId, options.parent_id, options.parent_type, input.cred, input.calendar);
    }

    async getActionCredID(input: DeleteEventInput): Promise<string> {
        return input.cred;
    }

    async getActionName(): Promise<string> {
        return 'DeleteEvent';
    }

    async getActionInput({ input }: { input: DeleteEventInput }): Promise<typeof DeleteEventIO['Input']> {
        return {
            calendarId: input.calendar,
            keywords: input.keywords,
            skipNotFound: !!input.skipNotFound,
            sendNotifications: !input.skipNotifications,
        };
    }
}
