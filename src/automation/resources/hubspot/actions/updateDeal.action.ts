import { TFunction } from 'i18next';
import * as automationService from '@clickup-legacy/automation/services/automationService';
import Action from '../../../lib/Action';
import { JsonSchema } from '../../../lib/types/JsonSchema';
import { AutoErrorCodes } from '../../../lib/errors/ErrorCodes';
import { AutomationError } from '../../../AutomationError';
import { EventMessage } from '../../../lib/types/EventMessage';
import { updateHubSpotDeal } from '../datastores/hubspot.datastore';
import { createUpdateDealSchema } from './updateDeal.inputSchema';
import { formatCurrencyValue, parseDateFromInput } from '../hubspot.utils';
import { getExternalResourcesForTask } from '../../../../models/integrations/lib/external_resources/externalResources.service';
import { HubSpotResourceType } from '../../../lib/types/HubSpotTypes';
import { validateMessage } from './validation/hubspotActionValidators';
import { ClickUpError } from '../../../../utils/errors';

const logger = ClickUpError.getBasicLogger('automation');

interface UpdateDealPropertyInput {
    dealname: string;
    dealstage: string;
    pipeline: string;
    amount?: string;
    closedate?: {
        date: string;
    };
    run_once?: boolean;
}

export class UpdateDealAction extends Action<UpdateDealPropertyInput, void> {
    readonly type = 'update_deal';

    readonly i18nKey = 'automation_hubspot-resource--update-deal';

    readonly verb = 'update';

    readonly noun = 'deal';

    buildSchema(textResolvers: Record<string, TFunction>, lang: string): JsonSchema {
        return createUpdateDealSchema(textResolvers, lang);
    }

    /**
     * Validates the provided user input when saving/creating the Action
     */
    validate(userid: number, input: UpdateDealPropertyInput): void {
        // A user needs to provide at least one field to update
        if (!Object.values(input).map(Boolean).includes(true)) {
            throw new AutomationError(
                'At least one field must set to update a Deal',
                AutoErrorCodes.HubSpotUpdateDealActionValidationFailed,
                400
            );
        }
    }

    async perform(
        message: EventMessage,
        autoId: string,
        triggerId: string,
        input: UpdateDealPropertyInput
    ): Promise<any> {
        if (input.run_once) {
            const available = await automationService.checkIfActionAlreadyExecuted(triggerId, this.type);
            if (!available) {
                return { skip_increment: true, skip_audit_log: true };
            }
        }

        validateMessage(message);

        const external_resources = await getExternalResourcesForTask(
            message.team_id,
            message.task_id,
            HubSpotResourceType.Deal
        );

        // If the Task is not associated with a HubSpot resource, there is nothing left to do
        if (external_resources.length === 0) {
            return null;
        }

        // The Task can only be associated with a single HubSpot resource. If there are multiple,
        // we should just use the first
        const deal_id = external_resources[0].resource_id;

        const parsed_close_date = await parseDateFromInput(message.trigger_id, input.closedate);

        try {
            await updateHubSpotDeal({
                workspace_id: message.team_id,
                deal_id,
                properties: {
                    dealname: input?.dealname,
                    pipeline: input?.pipeline,
                    dealstage: input?.dealstage,
                    amount: formatCurrencyValue(input?.amount?.toString()),
                    closedate: parsed_close_date,
                },
            });
        } catch (err) {
            logger.error({
                msg: 'Failed to update HubSpot Deal',
                err,
            });
            throw new AutomationError(err, AutoErrorCodes.HubSpotUpdateDealActionRequestFailed);
        }
        return null;
    }
}

export default new UpdateDealAction();
