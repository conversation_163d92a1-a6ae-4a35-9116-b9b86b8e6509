import { DbQueryResult } from '@clickup-legacy/utils/interfaces/DbQueryResult';
import * as db from '../../utils/db';

interface field_value {
    item_id: string;
    field_id: string;
    value: unknown;
}

export const FIELD_VALUE_BY_ID = `
SELECT 
       item_id,
       field_id,
       value
FROM task_mgmt.field_values
WHERE item_id = $1 AND field_id = ANY($2) AND workspace_id = $3 AND deleted IS NOT TRUE
`;

export async function getFieldValues(
    task_id: string,
    field_ids: string[],
    workspace_id: number | string
): Promise<DbQueryResult<field_value>> {
    const params = [task_id, field_ids, Number(workspace_id)];
    return db.promiseReplicaQuery(FIELD_VALUE_BY_ID, params);
}
