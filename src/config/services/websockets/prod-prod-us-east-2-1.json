{"db": {"connections": {"teamActivityReader": {"dbname": "teamactivity", "secret": "aws/json/arn:aws:secretsmanager:us-east-2::secret:ProdUsEast21TeamactivityReader"}, "teamActivityWriter": {"dbname": "teamactivity", "secret": "aws/json/arn:aws:secretsmanager:us-east-2::secret:ProdUsEast21TeamactivityWriter"}, "websocketDistributorServiceReader": {"dbname": "websockets", "secret": "aws/json/arn:aws:secretsmanager:us-east-2::secret:ProdUsEast21WebsocketsReader"}, "websocketsReader": {"dbname": "websockets", "secret": "aws/json/arn:aws:secretsmanager:us-east-2::secret:ProdUsEast21WebsocketsReader"}, "websocketsWriter": {"dbname": "websockets", "secret": "aws/json/arn:aws:secretsmanager:us-east-2::secret:ProdUsEast21WebsocketsWriter"}}}, "websocket_distributor": {"enabled": true, "redis": {"cluster": true, "host": "clustercfg.prod-us-east-2-1-ws.5zhr7g.use2.cache.amazonaws.com", "port": 6379, "useTls": true}}}