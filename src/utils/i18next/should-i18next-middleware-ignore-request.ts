import http from 'http';
import { IgnoreRoutesFunction } from 'i18next-http-middleware';
import { routesUsingI18nextMiddleware } from '../../routers/routes-using-i18next-middleware';

export const shouldI18nextMiddlewareIgnoreRequest: IgnoreRoutesFunction = req => {
    const lng = req.query?.lng;

    if (lng) {
        try {
            http.validateHeaderValue('Content-Language', lng.toString());
        } catch (err) {
            return true;
        }
    }

    if (!req.headers.accept?.includes('json')) {
        return false;
    }

    return routesUsingI18nextMiddleware.every(route => route.method !== req.method || !req.path.match(route.path));
};
