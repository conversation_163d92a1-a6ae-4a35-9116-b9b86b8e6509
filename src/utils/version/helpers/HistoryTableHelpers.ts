import type { ObjectVersionChangeData, ObjectVersionUpdateRequest } from '@time-loop/ovm-object-version';

export interface HistoryRow {
    after?: string | number | boolean;
    cdc_after?: string | number | boolean;
    cdc_before?: string | number | boolean;
    cdc_field: string;
    cdc_history_id?: string;
    cdc_object_id: string;
    cdc_object_type: string;
}

export function updateObjectVersionUpdatesFromHistoryRows(
    versionUpdates: ObjectVersionUpdateRequest[],
    candidateRows: unknown[]
): void {
    if (!candidateRows?.length || !versionUpdates?.length) {
        return;
    }

    const objectKey = (type: string, id: string) => `${type}:${id}`;
    const historyRowsMap = new Map<string, HistoryRow[]>();

    for (const row of candidateRows) {
        if (
            row &&
            Object.keys(row).includes('cdc_field') &&
            Object.keys(row).includes('cdc_object_id') &&
            Object.keys(row).includes('cdc_object_type')
        ) {
            const historyRow = row as HistoryRow;
            const key = objectKey(historyRow.cdc_object_type, historyRow.cdc_object_id);
            if (!historyRowsMap.has(key)) {
                historyRowsMap.set(key, []);
            }
            historyRowsMap.get(key)?.push(historyRow);
        }
    }

    if (historyRowsMap.size === 0) {
        return;
    }

    const fieldKey = (type: string, id: string, field: string) => `${type}:${id}:${field}`;

    for (const versionUpdate of versionUpdates) {
        const historyRows = historyRowsMap.get(objectKey(versionUpdate.object_type, versionUpdate.object_id));
        if (historyRows) {
            // there are some CDC changes applicable to this version update request
            const changesMap = new Map<string, ObjectVersionChangeData>();
            if (versionUpdate.data?.changes) {
                for (const change of versionUpdate.data.changes) {
                    changesMap.set(fieldKey(versionUpdate.object_type, versionUpdate.object_id, change.field), change);
                }
            }
            // new history rows will override the existing changes
            for (const historyRow of historyRows) {
                const {
                    cdc_history_id: history_id,
                    cdc_field: field,
                    cdc_before: before,
                    cdc_after: after,
                    after: old_after,
                } = historyRow;
                changesMap.set(fieldKey(versionUpdate.object_type, versionUpdate.object_id, historyRow.cdc_field), {
                    history_id,
                    field,
                    before,
                    after: after ?? old_after,
                });
            }

            if (!versionUpdate.data) {
                versionUpdate.data = {};
            }
            versionUpdate.data.changes = Array.from(changesMap.values());
        }
    }
}
