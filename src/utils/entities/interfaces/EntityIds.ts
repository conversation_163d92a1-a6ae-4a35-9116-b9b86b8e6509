import { ObjectType } from '@time-loop/ovm-object-version';

export interface EntityIds {
    team_ids?: number[];
    project_ids?: number[];
    category_ids?: number[];
    subcategory_ids?: number[];
    task_ids?: string[];
    field_ids?: string[];
    dashboard_ids?: string[];
    widget_ids?: string[];
    view_ids?: string[];
    page_ids?: string[];
    doc_ids?: string[];
    checklist_ids?: string[];
    checklist_item_ids?: string[];
    checklist_template_ids?: string[];
    checklist_v1_template_ids?: string[];
    template_id?: string;
    repo_id?: string | number;
    comment_id?: number;
    attachment_ids?: string[];
    trigger_ids?: string[];
    goal_ids?: string[];
    goal_folder_ids?: string[];
    group_ids?: string[];
    object_type?: ObjectType;
    portfolio_ids?: string[];
}
