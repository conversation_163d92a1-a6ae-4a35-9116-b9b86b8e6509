import config from 'config';
import { QueryConfig } from 'pg';
import { partition } from 'lodash';

import { isConversationView } from '@clickup/views/utils';
import type { DbQuery, DbQueryResult, QueryParams, SimpleClient } from '@clickup/utils/db-types';
import { DbPool } from '@clickup/data-platform/pg-pools';
import {
    CHAT_SHOULD_USE_COMMENTS_DB,
    shouldAnalyzeWorkspaceInclusionInChatQueries,
    shouldUseCommentsDbForChat,
    useCommentsDbForRoomRelationshipsConfig,
} from '../../models/integrations/split/squadTreatments/chatTreatments';
import { commentsReadQueryAsync, commentsWriteQueryAsync, replicaQueryAsync } from '../db';
import { writeAsyncFunction } from '../db2';
import { getLogger } from '../logging';
import { ClickUpError } from '../errors';
import { attachWorkspaceInclusionInCommentsQueryAnalyzerIfNeeded } from './workspaceInclusionInCommentsQuery';
import { connectedSplitClient } from '../../models/integrations/split/splitTreatment';
import { getWorkspaceJoinDate } from '../get-workspace-join-date';
import { redis as redisClient } from '../redis';

const logger = getLogger('CommentsDBUtils');

const CommentsDBUtilsError = ClickUpError.makeNamedError('CommentsDBUtilsError');

export enum PoolOptions {
    READ = 'READ',
    RW = 'RW',
}

export enum CommentsWriteMode {
    COMMENTS_DB = 'COMMENTS_DB',
    MAIN_DB = 'MAIN_DB',
    DOUBLE_WRITE = 'DOUBLE_WRITE',
}

/**
 * Reads from the correct comments db based on the comment id provided in the context param.
 * It will use the comments Aurora DB if the comment id starts with 8, which indicates comments written to that DB.
 * Otherwise, it will query the main DB (BDR/PRR) using the provided mainDbQueryFunc (should be async, not callback based).
 */
export async function readFromCorrectCommentsDbAsync<T>(params: {
    context: { commentId: string | number | bigint };
    mainDbQueryFunc: (query: DbQuery, params: QueryParams) => Promise<DbQueryResult<T>>;
    query: DbQuery;
    params?: QueryParams;
}): Promise<DbQueryResult<T>> {
    const queryFunc = isCommentIdFromCommentsDb(params.context.commentId)
        ? commentsReadQueryAsync
        : params.mainDbQueryFunc;

    const result = await queryFunc(params.query, params.params);

    return result;
}

/**
 * Callback based version of {@link readFromCorrectCommentsDbAsync}.
 */
export async function readFromCorrectCommentsDb<T>(
    params: {
        context: { commentId: string | number | bigint };
        mainDbQueryFunc: (query: DbQuery, params: QueryParams) => Promise<DbQueryResult<T>>;
        query: DbQuery;
        params?: QueryParams;
    },
    callback: (err: any, result?: DbQueryResult<T>) => void
): Promise<void> {
    let result: DbQueryResult<T>;

    try {
        result = await readFromCorrectCommentsDbAsync(params);
    } catch (err) {
        callback(err);
        return;
    }

    callback(null, result);
}

type NestedCommentsQuery = { forCommentsDb: DbQuery; forMainDb: DbQuery };

/**
 * Writes to the correct comments db based on the comment id provided in the context param.
 * It will get the write mode for the comment id using {@link getWriteModeForCommentId}.
 * It will use the comments Aurora DB if the comment id starts with 8, which indicates comments written to that DB.
 * It will write both to the comments Aurora DB and the main DB (BDR/PRR) if the write mode is doubleWrite (a chat comment has been migrated and lives in both DBs).
 * Otherwise, it will query the main DB (BDR/PRR) using the provided mainDbQueryFunc (should be async, not callback based).
 * Because of slightly different DB schemas, it is possible to provide two different queries for the comments DB and the main DB.
 */
export async function writeToCorrectCommentsDbAsync<T>(params: {
    context: { commentId: string | number | bigint; writeMode?: CommentsWriteMode };
    mainDbQueryFunc: (query: DbQuery, params: QueryParams) => Promise<DbQueryResult<T>>;
    query: DbQuery | NestedCommentsQuery;
    params?: QueryParams;
}): Promise<DbQueryResult<T>> {
    const writeMode = params.context.writeMode ?? (await getWriteModeForCommentId(params.context.commentId));

    const queryForCommentsDb = (params.query as NestedCommentsQuery)?.forCommentsDb ?? (params.query as DbQuery);
    const queryForMainDb = (params.query as NestedCommentsQuery)?.forMainDb ?? (params.query as DbQuery);

    let promises;
    switch (writeMode) {
        case CommentsWriteMode.COMMENTS_DB:
            promises = [commentsWriteQueryAsync(queryForCommentsDb, params.params)];
            break;
        case CommentsWriteMode.MAIN_DB:
            promises = [params.mainDbQueryFunc(queryForMainDb, params.params)];
            break;
        case CommentsWriteMode.DOUBLE_WRITE:
            promises = [
                commentsWriteQueryAsync(queryForCommentsDb, params.params),
                params.mainDbQueryFunc(queryForMainDb, params.params),
            ];
            break;
        default:
            throw new CommentsDBUtilsError('Invalid write mode', 'COMMENTS_DB_UTILS_001');
    }

    const results = await Promise.allSettled(promises);
    const errors = results.map(r => (r.status === 'rejected' ? r.reason : null)).filter(Boolean);

    if (errors.length) {
        if (errors.length > 1) {
            logger.error({
                msg: 'CommentsDBUtils: Multiple errors while writing to comments db',
                ECODE: 'COMMENTS_DB_UTILS_002',
                errors,
            });
        }

        throw errors[0];
    }

    return (results[0] as PromiseFulfilledResult<DbQueryResult<T>>).value;
}

/**
 * Provides the write mode for the comment id.
 * It will use the comments Aurora DB if the comment id starts with 8, which indicates comments written to that DB.
 * It will return the double write mode if the comment is a chat comment, has been migrated to the comments DB and now lives in both.
 * Otherwise, it will return the main DB (BDR/PRR).
 */
async function getWriteModeForCommentId(commentId: string | number | bigint): Promise<CommentsWriteMode> {
    if (isCommentIdFromCommentsDb(commentId)) {
        return CommentsWriteMode.COMMENTS_DB;
    }

    const query = `
        SELECT
            comments.workspace_id
        FROM
            task_mgmt.comments
            INNER JOIN task_mgmt.views
                ON comments.root_parent_id = views.view_id
        WHERE
            comments.id = $1
            AND comments.root_parent_type = $2
            AND views.type = ANY($3)`;

    const viewTypes = config.get<Record<string, number>>('views.view_types');
    const conversationViewTypes = [viewTypes.conversation, viewTypes.conversation_dm, viewTypes.conversation_group_dm];

    const { rows } = await replicaQueryAsync(query, [
        commentId,
        config.get<number>('comments.types.view'),
        conversationViewTypes,
    ]);

    if (rows.length && (await isNewCommentsDbEnabledForWorkspace(rows[0].workspace_id))) {
        return CommentsWriteMode.DOUBLE_WRITE;
    }

    return CommentsWriteMode.MAIN_DB;
}

/**
 * Callback based version of {@link writeToCorrectCommentsDbAsync}.
 */
export async function writeToCorrectCommentsDb<T>(
    params: {
        context: { commentId: string | number | bigint };
        mainDbQueryFunc: (query: DbQuery, params: QueryParams) => Promise<DbQueryResult<T>>;
        query: DbQuery | NestedCommentsQuery;
        params?: QueryParams;
    },
    callback: (err: any, result?: DbQueryResult<T>) => void
): Promise<void> {
    let result: DbQueryResult<T>;

    try {
        result = await writeToCorrectCommentsDbAsync(params);
    } catch (err) {
        callback(err);
        return;
    }

    callback(null, result);
}

/**
 * Checks if the comment id is from the comments Aurora DB.
 */
export function isCommentIdFromCommentsDb(commentId: string | number | bigint): boolean {
    const commentIdStr = String(commentId);
    return (
        commentIdStr.startsWith('8') &&
        commentIdStr.length >= config.get<Record<string, string>>('db_minimums')['task_mgmt.comments'].length
    );
}

export function partitionCommentIdsByType(commentIds: string[]) {
    return partition(commentIds, id => isCommentIdFromCommentsDb(id));
}

/**
 * Returns the comments DB context for new comment creation, used when creating threaded comments.
 * A comments DB context can be null so that all writes go to the main DB (BDR/PRR).
 * When the context is not null, it will contain the comments DB pool type.
 * It can also contain the double write mode if the parent comment is a chat comment, has been migrated to the comments DB and now lives in both.
 */
export async function getCommentsDBContextForCommentCreation(params: {
    commentType: number;
    isChatComment?: boolean;
    parentCommentContext?: null | {
        parentCommentId: string | number;
        parentCommentType: number;
        parentCommentParentId: string | number;
    };
    workspaceId: string | number;
}) {
    const shouldUseSeparateDbForComments = await isNewCommentsDbEnabledForWorkspace(params.workspaceId);
    const { isChatComment } = params;
    const isViewComment = params.commentType === config.get<number>('comments.types.view');
    const isThreadedComment = params.commentType === config.get<number>('comments.types.comment');

    if (isViewComment && isChatComment && shouldUseSeparateDbForComments) {
        // Creating a comment in a conversation (chat) view and the flag is on: save the comment in the comments Aurora DB.
        return { commentsPool: PoolOptions.RW };
    }

    if (!isThreadedComment || !params.parentCommentContext) {
        /**
         * Chat comments can also be children of other chat comments.
         * If that is not the case, use the main DB (BDR/PRR).
         */
        return null;
    }

    if (isCommentIdFromCommentsDb(params.parentCommentContext.parentCommentId)) {
        // If the parent comment is from the comments Aurora DB, use the comments Aurora DB.
        return { commentsPool: PoolOptions.RW };
    }

    if (isChatComment && shouldUseSeparateDbForComments) {
        /**
         * If the parent comment is:
         *  1. from the main DB (BDR/PRR)
         *  2. in a conversation (chat) view
         *  3. the flag is on
         * Then the parent comment is a migrated chat comment, currently lives in both dbs and we should:
         *  1. save the child comment with an id from the main DB (BDR/PRR), which means it should start with the BDR prefix (usually 9)
         *  2. save the child comment in both the main DB (BDR/PRR) and the comments Aurora DB
         */
        return { commentsPool: PoolOptions.RW, doubleWrite: true };
    }

    return null;
}

/**
 * Returns the comments DB context for comment update, used when updating threaded comments.
 * A comments DB context can be null so that all writes go to the main DB (BDR/PRR).
 * When the context is not null, it will contain the comments DB pool type.
 * It can also contain the double write mode if the comment is a chat comment, has been migrated to the comments DB and now lives in both.
 */
export async function getCommentsDBContextForCommentUpdate(params: {
    commentId: string | number | bigint;
    rootCommentType: number;
    rootCommentParentId: string | number | bigint;
    workspaceId: string | number;
}) {
    if (isCommentIdFromCommentsDb(params.commentId)) {
        return { commentsPool: PoolOptions.RW };
    }

    if (params.rootCommentType !== config.get<number>('comments.types.view')) {
        return null;
    }

    if (!(await isNewCommentsDbEnabledForWorkspace(params.workspaceId))) {
        return null;
    }

    if (!(await isConversationViewByViewId(params.rootCommentParentId))) {
        return null;
    }

    return { commentsPool: PoolOptions.RW, doubleWrite: true };
}

export async function getViewTypeByViewId(viewId: string | number | bigint) {
    const { rows } = await replicaQueryAsync('SELECT type FROM task_mgmt.views WHERE view_id = $1', [viewId]);

    if (!rows.length) {
        return null;
    }

    return rows[0].type;
}

async function isConversationViewByViewId(viewId: string | number | bigint) {
    const viewType = await getViewTypeByViewId(viewId);

    if (!viewType) {
        return false;
    }

    return isConversationView(viewType, config.get<Record<string, number>>('views.view_types'));
}

export function makeWriteSimpleClientForCommentsDb() {
    const simpleClient = {
        queryAsync: (query: string | QueryConfig<QueryParams>, params: QueryParams) =>
            writeAsyncFunction(client => client.queryAsync(query, params), {
                clientOptions: { poolType: DbPool.CommentsDbWrite },
            }),
    } as SimpleClient;

    attachWorkspaceInclusionInCommentsQueryAnalyzerIfNeeded(simpleClient, {
        shouldAnalyze: shouldAnalyzeWorkspaceInclusionInChatQueries,
    });

    return simpleClient;
}

export async function isNewCommentsDbEnabledForWorkspace(workspaceId: number | string | null): Promise<boolean> {
    if (connectedSplitClient(CHAT_SHOULD_USE_COMMENTS_DB)) {
        const workspaceJoinDate = await getWorkspaceJoinDate(workspaceId, redisClient, replicaQueryAsync);
        return shouldUseCommentsDbForChat(workspaceId, workspaceJoinDate);
    }

    logger.info({
        msg: 'Cannot connect to split, falling back to db check for new comments db enablement',
        workspaceId,
        ECODE: 'COMMENTS_DB_UTILS_003',
    });

    try {
        const { rows } = await commentsReadQueryAsync(
            'SELECT 1 FROM task_mgmt.chat_views WHERE workspace_id = $1 LIMIT 1',
            [workspaceId]
        );

        return rows.length > 0;
    } catch (err) {
        logger.info({
            msg: 'Failed to check if new comments db is enabled for workspace. Falling back to false',
            err,
            ECODE: 'COMMENTS_DB_UTILS_004',
        });

        return false;
    }
}

export async function getConfigForCommentsDbUsageInViewRelationships(params: {
    viewType: number;
    workspaceId: number | string | null;
}) {
    if (
        params.viewType &&
        params.workspaceId &&
        isConversationView(params.viewType, config.views.view_types) &&
        (await isNewCommentsDbEnabledForWorkspace(params.workspaceId))
    ) {
        return useCommentsDbForRoomRelationshipsConfig(params.workspaceId);
    }

    return null;
}
