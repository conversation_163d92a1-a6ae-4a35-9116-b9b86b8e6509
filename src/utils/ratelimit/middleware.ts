/**
 * This module provides simple middleware wrappers for the memoized rate limiter functions defined in `index.ts`.
 *
 * The purpose of these wrappers is to defer the instantiation of the rate limiters until a request is made,
 * which avoids potential static initialization issues. By wrapping the memoized functions, we ensure they
 * are only invoked when the middleware is executed by Express during the request-response cycle.
 */

import { NextFunction, Request, Response } from 'express';
import * as rateLimiters from './index';

export const globalLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.globalLimiter()(req, res, next);

export const publicApiLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.publicApiLimiter()(req, res, next);

export const ticketLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.ticket_limiter()(req, res, next);

export const paymentLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.payment_limiter()(req, res, next);

export const teamPaymentLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.team_payment_limiter()(req, res, next);

export const paymentIpLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.payment_ip_limiter()(req, res, next);

export const promoCodeLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.promo_code_limiter()(req, res, next);

export const workspaceCreationLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.workspace_creation_limiter()(req, res, next);

export const genericViewLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.genericView_limiter()(req, res, next);

export const genericViewWSLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.genericViewWS_limiter()(req, res, next);

export const taskInviteEndpointIpLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.task_invite_endpoint_ip_limiter()(req, res, next);

export const freeTrialLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.freeTrial_limiter()(req, res, next);

export const emailEndpointIpLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.email_endpoint_ip_limiter()(req, res, next);

export const formLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.form_limiter()(req, res, next);

export const twoFATotpLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.twoFA_totp_limiter()(req, res, next);

export const twoFAPhoneLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.twoFA_phone_limiter()(req, res, next);

export const createUserEndpointIpLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.create_user_endpoint_ip_limiter()(req, res, next);

export const editUserLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.editUserLimiter()(req, res, next);

export const shortTermLoginLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.short_term_login_limiter()(req, res, next);

export const longTermLoginLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.long_term_login_limiter()(req, res, next);

export const superLongTermLoginLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.super_long_term_login_limiter()(req, res, next);

export const banningLoginLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.banning_login_limiter()(req, res, next);

export const shortTermPasswordChangeLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.short_term_password_change_limiter()(req, res, next);

export const oauthLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.oauth_limiter()(req, res, next);

export const refreshTokenLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.refresh_token_limiter()(req, res, next);

export const commentsReadLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.commentsReadLimiter()(req, res, next);

export const commentsWriteLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.commentsWriteLimiter()(req, res, next);

export const commentsDeleteLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.commentsDeleteLimiter()(req, res, next);

export const commentsEditLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.commentsEditLimiter()(req, res, next);

export const userWebhookIPlimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.userWebhookIPlimiter()(req, res, next);

export const emailWebhookIPlimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.emailWebhookIPlimiter()(req, res, next);

export const sharedAndHierarchyLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.sharedAndHierarchy_limiter()(req, res, next);

export const genericIncidentRateLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.genericIncidentRateLimiter()(req, res, next);

export const shortTermGenerateAutoLoginTokenLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.shortTermGenerateAutoLoginTokenLimiter()(req, res, next);

export const longTermGenerateAutoLoginTokenLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.longTermGenerateAutoLoginTokenLimiter()(req, res, next);

export const reloadEventLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.reloadEventLimiter()(req, res, next);

export const authzTokenLimiter = (req: Request, res: Response, next: NextFunction) =>
    rateLimiters.authz_token_limiter()(req, res, next);
