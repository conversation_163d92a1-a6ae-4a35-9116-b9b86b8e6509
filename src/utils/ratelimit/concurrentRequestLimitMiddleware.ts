import { Request } from 'express';
import { ConcurrentRequestLimiterOptions, createConcurrentRequestLimiter } from './concurrentRequestLimiter';
import { ConcurrentRequestLimitMiddlewareConfig } from '../../models/integrations/split/squadTreatments/hierarchyTreatments';

// Default configuration constants
const DEFAULT_CONCURRENT_REQUEST_LIMITER_TIMEOUT_MS = 60000;
const DEFAULT_CONCURRENT_REQUEST_LIMITER_MAX_CONCURRENT_REQUESTS = 200;

// Type for custom request ID extraction functions
export type RequestIdExtractor = (req: Request) => string | null;

// Registry for custom request ID extraction functions per URL
const customRequestIdExtractors: Record<string, RequestIdExtractor> = {};

/**
 * Default request ID extractor that uses the full URL with query params and route params
 */
export const defaultGetRequestId = (req: Request): string | null => `${req.method}:${req.baseUrl}${req.path}`;

/**
 * Register a custom request ID extractor for a specific URL pattern
 */
export const registerCustomRequestIdExtractor = (urlPattern: string, extractor: RequestIdExtractor): void => {
    customRequestIdExtractors[urlPattern] = extractor;
};

/**
 * Clear all custom request ID extractors (primarily for testing)
 */
export const clearCustomRequestIdExtractors = (): void => {
    Object.keys(customRequestIdExtractors).forEach(key => {
        delete customRequestIdExtractors[key];
    });
};

/**
 * Get the request ID extractor for a given request
 */
export const getRequestIdExtractor = (req: Request): RequestIdExtractor => {
    const keys = Object.keys(customRequestIdExtractors);
    const matchingPattern = keys.length ? resolvePathPattern(keys, req) : null;

    if (matchingPattern && customRequestIdExtractors[matchingPattern]) {
        return customRequestIdExtractors[matchingPattern];
    }

    return defaultGetRequestId;
};

/**
 * Get the configuration for a specific URL path
 */
export const getConfigForPath = (
    req: Request,
    configProvider: () => ConcurrentRequestLimitMiddlewareConfig
):
    | { disabled: true }
    | { disabled: false; maxConcurrentRequests: number; lockAcquireTimeout: number; lockTTL: number } => {
    const globalConfig = configProvider();

    const { defaultConfig } = globalConfig;

    const matchingPattern = globalConfig.limitsByPath
        ? resolvePathPattern(Object.keys(globalConfig.limitsByPath), req)
        : null;
    const pathConfig = matchingPattern ? globalConfig.limitsByPath[matchingPattern] : null;

    const enabled = pathConfig?.enabled ?? defaultConfig?.enabled ?? false;

    if (!enabled) {
        return { disabled: true as const };
    }

    return {
        maxConcurrentRequests:
            pathConfig?.maxConcurrentRequests ||
            defaultConfig.maxConcurrentRequests ||
            DEFAULT_CONCURRENT_REQUEST_LIMITER_MAX_CONCURRENT_REQUESTS,
        lockAcquireTimeout:
            pathConfig?.lockAcquireTimeout ||
            defaultConfig.lockAcquireTimeout ||
            DEFAULT_CONCURRENT_REQUEST_LIMITER_TIMEOUT_MS,
        lockTTL: pathConfig?.lockTTL || defaultConfig.lockTTL || DEFAULT_CONCURRENT_REQUEST_LIMITER_TIMEOUT_MS,
        disabled: !enabled,
    };
};

/**
 * Create a concurrent request limit middleware with a custom configuration provider
 */
export const createConcurrentRequestLimitMiddleware = (
    configProvider: () => ConcurrentRequestLimitMiddlewareConfig,
    prefix = 'crlm' // Concurrent Request Limit Middleware
) => {
    /**
     * Create the concurrent request limit middleware options
     */
    const concurrentRequestLimitMiddlewareOptions = (req: Request): ConcurrentRequestLimiterOptions => {
        const config = getConfigForPath(req, configProvider);

        if (config.disabled === false) {
            const requestIdExtractor = getRequestIdExtractor(req);

            return {
                prefix,
                maxConcurrentRequests: config.maxConcurrentRequests,
                lockAcquireTimeout: config.lockAcquireTimeout,
                lockTTL: config.lockTTL,
                disabled: false,
                getRequestId: requestIdExtractor,
            };
        }

        return {
            prefix,
            maxConcurrentRequests: DEFAULT_CONCURRENT_REQUEST_LIMITER_MAX_CONCURRENT_REQUESTS,
            lockAcquireTimeout: DEFAULT_CONCURRENT_REQUEST_LIMITER_TIMEOUT_MS,
            lockTTL: DEFAULT_CONCURRENT_REQUEST_LIMITER_TIMEOUT_MS,
            disabled: true,
        };
    };

    return createConcurrentRequestLimiter(concurrentRequestLimitMiddlewareOptions);
};

/**
 * Utility function to register custom request ID extractors
 * This can be used by services to define custom key extraction logic
 */
export const configureConcurrencyLimitKeys = (keyMappings: Record<string, RequestIdExtractor>): void => {
    Object.entries(keyMappings).forEach(([urlPattern, extractor]) => {
        registerCustomRequestIdExtractor(urlPattern, extractor);
    });
};

/**
 * Resolves the matching path pattern for a request from a list of configured patterns.
 *
 * Supports two key formats:
 * 1. Standard format: "method:url"
 *    - Example: "GET:/hierarchy/v2/lists"
 *    - Matches method exactly and checks if request URL starts with the configured URL
 *    - This allows query parameters to be ignored (e.g., "/api/users" matches "/api/users?limit=10")
 *
 * 2. Regex format: "regex:method:pattern"
 *    - Example: "regex:PUT:\/hierarchy\/v2\/(\d+)"
 *    - Matches method exactly and URL using regex pattern
 *
 * @param pathPatterns - Array of path pattern strings in the above formats
 * @param req - Express request object containing method, baseUrl, and path
 * @returns The first matching path pattern, or undefined if no match found
 */
export function resolvePathPattern(pathPatterns: string[] | undefined, req: Request): string | undefined {
    if (!pathPatterns) {
        return undefined;
    }

    const requestMethod = req.method;
    const requestUrl = `${req.baseUrl}${req.path}`;

    for (const pattern of pathPatterns) {
        const parts = pattern.split(':');

        if (parts[0] === 'regex') {
            // Format: regex:method:pattern
            if (parts.length < 3) continue;

            const method = parts[1];
            const regexPattern = parts.slice(2).join(':'); // In case the pattern contains ':'

            if (method === requestMethod && testCachedRegexPattern(regexPattern, requestUrl)) {
                return pattern;
            }
        } else {
            // Format: method:url
            if (parts.length < 2) continue;

            const method = parts[0];
            const url = parts.slice(1).join(':'); // In case the URL contains ':'

            if (method === requestMethod && requestUrl.startsWith(url)) {
                return pattern;
            }
        }
    }

    return undefined;
}

// Cache for compiled regex patterns with access-based expiration
interface CacheEntry {
    regex: RegExp | null;
    lastAccessTime: number;
}

const regexCache = new Map<string, CacheEntry>();

const FiveMinutes = 1000 * 60 * 5;

/**
 * Compiles and caches regex patterns to avoid repeated compilation costs.
 * Cache entries are kept as long as they're being accessed. Unused entries expire after 5 minutes of inactivity.
 * @param pattern - The regex pattern string to compile
 * @returns Compiled RegExp object or null if pattern is invalid
 */
function getCompiledRegex(pattern: string): RegExp | null {
    const now = Date.now();
    const cached = regexCache.get(pattern);

    // If we have a cached entry, update its access time and return it
    if (cached) {
        cached.lastAccessTime = now;
        return cached.regex;
    }

    // No cached entry exists, compile new regex and cache it
    let compiledRegex: RegExp | null;
    try {
        compiledRegex = new RegExp(pattern);
    } catch (e) {
        // Invalid regex pattern, cache null result
        compiledRegex = null;
    }

    regexCache.set(pattern, {
        regex: compiledRegex,
        lastAccessTime: now,
    });

    // Clean up unused entries when cache gets large
    if (regexCache.size > 50) {
        cleanupExpiredRegexCache();
    }

    return compiledRegex;
}

/**
 * Removes unused entries from the regex cache.
 * Only removes entries that haven't been accessed for 5 minutes.
 */
function cleanupExpiredRegexCache(): void {
    const now = Date.now();
    const entries = Array.from(regexCache.entries());
    for (const [pattern, entry] of entries) {
        if (now - entry.lastAccessTime >= FiveMinutes) {
            regexCache.delete(pattern);
        }
    }
}

/**
 * Tests a URL against a regex pattern using cached compiled regex objects.
 * @param pattern - The regex pattern string
 * @param url - The URL to test against
 * @returns True if the pattern matches the URL, false otherwise
 */
function testCachedRegexPattern(pattern: string, url: string): boolean {
    const compiledRegex = getCompiledRegex(pattern);
    if (!compiledRegex) {
        return false;
    }
    return compiledRegex.test(url);
}
