import config from 'config';
import { getLogger } from '@clickup/shared/utils-logging';
import { metricsClient } from '@clickup/shared/utils-metrics';
import { MetricNames } from './MetricNames';

const logger = getLogger('cookies');

let allValidCookieOrigins: { validCookieOrigins: string[]; validFormCookieOrigins: string[] };

function getAllValidCookieOrigins(key: string): string[] {
    if (!config.has(key)) {
        return [];
    }

    return Object.values(config.get<{ [env: string]: string[] }>(key)).flat();
}

function loadClickUpCookieOrigins() {
    allValidCookieOrigins = {
        validCookieOrigins: getAllValidCookieOrigins('validCookieOrigins'),
        validFormCookieOrigins: getAllValidCookieOrigins('validFormCookieOrigins'),
    };
}

function isValidCookieOriginFromKey(key: keyof typeof allValidCookieOrigins, origin: string, hostname?: string) {
    let validCookieOrigins = allValidCookieOrigins[key];

    let { CLICKUP_ENV } = process.env;

    if (hostname === 'stage1-api.clickup.com') {
        // Check for canary deployment only if hostname is provided
        CLICKUP_ENV = 'stage1';
    } else if (!CLICKUP_ENV) {
        // Local dev never has CLICKUP_ENV set
        CLICKUP_ENV = 'local';
    }

    const configKey = `${key}.${CLICKUP_ENV}`;

    if (config.has(configKey)) {
        validCookieOrigins = config.get(configKey);
    } else if (process.env.NODE_ENV !== 'test') {
        logger.error(`No environment-specific ${key} found for ${CLICKUP_ENV}`);
    }

    const valid = validCookieOrigins.includes(origin);

    metricsClient.increment(MetricNames.VALID_ORIGIN_CHECKS, 1, { key, valid: valid.toString() });

    return valid;
}

export function isValidCookieOrigin(origin: string, hostname?: string) {
    if (!allValidCookieOrigins) {
        loadClickUpCookieOrigins();
    }
    return isValidCookieOriginFromKey('validCookieOrigins', origin, hostname);
}

export function isValidFormCookieOrigin(origin: string, hostname?: string) {
    if (!allValidCookieOrigins) {
        loadClickUpCookieOrigins();
    }
    return isValidCookieOriginFromKey('validFormCookieOrigins', origin, hostname);
}
