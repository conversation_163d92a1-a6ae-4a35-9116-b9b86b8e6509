/**
 * Bucketize a count into a range to limit the cardinality of the tag values
 * @param count
 */
export function bucketize(count: number) {
    count = +count;
    if (!count) {
        return '0';
    }
    if (count === 1) {
        return '1';
    }
    if (count <= 5) {
        return '2-5';
    }
    if (count <= 10) {
        return '6-10';
    }
    if (count <= 20) {
        return '11-20';
    }
    if (count <= 50) {
        return '21-50';
    }
    if (count <= 100) {
        return '51-100';
    }
    return '101+';
}
