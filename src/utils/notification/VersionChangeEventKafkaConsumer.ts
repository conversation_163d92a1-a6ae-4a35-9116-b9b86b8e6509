import { ObjectVersionChangeEvent } from '@time-loop/ovm-object-version';
import { EventsListener } from '@clickup/object-version-common';
import { NotificationManager } from '@clickup/object-version-notification-manager';

import { NOTIFICATION_MANAGER_CONFIG } from '@clickup/object-version-notification-manager-config';
import { ClickUpError } from '@clickup-legacy/utils/errors';
import { exitProcess, EXIT_CODES } from '@clickup-legacy/utils/process';
import { VersionChangeKafkaConsumerErrorCodes } from '@clickup-legacy/utils/errors/constants';
import { metricsClient } from '@clickup-legacy/metrics/metricsClient';
import { JSONValueDecoder } from '@clickup/shared/utils-kafka/client-common';
import { getKafkaConfigTags, KafkaConsumerImpl, RetryableError } from '@clickup/shared/utils-kafka/client';

enum MetricNames {
    CHANGE_NOTIF_KAFKA_MESSAGE_RECEIVED = 'change_notif.kafka_consume.count',
    CHANGE_NOTIF_KAFKA_CALLS = 'change_notif.kafka_consume.calls',
    CHANGE_NOTIF_KAFKA_MESSAGE_PROCESSED = 'change_notif.kafka_consume.processed',
    CHANGE_NOTIF_KAFKA_MESSAGE_ERRORED = 'change_notif.kafka_consume.errored',
}

const {
    topic_name,
    kafka_client_id,
    kafka_consumer_group_name,
    kafka_consumer_max_bytes,
    consumer_processor_timeout_ms,
} = NOTIFICATION_MANAGER_CONFIG;

const logger = ClickUpError.getBasicLogger('version-change-kafka-consumer');

/**
 * Receive version change notification from an external Kafka service.  Version changes are generated
 * by each region that makes changes to logical data objects.  Those change messages are sent by
 * {@link VersionChangeKafkaSender}.
 *
 * When a change notification message is received by this consumer, the consumer will invalidate the
 * local region's cache manager to indicate that an external change to the logical object has occurred.
 *
 * This consumer will ignore any messages originated from the same region.  This is because
 * for the same region the following sequence of event happens:
 *
 *    NotificationManager.notify(ObjectVersionChangeEvent)
 *        1) invalidate cache in local region
 *        2) send event on to Kafka via VersionChangeKafkaSender
 *        3) commit to database that the event is sent
 *
 *   After 2) VersionChangeEventKafkaConsumer receives the event
 *
 * Given that the message is sent only after invalidation of cache in the local region, when a message
 * from the same region is received after step 2) above, it is guaranteed that the cache is already invalidated
 * and the processing of that message can be skipped.
 */
export class VersionChangeEventKafkaConsumer {
    private isInitialized = false;

    private listeners: EventsListener<ObjectVersionChangeEvent>[] = [];

    private readonly master_ids: () => number[];

    private readonly cuRegion: string;

    private readonly tags;

    constructor(master_ids: () => number[], cuRegion: string) {
        this.master_ids = master_ids;
        this.cuRegion = cuRegion;
        this.tags = { 'kafka.topic': topic_name, ...getKafkaConfigTags() };
    }

    /**
     * Register a new listener to receive locally generated events.
     */
    public registerListener(listener: EventsListener<ObjectVersionChangeEvent>): void {
        this.listeners.push(listener);
    }

    /**
     * Start the consumer, once started, it goes into the background
     */
    public async start(): Promise<void> {
        if (this.isInitialized) {
            return;
        }

        const consumer = await KafkaConsumerImpl.getKafkaConsumer({
            clientId: kafka_client_id,
            // each region must use its own consumer group name
            groupId: `${kafka_consumer_group_name}-region-${this.cuRegion}`,
            maxBytes: kafka_consumer_max_bytes,
        });

        // logic to use in the event all retries fail and the batch is not succeeding
        const errorHandler = async (values: ObjectVersionChangeEvent[], error: ClickUpError) => {
            logger.error({
                msg: 'consumer failed to process a batch - restarting',
                values,
                error,
                ECODE: VersionChangeKafkaConsumerErrorCodes.ProcessRestarting,
            });
            exitProcess(EXIT_CODES.FAILURE);
        };

        await consumer.subscribeBatch(
            { topic: topic_name, processorTimeoutMs: consumer_processor_timeout_ms },
            this.processBatch.bind(this),
            JSONValueDecoder,
            errorHandler
        );

        this.isInitialized = true;
    }

    /**
     * Process one batch of events from Kafka
     */
    async processBatch(events: ObjectVersionChangeEvent[]): Promise<void> {
        metricsClient.increment(MetricNames.CHANGE_NOTIF_KAFKA_CALLS, 1, this.tags);

        try {
            const filteredEvents = events.filter(e => !this.master_ids().includes(e.master_id)); // remove same region messages
            if (filteredEvents.length > 0) {
                await NotificationManager.fireListeners(filteredEvents, this.listeners);
            }

            metricsClient.increment(MetricNames.CHANGE_NOTIF_KAFKA_MESSAGE_RECEIVED, events.length, this.tags);
            metricsClient.increment(MetricNames.CHANGE_NOTIF_KAFKA_MESSAGE_PROCESSED, filteredEvents.length, this.tags);
        } catch (err) {
            logger.error({ msg: `processBatch error on ObjectVersionChangeEvents.`, err });
            metricsClient.increment(MetricNames.CHANGE_NOTIF_KAFKA_MESSAGE_ERRORED, 1, this.tags);

            // throw a retryable error to signal the KafkaConsumer to retry the processing
            throw new RetryableError(
                `processBatch error on ObjectVersionChangeEvents`,
                VersionChangeKafkaConsumerErrorCodes.ProcessEventsFail
            );
        }
    }
}
