import { AxiosResponse } from 'axios';
import { makeJWTBasicAuthRequest } from '../request';

export async function createPaymentMethod(userId: number, workspaceId: number, data: object): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/paymentMethod`,
        method: 'POST',
        data,
    });
}

export async function setTeamPaymentMethod(userId: number, workspaceId: number, data: object): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/paymentMethod`,
        method: 'PUT',
        data,
    });
}

export async function editPaymentMethod(userId: number, workspaceId: number, token: string): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/paymentMethod/${token}`,
        method: 'PUT',
    });
}

export async function deletePaymentMethod(userId: number, workspaceId: number, token: string): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/paymentMethod/${token}`,
        method: 'DELETE',
    });
}

export async function getPaymentMethods(userId: number, workspaceId: number): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/paymentMethod`,
        method: 'GET',
    });
}

export async function getClientToken(userId: number, workspaceId: number): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/btToken`,
        method: 'GET',
    });
}

export async function submitPayment(userId: number, workspaceId: number, data: object): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/payment`,
        method: 'POST',
        data,
    });
}

export async function getAddGuestInfo(userId: number, workspaceId: number): Promise<AxiosResponse> {
    return makeJWTBasicAuthRequest(userId, {
        url: `/billing/v1/team/${workspaceId}/guestInfo`,
        method: 'GET',
    });
}
