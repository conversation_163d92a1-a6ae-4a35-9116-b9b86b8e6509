/**
 * View access integration test
 * @group functional/utils/access
 */

jest.mock('../../../../src/utils/workspaceId/checks', () => ({
    ...jest.requireActual('../../../../src/utils/workspaceId/checks'),
    allShardChecks: jest.fn(),
}));

import config from 'config';
import { any } from 'superstruct';
import { checkAccessViewsAsync, promiseCheckAccessView } from '../../../../src/utils/access/viewAccess';
import ClickUpContext from '../../../../src/jest-context/ClickUpContext';
import { allShardChecks } from '../../../../src/utils/workspaceId/checks';
import { ClickUpError } from '../../../../src/utils/errors';
import { ShardRoutingErrorCodes } from '../../../../src/models/sharding/interfaces/errorCodes.interface';
import { expectHttpError } from '../../../sdk/testUtils/errors';
import { DbPool } from '../../../../src/utils/interfaces/TransactionClient';

describe('view access', () => {
    const privateDocView = '6-858777-1';

    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('successfully passes access check', async () => {
        const ownerId = 888880;

        const result = await checkAccessViewsAsync(ownerId, [privateDocView], {
            permissions: [],
            return_accessible: false,
            exclude_deleted: true,
        });

        expect(result[privateDocView]).toBeDefined();
    });

    it('should throw if user has delete workspace role permission off when trying to delete self created view', async () => {
        const adminWithEverythingOff = ********;

        await expect(
            promiseCheckAccessView(adminWithEverythingOff, '6-858721-1', {
                permissions: ['delete'],
                viewPermissions: ['delete_view'],
                is_delete: true,
            })
        ).rejects.toThrowError(
            expect.objectContaining({
                name: 'AccessClickUpError',
                message: 'You do not have permission to do this action',
                ECODE: 'ACCESS_009',
            })
        );
    });

    describe('copying view using "personal" option when "create_views" permission is off for members', () => {
        let view;
        let member;

        beforeAll(async () => {
            const context = new ClickUpContext();
            const user = await context.provideUser();
            const team = await context.provideTeam({ plan: '4' }, { client: user.client });
            member = await context.provideUser({ username: 'member' });
            await context.teamAddUser(team.id, member.id);

            await user.client
                .writeJson(
                    `user/v1/team/${team.id}/rolePermissionSettings`,
                    {
                        settings: [
                            {
                                role_id: 3,
                                views: 0,
                            },
                        ],
                    },
                    'PUT'
                )
                .json(any());

            const subcategory = await context.provideSubcategory({}, { client: user.client });
            view = await context.provideView(
                {
                    parentSubcategoryId: subcategory.id,
                    viewType: 1,
                },
                { client: user.client }
            );
        });

        it('should allow copying view when personal option is set to true', async () => {
            await expect(
                member.client
                    .writeJson(`/viz/v1/view/${view.id}/copyAs`, {
                        locked: false,
                        name: 'copied view',
                        source_view: { ...view },
                        target_view: { type: 2 },
                        personal: true,
                    })
                    .json(any())
            ).resolves.toBeDefined();
        });

        it('should not allow copying view without personal option', async () => {
            await expect(
                member.client
                    .writeJson(`/viz/v1/view/${view.id}/copyAs`, {
                        locked: false,
                        name: 'copied view',
                        source_view: { ...view },
                        target_view: { type: 2 },
                    })
                    .json(any())
            ).rejects.toThrowError();
        });
    });

    // FIXME: Don't this is accurate, must have been an existing bug
    it.skip('Denies access to creator if view is private', async () => {
        const viewCreatorId = 888882;

        await expect(
            checkAccessViewsAsync(viewCreatorId, [privateDocView], {
                permissions: [],
                return_accessible: false,
                exclude_deleted: true,
            })
        ).rejects.toThrowError(
            expect.objectContaining({
                name: 'AccessClickUpError',
                message: 'You dont have access to this view',
                ECODE: 'ACCESS_120',
            })
        );
    });

    it('should successfully pass access check for doc shared with workspace and location', async () => {
        const memberId = 888882;
        const public_doc = '6-858888-1';

        const result = await promiseCheckAccessView(memberId, public_doc, {
            permissions: [],
            viewPermissions: [config.get<string>('permission_constants.comment')],
            type: config.get<number>('views.view_types.doc'),
            hierarchyFallback: true,
        });

        expect(result).toBeDefined();
    });

    it('should successfully pass access check for form', async () => {
        const context = new ClickUpContext();
        const user = await context.provideUser();
        const subcategory = await context.provideSubcategory();
        const view = await context.provideView({
            parentSubcategoryId: subcategory.id,
            viewType: config.get<number>('views.view_types.form'),
        });

        const result = await promiseCheckAccessView(user.id, view.id, {
            permissions: [],
            viewPermissions: [],
            type: config.get<number>('views.view_types.form'),
        });

        expect(result).toBeDefined();
    });

    it('Should throw an error if trying to access view in another shard', async () => {
        const ShardRoutingError = ClickUpError.makeNamedError('sharding');
        (allShardChecks as jest.Mock).mockRejectedValue(
            new ShardRoutingError('Not found', ShardRoutingErrorCodes.RequestAtWrongShard, 404)
        );

        const context = new ClickUpContext();
        const { id: userId } = await context.provideUser();
        const { id: viewId } = await context.provideDoc();

        await expect(
            checkAccessViewsAsync(userId, [viewId], {
                permissions: [],
            })
        ).rejects.toThrowError(
            expect.objectContaining({
                name: 'ShardingClickUpError',
                message: 'Not found',
                ECODE: 'SHARD_002',
            })
        );
    });

    it('Should allow to create team-level template for guest if permission to create view is present', async () => {
        const context = new ClickUpContext();
        const user = await context.provideUser();
        const team = await context.provideTeam({ plan: '6' }, { client: user.client });
        const guest = await context.provideUser({ username: 'Guest' });
        await context.teamAddUser(team.id, guest.id, 4, null, DbPool.Write, undefined, { canCreateViews: true });

        const { id: viewId } = await context.provideView(
            { parentTeamId: team.id, viewType: config.get<number>('views.view_types.list') },
            { client: guest.client }
        );

        const { id: templateId } = await context.provideViewTemplate(
            { sourceViewId: viewId, teamId: team.id },
            { parentTeamId: team.id },
            { client: guest.client }
        );
        expect(templateId).toBeDefined();
    });

    it('Should allow to create hierarchy-level template for guest if permission to create view is present', async () => {
        const context = new ClickUpContext();
        const user = await context.provideUser();
        const team = await context.provideTeam({ plan: '6' }, { client: user.client });
        const subcategory = await context.provideSubcategory({}, { client: user.client });
        const guest = await context.provideUser({ username: 'Guest' });
        await context.teamAddUser(team.id, guest.id, 4, null, DbPool.Write, undefined, { canCreateViews: true });

        // Share comment level first
        await context.subcategoryAddMember(
            {
                subcategory_id: subcategory.id,
                member: {
                    userid: guest.id,
                    permission_level: 3,
                },
            },
            { client: user.client }
        );

        await context.expectAPIError(
            () =>
                context.provideView(
                    { parentSubcategoryId: subcategory.id, viewType: config.get<number>('views.view_types.list') },
                    { client: guest.client }
                ),
            apiError => {
                expect(apiError).toMatchInlineSnapshot(`
                    Object {
                      "ECODE": "ACCESS_433",
                      "err": "You don't have permission to do this action",
                      "status": 401,
                    }
                `);
            }
        );

        // Share edit level, now it should work
        await context.subcategoryUpdateMember(
            {
                subcategory_id: subcategory.id,
                member: {
                    userid: guest.id,
                    permission_level: 4,
                },
            },
            { client: user.client }
        );

        const { id: viewId } = await context.provideView(
            { parentSubcategoryId: subcategory.id, viewType: config.get<number>('views.view_types.list') },
            { client: guest.client }
        );

        const { id: templateId } = await context.provideViewTemplate(
            { sourceViewId: viewId, teamId: team.id },
            { parentSubcategoryId: subcategory.id },
            { client: guest.client }
        );
        expect(templateId).toBeDefined();
    });

    describe('External access', () => {
        const context = new ClickUpContext();
        const secondContext = new ClickUpContext();
        let user;
        let team;
        let secondUser;

        beforeAll(async () => {
            user = await context.provideUser();
            team = await context.provideTeam({}, { client: user.client });
            secondUser = await secondContext.provideUser();
            await secondContext.provideTeam({}, { client: secondUser.client });
        });

        it.each(Object.entries(config.get<object>('views.template_visibility')))(
            'Blocks external access for view templates with %s visibility',
            async (visibility_name: string, visibility_value: number) => {
                const subcategory = await context.provideSubcategory(
                    { name: `View Subcategory - ${visibility_name}` },
                    { client: user.client }
                );

                const { id: viewId } = await context.provideView(
                    {
                        parentSubcategoryId: subcategory.id,
                        viewType: config.get<number>('views.view_types.list'),
                        name: `View Test - ${visibility_name}`,
                    },
                    { client: user.client }
                );

                const { id: templateId } = await context.provideViewTemplate(
                    { sourceViewId: viewId, teamId: team.id, templateVisibility: visibility_value },
                    { parentSubcategoryId: subcategory.id, name: `View Template Test - ${visibility_name}` },
                    { client: user.client }
                );
                expect(templateId).toBeDefined();
                await expect(checkAccessViewsAsync(user.id, [templateId], { permissions: [] })).resolves.toBeDefined();
                await expect(
                    checkAccessViewsAsync(secondUser.id, [templateId], { permissions: [] })
                ).rejects.toThrowError(
                    expect.objectContaining({
                        name: 'AccessClickUpError',
                        message: 'Team not found',
                        ECODE: 'ACCESS_002',
                    })
                );
            }
        );
    });
});
