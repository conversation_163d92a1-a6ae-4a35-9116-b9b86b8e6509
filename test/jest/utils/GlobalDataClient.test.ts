/**
 * @group unit/utils
 */
import '../_mocks/utilsMocks';

import { DbPool } from '@clickup/data-platform/pg-pools';
import { readAsyncFunction, writeAsyncFunction } from '@clickup/utils/db-ovm';
import { GlobalDataClient } from '../../../src/utils/GlobalDataClient';

describe('GlobalDataClient', () => {
    describe('readAsyncFunction', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        const testParams = [
            [
                'options undefined',
                {
                    options: undefined,
                    expected: { poolType: DbPool.GlobalReplica },
                },
            ],
            [
                'useReplica: true',
                {
                    options: { useReplica: true },
                    expected: { poolType: DbPool.GlobalReplica },
                },
            ],
            [
                'useReplica: false',
                {
                    options: { useReplica: false },
                    expected: { poolType: DbPool.GlobalRead },
                },
            ],
        ];

        it.each(testParams)('should pass correct pool type: %p', async (desc, params) => {
            const client = await GlobalDataClient.factory();
            await client.readAsyncFunction(simpleClient => {}, params.options);
            expect(readAsyncFunction).toHaveBeenCalledTimes(1);
            const { clientOptions } = (readAsyncFunction as jest.Mock).mock.calls[0][1];
            expect(clientOptions.poolType).toEqual(params.expected.poolType);
        });
    });

    describe('writeAsyncFunction', () => {
        it('should pass correct pool type', async () => {
            const client = await GlobalDataClient.factory();
            await client.writeAsyncFunction(simpleClient => {}, {});
            expect(writeAsyncFunction).toHaveBeenCalledTimes(1);
            const { clientOptions } = (writeAsyncFunction as jest.Mock).mock.calls[0][1];
            expect(clientOptions.poolType).toEqual(DbPool.GlobalTransaction);
        });
    });
});
