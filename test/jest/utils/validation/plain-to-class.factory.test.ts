import 'reflect-metadata';

import { Type } from 'class-transformer';
import { IsJSON } from 'class-validator';

import {
    createPlainToClassFactory,
    PlainToClassFactory,
} from '../../../../src/utils/validation/plain-to-class.factory';

describe('plainToClassFactory unit tests', () => {
    class FactoryTest {
        @Type(() => String)
        str: string;

        @Type(() => Number)
        num: number;

        @IsJSON()
        json: Record<string, any>;
    }

    let factory: PlainToClassFactory<FactoryTest>;
    const payload = {
        str: 'abc',
        num: 123,
        json: { a: true },
    };

    beforeEach(() => {
        factory = createPlainToClassFactory<FactoryTest>(FactoryTest);
    });

    it('factory should parse object into class instance', () => {
        const result = factory.toClass(payload);

        expect(result).toBeInstanceOf(FactoryTest);
    });

    it('values should be present', () => {
        const result = factory.toClass(payload);

        expect(result).toMatchObject(payload);
    });

    it('values should be implicitly transformed', () => {
        const result = factory.toClass({ ...payload, num: '123' });

        expect(result).toMatchObject(payload);
    });
});
