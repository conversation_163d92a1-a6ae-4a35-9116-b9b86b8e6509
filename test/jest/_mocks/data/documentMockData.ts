import 'reflect-metadata';
import { randomInt, randomString } from '../../../../src/jest-context/helpers/random';
import { DocumentDbRow } from '../../../../src/models/views/documents/datastores/types/documentDbRow';
import { RightSidebarState } from '../../../../src/models/views/documents/interfaces/DocSettings';
import { DocumentSearchResult } from '../../../../src/models/views/documents/interfaces/DocumentSearchResult';
import { DocumentTag } from '../../../../src/models/views/documents/interfaces/DocumentTag';
import ClickUpContext from '../../../../src/jest-context/ClickUpContext';
import { DocumentDBSearchResult } from '../../../../src/models/views/documents/interfaces/DocumentDBSearchResult';

export const createSearchResultDBDocumentDtoMock = (
    valueOverrides?: Partial<DocumentDBSearchResult>
): DocumentDBSearchResult => ({
    view_id: randomString(),
    parent_id: randomString(),
    parent_type: randomInt(1, 10),
    archived: false,
    share_with_team: null,
    view_member: null,
    view_group_member: null,
    date_viewed: null,
    date_created: randomInt(1, 10),
    date_updated: randomInt(1, 10),
    ...valueOverrides,
});

export const createSearchResultDocumentDtoMock = (
    valueOverrides?: Partial<DocumentSearchResult>
): DocumentSearchResult => ({
    id: randomString(),
    parentId: randomString(),
    parentType: randomInt(1, 10),
    isArchived: false,
    shareWithTeam: null,
    viewMember: null,
    viewGroupMember: null,
    ...valueOverrides,
});

export const createDocumentDbRowMock = (valueOverrides: Partial<DocumentDbRow> = {}): DocumentDbRow => {
    return {
        doc_id: randomString(),
        doc_name: randomString(),
        doc_type: randomInt(1, 2),
        is_archived: false,
        is_locked: false,
        parent_id: randomString(),
        parent_type: randomInt(1, 10),
        date_created: randomString(),
        date_updated: randomString(),
        date_viewed: randomString(),
        visibility: randomInt(1, 10),
        assigned_comments_count: randomInt(1, 10),
        pages: [randomString(), randomString()],
        view_member: null,
        view_group_member: null,
        share_with_team: null,
        sidebar_view: false,
        settings_collapsed: [randomString(), randomString()],
        settings_last_page: randomString(),
        settings_full_modal: false,
        settings_sidebar_open: false,
        settings_right_sidebar: randomInt(0, 1) < 1 ? RightSidebarState.Comments : RightSidebarState.Sidebar,
        settings_workspace_id: randomInt(3000, 5000),
        ...valueOverrides,
    };
};

export const createDocumentTagMock = (valueOverrides: Partial<DocumentTag> = {}): DocumentTag => ({
    id: randomString(),
    name: randomString(),
    team_id: randomInt(1, 1000),
    private: randomInt(1, 2) < 2,
    color_fg: randomString(),
    color_bg: randomString(),
    ...valueOverrides,
});

export const createDocumentsForFilterTests = async () => {
    const context = new ClickUpContext();
    const { id: teamId } = await context.provideTeam();
    const { id: userId } = await context.provideUser();
    const { id: docViewId } = await context.provideDoc({
        parentTeamId: teamId,
    });

    const context2 = new ClickUpContext();
    const { id: userId2 } = await context2.provideUser();
    await context.teamAddUser(teamId, userId2);
    const { id: docViewId2 } = await context2.provideDoc({
        parentTeamId: teamId,
    });

    return {
        userId,
        userId2,
        teamId,
        docViewId,
        docViewId2,
        context,
        context2,
    };
};

export const createSetupForLocationFilterTests = async () => {
    const context = new ClickUpContext();

    const { id: teamId } = await context.provideTeam();
    const { id: userId } = await context.provideUser();

    const makeName = (type: string, idx: number) => `${randomString()}-${type}-${idx}`;
    const makeNamesOfType = (type: string, length: number) =>
        Array.from({ length }).map((_, idx) => makeName(type, idx));

    const [project1Name, project2Name] = makeNamesOfType('project', 2);
    const [category1Name, category2Name] = makeNamesOfType('category', 2);
    const [subcategory1Name, subcategory2Name] = makeNamesOfType('subcategory', 2);
    const [task1Name, task2Name] = makeNamesOfType('task', 2);

    const { id: projectId1 } = await context.provideProject({ name: project1Name });
    const { id: categoryId1 } = await context.provideCategory({ name: category1Name, projectId: projectId1 });
    const { id: subcategoryId1 } = await context.provideSubcategory({
        name: subcategory1Name,
        categoryId: categoryId1,
    });
    const { id: taskId1 } = await context.provideTask({ name: task1Name, subcategoryId: subcategoryId1 });

    const { id: projectId2 } = await context.provideProject({ name: project2Name });
    const { id: categoryId2 } = await context.provideCategory({ name: category2Name, projectId: projectId2 });
    const { id: subcategoryId2 } = await context.provideSubcategory({
        name: subcategory2Name,
        categoryId: categoryId2,
    });
    const { id: taskId2 } = await context.provideTask({ name: task2Name, subcategoryId: subcategoryId2 });

    const { id: docTeam } = await context.provideDoc({
        parentTeamId: teamId,
    });
    const { id: docProject1 } = await context.provideDoc({ parentProjectId: projectId1 });
    const { id: docCategory1 } = await context.provideDoc({ parentCategoryId: categoryId1 });
    const { id: docSubcategory1 } = await context.provideDoc({ parentSubcategoryId: subcategoryId1 });
    const { id: docTask1 } = await context.provideDoc({ parentTaskId: taskId1 });

    return {
        teamId,
        userId,
        projectId1,
        projectId2,
        categoryId1,
        categoryId2,
        subcategoryId1,
        subcategoryId2,
        taskId1,
        taskId2,
        docTeam,
        docProject1,
        docCategory1,
        docSubcategory1,
        docTask1,
    };
};
