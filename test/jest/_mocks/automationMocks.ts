import { backfillIsAiCustomField } from '@clickup-legacy/automation/services/automationService';
import { getSubcategoryById } from '@clickup-legacy/automation/datastores/subcategoryDatastore';
import { AutomationError } from '../../../src/automation/AutomationError';

jest.mock('../../../src/automation/resources/helpers', () => {
    return {
        evaluateConditions: jest.fn(),
    };
});

jest.mock('../../../src/automation/resources/task', () => {
    return {
        validateLocation: jest.fn(),
    };
});

jest.mock('../../../src/automation/resources/task/utils/dateUtils', () => {
    return {
        parseDaysFromNow: jest.fn(),
        parseDaysFromNowAsync: jest.fn(),
    };
});

jest.mock('../../../src/automation/datastores/subcategoryDatastore', () => {
    return {
        getSubcategoriesFromTriggerListAsync: jest.fn(),
        getSimilarSubcategories: jest.fn(),
        getSubcategoryIdsByHierarchy: jest.fn(),
        getSubcategoryById: jest.fn(),
        getSubcategoryIdForTask: jest.fn(),
    };
});
jest.mock('../../../src/automation/datastores/categoryDatastore', () => {
    return {
        getCategoriesFromTriggerListAsync: jest.fn(),
    };
});
jest.mock('../../../src/automation/datastores/projectDatastore', () => {
    return {
        getProjectsFromTriggerListAync: jest.fn(),
        getProjectFromTriggerAsync: jest.fn(),
        getProjectFromParentIdAsync: jest.fn(),
    };
});
jest.mock('../../../src/automation/datastores/userDatastore', () => {
    return {
        getUsersFromIds: jest.fn(),
        getTimezoneFromId: jest.fn(),
    };
});
jest.mock('../../../src/automation/datastores/automationDatastore', () => {
    return {
        getPaginatedTriggerByIdAsync: jest.fn(),
        getPaginatedTriggersByIdsAsync: jest.fn(),
        getPaginatedTriggersByParentAsync: jest.fn(),
        getSortedActionsByTriggerIdAsync: jest.fn(),
        getSortedActionsByTriggerIdsAsync: jest.fn(),
        getSortedActionsByTriggerParentAsync: jest.fn(),
        getSubcategoryIdForTask: jest.fn(),
        getTriggers: jest.fn(),
        getTriggersByParents: jest.fn(),
        getTriggersByParentsWithoutThirdPartyActions: jest.fn(),
    };
});

jest.mock('../../../src/automation/datastores/fieldDatastore', () => {
    return {
        getFieldValues: jest.fn(),
    };
});

jest.mock('../../../src/models/project/helpers', () => {
    return {
        formatFeaturesFromRow: jest.fn(),
    };
});

jest.mock('../../../src/clients/AccessClient', () => {
    return {
        accessClientInstance: {
            checkAccessSubcategory: jest.fn(),
            checkAccessCategory: jest.fn(),
            checkAccessProject: jest.fn(),
            checkAccessTeam: jest.fn(),
            checkUsersWithValidAccess: jest.fn(),
            checkGroupsWithValidAccess: jest.fn(),
            checkGroupAccessToLocation: jest.fn(),
            checkAccessHierarchy: jest.fn(),
            filterGroupsWithAccessToTask: jest.fn(),
            filterUsersWithAccessToTask: jest.fn(),
        },
    };
});

jest.mock('../../../src/clients/TaskClient', () => {
    return {
        taskClientInstance: {
            editTask: jest.fn(),
            getTask: jest.fn(),
            addToSubcategory: jest.fn(),
            createList: jest.fn(),
        },
    };
});

jest.mock('../../../src/clients/HierarchyClient', () => {
    return {
        hierarchyClientInstance: {
            createList: jest.fn(),
        },
    };
});

jest.mock('../../../src/clients/FieldClient', () => {
    return {
        fieldClientInstance: {
            checkAccessField: jest.fn(),
            addFieldValue: jest.fn(),
            removeFieldValue: jest.fn(),
            getField: jest.fn(),
        },
    };
});

jest.mock('../../../src/clients/UserClient', () => {
    return {
        checkUserInGroup: jest.fn(),
    };
});

jest.mock('../../../src/automation/AutomationError', () => {
    return {
        AutomationError: jest.fn().mockImplementation(() => {
            return AutomationError;
        }),
        logAutomationError: jest.fn(),
    };
});

jest.mock('../../../src/automation/services/automationService', () => {
    return {
        getTriggers: jest.fn(),
        getTriggersByParents: jest.fn(),
        getTriggersByParentsWithoutThirdPartyActions: jest.fn(),
        checkIfActionAlreadyExecuted: jest.fn(),
        backfillIsAiCustomField: jest.fn(),
    };
});
