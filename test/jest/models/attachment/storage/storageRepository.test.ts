import '../../../_mocks/utilsMocks';

import { readClientQuery } from '../../../../../src/utils/db2';
import { getWorkspaceStorageUsed } from '../../../../../src/models/attachment/storage/storageRepository';

describe('storageRepository tests', () => {
    const mockedReadClientQuery = jest.mocked(readClientQuery);

    describe('getWorkspaceStorageUsed tests', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('Returns the storage used', async () => {
            const mockWorkspaceId = 546;
            mockedReadClientQuery.mockResolvedValue({ rows: [{ storage_used: 667565 }] });

            expect(await getWorkspaceStorageUsed(mockWorkspaceId)).toEqual(667565);
            expect(mockedReadClientQuery.mock.calls[0]).toMatchInlineSnapshot(`
                Array [
                  "
                        SELECT SUM(COALESCE(projects.storage_used, 0)) as storage_used
                        FROM task_mgmt.projects 
                        WHERE team = $1 AND deleted = false AND (projects.importing = false OR projects.importing IS NULL)",
                  Array [
                    546,
                  ],
                ]
            `);
        });

        it('Returns zero if no rows are found', async () => {
            const mockWorkspaceId = 332;
            mockedReadClientQuery.mockResolvedValue({ rows: [] });

            expect(await getWorkspaceStorageUsed(mockWorkspaceId)).toEqual(0);
            expect(mockedReadClientQuery.mock.calls[0]).toMatchInlineSnapshot(`
                Array [
                  "
                        SELECT SUM(COALESCE(projects.storage_used, 0)) as storage_used
                        FROM task_mgmt.projects 
                        WHERE team = $1 AND deleted = false AND (projects.importing = false OR projects.importing IS NULL)",
                  Array [
                    332,
                  ],
                ]
            `);
        });
    });
});
