import axios from 'axios';
import { sendClipsTranscodeRequest } from '../../../../src/models/attachment/clipsHelpers'; // Replace with the actual file path

jest.mock('axios');

const mockLogger = {
    error: jest.fn(),
};

describe('sendClipsTranscodeRequest', () => {
    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should send transcoder request when transcode and clip are true', async () => {
        const team_id = 'team123';
        const attachment_id = 'attachment123';
        const token = 'token123';
        const bucket = 'bucket123';

        await sendClipsTranscodeRequest(true, true, team_id, attachment_id, token, bucket, mockLogger);

        const expectedUrl = `http://localhost:4040/clips-service-v3/clips/workspaces/team123/transcoder/job/clip/attachment123`;
        const expectedHeaders = {
            'Content-Type': 'application/json',
            Authorization: `${token}`,
        };
        const expectedData = {
            bucket,
        };
        const expectedTimeout = 2000;

        expect(axios).toHaveBeenCalledWith({
            method: 'post',
            url: expectedUrl,
            headers: expectedHeaders,
            data: expectedData,
            timeout: expectedTimeout,
        });

        // Ensure logger.error is not called
        expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it('should log an error when missing required parameters', async () => {
        const team_id = '';
        const attachment_id = 'attachment123';
        const token = 'token123';
        const bucket = 'bucket123';

        await sendClipsTranscodeRequest(true, true, team_id, attachment_id, token, bucket, mockLogger);

        // Ensure logger.error is called with the correct message and error code
        expect(mockLogger.error).toHaveBeenCalledWith({
            msg: 'Error in creating transcoder job, missing one of the following: team_id, attachment_id, token, bucket',
            ECODE: 'CLIPS_001',
        });

        // Ensure axios is not called
        expect(axios).not.toHaveBeenCalled();
    });

    it('should log an error when Axios request fails', async () => {
        const team_id = 'team123';
        const attachment_id = 'attachment123';
        const token = 'token123';
        const bucket = 'bucket123';

        // Mock Axios to simulate a request failure
        jest.mocked(axios).mockRejectedValue(new Error('Axios error'));

        await sendClipsTranscodeRequest(true, true, team_id, attachment_id, token, bucket, mockLogger);

        // Ensure logger.error is called with the correct message and error code
        expect(mockLogger.error).toHaveBeenCalledWith({
            msg: 'Error in creating transcoder job',
            err: expect.any(Error),
            ECODE: 'CLIPS_002',
        });
    });

    it('should not send a request when transcode and clip are false', async () => {
        await sendClipsTranscodeRequest(false, false, 'team123', 'attachment123', 'token123', 'bucket123', mockLogger);

        // Ensure axios is not called
        expect(axios).not.toHaveBeenCalled();

        // Ensure logger.error is not called
        expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it('should not send a request when transcode is false', async () => {
        await sendClipsTranscodeRequest(false, true, 'team123', 'attachment123', 'token123', 'bucket123', mockLogger);

        // Ensure axios is not called
        expect(axios).not.toHaveBeenCalled();

        // Ensure logger.error is not called
        expect(mockLogger.error).not.toHaveBeenCalled();
    });

    it('should not send a request when clip is false', async () => {
        await sendClipsTranscodeRequest(true, false, 'team123', 'attachment123', 'token123', 'bucket123', mockLogger);

        // Ensure axios is not called
        expect(axios).not.toHaveBeenCalled();

        // Ensure logger.error is not called
        expect(mockLogger.error).not.toHaveBeenCalled();
    });
});
