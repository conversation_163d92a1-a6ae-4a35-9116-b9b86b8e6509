import {
    getStatusGroup,
    parseStatusGroup,
    normalizeVisualFilters,
    normalizeSeries,
    restoreOriginalSeriesIds,
} from '../../../../../../src/models/dashboard/widgets/util/groupUtils';
import { BarSeries } from '../../../../../../src/models/dashboard/widgets/interfaces/StatsSeries';

describe('groupUtils', () => {
    describe('getStatusGroup', () => {
        it('should format status group with type and name', () => {
            const result = getStatusGroup('open', 'to do');
            expect(result).toBe('open:to do');
        });

        it('should handle status names with special characters', () => {
            const result = getStatusGroup('custom', 'in progress');
            expect(result).toBe('custom:in progress');
        });

        it('should handle empty values', () => {
            const result = getStatusGroup('', '');
            expect(result).toBe(':');
        });
    });

    describe('parseStatusGroup', () => {
        it('should parse status group with type and name', () => {
            const result = parseStatusGroup('open:to do');
            expect(result).toEqual({ type: 'open', name: 'to do' });
        });

        it('should handle status group with only name', () => {
            const result = parseStatusGroup('to do');
            expect(result).toEqual({ type: null, name: 'to do' });
        });

        it('should handle status name with colons', () => {
            const result = parseStatusGroup('custom:feature: implementation');
            expect(result).toEqual({ type: 'custom', name: 'feature: implementation' });
        });

        it('should handle null and undefined values', () => {
            expect(parseStatusGroup(null as unknown as string)).toEqual({ type: null, name: null });
            expect(parseStatusGroup(undefined as unknown as string)).toEqual({ type: undefined, name: undefined });
            expect(parseStatusGroup('')).toEqual({ type: null, name: '' }); // Empty string is preserved as name
        });
    });

    describe('normalizeSeries', () => {
        it('should normalize status series by extracting status names', () => {
            const series: BarSeries[] = [
                {
                    id: 'open:to do',
                    bar_id: 'open:to do',
                    name: 'To Do',
                    data: [1, 2, 3],
                    color: '#d3d3d3',
                },
                {
                    id: 'custom:in progress',
                    bar_id: 'custom:in progress',
                    name: 'In Progress',
                    data: [4, 5, 6],
                    color: '#1090e0',
                },
            ];

            const result = normalizeSeries(series, 'status');

            expect(result).toHaveLength(2);
            expect(result[0]).toEqual({
                id: 'to do',
                bar_id: 'to do',
                name: 'To Do',
                data: [1, 2, 3],
                color: '#d3d3d3',
                original_id: 'open:to do',
                original_bar_id: 'open:to do',
            });
            expect(result[1]).toEqual({
                id: 'in progress',
                bar_id: 'in progress',
                name: 'In Progress',
                data: [4, 5, 6],
                color: '#1090e0',
                original_id: 'custom:in progress',
                original_bar_id: 'custom:in progress',
            });
        });

        it('should not modify series for non-status units', () => {
            const series: BarSeries[] = [
                {
                    id: 'user123',
                    bar_id: 'user123',
                    name: 'John Doe',
                    data: [1, 2, 3],
                    color: '#000000',
                },
            ];

            const result = normalizeSeries(series, 'assignee');

            expect(result).toEqual(series);
        });

        it('should handle series with simple status names (no type prefix)', () => {
            const series: BarSeries[] = [
                {
                    id: 'completed',
                    bar_id: 'completed',
                    name: 'Completed',
                    data: [1, 2, 3],
                    color: '#00ff00',
                },
            ];

            const result = normalizeSeries(series, 'status');

            expect(result[0]).toEqual({
                id: 'completed',
                bar_id: 'completed',
                name: 'Completed',
                data: [1, 2, 3],
                color: '#00ff00',
                original_id: 'completed',
                original_bar_id: 'completed',
            });
        });

        it('should normalize id and bar_id independently when they have different values', () => {
            const series: BarSeries[] = [
                {
                    id: 'open:to do',
                    bar_id: 'custom:in progress',
                    name: 'Mixed Status',
                    data: [1, 2, 3],
                    color: '#ff0000',
                },
            ];

            const result = normalizeSeries(series, 'status');

            expect(result[0]).toEqual({
                id: 'to do',
                bar_id: 'in progress',
                name: 'Mixed Status',
                data: [1, 2, 3],
                color: '#ff0000',
                original_id: 'open:to do',
                original_bar_id: 'custom:in progress',
            });
        });

        it('should preserve undefined values for bar_id and handle edge cases', () => {
            const series: BarSeries[] = [
                {
                    id: '',
                    bar_id: undefined,
                    name: 'Empty Status',
                    data: [1, 2, 3],
                    color: '#cccccc',
                },
                {
                    id: 'open:to do',
                    bar_id: undefined,
                    name: 'Partial Undefined',
                    data: [4, 5, 6],
                    color: '#aaaaaa',
                },
            ];

            const result = normalizeSeries(series, 'status');

            expect(result[0]).toEqual({
                id: '',
                bar_id: undefined,
                name: 'Empty Status',
                data: [1, 2, 3],
                color: '#cccccc',
                original_id: '',
                original_bar_id: undefined,
            });
            expect(result[1]).toEqual({
                id: 'to do',
                bar_id: undefined,
                name: 'Partial Undefined',
                data: [4, 5, 6],
                color: '#aaaaaa',
                original_id: 'open:to do',
                original_bar_id: undefined,
            });
        });

        it('should normalize when group_by is status or status_type', () => {
            const series: BarSeries[] = [
                {
                    id: 'open:to do',
                    bar_id: 'open:to do',
                    name: 'To Do',
                    data: [1, 2, 3],
                    color: '#d3d3d3',
                },
            ];

            const resultGroupByStatus = normalizeSeries(series, 'assignee', { type: 'status' });
            const resultGroupByStatusType = normalizeSeries(series, 'assignee', { type: 'status_type' });

            expect(resultGroupByStatus[0]).toEqual({
                id: 'to do',
                bar_id: 'to do',
                name: 'To Do',
                data: [1, 2, 3],
                color: '#d3d3d3',
                original_id: 'open:to do',
                original_bar_id: 'open:to do',
            });
            expect(resultGroupByStatusType).toEqual(resultGroupByStatus);
        });

        it('should NOT normalize when x-axis is status but group_by is non-status', () => {
            const series: BarSeries[] = [
                {
                    id: 'Unchecked',
                    bar_id: 'Unchecked',
                    name: 'Unchecked',
                    data: [1, 2, 3],
                    color: '#cccccc',
                },
                {
                    id: 'Checked',
                    bar_id: 'Checked',
                    name: 'Checked',
                    data: [4, 5, 6],
                    color: '#00ff00',
                },
            ];

            // x-axis is status but group_by is checkbox - should NOT normalize
            const result = normalizeSeries(series, 'status', { type: 'cf_checkbox' });

            expect(result).toEqual(series); // Should return unchanged
        });
    });

    describe('restoreOriginalSeriesIds', () => {
        it('should restore original id and bar_id values', () => {
            const normalizedSeries: BarSeries[] = [
                {
                    id: 'to do',
                    bar_id: 'to do',
                    name: 'To Do',
                    data: [1, 2, 3],
                    color: '#d3d3d3',
                    original_id: 'open:to do',
                    original_bar_id: 'open:to do',
                },
                {
                    id: 'in progress',
                    bar_id: 'in progress',
                    name: 'In Progress',
                    data: [4, 5, 6],
                    color: '#1090e0',
                    original_id: 'custom:in progress',
                    original_bar_id: 'custom:in progress',
                },
            ];

            const result = restoreOriginalSeriesIds(normalizedSeries);

            expect(result).toHaveLength(2);
            expect(result[0]).toEqual({
                id: 'open:to do',
                bar_id: 'open:to do',
                name: 'To Do',
                data: [1, 2, 3],
                color: '#d3d3d3',
            });
            expect(result[1]).toEqual({
                id: 'custom:in progress',
                bar_id: 'custom:in progress',
                name: 'In Progress',
                data: [4, 5, 6],
                color: '#1090e0',
            });
        });

        it('should handle series without original values', () => {
            const series: BarSeries[] = [
                {
                    id: 'user123',
                    bar_id: 'user123',
                    name: 'John Doe',
                    data: [1, 2, 3],
                    color: '#000000',
                },
            ];

            const result = restoreOriginalSeriesIds(series);

            expect(result).toEqual(series);
        });

        it('should handle partial original values', () => {
            const series: BarSeries[] = [
                {
                    id: 'to do',
                    bar_id: 'to do',
                    name: 'To Do',
                    data: [1, 2, 3],
                    color: '#d3d3d3',
                    original_id: 'open:to do',
                    // No original_bar_id
                },
            ];

            const result = restoreOriginalSeriesIds(series);

            expect(result[0]).toEqual({
                id: 'open:to do',
                bar_id: 'to do', // Falls back to current bar_id
                name: 'To Do',
                data: [1, 2, 3],
                color: '#d3d3d3',
            });
        });

        it('should handle undefined original values correctly', () => {
            const series: BarSeries[] = [
                {
                    id: 'to do',
                    bar_id: 'in progress',
                    name: 'Mixed Status',
                    data: [1, 2, 3],
                    color: '#ff0000',
                    original_id: undefined,
                    original_bar_id: 'custom:in progress',
                },
                {
                    id: '',
                    bar_id: undefined,
                    name: 'Empty Status',
                    data: [4, 5, 6],
                    color: '#cccccc',
                    original_id: undefined,
                    original_bar_id: undefined,
                },
            ];

            const result = restoreOriginalSeriesIds(series);

            expect(result[0]).toEqual({
                id: 'to do', // Falls back to current id when original_id is undefined
                bar_id: 'custom:in progress',
                name: 'Mixed Status',
                data: [1, 2, 3],
                color: '#ff0000',
            });
            expect(result[1]).toEqual({
                id: '', // Preserves empty when both current and original are undefined
                bar_id: undefined,
                name: 'Empty Status',
                data: [4, 5, 6],
                color: '#cccccc',
            });
        });
    });

    describe('normalizeVisualFilters', () => {
        it('should handle null visual filters', () => {
            expect(normalizeVisualFilters(null)).toBe(null);
        });

        it('should handle undefined visual filters', () => {
            expect(normalizeVisualFilters(undefined)).toBe(undefined);
        });

        it('should normalize JSON status identifiers to names', () => {
            const visualFilters = ['{"type": "open", "name": "to do"}', '{"type": "custom", "name": "in progress"}'];

            const result = normalizeVisualFilters(visualFilters);

            expect(result).toEqual(['to do', 'in progress']);
        });

        it('should pass through simple string identifiers', () => {
            const visualFilters = ['to do', 'completed'];

            const result = normalizeVisualFilters(visualFilters);

            expect(result).toEqual(['to do', 'completed']);
        });
    });
});
