import {
    mapWidgetPreMadeFromInt,
    mapWidgetPremadeToInt,
} from '@clickup-legacy/models/dashboard/widgets/datastores/widgets/mapper';
import { WidgetPreMadeInt, WidgetPremade } from '@clickup-legacy/models/dashboard/widgets/interfaces';

describe('Mapping widget between pre_made text and int', () => {
    it('Should throw when unknown pre_made_int is encountered', () => {
        expect(() => mapWidgetPreMadeFromInt(999)).toThrowErrorMatchingInlineSnapshot(
            `"Unknown premade widget int: 999"`
        );
    });

    it('Should throw when an unknown pre_made is encountered', () => {
        expect(() => mapWidgetPremadeToInt('unknown' as any)).toThrowErrorMatchingInlineSnapshot(
            `"Unknown premade widget: unknown"`
        );
    });

    it('Should map undefined pre_made_int to undefined', () => {
        expect(mapWidgetPreMadeFromInt(undefined)).toBeUndefined();
    });

    it('Should map undefined pre_made to undefined', () => {
        expect(mapWidgetPremadeToInt(undefined)).toBeUndefined();
    });

    it('Should map all pre_made int values to text', () => {
        const mapping = Object.fromEntries(
            Object.values(WidgetPreMadeInt).map(value => [value, mapWidgetPreMadeFromInt(value)])
        );

        // Note: this snapshot should be append-only because int values are stored in DB
        expect(JSON.stringify(mapping, null, 4)).toMatchInlineSnapshot(`
            "{
                \\"1\\": \\"custom_line_chart\\",
                \\"2\\": \\"custom_bar_chart\\",
                \\"3\\": \\"custom_pie_chart\\",
                \\"4\\": \\"custom_battery_chart\\",
                \\"5\\": \\"custom_calculation\\",
                \\"6\\": \\"embed_portfolio\\",
                \\"7\\": \\"custom_description\\",
                \\"8\\": \\"people_chat\\",
                \\"9\\": \\"bookmark\\",
                \\"10\\": \\"sprint_velocity\\",
                \\"11\\": \\"sprint_burndown\\",
                \\"12\\": \\"sprint_burnup\\",
                \\"13\\": \\"acumulative_flow\\",
                \\"14\\": \\"cycle\\",
                \\"15\\": \\"lead\\",
                \\"16\\": \\"velocity\\",
                \\"17\\": \\"statuses_burndown\\",
                \\"18\\": \\"statuses_burnup\\",
                \\"19\\": \\"statuses_bar_tasks\\",
                \\"20\\": \\"statuses_battery_tasks\\",
                \\"21\\": \\"statuses_pie_tasks\\",
                \\"22\\": \\"statuses_line_tasks\\",
                \\"23\\": \\"statuses_time_in_status\\",
                \\"24\\": \\"statuses_calc_closed_tasks\\",
                \\"25\\": \\"statuses_calc_done_tasks\\",
                \\"26\\": \\"statuses_calc_open_tasks\\",
                \\"27\\": \\"tags_bar_tasks\\",
                \\"28\\": \\"tags_battery_tasks\\",
                \\"29\\": \\"tags_pie_tasks\\",
                \\"30\\": \\"tags_line_tasks\\",
                \\"31\\": \\"assignees_bar_tasks\\",
                \\"32\\": \\"assignees_battery_tasks\\",
                \\"33\\": \\"assignees_pie_tasks\\",
                \\"34\\": \\"assignee_calc_assigned_tasks\\",
                \\"35\\": \\"assignee_calc_unassigned_tasks\\",
                \\"36\\": \\"goal\\",
                \\"37\\": \\"priority_bar_tasks\\",
                \\"38\\": \\"priority_battery_tasks\\",
                \\"39\\": \\"priority_pie_tasks\\",
                \\"40\\": \\"priority_line_tasks\\",
                \\"41\\": \\"priority_calc_high_tasks\\",
                \\"42\\": \\"priority_calc_low_tasks\\",
                \\"43\\": \\"priority_calc_No Priority_tasks\\",
                \\"44\\": \\"priority_calc_normal_tasks\\",
                \\"45\\": \\"priority_calc_urgent_tasks\\",
                \\"46\\": \\"time_reporting\\",
                \\"47\\": \\"time_sheet\\",
                \\"48\\": \\"billable_reporting\\",
                \\"49\\": \\"time_estimated\\",
                \\"50\\": \\"time_tracked\\",
                \\"51\\": \\"custom_task_list\\",
                \\"52\\": \\"completed_report\\",
                \\"53\\": \\"worked_on_report\\",
                \\"54\\": \\"worked_space_points\\",
                \\"55\\": \\"whose_behind\\",
                \\"56\\": \\"custom_activity_view\\",
                \\"57\\": \\"embed_custom\\",
                \\"58\\": \\"embed_google_doc\\",
                \\"59\\": \\"embed_google_sheet\\",
                \\"60\\": \\"embed_google_slides\\",
                \\"61\\": \\"embed_youtube\\",
                \\"62\\": \\"embed_figma\\",
                \\"63\\": \\"embed_invision\\",
                \\"64\\": \\"embed_twitter\\",
                \\"65\\": \\"embed_typeform\\",
                \\"66\\": \\"embed_miro\\",
                \\"67\\": \\"lo_description\\",
                \\"68\\": \\"location_portfolio\\",
                \\"69\\": \\"folders\\",
                \\"70\\": \\"recent\\",
                \\"71\\": \\"docs\\",
                \\"72\\": \\"attachments\\",
                \\"73\\": \\"search\\",
                \\"74\\": \\"search_new_content\\",
                \\"75\\": \\"search_trending_work\\",
                \\"76\\": \\"search_most_popular\\",
                \\"77\\": \\"search_g_drive\\",
                \\"78\\": \\"search_github\\",
                \\"79\\": \\"search_figma_files\\",
                \\"80\\": \\"search_dropbox_files\\",
                \\"81\\": \\"search_box_files\\",
                \\"82\\": \\"search_confluence_files\\",
                \\"83\\": \\"overdue_tasks\\",
                \\"84\\": \\"tasks_due_soon\\",
                \\"85\\": \\"important_tasks\\",
                \\"86\\": \\"milestones_tasks\\",
                \\"87\\": \\"open_assigned_comments\\",
                \\"88\\": \\"action_report\\",
                \\"89\\": \\"portfolio_v2\\",
                \\"90\\": \\"ask_brain\\",
                \\"91\\": \\"ai_project_update\\",
                \\"92\\": \\"ai_executive_summary\\",
                \\"93\\": \\"ai_standup\\",
                \\"94\\": \\"ai_team_standup\\"
            }"
        `);
    });

    it('Should map all pre_made text values to int', () => {
        const mapping = Object.fromEntries(
            Object.values(WidgetPremade).map(value => [value, mapWidgetPremadeToInt(value)])
        );

        // Note: this snapshot should be append-only because int values are stored in DB
        expect(JSON.stringify(mapping, null, 4)).toMatchInlineSnapshot(`
            "{
                \\"custom_line_chart\\": 1,
                \\"custom_bar_chart\\": 2,
                \\"custom_pie_chart\\": 3,
                \\"custom_battery_chart\\": 4,
                \\"custom_calculation\\": 5,
                \\"embed_portfolio\\": 6,
                \\"custom_description\\": 7,
                \\"people_chat\\": 8,
                \\"bookmark\\": 9,
                \\"sprint_velocity\\": 10,
                \\"sprint_burndown\\": 11,
                \\"sprint_burnup\\": 12,
                \\"acumulative_flow\\": 13,
                \\"cycle\\": 14,
                \\"lead\\": 15,
                \\"action_report\\": 88,
                \\"velocity\\": 16,
                \\"statuses_burndown\\": 17,
                \\"statuses_burnup\\": 18,
                \\"statuses_bar_tasks\\": 19,
                \\"statuses_battery_tasks\\": 20,
                \\"statuses_pie_tasks\\": 21,
                \\"statuses_line_tasks\\": 22,
                \\"statuses_time_in_status\\": 23,
                \\"statuses_calc_closed_tasks\\": 24,
                \\"statuses_calc_done_tasks\\": 25,
                \\"statuses_calc_open_tasks\\": 26,
                \\"tags_bar_tasks\\": 27,
                \\"tags_battery_tasks\\": 28,
                \\"tags_pie_tasks\\": 29,
                \\"tags_line_tasks\\": 30,
                \\"assignees_bar_tasks\\": 31,
                \\"assignees_battery_tasks\\": 32,
                \\"assignees_pie_tasks\\": 33,
                \\"assignee_calc_assigned_tasks\\": 34,
                \\"assignee_calc_unassigned_tasks\\": 35,
                \\"goal\\": 36,
                \\"priority_bar_tasks\\": 37,
                \\"priority_battery_tasks\\": 38,
                \\"priority_pie_tasks\\": 39,
                \\"priority_line_tasks\\": 40,
                \\"priority_calc_high_tasks\\": 41,
                \\"priority_calc_low_tasks\\": 42,
                \\"priority_calc_No Priority_tasks\\": 43,
                \\"priority_calc_normal_tasks\\": 44,
                \\"priority_calc_urgent_tasks\\": 45,
                \\"time_reporting\\": 46,
                \\"time_sheet\\": 47,
                \\"billable_reporting\\": 48,
                \\"time_estimated\\": 49,
                \\"time_tracked\\": 50,
                \\"completed_report\\": 52,
                \\"worked_on_report\\": 53,
                \\"worked_space_points\\": 54,
                \\"whose_behind\\": 55,
                \\"custom_activity_view\\": 56,
                \\"custom_task_list\\": 51,
                \\"open_assigned_comments\\": 87,
                \\"overdue_tasks\\": 83,
                \\"tasks_due_soon\\": 84,
                \\"important_tasks\\": 85,
                \\"milestones_tasks\\": 86,
                \\"embed_custom\\": 57,
                \\"embed_google_doc\\": 58,
                \\"embed_google_sheet\\": 59,
                \\"embed_google_slides\\": 60,
                \\"embed_youtube\\": 61,
                \\"embed_figma\\": 62,
                \\"embed_invision\\": 63,
                \\"embed_twitter\\": 64,
                \\"embed_typeform\\": 65,
                \\"embed_miro\\": 66,
                \\"location_portfolio\\": 68,
                \\"folders\\": 69,
                \\"recent\\": 70,
                \\"docs\\": 71,
                \\"attachments\\": 72,
                \\"portfolio_v2\\": 89,
                \\"search\\": 73,
                \\"search_new_content\\": 74,
                \\"search_trending_work\\": 75,
                \\"search_most_popular\\": 76,
                \\"search_g_drive\\": 77,
                \\"search_github\\": 78,
                \\"search_figma_files\\": 79,
                \\"search_dropbox_files\\": 80,
                \\"search_box_files\\": 81,
                \\"search_confluence_files\\": 82,
                \\"ask_brain\\": 90,
                \\"ai_project_update\\": 91,
                \\"ai_executive_summary\\": 92,
                \\"ai_standup\\": 93,
                \\"ai_team_standup\\": 94,
                \\"lo_description\\": 67
            }"
        `);
    });
});
