/**
 * @group functional/models/teamMembers
 */

import { v4 as uuidv4 } from 'uuid';
import ClickUpContext from '@clickup-legacy/jest-context/ClickUpContext';
import { readQueryAsync, writeQueryAsync } from '@clickup-legacy/utils/db';

import { retryUntilSuccessOrTimeout } from '../../../sdk/utils/retryUtils';

describe('Team member role update to guest', () => {
    jest.setTimeout(30_000);

    let context: ClickUpContext;
    let workspace: Awaited<ReturnType<typeof context.provideTeam>>;
    let mainUser: Awaited<ReturnType<typeof context.provideUser>>;
    let userToRunRoleUpdate: Awaited<ReturnType<typeof context.provideUser>>;
    let _userToBecomeGuest: Awaited<ReturnType<typeof context.provideUser>>;
    let _userToBecomeGuestInFreeForever: Awaited<ReturnType<typeof context.provideUser>>;

    beforeAll(async () => {
        context = new ClickUpContext();
        _userToBecomeGuest = await context.provideUser({ username: 'userToBecomeGuest' });
        _userToBecomeGuestInFreeForever = await context.provideUser({ username: 'userToBecomeGuestInFreeForever' });
        userToRunRoleUpdate = await context.provideUser({ username: 'userToRunRoleUpdate' });
        mainUser = await context.provideUser({ username: 'mainUser' });

        workspace = await context.provideTeam();

        await context.teamAddUser(workspace.id, _userToBecomeGuest.id);
        await context.teamAddUser(workspace.id, _userToBecomeGuestInFreeForever.id);
        await context.teamAddUser(workspace.id, userToRunRoleUpdate.id, 2);
        await context.teamAddUser(workspace.id, mainUser.id);
    });

    test('Object membership is correctly updated', async () => {
        // This assignment is here to avoid accidental use of userToBecomeGuestInFreeForever
        const userToBecomeGuest = _userToBecomeGuest;

        const space = await context.provideProject(
            { teamId: workspace.id, private: true, name: `Some space ${uuidv4()}` },
            { client: userToBecomeGuest.client }
        );
        const folder = await context.provideCategory(
            { projectId: space.id, isPrivate: true },
            { client: userToBecomeGuest.client }
        );
        const list = await context.provideSubcategory(
            { categoryId: folder.id, private: true },
            { client: userToBecomeGuest.client }
        );
        const tasks = await Promise.all([
            context.provideTask(
                { name: 'task 1', subcategoryId: list.id, private: true },
                { client: userToBecomeGuest.client }
            ),
            context.provideTask(
                { name: 'task 2', subcategoryId: list.id, private: true },
                { client: userToBecomeGuest.client }
            ),
            context.provideTask(
                { name: 'task 3', subcategoryId: list.id, private: true },
                { client: userToBecomeGuest.client }
            ),
        ]);

        await context.projectUpdate(
            {
                projectId: space.id,
                add: [{ id: userToRunRoleUpdate.id, permission_level: 4 }],
            },
            { client: userToBecomeGuest.client }
        );

        await context.addCategoryMember(
            {
                categoryId: folder.id,
                data: { userid: userToRunRoleUpdate.id, permission_level: 4 },
            },
            { client: userToBecomeGuest.client }
        );

        await context.subcategoryAddMember(
            {
                subcategory_id: list.id,
                member: { userid: userToRunRoleUpdate.id, permission_level: 4 },
            },
            { client: userToBecomeGuest.client }
        );

        await Promise.all(
            tasks.map(task =>
                context.taskAddMember(
                    {
                        task_id: task.id,
                        member: { userid: userToRunRoleUpdate.id, permission_level: 4 },
                    },
                    { client: userToBecomeGuest.client }
                )
            )
        );

        await context.teamChangeUsersRole(
            {
                teamId: workspace.id,
                edit: [{ id: userToBecomeGuest.id, role: 4 }],
            },
            { client: userToRunRoleUpdate.client }
        );

        const queries = [
            {
                query: `
                        SELECT task_members.userid, task_members.permission_level
                        FROM task_mgmt.task_members
                        WHERE task_members.task_id = ANY($1)
                    `,
                params: [tasks.map(task => task.id)],
            },
            {
                query: `
                        SELECT subcategory_members.userid, subcategory_members.permission_level
                        FROM task_mgmt.subcategory_members
                        WHERE subcategory_members.subcategory = $1
                    `,
                params: [list.id],
            },
            {
                query: `
                        SELECT category_members.userid, category_members.permission_level
                        FROM task_mgmt.category_members
                        WHERE category_members.category = $1
                    `,
                params: [folder.id],
            },
            {
                query: `
                        SELECT project_members.userid, project_members.permission_level
                        FROM task_mgmt.project_members
                        WHERE project_members.project_id = $1
                    `,
                params: [space.id],
            },
            {
                query: 'SELECT owner FROM task_mgmt.tasks WHERE id = ANY($1)',
                params: [tasks.map(task => task.id)],
            },
            {
                query: 'SELECT owner FROM task_mgmt.subcategories WHERE id = $1',
                params: [list.id],
            },
            {
                query: 'SELECT owner FROM task_mgmt.categories WHERE id = $1',
                params: [folder.id],
            },
            {
                query: 'SELECT owner FROM task_mgmt.projects WHERE id = $1',
                params: [space.id],
            },
        ];

        // Tasks are handled async so we need to retry until they are updated
        await retryUntilSuccessOrTimeout({
            fetch: () => Promise.all(queries.map(query => readQueryAsync(query.query, query.params))),
            assert: results => {
                // Task members
                expect(results[0].rows.length).toEqual(6);
                expect([...results[0].rows].sort((a, b) => a.userid - b.userid)).toEqual(
                    [
                        { userid: userToBecomeGuest.id, permission_level: 5 },
                        { userid: userToBecomeGuest.id, permission_level: 5 },
                        { userid: userToBecomeGuest.id, permission_level: 5 },
                        { userid: userToRunRoleUpdate.id, permission_level: 5 },
                        { userid: userToRunRoleUpdate.id, permission_level: 5 },
                        { userid: userToRunRoleUpdate.id, permission_level: 5 },
                    ].sort((a, b) => a.userid - b.userid)
                );

                // List members
                expect(results[1].rows.length).toEqual(2);
                expect(results[1].rows).toEqual(
                    expect.arrayContaining([
                        { userid: userToBecomeGuest.id, permission_level: 5 },
                        { userid: userToRunRoleUpdate.id, permission_level: 5 },
                    ])
                );

                // Folder members
                expect(results[2].rows.length).toEqual(2);
                expect(results[2].rows).toEqual(
                    expect.arrayContaining([
                        { userid: userToBecomeGuest.id, permission_level: 5 },
                        { userid: userToRunRoleUpdate.id, permission_level: 5 },
                    ])
                );

                // Space members
                expect(results[3].rows.length).toEqual(2);
                expect(results[3].rows).toEqual(
                    expect.arrayContaining([
                        { userid: userToBecomeGuest.id, permission_level: 5 },
                        { userid: userToRunRoleUpdate.id, permission_level: 5 },
                    ])
                );

                // Task owner
                expect(results[4].rows[0].owner).toEqual(userToRunRoleUpdate.id);
                expect(results[4].rows[1].owner).toEqual(userToRunRoleUpdate.id);
                expect(results[4].rows[2].owner).toEqual(userToRunRoleUpdate.id);

                // List owner
                expect(results[5].rows[0].owner).toEqual(userToRunRoleUpdate.id);

                // Folder owner
                expect(results[6].rows[0].owner).toEqual(userToRunRoleUpdate.id);

                // Space owner
                expect(results[7].rows[0].owner).toEqual(userToRunRoleUpdate.id);
            },
        });
    });

    describe('Free forever plan', () => {
        test('Object membership is correctly updated', async () => {
            // This assignment is here to avoid accidental use of userToBecomeGuest
            const userToBecomeGuestInFreeForever = _userToBecomeGuestInFreeForever;

            await Promise.all([
                writeQueryAsync(`UPDATE task_mgmt.teams SET plan_id = 7 WHERE id = $1`, [workspace.id]),
                writeQueryAsync(`UPDATE task_mgmt.team_billing_info SET billed_plan_id = 7 WHERE team_id = $1`, [
                    workspace.id,
                ]),
                writeQueryAsync(`UPDATE task_mgmt.free_trials SET plan_id = NULL WHERE team_id = $1`, [workspace.id]),
            ]);

            const space = await context.provideProject({
                teamId: workspace.id,
                name: `Some space in free forever ${uuidv4()}`,
            });
            const folder = await context.provideCategory({ projectId: space.id, isPrivate: true });
            const list = await context.provideSubcategory({ categoryId: folder.id, private: true });

            const tasks = await Promise.all([
                context.provideTask({ name: 'task 1', subcategoryId: list.id, private: true }),
                context.provideTask({ name: 'task 2', subcategoryId: list.id, private: true }),
                context.provideTask({ name: 'task 3', subcategoryId: list.id, private: true }),
            ]);

            await context.addCategoryMember({
                categoryId: folder.id,
                data: { userid: userToBecomeGuestInFreeForever.id, permission_level: 5 },
            });

            await context.subcategoryAddMember({
                subcategory_id: list.id,
                member: { userid: userToBecomeGuestInFreeForever.id, permission_level: 5 },
            });

            await Promise.all(
                tasks.map(task =>
                    context.taskAddMember({
                        task_id: task.id,
                        member: { userid: userToBecomeGuestInFreeForever.id, permission_level: 5 },
                    })
                )
            );

            // Make sure the members have the correct permission levels, they can be forced to 5 for free forever
            await Promise.all([
                writeQueryAsync(
                    `UPDATE task_mgmt.task_members SET permission_level = 4 WHERE task_id = ANY($1) AND userid = $2`,
                    [tasks.map(task => task.id), userToBecomeGuestInFreeForever.id]
                ),
                writeQueryAsync(
                    `UPDATE task_mgmt.subcategory_members SET permission_level = 4 WHERE subcategory = $1 AND userid = $2`,
                    [list.id, userToBecomeGuestInFreeForever.id]
                ),
                writeQueryAsync(
                    `UPDATE task_mgmt.category_members SET permission_level = 4 WHERE category = $1 AND userid = $2`,
                    [folder.id, userToBecomeGuestInFreeForever.id]
                ),
            ]);

            const readQueries = [
                {
                    query: `
                        SELECT task_members.permission_level
                        FROM task_mgmt.task_members
                        WHERE
                            task_members.task_id = ANY($1)
                            AND task_members.userid = $2
                    `,
                    params: [tasks.map(task => task.id), userToBecomeGuestInFreeForever.id],
                },
                {
                    query: `
                        SELECT subcategory_members.permission_level
                        FROM task_mgmt.subcategory_members
                        WHERE
                            subcategory_members.subcategory = $1
                            AND subcategory_members.userid = $2
                    `,
                    params: [list.id, userToBecomeGuestInFreeForever.id],
                },
                {
                    query: `
                        SELECT category_members.permission_level
                        FROM task_mgmt.category_members
                        WHERE
                            category_members.category = $1
                            AND category_members.userid = $2
                    `,
                    params: [folder.id, userToBecomeGuestInFreeForever.id],
                },
            ];

            const resultsBeforeRoleChange = await Promise.all(
                readQueries.map(query => readQueryAsync(query.query, query.params))
            );

            // Task members
            expect(resultsBeforeRoleChange[0].rows[0].permission_level).toEqual(4);
            expect(resultsBeforeRoleChange[0].rows[1].permission_level).toEqual(4);
            expect(resultsBeforeRoleChange[0].rows[2].permission_level).toEqual(4);

            // List members
            expect(resultsBeforeRoleChange[1].rows[0].permission_level).toEqual(4);

            // Folder members
            expect(resultsBeforeRoleChange[2].rows[0].permission_level).toEqual(4);

            await context.teamChangeUsersRole({
                teamId: workspace.id,
                edit: [{ id: userToBecomeGuestInFreeForever.id, role: 4 }],
            });

            // Tasks are handled async so we need to retry until they are updated
            await retryUntilSuccessOrTimeout({
                fetch: () => Promise.all(readQueries.map(query => readQueryAsync(query.query, query.params))),
                assert: resultsAfterRoleChange => {
                    // Task members
                    expect(resultsAfterRoleChange[0].rows[0].permission_level).toEqual(5);
                    expect(resultsAfterRoleChange[0].rows[1].permission_level).toEqual(5);
                    expect(resultsAfterRoleChange[0].rows[2].permission_level).toEqual(5);

                    // List members
                    expect(resultsAfterRoleChange[1].rows[0].permission_level).toEqual(5);

                    // Folder members
                    expect(resultsAfterRoleChange[2].rows[0].permission_level).toEqual(5);
                },
            });
        });
    });

    describe('Ownership transfer with guest exclusion logic', () => {
        test('Should not transfer ownership to guest users', async () => {
            const newContext = new ClickUpContext();

            const ownerUser = await newContext.provideUser({ username: 'ownerUser' });
            const adminUser = await newContext.provideUser({ username: 'adminUser' });
            const guestUser = await newContext.provideUser({ username: 'guestUser' });

            const newWorkspace = await newContext.provideTeam({}, { client: ownerUser.client });

            await newContext.teamAddUser(newWorkspace.id, ownerUser.id, 1);
            await newContext.teamAddUser(newWorkspace.id, adminUser.id, 2);
            await newContext.teamAddUser(newWorkspace.id, guestUser.id, 4);

            const space = await newContext.provideProject(
                { teamId: newWorkspace.id, private: true, name: `Test ownership transfer space ${uuidv4()}` },
                { client: ownerUser.client }
            );
            await newContext.projectUpdate(
                {
                    projectId: space.id,
                    add: [
                        { id: adminUser.id, permission_level: 1 },
                        { id: guestUser.id, permission_level: 4 },
                    ],
                },
                { client: ownerUser.client }
            );

            const folder = await newContext.provideCategory(
                { projectId: space.id, isPrivate: true, name: 'Test folder' },
                { client: adminUser.client }
            );
            const list = await newContext.provideSubcategory(
                { categoryId: folder.id, private: true, name: 'Test list' },
                { client: adminUser.client }
            );

            await newContext.addCategoryMember(
                {
                    categoryId: folder.id,
                    data: { userid: guestUser.id, permission_level: 4 },
                },
                { client: adminUser.client }
            );
            await newContext.subcategoryAddMember(
                {
                    subcategory_id: list.id,
                    member: { userid: guestUser.id, permission_level: 4 },
                },
                { client: adminUser.client }
            );

            // Triggers ownership transfer
            await newContext.editTeam(
                {
                    teamId: newWorkspace.id,
                    rem: [{ id: adminUser.id }],
                },
                { client: ownerUser.client }
            );

            await retryUntilSuccessOrTimeout({
                fetch: () =>
                    Promise.all([
                        readQueryAsync('SELECT owner FROM task_mgmt.categories WHERE id = $1', [folder.id]),
                        readQueryAsync('SELECT owner FROM task_mgmt.subcategories WHERE id = $1', [list.id]),
                    ]),
                assert: results => {
                    expect(results[0].rows[0].owner).toEqual(adminUser.id);
                    expect(results[1].rows[0].owner).toEqual(adminUser.id);
                },
            });
        });
        test('Should transfer ownership to ws owner', async () => {
            const newContext = new ClickUpContext();

            const ownerUser = await newContext.provideUser({ username: 'ownerUser' });
            const adminUser = await newContext.provideUser({ username: 'adminUser' });
            const guestUser = await newContext.provideUser({ username: 'guestUser' });

            const newWorkspace = await newContext.provideTeam({}, { client: ownerUser.client });

            await newContext.teamAddUser(newWorkspace.id, ownerUser.id, 1);
            await newContext.teamAddUser(newWorkspace.id, adminUser.id, 2);
            await newContext.teamAddUser(newWorkspace.id, guestUser.id, 4);

            const space = await newContext.provideProject(
                { teamId: newWorkspace.id, name: `Test ownership transfer space ${uuidv4()}` },
                { client: ownerUser.client }
            );
            await newContext.projectUpdate(
                {
                    projectId: space.id,
                    add: [
                        { id: adminUser.id, permission_level: 1 },
                        { id: guestUser.id, permission_level: 4 },
                    ],
                },
                { client: ownerUser.client }
            );

            const folder = await newContext.provideCategory(
                { projectId: space.id, name: 'Test folder' },
                { client: adminUser.client }
            );
            const list = await newContext.provideSubcategory(
                { categoryId: folder.id, name: 'Test list' },
                { client: adminUser.client }
            );

            await newContext.addCategoryMember(
                {
                    categoryId: folder.id,
                    data: { userid: guestUser.id, permission_level: 4 },
                },
                { client: adminUser.client }
            );
            await newContext.subcategoryAddMember(
                {
                    subcategory_id: list.id,
                    member: { userid: guestUser.id, permission_level: 4 },
                },
                { client: adminUser.client }
            );

            // Triggers ownership transfer
            await newContext.editTeam(
                {
                    teamId: newWorkspace.id,
                    rem: [{ id: adminUser.id }],
                },
                { client: ownerUser.client }
            );

            await retryUntilSuccessOrTimeout({
                fetch: () =>
                    Promise.all([
                        readQueryAsync('SELECT owner FROM task_mgmt.categories WHERE id = $1', [folder.id]),
                        readQueryAsync('SELECT owner FROM task_mgmt.subcategories WHERE id = $1', [list.id]),
                    ]),
                assert: results => {
                    expect(results[0].rows[0].owner).toEqual(ownerUser.id);
                    expect(results[1].rows[0].owner).toEqual(ownerUser.id);
                },
            });
        });
    });
});
