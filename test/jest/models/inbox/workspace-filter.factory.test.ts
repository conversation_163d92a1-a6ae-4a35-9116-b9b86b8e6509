import { createWorkspaceIdSqlStatement } from '@clickup-legacy/models/inbox/workspace-filter.factory';
import { cardsFilterByWorkspaceIdInHomeApi } from '@clickup-legacy/models/integrations/split/squadTreatments/cardsTreatments';

jest.mock('@clickup-legacy/models/integrations/split/squadTreatments/cardsTreatments', () => ({
    cardsFilterByWorkspaceIdInHomeApi: jest.fn(),
}));

function getInput(overrides = {}) {
    return {
        ...{
            sqlKey: 'TABLE_NAME',
            workspaceId: '$1',
        },
        ...overrides,
    };
}

describe('workspace-filter.factory', () => {
    describe('when ff enabled', () => {
        beforeEach(() => {
            (cardsFilterByWorkspaceIdInHomeApi as jest.Mock).mockReturnValue(true);
        });

        it.each([
            {
                input: getInput(),
                expected: 'AND TABLE_NAME.workspace_id = $1 ',
            },
            {
                input: getInput({
                    sqlKey: 'TABLE_NAME_2',
                }),
                expected: 'AND TABLE_NAME_2.workspace_id = $1 ',
            },
            {
                input: getInput({
                    workspaceId: '123',
                }),
                expected: 'AND TABLE_NAME.workspace_id = 123 ',
            },
            {
                input: getInput({
                    workspaceId: 123,
                }),
                expected: 'AND TABLE_NAME.workspace_id = 123 ',
            },
            {
                input: getInput({
                    workspaceId: '$2',
                }),
                expected: 'AND TABLE_NAME.workspace_id = $2 ',
            },
            {
                input: getInput({
                    workspaceId: undefined,
                }),
                expected: '',
            },
            {
                input: getInput({
                    sqlKey: '',
                }),
                expected: '',
            },
            {
                input: getInput({
                    sqlKey: undefined,
                }),
                expected: '',
            },
            {
                input: getInput({
                    workspaceId: '',
                }),
                expected: '',
            },
        ])('createWorkspaceIdSqlStatement %s %#', ({ input, expected }) => {
            expect(createWorkspaceIdSqlStatement(input.sqlKey, input.workspaceId)).toBe(expected);
        });
    });

    it('should return empty string if feature-flag is turned off', () => {
        (cardsFilterByWorkspaceIdInHomeApi as jest.Mock).mockReturnValue(false);
        expect(createWorkspaceIdSqlStatement('TABLE_NAME', 1)).toBe('');
    });
});
