// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InboxLegacyMain tests LegacyInbox with params: { sort_by: 'assignee' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Completed;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInbox with params: { sort_by: 'assignee' } endpoint should return all eligible tasks 1`] = `
Array [
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
]
`;

exports[`InboxLegacyMain tests LegacyInbox with params: { sort_by: 'manual' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Completed;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInbox with params: { sort_by: 'manual' } endpoint should return all eligible tasks 1`] = `
Array [
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxDone with params: { sort_by: 'assignee' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxDone with params: { sort_by: 'assignee' } endpoint should return all eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Completed;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxDone with params: { sort_by: 'manual' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxDone with params: { sort_by: 'manual' } endpoint should return all eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Completed;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxFuture with params: { sort_by: 'assignee' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Completed;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxFuture with params: { sort_by: 'assignee' } endpoint should return all eligible tasks 1`] = `
Array [
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxFuture with params: { sort_by: 'manual' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Completed;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxFuture with params: { sort_by: 'manual' } endpoint should return all eligible tasks 1`] = `
Array [
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxUnscheduled with params: { sort_by: 'assignee' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Completed;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxUnscheduled with params: { sort_by: 'assignee' } endpoint should return all eligible tasks 1`] = `
Array [
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxUnscheduled with params: { sort_by: 'manual' } endpoint should not return non eligible tasks 1`] = `
Array [
  "done-Case-Lifecycle-Archived-Archived-Archived",
  "done-Case-Lifecycle-Archived-Archived-Default",
  "done-Case-Lifecycle-Archived-Archived-Deleted",
  "done-Case-Lifecycle-Archived-Default-Archived",
  "done-Case-Lifecycle-Archived-Default-Default",
  "done-Case-Lifecycle-Archived-Default-Deleted",
  "done-Case-Lifecycle-Archived-Deleted-Archived",
  "done-Case-Lifecycle-Archived-Deleted-Default",
  "done-Case-Lifecycle-Archived-Deleted-Deleted",
  "done-Case-Lifecycle-Default-Archived-Archived",
  "done-Case-Lifecycle-Default-Archived-Default",
  "done-Case-Lifecycle-Default-Archived-Deleted",
  "done-Case-Lifecycle-Default-Default-Archived",
  "done-Case-Lifecycle-Default-Default-Default",
  "done-Case-Lifecycle-Default-Default-Deleted",
  "done-Case-Lifecycle-Default-Deleted-Archived",
  "done-Case-Lifecycle-Default-Deleted-Default",
  "done-Case-Lifecycle-Default-Deleted-Deleted",
  "done-Case-Lifecycle-Deleted-Archived-Archived",
  "done-Case-Lifecycle-Deleted-Archived-Default",
  "done-Case-Lifecycle-Deleted-Archived-Deleted",
  "done-Case-Lifecycle-Deleted-Default-Archived",
  "done-Case-Lifecycle-Deleted-Default-Default",
  "done-Case-Lifecycle-Deleted-Default-Deleted",
  "done-Case-Lifecycle-Deleted-Deleted-Archived",
  "done-Case-Lifecycle-Deleted-Deleted-Default",
  "done-Case-Lifecycle-Deleted-Deleted-Deleted",
  "done-Case-Permissions-Default-Default-Default",
  "done-Case-Permissions-Default-Default-Private",
  "done-Case-Permissions-Default-Default-SharedDirectly",
  "done-Case-Permissions-Default-Default-SharedGroup",
  "done-Case-Permissions-Default-Private-Default",
  "done-Case-Permissions-Default-Private-Private",
  "done-Case-Permissions-Default-Private-SharedDirectly",
  "done-Case-Permissions-Default-Private-SharedGroup",
  "done-Case-Permissions-Default-SharedDirectly-Default",
  "done-Case-Permissions-Default-SharedDirectly-Private",
  "done-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Default-SharedGroup-Default",
  "done-Case-Permissions-Default-SharedGroup-Private",
  "done-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Default-SharedGroup-SharedGroup",
  "done-Case-Permissions-Private-Default-Default",
  "done-Case-Permissions-Private-Default-Private",
  "done-Case-Permissions-Private-Default-SharedDirectly",
  "done-Case-Permissions-Private-Default-SharedGroup",
  "done-Case-Permissions-Private-Private-Default",
  "done-Case-Permissions-Private-Private-Private",
  "done-Case-Permissions-Private-Private-SharedDirectly",
  "done-Case-Permissions-Private-Private-SharedGroup",
  "done-Case-Permissions-Private-SharedDirectly-Default",
  "done-Case-Permissions-Private-SharedDirectly-Private",
  "done-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "done-Case-Permissions-Private-SharedGroup-Default",
  "done-Case-Permissions-Private-SharedGroup-Private",
  "done-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "done-Case-Permissions-Private-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Default-Default",
  "done-Case-Permissions-SharedDirectly-Default-Private",
  "done-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "done-Case-Permissions-SharedDirectly-Private-Default",
  "done-Case-Permissions-SharedDirectly-Private-Private",
  "done-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "done-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "done-Case-Permissions-SharedGroup-Default-Default",
  "done-Case-Permissions-SharedGroup-Default-Private",
  "done-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Default-SharedGroup",
  "done-Case-Permissions-SharedGroup-Private-Default",
  "done-Case-Permissions-SharedGroup-Private-Private",
  "done-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "done-Case-Permissions-SharedGroup-Private-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "done-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "done-Case-Permissions-SharedGroup-SharedGroup-Default",
  "done-Case-Permissions-SharedGroup-SharedGroup-Private",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "done-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "done-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "done-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "done-Case-TaskSpecific-Archived;",
  "done-Case-TaskSpecific-AssignedToMe;",
  "done-Case-TaskSpecific-AssignedToMyGroup;",
  "done-Case-TaskSpecific-AssignedToOtherUser;",
  "done-Case-TaskSpecific-Completed;",
  "done-Case-TaskSpecific-Deleted;",
  "done-Case-TaskSpecific-Unassigned;",
  "future-Case-Lifecycle-Archived-Archived-Archived",
  "future-Case-Lifecycle-Archived-Archived-Default",
  "future-Case-Lifecycle-Archived-Archived-Deleted",
  "future-Case-Lifecycle-Archived-Default-Archived",
  "future-Case-Lifecycle-Archived-Default-Default",
  "future-Case-Lifecycle-Archived-Default-Deleted",
  "future-Case-Lifecycle-Archived-Deleted-Archived",
  "future-Case-Lifecycle-Archived-Deleted-Default",
  "future-Case-Lifecycle-Archived-Deleted-Deleted",
  "future-Case-Lifecycle-Default-Archived-Archived",
  "future-Case-Lifecycle-Default-Archived-Default",
  "future-Case-Lifecycle-Default-Archived-Deleted",
  "future-Case-Lifecycle-Default-Default-Archived",
  "future-Case-Lifecycle-Default-Default-Default",
  "future-Case-Lifecycle-Default-Default-Deleted",
  "future-Case-Lifecycle-Default-Deleted-Archived",
  "future-Case-Lifecycle-Default-Deleted-Default",
  "future-Case-Lifecycle-Default-Deleted-Deleted",
  "future-Case-Lifecycle-Deleted-Archived-Archived",
  "future-Case-Lifecycle-Deleted-Archived-Default",
  "future-Case-Lifecycle-Deleted-Archived-Deleted",
  "future-Case-Lifecycle-Deleted-Default-Archived",
  "future-Case-Lifecycle-Deleted-Default-Default",
  "future-Case-Lifecycle-Deleted-Default-Deleted",
  "future-Case-Lifecycle-Deleted-Deleted-Archived",
  "future-Case-Lifecycle-Deleted-Deleted-Default",
  "future-Case-Lifecycle-Deleted-Deleted-Deleted",
  "future-Case-Permissions-Default-Default-Default",
  "future-Case-Permissions-Default-Default-Private",
  "future-Case-Permissions-Default-Default-SharedDirectly",
  "future-Case-Permissions-Default-Default-SharedGroup",
  "future-Case-Permissions-Default-Private-Default",
  "future-Case-Permissions-Default-Private-Private",
  "future-Case-Permissions-Default-Private-SharedDirectly",
  "future-Case-Permissions-Default-Private-SharedGroup",
  "future-Case-Permissions-Default-SharedDirectly-Default",
  "future-Case-Permissions-Default-SharedDirectly-Private",
  "future-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Default-SharedGroup-Default",
  "future-Case-Permissions-Default-SharedGroup-Private",
  "future-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Default-SharedGroup-SharedGroup",
  "future-Case-Permissions-Private-Default-Default",
  "future-Case-Permissions-Private-Default-Private",
  "future-Case-Permissions-Private-Default-SharedDirectly",
  "future-Case-Permissions-Private-Default-SharedGroup",
  "future-Case-Permissions-Private-Private-Default",
  "future-Case-Permissions-Private-Private-Private",
  "future-Case-Permissions-Private-Private-SharedDirectly",
  "future-Case-Permissions-Private-Private-SharedGroup",
  "future-Case-Permissions-Private-SharedDirectly-Default",
  "future-Case-Permissions-Private-SharedDirectly-Private",
  "future-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "future-Case-Permissions-Private-SharedGroup-Default",
  "future-Case-Permissions-Private-SharedGroup-Private",
  "future-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "future-Case-Permissions-Private-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Default-Default",
  "future-Case-Permissions-SharedDirectly-Default-Private",
  "future-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "future-Case-Permissions-SharedDirectly-Private-Default",
  "future-Case-Permissions-SharedDirectly-Private-Private",
  "future-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "future-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "future-Case-Permissions-SharedGroup-Default-Default",
  "future-Case-Permissions-SharedGroup-Default-Private",
  "future-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Default-SharedGroup",
  "future-Case-Permissions-SharedGroup-Private-Default",
  "future-Case-Permissions-SharedGroup-Private-Private",
  "future-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "future-Case-Permissions-SharedGroup-Private-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "future-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "future-Case-Permissions-SharedGroup-SharedGroup-Default",
  "future-Case-Permissions-SharedGroup-SharedGroup-Private",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "future-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "future-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "future-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "future-Case-TaskSpecific-Archived;",
  "future-Case-TaskSpecific-AssignedToMe;",
  "future-Case-TaskSpecific-AssignedToMyGroup;",
  "future-Case-TaskSpecific-AssignedToOtherUser;",
  "future-Case-TaskSpecific-Completed;",
  "future-Case-TaskSpecific-Deleted;",
  "future-Case-TaskSpecific-Unassigned;",
  "today-Case-Lifecycle-Archived-Archived-Archived",
  "today-Case-Lifecycle-Archived-Archived-Default",
  "today-Case-Lifecycle-Archived-Archived-Deleted",
  "today-Case-Lifecycle-Archived-Default-Archived",
  "today-Case-Lifecycle-Archived-Default-Default",
  "today-Case-Lifecycle-Archived-Default-Deleted",
  "today-Case-Lifecycle-Archived-Deleted-Archived",
  "today-Case-Lifecycle-Archived-Deleted-Default",
  "today-Case-Lifecycle-Archived-Deleted-Deleted",
  "today-Case-Lifecycle-Default-Archived-Archived",
  "today-Case-Lifecycle-Default-Archived-Default",
  "today-Case-Lifecycle-Default-Archived-Deleted",
  "today-Case-Lifecycle-Default-Default-Archived",
  "today-Case-Lifecycle-Default-Default-Default",
  "today-Case-Lifecycle-Default-Default-Deleted",
  "today-Case-Lifecycle-Default-Deleted-Archived",
  "today-Case-Lifecycle-Default-Deleted-Default",
  "today-Case-Lifecycle-Default-Deleted-Deleted",
  "today-Case-Lifecycle-Deleted-Archived-Archived",
  "today-Case-Lifecycle-Deleted-Archived-Default",
  "today-Case-Lifecycle-Deleted-Archived-Deleted",
  "today-Case-Lifecycle-Deleted-Default-Archived",
  "today-Case-Lifecycle-Deleted-Default-Default",
  "today-Case-Lifecycle-Deleted-Default-Deleted",
  "today-Case-Lifecycle-Deleted-Deleted-Archived",
  "today-Case-Lifecycle-Deleted-Deleted-Default",
  "today-Case-Lifecycle-Deleted-Deleted-Deleted",
  "today-Case-Permissions-Default-Default-Default",
  "today-Case-Permissions-Default-Default-Private",
  "today-Case-Permissions-Default-Default-SharedDirectly",
  "today-Case-Permissions-Default-Default-SharedGroup",
  "today-Case-Permissions-Default-Private-Default",
  "today-Case-Permissions-Default-Private-Private",
  "today-Case-Permissions-Default-Private-SharedDirectly",
  "today-Case-Permissions-Default-Private-SharedGroup",
  "today-Case-Permissions-Default-SharedDirectly-Default",
  "today-Case-Permissions-Default-SharedDirectly-Private",
  "today-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Default-SharedGroup-Default",
  "today-Case-Permissions-Default-SharedGroup-Private",
  "today-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Default-SharedGroup-SharedGroup",
  "today-Case-Permissions-Private-Default-Default",
  "today-Case-Permissions-Private-Default-Private",
  "today-Case-Permissions-Private-Default-SharedDirectly",
  "today-Case-Permissions-Private-Default-SharedGroup",
  "today-Case-Permissions-Private-Private-Default",
  "today-Case-Permissions-Private-Private-Private",
  "today-Case-Permissions-Private-Private-SharedDirectly",
  "today-Case-Permissions-Private-Private-SharedGroup",
  "today-Case-Permissions-Private-SharedDirectly-Default",
  "today-Case-Permissions-Private-SharedDirectly-Private",
  "today-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "today-Case-Permissions-Private-SharedGroup-Default",
  "today-Case-Permissions-Private-SharedGroup-Private",
  "today-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "today-Case-Permissions-Private-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Default-Default",
  "today-Case-Permissions-SharedDirectly-Default-Private",
  "today-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "today-Case-Permissions-SharedDirectly-Private-Default",
  "today-Case-Permissions-SharedDirectly-Private-Private",
  "today-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "today-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "today-Case-Permissions-SharedGroup-Default-Default",
  "today-Case-Permissions-SharedGroup-Default-Private",
  "today-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Default-SharedGroup",
  "today-Case-Permissions-SharedGroup-Private-Default",
  "today-Case-Permissions-SharedGroup-Private-Private",
  "today-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "today-Case-Permissions-SharedGroup-Private-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "today-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "today-Case-Permissions-SharedGroup-SharedGroup-Default",
  "today-Case-Permissions-SharedGroup-SharedGroup-Private",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "today-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "today-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "today-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "today-Case-TaskSpecific-Archived;",
  "today-Case-TaskSpecific-AssignedToMe;",
  "today-Case-TaskSpecific-AssignedToMyGroup;",
  "today-Case-TaskSpecific-AssignedToOtherUser;",
  "today-Case-TaskSpecific-Completed;",
  "today-Case-TaskSpecific-Deleted;",
  "today-Case-TaskSpecific-Unassigned;",
  "unscheduled-Case-Lifecycle-Archived-Archived-Archived",
  "unscheduled-Case-Lifecycle-Archived-Archived-Default",
  "unscheduled-Case-Lifecycle-Archived-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Default-Archived",
  "unscheduled-Case-Lifecycle-Archived-Default-Default",
  "unscheduled-Case-Lifecycle-Archived-Default-Deleted",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Default",
  "unscheduled-Case-Lifecycle-Archived-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Default-Archived-Archived",
  "unscheduled-Case-Lifecycle-Default-Archived-Default",
  "unscheduled-Case-Lifecycle-Default-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Default-Default-Archived",
  "unscheduled-Case-Lifecycle-Default-Default-Deleted",
  "unscheduled-Case-Lifecycle-Default-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Default-Deleted-Default",
  "unscheduled-Case-Lifecycle-Default-Deleted-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Default",
  "unscheduled-Case-Lifecycle-Deleted-Archived-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Default-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Default-Default",
  "unscheduled-Case-Lifecycle-Deleted-Default-Deleted",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Archived",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Default",
  "unscheduled-Case-Lifecycle-Deleted-Deleted-Deleted",
  "unscheduled-Case-Permissions-Default-Default-Private",
  "unscheduled-Case-Permissions-Default-Private-Private",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Default-SharedGroup-Private",
  "unscheduled-Case-Permissions-Private-Default-Default",
  "unscheduled-Case-Permissions-Private-Default-Private",
  "unscheduled-Case-Permissions-Private-Private-Default",
  "unscheduled-Case-Permissions-Private-Private-Private",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Private",
  "unscheduled-Case-Permissions-Private-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Default-Private",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Default",
  "unscheduled-Case-Permissions-SharedDirectly-Private-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Private",
  "unscheduled-Case-Permissions-SharedGroup-Default-Private",
  "unscheduled-Case-Permissions-SharedGroup-Private-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Private",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Private",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Subtask",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Parent",
  "unscheduled-Case-TaskSpecific-Archived;",
  "unscheduled-Case-TaskSpecific-AssignedToOtherUser;",
  "unscheduled-Case-TaskSpecific-Completed;",
  "unscheduled-Case-TaskSpecific-Deleted;",
  "unscheduled-Case-TaskSpecific-Unassigned;",
]
`;

exports[`InboxLegacyMain tests LegacyInboxUnscheduled with params: { sort_by: 'manual' } endpoint should return all eligible tasks 1`] = `
Array [
  "unscheduled-Case-Lifecycle-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-Default",
  "unscheduled-Case-Permissions-Default-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Default-SharedGroup",
  "unscheduled-Case-Permissions-Default-Private-Default",
  "unscheduled-Case-Permissions-Default-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Default-Private-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Default-SharedGroup-Default",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Default-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-Private-Default-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Default-SharedGroup",
  "unscheduled-Case-Permissions-Private-Private-SharedDirectly",
  "unscheduled-Case-Permissions-Private-Private-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedDirectly-Default",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-Private-SharedGroup-Default",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-Private-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedDirectly-SharedGroup-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Default-Default",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Default-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-Private-Default",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-Private-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedDirectly-SharedGroup",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-Default",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedDirectly",
  "unscheduled-Case-Permissions-SharedGroup-SharedGroup-SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:SharedGroup",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Default",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedDirectly",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:SharedGroup",
  "unscheduled-Case-SubTaskSpecific-ParentAssignedButSubtaskUnassigned;-Parent",
  "unscheduled-Case-SubTaskSpecific-ParentUnassignedButSubtaskAssigned;-Subtask",
  "unscheduled-Case-TaskSpecific-AssignedToMe;",
  "unscheduled-Case-TaskSpecific-AssignedToMyGroup;",
]
`;

exports[`InboxLegacyMain tests cases not supported should match snapshot 1`] = `
Array [
  "done-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "done-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "future-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "today-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Default-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:Private-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Default-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedDirectly-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Default-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:Private-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedDirectly-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
  "unscheduled-Case-SecondaryListSpecific-Project:SharedGroup-Category:SharedGroup-Subcategory:Private-Reason:Not able to assign task - assignee can't access task",
]
`;
