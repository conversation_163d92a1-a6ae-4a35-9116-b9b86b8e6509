/* eslint-disable jest/expect-expect */
/**
 * @group functional/models/inbox
 * @group cards
 */

import ClickUpContext from '../../../../../../src/jest-context/ClickUpContext';
import { restoreSplitTest } from '../../../../../sdk/split';
import { InboxTestEnv } from '../inbox-test-env';
import type { Project, Category, Subcategory } from '../inbox-test-env';
import { prepareData, getProject, getCategory, getSubcategory, getLegacyInboxDone } from '../helpers';

jest.setTimeout(100_000);

describe('InboxLegacyDoneSpecificCases tests', () => {
    let clickupContext: ClickUpContext;
    let inboxContext: InboxTestEnv;

    const prepareTestData = async () => {
        return prepareData(inboxContext, {
            taskCommonProps: [
                {
                    taskNamePrefix: 'done',
                    taskOverrides: {
                        status: 'complete',
                    },
                },
            ],
        });
    };

    const getLegacyInbox = async () => {
        return getLegacyInboxDone(clickupContext, inboxContext);
    };

    beforeAll(async () => {
        clickupContext = new ClickUpContext();
        inboxContext = new InboxTestEnv(clickupContext);
        await inboxContext.bootstrapInboxContext({
            skipCreatingTasksAndHierarchy: true,
            taskCommonProps: [],
        });
    });

    afterAll(() => restoreSplitTest());

    describe('LegacyInbox', () => {
        let inboxIds: string[];

        beforeAll(async () => {
            await prepareTestData();
            await inboxContext.populateEgressAuthzData({ workspaceId: inboxContext.team.id });
            const legacyInbox = await getLegacyInbox();
            inboxIds = legacyInbox.inbox.ids;
        });

        it('endpoint should return all eligible tasks', async () => {
            const visibleTaskNames = inboxContext.getTaskNames(inboxIds);
            expect(visibleTaskNames.sort()).toMatchSnapshot();
        });

        it('endpoint should not return non eligible tasks', async () => {
            const notVisibleTaskNames = inboxContext.getTaskNamesExcept(inboxIds);
            expect(notVisibleTaskNames.sort()).toMatchSnapshot();
        });
    });

    describe('Legacy inbox with archived hierarchy', () => {
        let project: Project | undefined;
        let category: Category | undefined;
        let subcategory: Subcategory | undefined;

        beforeAll(async () => {
            await prepareTestData();
            project = await getProject(inboxContext);
            category = await getCategory(inboxContext);
            subcategory = await getSubcategory(inboxContext);
        });
        afterEach(async () => {
            await clickupContext.projectUpdate(
                { projectId: project.id, archived: false },
                { client: inboxContext.admin.client }
            );
            await clickupContext.categoryUpdate(
                { categoryId: category.id, archived: false },
                { client: inboxContext.admin.client }
            );
            await clickupContext.subcategoryUpdate(
                { subcategoryId: subcategory.id, archived: false },
                { client: inboxContext.admin.client }
            );
        });
        it('should not return tasks under archived project', async () => {
            await clickupContext.projectUpdate(
                { projectId: project.id, archived: true },
                { client: inboxContext.admin.client }
            );
            await inboxContext.populateEgressAuthzData({ workspaceId: inboxContext.team.id });

            const legacyInbox = await getLegacyInbox();
            const inboxIds = legacyInbox.inbox.ids;

            expect(inboxIds).toEqual([]);
        });
        it('should not return tasks under archived category', async () => {
            await clickupContext.categoryUpdate(
                { categoryId: category.id, archived: true },
                { client: inboxContext.admin.client }
            );
            await inboxContext.populateEgressAuthzData({ workspaceId: inboxContext.team.id });
            const legacyInbox = await getLegacyInbox();
            const inboxIds = legacyInbox.inbox.ids;

            expect(inboxIds).toEqual([]);
        });
        it('should not return tasks under archived subcategory', async () => {
            await clickupContext.subcategoryUpdate(
                { subcategoryId: subcategory.id, archived: true },
                { client: inboxContext.admin.client }
            );
            await inboxContext.populateEgressAuthzData({ workspaceId: inboxContext.team.id });
            const legacyInbox = await getLegacyInbox();
            const inboxIds = legacyInbox.inbox.ids;

            expect(inboxIds).toEqual([]);
        });
    });
});
