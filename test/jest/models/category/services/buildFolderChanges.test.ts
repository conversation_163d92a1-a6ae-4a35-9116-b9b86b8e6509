import { buildFolderChanges } from '../../../../../src/models/category/services/buildFolderChanges';

describe('buildFolderChanges', () => {
    it('should return an array of changes', () => {
        const options = {
            status: 'red',
            name: 'test',
            content: 'contentData',
        };
        const statusObj = {
            color: '#0000FF',
            status: 'blue',
        };
        const oldFolder = {
            color: null,
            status: null,
            content: null,
            name: null,
        };
        const changes = buildFolderChanges(options, statusObj, oldFolder);
        expect(changes).toEqual([
            {
                field: 'color',
                before: null,
                after: '#0000FF',
            },
            {
                field: 'status',
                before: null,
                after: 'blue',
            },
            {
                field: 'content',
            },
            {
                field: 'name',
                before: null,
                after: 'test',
            },
        ]);
    });

    it('should detect changes in the name field', () => {
        const options = {
            name: 'newName',
        };
        const oldFolder = {
            name: 'oldName',
        };
        const changes = buildFolderChanges(options, undefined, oldFolder);
        expect(changes).toEqual([
            {
                field: 'name',
                before: 'oldName',
                after: 'newName',
            },
        ]);
    });

    it('should detect changes in status field', () => {
        const options = {
            status: 'done',
        };
        const oldFolder = {
            status: 'in_progress',
        };
        const changes = buildFolderChanges(options, undefined, oldFolder);
    });

    it('should detect changes in the archived field', () => {
        const options = {
            archived: true,
        };
        const oldFolder = {
            archived: false,
        };
        const changes = buildFolderChanges(options, undefined, oldFolder);
        expect(changes).toEqual([
            {
                field: 'archived',
                before: false,
                after: true,
            },
        ]);
    });

    it('should not include a changes array if no values have changed between options and oldFolder', () => {
        const options = {
            color: 'red',
        };

        const oldFolder = {
            color: 'red',
        };
        const changes = buildFolderChanges(options, undefined, oldFolder);
        expect(changes).toEqual([]);
    });
    it('should return an empty array if no valid options are passed', () => {
        const changes = buildFolderChanges({
            test: 'test',
            fauxProp: 'faux',
        });

        expect(changes).toEqual([]);
    });

    it('should return an array of changes with unset_status', () => {
        const options = {
            unset_status: true,
        };
        const changes = buildFolderChanges(options);
        expect(changes).toEqual([
            {
                field: 'status',
                after: null,
            },
            {
                field: 'color',
                after: null,
            },
        ]);
    });

    it('should correctly handle values of "none"', () => {
        const options = {
            start_date: 'none',
        };
        const oldFolder = {
            start_date: null,
        };
        const changes = buildFolderChanges(options, undefined, oldFolder);
        expect(changes).toEqual([]);

        const optionsCase2 = { name: 'none' };
        const oldFolderCase2 = { name: null };
        const changesCase2 = buildFolderChanges(optionsCase2, undefined, oldFolderCase2);
        expect(changesCase2).toEqual([
            {
                field: 'name',
                before: null,
                after: 'none',
            },
        ]);
    });
});
