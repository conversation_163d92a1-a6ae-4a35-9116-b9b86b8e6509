/**
 * Functional undo delete category test
 * Require server running
 * @group functional/models/category
 */

import randomstring from 'randomstring';
import ClickUpContext from '../../../../src/jest-context/ClickUpContext';
import * as db from '../../../../src/utils/db';
import { promiseCreateItem } from '../../../../src/models/task/CRUD/createTask';
import { undoDeleteCategory } from '../../../../src/models/category/categoryUndo';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const cat_crud = require('../../../../src/models/category/CRUD');

// eslint-disable-next-line @typescript-eslint/no-var-requires
const subcat_crud = require('../../../../src/models/subcategory/CRUD');

describe.skip('undo delete category', () => {
    const context = new ClickUpContext();

    // Test Case 1
    test('undo delete an empty category', async () => {
        const { id: category_id } = await context.provideCategory();
        const { id: user_id } = await context.provideUser();
        let result;

        await cat_crud.promiseDeleteCategoriesCheckingAccess(user_id, [category_id]);
        result = await db.promiseReadQuery(`SELECT * from task_mgmt.categories where id = $1`, [category_id]);
        expect(result.rows[0].deleted).toEqual(true);

        await undoDeleteCategory(user_id, Number(category_id));

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.categories where id = $1`, [category_id]);
        expect(result.rows[0].deleted).toEqual(false);
    });

    // Test Case 2
    test('undo delete a category with a empty subcategory', async () => {
        const { id: category_id } = await context.provideCategory();
        const { id: user_id } = await context.provideUser();
        let result;

        const subcat = await subcat_crud.promiseCreateSubcategory(user_id, category_id, {
            skipAccess: true,
            name: randomstring.generate(),
        });

        await cat_crud.promiseDeleteCategoriesCheckingAccess(user_id, [category_id]);

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.categories where id = $1`, [category_id]);
        expect(result.rows[0].deleted).toEqual(true);

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.subcategories where id = $1`, [subcat.id]);
        expect(result.rows[0].deleted).toEqual(true);

        await undoDeleteCategory(user_id, Number(category_id));

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.subcategories where id = $1`, [subcat.id]);
        expect(result.rows[0].deleted).toEqual(false);

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.categories where id = $1`, [category_id]);
        expect(result.rows[0].deleted).toEqual(false);
    });

    // Test Case 3
    test('undo delete a category that includes a subcategory with tasks', async () => {
        const { id: category_id } = await context.provideCategory();
        const { id: user_id } = await context.provideUser();

        const subcat = await subcat_crud.promiseCreateSubcategory(user_id, category_id, {
            skipAccess: true,
            name: randomstring.generate(),
        });

        await promiseCreateItem(user_id, 'task', subcat.id, {
            skipAccess: true,
            name: randomstring.generate(),
        });
        await promiseCreateItem(user_id, 'task', subcat.id, {
            skipAccess: true,
            name: randomstring.generate(),
        });
        await promiseCreateItem(user_id, 'task', subcat.id, {
            skipAccess: true,
            name: randomstring.generate(),
        });

        let result;
        await cat_crud.promiseDeleteCategoriesCheckingAccess(user_id, [category_id]);

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.categories where id = $1`, [category_id]);
        expect(result.rows[0].deleted).toEqual(true);

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.subcategories where id = $1`, [subcat.id]);
        expect(result.rows[0].deleted).toEqual(true);

        await undoDeleteCategory(user_id, Number(category_id));

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.subcategories where id = $1`, [subcat.id]);
        expect(result.rows[0].deleted).toEqual(false);

        result = await db.promiseReadQuery(`SELECT * from task_mgmt.categories where id = $1`, [category_id]);
        expect(result.rows[0].deleted).toEqual(false);
    });
});
