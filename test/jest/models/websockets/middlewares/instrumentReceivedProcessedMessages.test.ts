import { ClickUpRequestV2 } from '../../../../../src/interfaces/ClickUpRequest';
import { metricsClient } from '../../../../../src/metrics/metricsClient';
import { instrumentReceivedProcessedMessages } from '../../../../../src/models/websockets/middlewares/instrumentReceivedProcessedMessages';

jest.mock('../../../../../src/metrics/metricsClient', () => ({
    metricsClient: {
        distribution: jest.fn(),
        increment: jest.fn(),
    },
}));

describe('instrumentReceivedProcessedMessages', () => {
    const metricsDistributionMock = metricsClient.distribution as jest.Mock;
    const metricsIncrementMock = metricsClient.increment as jest.Mock;

    it('should not send metrics if messages body is not an array', () => {
        const req = {
            body: { messages: 'foo' },
        } as unknown as ClickUpRequestV2;
        const next = jest.fn();

        instrumentReceivedProcessedMessages(req, null, next);

        expect(metricsDistributionMock).not.toBeCalled();
        expect(metricsIncrementMock).not.toBeCalled();
        expect(next).toBeCalled();
    });

    it('should not send metrics if messages body is an empty array', () => {
        const req = {
            body: { messages: [] },
        } as unknown as ClickUpRequestV2;
        const next = jest.fn();

        instrumentReceivedProcessedMessages(req, null, next);

        expect(metricsDistributionMock).not.toBeCalled();
        expect(metricsIncrementMock).not.toBeCalled();
        expect(next).toBeCalled();
    });

    it('should correctly send metrics', () => {
        const req = {
            body: {
                messages: [{ msg: { method: 'foo' } }, { msg: { method: 'foo' } }],
            },
            get: jest.fn().mockReturnValue('12345'),
            path: '/v1/foo',
        } as unknown as ClickUpRequestV2;
        const next = jest.fn();

        instrumentReceivedProcessedMessages(req, null, next);

        expect(metricsDistributionMock).toBeCalledWith('websocket.message.processed.received.batch.size', 12345, {
            method: 'foo',
            path: '/v1/foo',
        });
        expect(metricsIncrementMock).toBeCalledWith('websocket.message.processed.received', 2, {
            method: 'foo',
            path: '/v1/foo',
        });
        expect(next).toBeCalled();
    });
});
