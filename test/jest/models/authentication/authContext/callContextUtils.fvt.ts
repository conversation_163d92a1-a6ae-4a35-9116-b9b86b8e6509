/**
 * Unit test for callContextUtils
 *
 * @group functional/models/auth
 */

import { AsyncStorage, initContext } from '@clickup/shared/utils-async-storage';

import {
    patchCurrentCallContext,
    getCurrentCallContext,
    collectUserWorkspaceMembership,
    collectUserInfoFromDB,
} from '../../../../../src/utils/auth/callContextUtils';

describe('CallContextUtils', () => {
    const user_with_many_workspace = 888880;
    const workspace_for_the_user = 888800;

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('Should collect workspace membership from DB', async () => {
        const asyncStorage = AsyncStorage.getInstance();
        await initContext(asyncStorage, async () => {
            patchCurrentCallContext({ userid: user_with_many_workspace });

            const cc = getCurrentCallContext();
            collectUserWorkspaceMembership(user_with_many_workspace, cc);
            expect(cc.promises?.workspace_membership).toBeTruthy();

            await cc.promises.workspace_membership;

            const ccc = getCurrentCallContext();
            expect(ccc.workspace_membership).toBeTruthy();
            expect(ccc.workspace_membership?.length).toBeGreaterThan(0);
        });
    });

    it('should get user information from datastore', async () => {
        const asyncStorage = AsyncStorage.getInstance();
        await initContext(asyncStorage, async () => {
            patchCurrentCallContext({ userid: user_with_many_workspace });

            const cc = getCurrentCallContext();

            collectUserInfoFromDB(user_with_many_workspace, workspace_for_the_user, cc);
            expect(cc.promises?.user_info).toBeTruthy();

            await cc.promises.user_info;

            const ccc = getCurrentCallContext();
            expect(ccc.workspace_membership).toBeTruthy();
            // here we specifically expect for 1 record, as we do not asked for "all of memberships" anywhere!
            expect(ccc.workspace_membership?.length).toBe(1);

            expect(ccc.username).toBe('SCIM Owner');
            expect(ccc.email).toBe('<EMAIL>');
        });
    });

    it('should not overwrite team membership when collecting user information from datastore', async () => {
        const asyncStorage = AsyncStorage.getInstance();
        await initContext(asyncStorage, async () => {
            patchCurrentCallContext({ userid: user_with_many_workspace });

            const cc = getCurrentCallContext();

            collectUserWorkspaceMembership(user_with_many_workspace, cc);
            expect(cc.promises?.workspace_membership).toBeTruthy();

            collectUserInfoFromDB(user_with_many_workspace, workspace_for_the_user, cc);
            expect(cc.promises?.user_info).toBeTruthy();

            await cc.promises.user_info;
            await cc.promises.workspace_membership;

            const ccc = getCurrentCallContext();
            expect(ccc.workspace_membership).toBeTruthy();
            // here we expect many (all of them) workspaces!
            expect(ccc.workspace_membership?.length).toBeGreaterThan(1);

            expect(ccc.username).toBe('SCIM Owner');
            expect(ccc.email).toBe('<EMAIL>');
        });
    });
});
