/**
 * Login User API Endpoint - login rate limiting test
 *
 * @group functional/models/auth
 */

import { logoutUser } from '../../../sdk/authenticationSDK/logoutUserSDK';
import request from '../../../sdk/request';

describe('Login User API endpoint - test rate limiter', () => {
    it(`rate limit should stay the same after successful logins and decrement after unsuccessful ones`, async () => {
        const userid = 777780;
        const user = request.users[userid];
        expect(user).toBeDefined();

        if (user.refresh_token) {
            await await logoutUser(user.refresh_token);
            user.refresh_token = null;
        }

        await request.loginUser(userid);
        const rateLimit = user.rate_limit;

        // login with correct password and check that rate limit stays the same
        await await logoutUser(user.refresh_token);
        user.refresh_token = null;
        await request.loginUser(userid);
        expect(user.rate_limit).toBe(rateLimit);

        // login with wrong password and check that rate limit is decremented
        await await logout<PERSON><PERSON>(user.refresh_token);
        user.refresh_token = null;
        try {
            await request.loginUser(userid, 'wrong_password');
        } catch (e) {
            // login is expected to fail
        }

        // get rate limit after failure by logging again
        await request.loginUser(userid);
        expect(user.rate_limit).toBe(rateLimit - 1);
    });
});
