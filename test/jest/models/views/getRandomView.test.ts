import ClickUpContext from '../../../../src/jest-context/ClickUpContext';
import { getRandomViewByType } from '../../../../src/models/views/services/randomViewService';
import { bootstrapDashboardsUsersContext } from '../dashboards/helpers';
import { ProvidedUser } from '../../../../src/jest-context/providers/users/provideUser';

/**
 * @group functional/models/views
 * @group views
 */

describe('getRandomViewByType', () => {
    jest.setTimeout(60_000);
    const context = new ClickUpContext();
    let team: Awaited<ReturnType<ClickUpContext['provideTeam']>>;
    let projectId: string;
    let categoryId: string;
    let subcategoryId: string;
    let projectViewId: string;
    let categoryViewIdA: string;
    let categoryViewIdB: string;
    let subcategoryViewId: string;
    let admin: ProvidedUser;
    let member: ProvidedUser;

    beforeAll(async () => {
        const usersContext = await bootstrapDashboardsUsersContext(context);
        admin = await usersContext.admin;
        member = await usersContext.member;
        team = usersContext.team;
        const teamId = team.id;
        projectId = (
            await context.provideProject(
                {
                    teamId,
                },
                { client: admin.client }
            )
        ).id;
        categoryId = (await context.provideCategory({ projectId }, { client: admin.client })).id;
        subcategoryId = (await context.provideSubcategory({ categoryId }, { client: admin.client })).id;
        projectViewId = (
            await context.provideView(
                {
                    name: 'Standard list view',
                    parentProjectId: projectId,
                    viewType: 1,
                },
                { client: admin.client }
            )
        ).id;
        categoryViewIdA = (
            await context.provideView(
                {
                    name: 'List view',
                    parentCategoryId: categoryId,
                    viewType: 2,
                },
                { client: admin.client }
            )
        ).id;
        categoryViewIdB = (
            await context.provideView(
                {
                    name: 'List view 2',
                    parentCategoryId: categoryId,
                    viewType: 2,
                },
                { client: admin.client }
            )
        ).id;
        subcategoryViewId = (
            await context.provideView(
                {
                    name: 'Table view',
                    parentSubcategoryId: subcategoryId,
                    viewType: 23,
                    visibility: 2,
                },
                { client: admin.client }
            )
        ).id;
    });

    it('should return a undefined if there is no view with such view type', async () => {
        const boxView = await getRandomViewByType(admin.id, team.id, 3);
        expect(boxView).toBeUndefined();
    });

    it('should return a view data if there is a view type and user has access to it', async () => {
        const listView = await getRandomViewByType(member.id, team.id, 1);
        expect(listView).toEqual({
            parent_id: projectId,
            parent_type: '4',
            view_id: projectViewId,
        });
    });

    it('should return undefined if user does not have access to view', async () => {
        const tableView = await getRandomViewByType(member.id, team.id, 23);
        expect(tableView).toBeUndefined();
    });

    it('should return random view if there are multiple views with the same view type', async () => {
        const listView = await getRandomViewByType(member.id, team.id, 2);
        expect(listView.parent_type).toEqual('5');
        expect(listView.parent_id).toEqual(categoryId);
        expect([categoryViewIdA, categoryViewIdB].includes(listView.view_id)).toBeTruthy();
    });
});
