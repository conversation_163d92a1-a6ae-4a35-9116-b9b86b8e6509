/**
 * Unit test for the document tag query factory used to manage document tags
 *
 * @group unit/models/views/documents
 */

jest.mock('node-uuid', () => {
    return {
        v4: jest.fn(() => 'abcdef01-2345-2abc-9876-123456fedcba'),
    };
});

import * as factory from '../../../../../../src/models/views/documents/factories/documentTagQueryFactory';

import {
    DocumentTagCreateOptions,
    DocumentTagEditOptions,
} from '../../../../../../src/models/views/documents/interfaces/DocumentTagOptions';

describe('document tag query factory', () => {
    beforeAll(() => {
        jest.useFakeTimers({ now: 1466424490000 });
    });

    afterAll(() => {
        jest.useRealTimers();
    });

    describe('prepareCreateTagInserts', () => {
        it('prepares the insert query for a public tag', () => {
            const createOptions: DocumentTagCreateOptions = {
                name: 'test',
                color_bg: '#000000',
                color_fg: '#ffffff',
                private: false,
            };

            const inserts = factory.prepareCreateTagInserts(300001, 1, createOptions);

            expect(inserts).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "params": Array [
                      "abcdef01-2345-2abc-9876-123456fedcba",
                      1,
                      1,
                      "test",
                      "#ffffff",
                      "#000000",
                      false,
                    ],
                    "query": "
                            INSERT INTO task_mgmt.team_tags (id, team_id, type, name, color_fg, color_bg, private, deleted)
                            VALUES ($1, $2, $3, $4, $5, $6, $7, FALSE)
                            RETURNING id, team_id, name, color_fg, color_bg, private
                        ",
                  },
                ]
            `);
        });

        it('prepares the insert queries for a private tag', () => {
            const createOptions: DocumentTagCreateOptions = {
                name: 'test',
                color_bg: '#000000',
                color_fg: '#ffffff',
                private: true,
            };

            const inserts = factory.prepareCreateTagInserts(300001, 1, createOptions);

            expect(inserts).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "params": Array [
                      "abcdef01-2345-2abc-9876-123456fedcba",
                      1,
                      1,
                      "test",
                      "#ffffff",
                      "#000000",
                      true,
                    ],
                    "query": "
                            INSERT INTO task_mgmt.team_tags (id, team_id, type, name, color_fg, color_bg, private, deleted)
                            VALUES ($1, $2, $3, $4, $5, $6, $7, FALSE)
                            RETURNING id, team_id, name, color_fg, color_bg, private
                        ",
                  },
                  Object {
                    "params": Array [
                      "abcdef01-2345-2abc-9876-123456fedcba",
                      300001,
                      1,
                    ],
                    "query": "
                                INSERT INTO task_mgmt.team_tag_members (tag_id, userid, workspace_id)
                                VALUES ($1, $2, $3)
                            ",
                  },
                ]
            `);
        });
    });

    describe('prepareDeleteTagQueries', () => {
        it('prepare the 3 delete queries used for any document tag', () => {
            const queries = factory.prepareDeleteTagQueries('tag-001', 300001);

            expect(queries).toMatchInlineSnapshot(`
                Array [
                  Object {
                    "params": Array [
                      1466424490000,
                      300001,
                      "tag-001",
                    ],
                    "query": "
                            UPDATE task_mgmt.team_tags
                            SET deleted = TRUE, date_deleted = $1, deleted_by = $2
                            WHERE id = $3
                        ",
                  },
                  Object {
                    "params": Array [
                      "tag-001",
                    ],
                    "query": "
                            DELETE FROM task_mgmt.team_tag_members
                            WHERE tag_id = $1
                        ",
                  },
                  Object {
                    "params": Array [
                      "tag-001",
                    ],
                    "query": "
                            DELETE FROM task_mgmt.document_tags
                            WHERE tag_id = $1
                        ",
                  },
                ]
            `);
        });
    });

    describe('prepareUpdateTagQuery', () => {
        it('updates the name when passed in as an option', () => {
            const updateOptions: DocumentTagEditOptions = {
                name: 'updated name',
            };

            const query = factory.prepareUpdateTagQuery('tag-001', updateOptions);

            expect(query).toMatchInlineSnapshot(`
                Object {
                  "params": Array [
                    "updated name",
                    "tag-001",
                  ],
                  "query": "UPDATE task_mgmt.team_tags SET name = $1
                        WHERE id = $2
                        RETURNING id, team_id, name, color_bg, color_fg, private
                    ",
                }
            `);
        });

        it('updates the foreground color when passed in as an option', () => {
            const updateOptions: DocumentTagEditOptions = {
                color_fg: '#123456',
            };

            const query = factory.prepareUpdateTagQuery('tag-001', updateOptions);

            expect(query).toMatchInlineSnapshot(`
                Object {
                  "params": Array [
                    "#123456",
                    "tag-001",
                  ],
                  "query": "UPDATE task_mgmt.team_tags SET color_fg = $1
                        WHERE id = $2
                        RETURNING id, team_id, name, color_bg, color_fg, private
                    ",
                }
            `);
        });

        it('updates the background color when passed in as an option', () => {
            const updateOptions: DocumentTagEditOptions = {
                color_bg: '#654321',
            };

            const query = factory.prepareUpdateTagQuery('tag-001', updateOptions);

            expect(query).toMatchInlineSnapshot(`
                Object {
                  "params": Array [
                    "#654321",
                    "tag-001",
                  ],
                  "query": "UPDATE task_mgmt.team_tags SET color_bg = $1
                        WHERE id = $2
                        RETURNING id, team_id, name, color_bg, color_fg, private
                    ",
                }
            `);
        });

        it('updates all possible values when passed in as an option', () => {
            const updateOptions: DocumentTagEditOptions = {
                name: 'updated name',
                color_fg: '#123456',
                color_bg: '#654321',
            };

            const query = factory.prepareUpdateTagQuery('tag-001', updateOptions);

            expect(query).toMatchInlineSnapshot(`
                Object {
                  "params": Array [
                    "updated name",
                    "#654321",
                    "#123456",
                    "tag-001",
                  ],
                  "query": "UPDATE task_mgmt.team_tags SET name = $1, color_bg = $2, color_fg = $3
                        WHERE id = $4
                        RETURNING id, team_id, name, color_bg, color_fg, private
                    ",
                }
            `);
        });

        it('produces no query when nothing is specified in update options', () => {
            const query = factory.prepareUpdateTagQuery('tag-001', {});
            expect(query).toEqual({});
        });
    });
});
