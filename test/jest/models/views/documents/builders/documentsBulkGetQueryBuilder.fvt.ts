/**
 * Functional document search query builder test
 * Require server running
 * @group functional/models/views/documents/builders
 */

import config from 'config';
import { ViewsConfig } from '../../../../../../src/config/interfaces/ViewsConfig';
import ClickUpContext from '../../../../../../src/jest-context/ClickUpContext';
import { DocumentBulkGetQueryBuilder } from '../../../../../../src/models/views/documents/builders/documentBulkGetQueryBuilder';
import * as db from '../../../../../../src/utils/db';

describe('DocumentsBulkGetQueryBuilder functional tests', () => {
    const { visibility }: ViewsConfig = config.get('views');
    describe('bulk get by id', () => {
        it('should return empty array - no documents', async () => {
            const ctx = new ClickUpContext();
            const { id: teamId } = await ctx.provideTeam();
            const { id: userId } = await ctx.provideUser();
            const { query, params } = DocumentBulkGetQueryBuilder.create({
                teamId,
                userId,
                docIds: [],
            }).getQueryObject();
            const { rows } = await db.promiseReadQuery(query, params);
            expect(rows).toHaveLength(0);
        });

        it('should return only existing documents', async () => {
            const ctx = new ClickUpContext();
            const { id: teamId } = await ctx.provideTeam();
            const { id: userId } = await ctx.provideUser();
            const { id: docId } = await ctx.provideDoc({
                parentTeamId: teamId,
                visibility: visibility.private,
            });
            const { query, params } = DocumentBulkGetQueryBuilder.create({
                teamId,
                userId,
                docIds: [docId, 'non-existing-id'],
            }).getQueryObject();
            const { rows } = await db.promiseReadQuery(query, params);
            expect(rows).toHaveLength(1);
        });
    });
});
