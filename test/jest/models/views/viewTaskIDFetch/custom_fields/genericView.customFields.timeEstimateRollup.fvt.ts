import config from 'config';
import { Task } from '@clickup-legacy/jest-context/providers/tasks/provideTask';
import { ProvidedUser } from '@clickup-legacy/jest-context/providers/users/provideUser';
import { Subcategory } from '@clickup-legacy/jest-context/providers/subcategories/subcategory.model';
import { CustomField } from '@clickup-legacy/jest-context/providers/custom_fields/provideCustomField';
import { ProvideViewResponse } from '@clickup-legacy/jest-context/providers/views/provideView';
import ClickUpContext from '@clickup-legacy/jest-context/ClickUpContext';
import { TEAM_ROLES } from '@clickup-legacy/jest-context/providers/teams/teamAddUser';
import { DropdownOption } from '@clickup-legacy/models/field/interfaces/DropdownOption';
import { _getGenericViewAsync } from '@clickup-legacy/models/views/viewTaskIDFetch';

/**
 * @group functional/models/views
 * @group genericView
 */
describe('Generic View with Time Estimate field is requested', () => {
    let context;
    let tasks: Map<string, Task>;
    let users: Map<string, ProvidedUser>;
    let teamId: string;
    let lists: Map<string, Subcategory>;
    let view: ProvideViewResponse;

    beforeAll(async () => {
        context = new ClickUpContext();
        const team = await context.provideTeam();
        teamId = team.id;

        await teamMembersExists([
            { username: 'user1', role: TEAM_ROLES.admin },
            { username: 'user2', role: TEAM_ROLES.user },
        ]);

        await hierarchyExists({
            lists: [{ name: 'list1', creatorName: 'user1', time_estimates: true }],
        });

        await tasksExist([
            { name: 'task without value', listName: 'list1' },
            { name: 'task with a value', listName: 'list1', time_estimate: 300 },
            { name: 'task with another value', listName: 'list1', time_estimate: 30 },
            { name: 'another task without a value', listName: 'list1' },
        ]);

        view = await context.provideView(
            {
                parentSubcategoryId: lists.get('list1').id,
                viewType: config.get('views.view_types.list'),
            },
            { client: users.get('user1').client }
        );
    });

    describe('calculation is requested', () => {
        let column: object;

        beforeAll(() => {
            column = {
                field: 'timeEstimateRollup',
                width: 160,
                hidden: false,
            };
        });

        it('should return correct values count', async () => {
            const result = await _getGenericViewAsync(
                users.get('user1').id,
                {
                    ...view,
                    columns: {
                        ...view.columns,
                        fields: [
                            ...view.columns.fields,
                            {
                                ...column,
                                calculation: {
                                    func: 'valuescount',
                                    unit: '',
                                },
                            },
                        ],
                    },
                },
                {}
            );

            assertGroups(result, [
                {
                    id: undefined,
                    tasks: [
                        'task without value',
                        'task with a value',
                        'task with another value',
                        'another task without a value',
                    ],
                    calculations: {
                        timeEstimateRollup: { valuescount: 2 },
                    },
                },
            ]);

            expect(result.overall_calculations).toStrictEqual([
                {
                    field: 'timeEstimateRollup',
                    value: 2,
                    func: 'valuescount',
                    unit: '',
                },
            ]);
        });

        it('should return correct unique values count', async () => {
            const result = await _getGenericViewAsync(
                users.get('user1').id,
                {
                    ...view,
                    columns: {
                        ...view.columns,
                        fields: [
                            ...view.columns.fields,
                            {
                                ...column,
                                calculation: {
                                    func: 'uniquevaluescount',
                                    unit: '',
                                },
                            },
                        ],
                    },
                },
                {}
            );

            assertGroups(result, [
                {
                    id: undefined,
                    tasks: [
                        'task without value',
                        'task with a value',
                        'task with another value',
                        'another task without a value',
                    ],
                    calculations: {
                        timeEstimateRollup: { uniquevaluescount: 2 },
                    },
                },
            ]);

            expect(result.overall_calculations).toStrictEqual([
                {
                    field: 'timeEstimateRollup',
                    value: 2,
                    func: 'uniquevaluescount',
                    unit: '',
                },
            ]);
        });

        it('should return correct empty count', async () => {
            const result = await _getGenericViewAsync(
                users.get('user1').id,
                {
                    ...view,
                    columns: {
                        ...view.columns,
                        fields: [
                            ...view.columns.fields,
                            {
                                ...column,
                                calculation: {
                                    func: 'emptycount',
                                    unit: '',
                                },
                            },
                        ],
                    },
                },
                {}
            );

            assertGroups(result, [
                {
                    id: undefined,
                    tasks: [
                        'task without value',
                        'task with a value',
                        'task with another value',
                        'another task without a value',
                    ],
                    calculations: {
                        timeEstimateRollup: { emptycount: 2 },
                    },
                },
            ]);

            expect(result.overall_calculations).toStrictEqual([
                {
                    field: 'timeEstimateRollup',
                    value: 2,
                    func: 'emptycount',
                    unit: '',
                },
            ]);
        });

        it('should return correct empty percent', async () => {
            const result = await _getGenericViewAsync(
                users.get('user1').id,
                {
                    ...view,
                    columns: {
                        ...view.columns,
                        fields: [
                            ...view.columns.fields,
                            {
                                ...column,
                                calculation: {
                                    func: 'emptypercent',
                                    unit: '',
                                },
                            },
                        ],
                    },
                },
                {}
            );

            assertGroups(result, [
                {
                    id: undefined,
                    tasks: [
                        'task without value',
                        'task with a value',
                        'task with another value',
                        'another task without a value',
                    ],
                    calculations: {
                        timeEstimateRollup: { emptypercent: 50 },
                    },
                },
            ]);

            expect(result.overall_calculations).toStrictEqual([
                {
                    field: 'timeEstimateRollup',
                    value: 50,
                    func: 'emptypercent',
                    unit: '',
                },
            ]);
        });

        it('should return correct not empty percent', async () => {
            const result = await _getGenericViewAsync(
                users.get('user1').id,
                {
                    ...view,
                    columns: {
                        ...view.columns,
                        fields: [
                            ...view.columns.fields,
                            {
                                ...column,
                                calculation: {
                                    func: 'notemptypercent',
                                    unit: '',
                                },
                            },
                        ],
                    },
                },
                {}
            );

            assertGroups(result, [
                {
                    id: undefined,
                    tasks: [
                        'task without value',
                        'task with a value',
                        'task with another value',
                        'another task without a value',
                    ],
                    calculations: {
                        timeEstimateRollup: { notemptypercent: 50 },
                    },
                },
            ]);

            expect(result.overall_calculations).toStrictEqual([
                {
                    field: 'timeEstimateRollup',
                    value: 50,
                    func: 'notemptypercent',
                    unit: '',
                },
            ]);
        });

        it('should return correct count per group', async () => {
            const result = await _getGenericViewAsync(
                users.get('user1').id,
                {
                    ...view,
                    columns: {
                        ...view.columns,
                        fields: [
                            ...view.columns.fields,
                            {
                                ...column,
                                calculation: {
                                    func: 'countpergroup',
                                    selected_value: 30,
                                    unit: '',
                                },
                            },
                        ],
                    },
                },
                {}
            );

            assertGroups(result, [
                {
                    id: undefined,
                    tasks: [
                        'task without value',
                        'task with a value',
                        'task with another value',
                        'another task without a value',
                    ],
                    calculations: {
                        timeEstimateRollup: { countpergroup: 1, count: 4 },
                    },
                },
            ]);

            expect(result.overall_calculations).toStrictEqual([
                {
                    field: 'timeEstimateRollup',
                    value: [1, 4],
                    func: 'countpergroup',
                    selected_value: 30,
                    unit: '',
                },
            ]);
        });

        it('should return correct percent per group', async () => {
            const result = await _getGenericViewAsync(
                users.get('user1').id,
                {
                    ...view,
                    columns: {
                        ...view.columns,
                        fields: [
                            ...view.columns.fields,
                            {
                                ...column,
                                calculation: {
                                    func: 'percentpergroup',
                                    selected_value: 30,
                                    unit: '',
                                },
                            },
                        ],
                    },
                },
                {}
            );

            assertGroups(result, [
                {
                    id: undefined,
                    tasks: [
                        'task without value',
                        'task with a value',
                        'task with another value',
                        'another task without a value',
                    ],
                    calculations: {
                        timeEstimateRollup: { percentpergroup: 25 },
                    },
                },
            ]);

            expect(result.overall_calculations).toStrictEqual([
                {
                    field: 'timeEstimateRollup',
                    value: 25,
                    func: 'percentpergroup',
                    selected_value: 30,
                    unit: '',
                },
            ]);
        });
    });

    function assertGroups(viewResult: any, expectedGroups: { id: string; tasks: string[]; calculations?: object }[]) {
        expect(viewResult.list.groups.length).toBe(expectedGroups.length);

        expectedGroups.forEach((expectedGroup, idx) => {
            const group = viewResult.list.groups[idx];
            expect(group.id).toBe(expectedGroup.id);
            expect(group.task_ids.length).toBe(expectedGroup.tasks.length);
            expect(group.task_ids).toStrictEqual(expectedGroup.tasks.map(taskName => tasks.get(taskName).id));
            if (expectedGroup.calculations) {
                expect(group.group_calculations).toStrictEqual(expectedGroup.calculations);
            }
        });
    }

    async function teamMembersExists(
        teamMembers: { username: string; role: typeof TEAM_ROLES[keyof typeof TEAM_ROLES] }[]
    ) {
        users = new Map<string, ProvidedUser>();

        for (const teamMember of teamMembers) {
            const user = await context.provideUser({ username: teamMember.username });
            await context.teamAddUser(teamId, user.id, teamMember.role);

            users.set(teamMember.username, user);
        }
    }

    async function tasksExist(taskDefinitions: { name: string; listName: string; time_estimate?: number }[]) {
        tasks = new Map<string, Task>();

        for (const { name, listName, time_estimate } of taskDefinitions) {
            const task = await context.provideTask({
                subcategoryId: lists.get(listName).id,
                time_estimate,
                name,
            });

            tasks.set(name, task);
        }
    }

    async function hierarchyExists(hierarchyDefinition: {
        lists?: { name: string; creatorName: string; time_estimates: boolean }[];
    }) {
        lists = new Map<string, Subcategory>();

        const { id: projectId } = await context.provideProject({
            features: {
                time_estimates: { enabled: true, rollup: true },
            },
        });
        const { id: categoryId } = await context.provideCategory({ projectId });

        for (const { name, creatorName } of hierarchyDefinition.lists ?? []) {
            const subcategory = await context.provideSubcategory(
                { name, categoryId },
                { client: users.get(creatorName).client }
            );
            lists.set(name, subcategory);
        }
    }
});
