/**
 * Unit tests for view group member service.
 *
 * @group unit/models/views/group_members
 */

import '../../../../_mocks/utilsMocks';
import '../../../../_mocks/viewsMocks';
import '../../../../_mocks/groupMocks';

jest.mock('../../../../../../src/models/views/members/datastores/viewMemberDatastore');
jest.mock('../../../../../../src/models/views/group_members/datastores/viewGroupMemberDatastore');
jest.mock('../../../../../../src/models/views/members/helpers', () => {
    return {
        createViewSharedHistoryItems: jest.fn(),
    };
});
jest.mock('../../../../../../src/utils/entities/services/entitiesService', () => {
    return {
        getTeamId: jest.fn(),
    };
});

import * as errors from '../../../../../../src/utils/errors';
import { checkAccessViewAsync } from '../../../../../../src/utils/access2';

import * as groupDatastore from '../../../../../../src/models/views/group_members/datastores/viewGroupMemberDatastore';
import * as memberDatastore from '../../../../../../src/models/views/members/datastores/viewMemberDatastore';
import { queryViewsInfo } from '../../../../../../src/models/views/datastores/CRUD/viewDataStore';
import { createViewSharedHistoryItems } from '../../../../../../src/models/views/members/helpers';

import * as groupMod from '../../../../../../src/models/groups';
import * as helpers from '../../../../../../src/models/helpers';

import * as viewGroupMemberService from '../../../../../../src/models/views/group_members/services/viewGroupMemberService';
import { GroupMemberResults } from '../../../../../../src/models/groups/interfaces/formatters/GroupModels';

const USERID = 123;
const VIEW_ID = 'v-001';
const GROUP_ID = 'g-001';

describe('View member service', () => {
    const mockNamedError = (errors as any).mockError as jest.Mock;
    const mockedGroupDatastore = jest.mocked(groupDatastore);
    const mockedMemberDatastore = jest.mocked(memberDatastore);
    const mockedGroupMod = jest.mocked(groupMod);
    const mockedCheckAccessView = jest.mocked(checkAccessViewAsync);
    const mockedGroupHelpers = jest.mocked(helpers);
    const mockedQueryViewsInfo = jest.mocked(queryViewsInfo);

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getMembers', () => {
        it('get group members error', async () => {
            mockedGroupDatastore.queryMembersByView.mockRejectedValue('some error');
            await expect(viewGroupMemberService.getMembers([VIEW_ID])).rejects.toThrowErrorMatchingInlineSnapshot(
                `undefined`
            );
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "some error",
                    "VIEWM_014",
                  ],
                ]
            `);
        });

        it('no group members', async () => {
            mockedGroupDatastore.queryMembersByView.mockResolvedValue({ rows: [] });
            const result = await viewGroupMemberService.getMembers([VIEW_ID]);
            expect(mockedGroupMod.promiseGetUserGroupsByIds).not.toBeCalled();
            expect(result).toEqual({});
        });

        it('get group members', async () => {
            const expected = {
                [`${GROUP_ID}`]: [
                    { group: { id: GROUP_ID }, date_added: '123', permission_level: Number, permissions: {} },
                ],
            } as unknown as GroupMemberResults;
            mockedGroupDatastore.queryMembersByView.mockResolvedValue({ rows: [{ group_id: GROUP_ID } as any] });
            mockedGroupMod.promiseGetUserGroupsByIds.mockResolvedValue({ groups: [{ members: [{ id: USERID }] }] });
            mockedMemberDatastore.queryMembersByUserView.mockResolvedValue({ rows: [] });
            mockedGroupHelpers.formatGroupMembersWithPermissions.mockReturnValue(expected);
            const result = await viewGroupMemberService.getMembers([VIEW_ID]);
            expect(mockedMemberDatastore.queryMembersByUserView).toBeCalledWith([VIEW_ID], [USERID]);
            expect(result).toEqual(expected);
        });
    });

    describe('addMember', () => {
        it('no group id error', async () => {
            await expect(
                viewGroupMemberService.addMember(USERID, VIEW_ID, null, 5)
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "Invalid group id",
                    "VIEWM_020",
                    400,
                  ],
                  Array [
                    Object {
                      "ECODE": "VIEWM_020",
                      "logError": [MockFunction],
                      "msg": "Invalid group id",
                      "statusCode": 400,
                    },
                    "VIEWM_003",
                  ],
                ]
            `);
        });

        it('no permission level error', async () => {
            mockedGroupDatastore.queryMember.mockResolvedValue({ rows: [] });
            await expect(
                viewGroupMemberService.addMember(USERID, VIEW_ID, GROUP_ID, null)
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "Invalid permission level",
                    "VIEWM_018",
                    400,
                  ],
                  Array [
                    Object {
                      "ECODE": "VIEWM_018",
                      "logError": [MockFunction],
                      "msg": "Invalid permission level",
                      "statusCode": 400,
                    },
                    "VIEWM_003",
                  ],
                ]
            `);
        });

        it('already shared with team error', async () => {
            mockedQueryViewsInfo.mockResolvedValueOnce({
                rows: [{}],
            });
            mockedGroupDatastore.queryMember.mockResolvedValue({ rows: [{ group_id: GROUP_ID } as any] });
            await expect(
                viewGroupMemberService.addMember(USERID, VIEW_ID, GROUP_ID, 5)
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "Already shared with team",
                    "VIEWM_019",
                    400,
                  ],
                  Array [
                    Object {
                      "ECODE": "VIEWM_019",
                      "logError": [MockFunction],
                      "msg": "Already shared with team",
                      "statusCode": 400,
                    },
                    "VIEWM_003",
                  ],
                ]
            `);
        });

        it('add group with higher permission level error', async () => {
            mockedGroupDatastore.queryMember.mockResolvedValueOnce({ rows: [] });
            mockedQueryViewsInfo.mockResolvedValueOnce({
                rows: [{}],
            });
            mockedCheckAccessView.mockResolvedValueOnce({ permission_level: 4 });
            await expect(
                viewGroupMemberService.addMember(USERID, VIEW_ID, GROUP_ID, 5)
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "Cannot add a group with a higher permission level than you",
                    "VIEWM_020",
                    400,
                  ],
                  Array [
                    Object {
                      "ECODE": "VIEWM_020",
                      "logError": [MockFunction],
                      "msg": "Cannot add a group with a higher permission level than you",
                      "statusCode": 400,
                    },
                    "VIEWM_003",
                  ],
                ]
            `);
        });

        it('add member', async () => {
            mockedGroupDatastore.queryMember.mockResolvedValueOnce({ rows: [] });
            mockedCheckAccessView.mockResolvedValueOnce({ permission_level: 5 });
            mockedQueryViewsInfo.mockResolvedValueOnce({
                rows: [{ type: 9 }],
            });
            mockedGroupMod.promiseGetUserGroup.mockResolvedValue({ members: [{ id: USERID }] });
            mockedMemberDatastore.queryMembersByUserView.mockResolvedValueOnce({ rows: [{ userid: -1 } as any] });
            mockedGroupHelpers.convertArrayToObjectWithKeys.mockReturnValue({});
            const result = await viewGroupMemberService.addMember(USERID, VIEW_ID, GROUP_ID, 5);
            expect(createViewSharedHistoryItems as jest.Mock).toBeCalled();
            expect(result.permission_level).toEqual(5);
        });
    });

    describe('removeMember', () => {
        it('no group id error', async () => {
            await expect(
                viewGroupMemberService.deleteMember(USERID, VIEW_ID, null)
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "Invalid group id",
                    "VIEWM_021",
                    500,
                  ],
                  Array [
                    Object {
                      "ECODE": "VIEWM_021",
                      "logError": [MockFunction],
                      "msg": "Invalid group id",
                      "statusCode": 500,
                    },
                    "VIEWM_012",
                  ],
                ]
            `);
        });

        it('team is not member of view error', async () => {
            mockedGroupDatastore.queryMember.mockResolvedValue({ rows: [] });
            await expect(
                viewGroupMemberService.deleteMember(USERID, VIEW_ID, GROUP_ID)
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "Team is not a member of view",
                    "VIEWM_022",
                    400,
                  ],
                  Array [
                    Object {
                      "ECODE": "VIEWM_022",
                      "logError": [MockFunction],
                      "msg": "Team is not a member of view",
                      "statusCode": 400,
                    },
                    "VIEWM_012",
                  ],
                ]
            `);
        });

        it('delete members', async () => {
            mockedGroupDatastore.queryMember.mockResolvedValue({ rows: [{ group_id: GROUP_ID } as any] });
            await viewGroupMemberService.deleteMember(USERID, VIEW_ID, GROUP_ID);
            expect(mockedGroupDatastore.deleteMembers).toBeCalled();
        });
    });

    describe('editMember', () => {
        it('edit member fail', async () => {
            mockedCheckAccessView.mockRejectedValueOnce('some error');
            await expect(
                viewGroupMemberService.editMember(USERID, VIEW_ID, { group_id: GROUP_ID, permission_level: 5 })
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "some error",
                    "VIEWM_023",
                  ],
                ]
            `);
        });

        it('skip admin edit group perm check', async () => {
            await viewGroupMemberService.editMember(USERID, VIEW_ID, {
                skip_access: true,
                group_id: GROUP_ID,
                permission_level: 5,
            });
            expect(mockedGroupMod.checkAdminEditGroupPermissions).not.toBeCalled();
        });

        it('edit member', async () => {
            await viewGroupMemberService.editMember(USERID, VIEW_ID, {
                group_id: GROUP_ID,
                permission_level: 5,
            });
            expect(mockedGroupMod.checkAdminEditGroupPermissions).toBeCalled();
            expect(mockedGroupDatastore.editMember).toBeCalled();
        });
    });

    describe('deleteMembers', () => {
        it('delete views members fail', async () => {
            mockedCheckAccessView.mockRejectedValueOnce('some error');
            await expect(
                viewGroupMemberService.deleteMembers(USERID, VIEW_ID, [GROUP_ID])
            ).rejects.toThrowErrorMatchingInlineSnapshot(`undefined`);
            expect(mockNamedError.mock.calls).toMatchInlineSnapshot(`
                Array [
                  Array [
                    "some error",
                    "VIEM_028",
                  ],
                ]
            `);
        });

        it('delete views members', async () => {
            const result = await viewGroupMemberService.deleteMembers(USERID, VIEW_ID, [GROUP_ID]);
            expect(result).toEqual(undefined);
        });
    });
});
