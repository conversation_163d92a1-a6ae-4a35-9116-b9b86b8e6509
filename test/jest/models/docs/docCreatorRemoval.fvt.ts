/**
 * @group functional/models/docs
 */

import ClickUpContext from '@clickup-legacy/jest-context/ClickUpContext';
import { TEAM_ROLES } from '@clickup-legacy/jest-context/providers/teams/teamAddUser';
import { writeAsync } from '@clickup-legacy/utils/db2';

const VISIBILITY_PRIVATE = 2;

describe('Doc creator removal', () => {
    test('Guest users cannot remove doc creators', async () => {
        const context = new ClickUpContext();

        const guestUser = await context.provideUser({ username: 'guestUser' });
        const mainUser = await context.provideUser({ username: 'mainUser' });

        const team = await context.provideTeam();
        await context.teamAddUser(team.id, guestUser.id, TEAM_ROLES.guest);

        const doc = await context.provideDoc(
            {
                name: 'new doc',
                parentUnparentedTeamId: team.id,
                visibility: VISIBILITY_PRIVATE,
                members: [{ user: { id: guestUser.id }, permission_level: 5 }],
            },
            { client: mainUser.client }
        );

        const runUpdate = async () => {
            await context.updateView(
                {
                    id: doc.id,
                    name: 'updated doc',
                    visibility: VISIBILITY_PRIVATE,
                    parentUnparentedTeamId: team.id,
                    keep_creator: false, // This would remove the creator
                },
                undefined,
                {
                    client: guestUser.client,
                }
            );
        };

        // Expect an error, as the guest user cannot remove the creator
        let error: any;
        try {
            await runUpdate();
        } catch (err) {
            error = err;
        }
        expect(error).toBeDefined();
        const errorResponse = JSON.parse(error.res.text);
        expect(errorResponse.err).toEqual('You are not allowed to remove view creators');
        expect(errorResponse.ECODE).toEqual('VIEWS_089');

        // Now update the doc, so that the guest user is the creator. The doc should now be updated and the creator should be kept anyway
        await writeAsync('UPDATE task_mgmt.views SET creator = $1 WHERE view_id = $2', [guestUser.id, doc.id]);
        await runUpdate();
        const updatedDoc = await context.viewGet(doc.id);
        expect(updatedDoc.name).toEqual('updated doc');
        expect(updatedDoc.creator).toEqual(guestUser.id);
    });
});
