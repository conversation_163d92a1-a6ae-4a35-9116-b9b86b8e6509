import { FollowerInfoService } from '../../../../../src/models/shared_entity_util/services/followerInfo.service';
import { getUsersAsync } from '../../../../../src/models/user/userProvider';
import { Follower } from '../../../../../src/models/shared_entity_util/models/follower.model';

jest.mock('../../../../../src/models/user/userProvider', () => ({
    getUsersAsync: jest.fn(),
}));

describe('FollowerInfoService', () => {
    const followerInfoService = new FollowerInfoService();
    const getUsersAsyncMock = jest.mocked(getUsersAsync);

    const MOCK_NOW = 1733784185000;

    beforeEach(() => {
        getUsersAsyncMock.mockReset();
        jest.useFakeTimers({ now: MOCK_NOW });
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers();
    });

    it('should return all followers', async () => {
        const followerIds = [1, 2, 3];
        const users = followerIds.map(id => ({
            id,
            username: `name${id}`,
            email: `email${id}`,
            color: `color${id}`,
            date_joined: MOCK_NOW,
            profilePicture: `profile_picture_key${id}`,
        }));
        const expectedFollowers = followerIds.map(
            id => new Follower(id, `name${id}`, `email${id}`, `color${id}`, MOCK_NOW, `profile_picture_key${id}`)
        );

        getUsersAsyncMock.mockResolvedValue(Array.from(users.values()));

        const followers = await followerInfoService.getFollowers(followerIds);

        expect(followers).toEqual(expectedFollowers);
        expect(getUsersAsyncMock).toHaveBeenCalledWith(followerIds, null, true);
    });

    it('should return an empty array if getUsersAsync returns an empty object', async () => {
        const followerIds = [1, 2, 3];
        getUsersAsyncMock.mockResolvedValue({});

        const followers = await followerInfoService.getFollowers(followerIds);

        expect(followers).toEqual([]);
        expect(getUsersAsyncMock).toHaveBeenCalledWith(followerIds, null, true);
    });
});
