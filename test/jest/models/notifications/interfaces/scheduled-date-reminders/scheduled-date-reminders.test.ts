import {
    ScheduledDateReminderRequest,
    ReminderType,
} from '../../../../../../src/models/notifications/interfaces/scheduled-date-reminders';

describe('ScheduledDateReminderRequest', () => {
    describe('getErn', () => {
        it('should return the expected ERN', () => {
            // Arrange
            const taskId = '123';
            const type = ReminderType.DueDateReminder;
            const date = 1634567890;
            const shard = 'g001';
            const teamId = '456';
            const wasTimeProvided = true;
            const request = new ScheduledDateReminderRequest(type, date, shard, teamId, wasTimeProvided, taskId);

            // Act
            const ern = request.getErn();

            // Assert
            expect(ern).toBe(`clickup:task:${teamId}:${taskId}`);
        });
    });
});
