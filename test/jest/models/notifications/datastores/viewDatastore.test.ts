import '../../../_mocks/utilsMocks';

import * as db from '../../../../../src/utils/db';
import * as viewsDatastore from '../../../../../src/models/notifications/datastores/viewsDatastore';

describe('view datastores', () => {
    beforeEach(() => {
        // Making sure that the counters for mock calls are reset
        // and there's no interference between what should be return in previous test vs current test.
        jest.clearAllMocks();
    });

    describe('getViewFromViewId', () => {
        it('should call promiseReplicaQuery with the right argument and return no error', async () => {
            // Given
            const view_id = 444999;
            (db.promiseReplicaQuery as jest.Mock).mockResolvedValue({ rows: [] });
            // When
            const result = await viewsDatastore.getViewFromViewId(view_id);
            // Then
            expect(db.promiseReplicaQuery).toHaveBeenCalledWith(viewsDatastore.GET_VIEW_FROM_VIEW_ID_QUERY, [view_id]);
            expect(result).toMatchInlineSnapshot(`
                Object {
                  "rows": Array [],
                }
            `);
        });

        it('should return an error when promiseReplicaQuery fails', async () => {
            // Given
            const view_id = 999444;
            (db.promiseReplicaQuery as jest.Mock).mockRejectedValue('some error');
            // When
            await expect(viewsDatastore.getViewFromViewId(view_id)).rejects.toMatchInlineSnapshot(`"some error"`);
            // Then
            expect(db.promiseReplicaQuery).toHaveBeenCalledWith(viewsDatastore.GET_VIEW_FROM_VIEW_ID_QUERY, [view_id]);
        });
    });
});
