// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Generate Invoice Endpoint Tests should return success for generating an invoice for business workspace. 1`] = `
"
<html lang='en'>

<head>
    <meta charset='utf-8' />
    <meta http-equiv='X-UA-Compatible' content='IE=edge' />
    <meta name='viewport' content='width=device-width, initial-scale=0.5' />
    <title>Invoice</title>

    <style type='text/css'>
        html {
            font-family: sans-serif;
            line-height: 1.15;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
        }

        body {
            margin: 0;
        }

        @page {
            margin: 1in 0;
        }

        @page :first {
            margin-top: 0;
        }

        article,
        aside,
        footer,
        header,
        nav,
        section {
            display: block;
        }

        h1 {
            font-size: 2em;
            margin: 0.67em 0;
        }

        figcaption,
        figure,
        main {
            display: block;
        }

        figure {
            margin: 1em 40px;
        }

        hr {
            -webkit-box-sizing: content-box;
            box-sizing: content-box;
            height: 0;
            overflow: visible;
        }

        pre {
            font-family: monospace, monospace;
            font-size: 1em;
        }

        a {
            background-color: transparent;
            -webkit-text-decoration-skip: objects;
        }

        a:active,
        a:hover {
            outline-width: 0;
        }

        abbr[title] {
            border-bottom: none;
            text-decoration: underline;
            -webkit-text-decoration: underline dotted;
            text-decoration: underline dotted;
        }

        b,
        strong {
            font-weight: inherit;
        }

        b,
        strong {
            font-weight: bolder;
        }

        code,
        kbd,
        samp {
            font-family: monospace, monospace;
            font-size: 1em;
        }

        dfn {
            font-style: italic;
        }

        mark {
            background-color: #ff0;
            color: #000;
        }

        small {
            font-size: 80%;
        }

        sub,
        sup {
            font-size: 75%;
            line-height: 0;
            position: relative;
            vertical-align: baseline;
        }

        sub {
            bottom: -0.25em;
        }

        sup {
            top: -0.5em;
        }

        audio,
        video {
            display: inline-block;
        }

        audio:not([controls]) {
            display: none;
            height: 0;
        }

        img {
            border-style: none;
        }

        svg:not(:root) {
            overflow: hidden;
        }

        button,
        input,
        optgroup,
        select,
        textarea {
            font-family: sans-serif;
            font-size: 100%;
            line-height: 1.15;
            margin: 0;
        }

        button,
        input {
            overflow: visible;
        }

        button,
        select {
            text-transform: none;
        }

        button,
        html [type='button'],
        [type='reset'],
        [type='submit'] {
            -webkit-appearance: button;
        }

        button::-moz-focus-inner,
        [type='button']::-moz-focus-inner,
        [type='reset']::-moz-focus-inner,
        [type='submit']::-moz-focus-inner {
            border-style: none;
            padding: 0;
        }

        button:-moz-focusring,
        [type='button']:-moz-focusring,
        [type='reset']:-moz-focusring,
        [type='submit']:-moz-focusring {
            outline: 1px dotted ButtonText;
        }

        fieldset {
            border: 1px solid #c0c0c0;
            margin: 0 2px;
            padding: 0.35em 0.625em 0.75em;
        }

        legend {
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            color: inherit;
            display: table;
            max-width: 100%;
            padding: 0;
            white-space: normal;
        }

        progress {
            display: inline-block;
            vertical-align: baseline;
        }

        textarea {
            overflow: auto;
        }

        [type='checkbox'],
        [type='radio'] {
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            padding: 0;
        }

        [type='number']::-webkit-inner-spin-button,
        [type='number']::-webkit-outer-spin-button {
            height: auto;
        }

        [type='search'] {
            -webkit-appearance: textfield;
            outline-offset: -2px;
        }

        [type='search']::-webkit-search-cancel-button,
        [type='search']::-webkit-search-decoration {
            -webkit-appearance: none;
        }

        ::-webkit-file-upload-button {
            -webkit-appearance: button;
            font: inherit;
        }

        details,
        menu {
            display: block;
        }

        summary {
            display: list-item;
        }

        canvas {
            display: inline-block;
        }

        template {
            display: none;
        }

        [hidden] {
            display: none;
        }

        /*! END normalize.css v5.0.0 */

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProLight.otf');
            font-weight: 300;
            font-style: normal;
        }

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProLightItalic.otf');
            font-weight: 300;
            font-style: italic;
        }

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProRegular.otf');
            font-weight: normal;
            font-style: normal;
        }

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProRegular.otf');
            font-weight: normal;
            font-style: italic;
        }

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProMedium.otf');
            font-weight: 500;
            font-style: normal;
        }

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProMediumItalic.otf');
            font-weight: 500;
            font-style: italic;
        }

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProBold.otf');
            font-weight: 600;
            font-style: normal;
        }

        @font-face {
            font-family: 'Gotham Pro';
            src: url('https://clickup.com/for-pdf-files/fonts/GothamProBoldItalic.otf');
            font-weight: 600;
            font-style: italic;
        }

        * {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }

        body {
            background-color: #ffffff;
            height: 100%;
            font-family: 'Gotham Pro', Helvetica, Arial, sans-serif;
            font-weight: 400;
            font-size: 1em;
            color: #222222;
        }

        .wrapper {
            width: 840px;
            margin: 0 auto;
        }

        .row_flex {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
        }

        .row_item {
            padding: 40px 20px 40px 40px;
            background: #ffffff;
            -webkit-box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.4);
            box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.4);
            border: 1px solid #ececec;
            position: relative;
            break-inside: avoid;
        }

        .row_item+.row_item {
            border-top: 0px;
        }

        .row_item .header {
            margin-bottom: 30px;
        }

        .row_info {
            margin-bottom: 35px;
            width: 840px;
            max-width: 840px;
        }

        .row_info_right {
            -ms-flex-negative: 0;
            flex-shrink: 0;
        }

        .section-header {
            background: #7b68ee;
            padding: 65px 0 55px;
            margin: 0 0 43px;
        }

        .section-header-block {
            width: 1100px;
            margin: 0 auto;
        }

        .section-header__row {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
        }

        .section-header__left {
            margin-top: 30px;
        }

        .section-header__logo {
            display: inline-block;
            height: 40px;
        }

        .section-header__divider {
            display: inline-block;
            width: 1px;
            height: 20px;
            margin: 0 30px 8px;
            border-right: 1px solid #cbacff;
        }

        .section-header__date {
            display: inline-block;
            color: #ffffff;
            font: 300 18px/18px 'Gotham Pro', Helvetica, Arial, serif;
            height: 42px;
            vertical-align: middle;
        }

        .section-header__title {
            color: #ffffff;
            font: 400 32px/50px 'Gotham Pro', Helvetica, Arial, serif;
            float: none;
            margin-top: 8px;
        }

        .section-header__right {
            text-align: right;
            font: 300 15px/24px 'Gotham Pro', Helvetica, Arial, serif;
            color: #ffffff;
            -ms-flex-negative: 0;
            flex-shrink: 0;
        }

        .section-header__right b {
            font: 500 15px/24px 'Gotham Pro', Helvetica, Arial, serif;
        }

        .row_payment-info {
            background-color: #ececec;
            padding: 40px 20px 30px 40px;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            -webkit-box-align: end;
            -ms-flex-align: end;
            align-items: flex-end;
        }

        .row_payment-info_right {
            -ms-flex-negative: 0;
            flex-shrink: 0;
        }

        .section-body-block {
            width: 1100px;
            margin: 0 auto;
        }

        .title {
            color: #7b68ee;
            font-family: 'Gotham';
            font: 500 14px/14px 'Gotham Pro', Helvetica, Arial, serif;
            letter-spacing: 2px;
            text-transform: uppercase;
            margin-bottom: 12px;
        }

        .title_right {
            text-align: right;
        }

        .header {
            color: #222222;
            font: 400 24px/32px 'Gotham Pro', Helvetica, Arial, serif;
        }

        .header_right {
            text-align: right;
            white-space: nowrap;
        }

        .header__first {
            padding-right: 40px;
        }

        .header__name {
            padding-right: 10px;
            font: 400 24px/32px 'Gotham Pro', Helvetica, Arial, serif;
        }

        .header__additional {
            color: #343434;
            font: 300 15px/24px 'Gotham Pro', Helvetica, Arial, serif;
            word-break: break-word;
            overflow-wrap: break-word;
            word-wrap: break-word;
            hyphens: auto;
        }

        .boxes {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .box {
            width: 180px;
        }
        .box.wide {
            width: 250px;
        }

        .box:first-child,
        .box:last-child {
            width: 235px;
        }

        .box-title {
            color: #4f495c;
            font: 500 13px/14px 'Gotham Pro', Helvetica, Arial, serif;
            letter-spacing: 2px;
            text-transform: uppercase;
            margin-bottom: 10px;
        }

        .box-value {
            color: #4f495c;
            font: 400 24px/22px 'Gotham Pro', Helvetica, Arial, serif;
        }

        .box-value_green {
            color: #67cb48;
        }

        .product-red {
            color: #f50000;
        }

        .box-value_bold {
            color: #4f495c;
            font: 500 34px/32px 'Gotham Pro', Helvetica, Arial, serif;
        }

        .box-value_sm {
            font-size: 20px;
            line-height: 19px;
        }

        .box_flex {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: end;
            -ms-flex-pack: end;
            justify-content: flex-end;
            -webkit-box-align: end;
            -ms-flex-align: end;
            align-items: flex-end;
            margin-bottom: 20px;
        }

        .box_flex:last-child,
        .box_flex_last {
            margin-bottom: 0;
        }

        .box_flex .box-value {
            width: 235px;
        }

        .box_flex_title {
            color: #4f495c;
            letter-spacing: 2.55px;
            font: 500 14px/20px 'Gotham Pro', Helvetica, Arial, serif;
            text-transform: uppercase;
            margin-right: 40px;
            text-align: right;
        }

        .box_flex_title_green {
            color: #67cb48;
        }

        .box_flex_title_bold {
            font-weight: 600;
        }

        .payment-method {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
        }

        .payment-method__name {
            color: #222222;
            font: 400 24px/24px 'Gotham Pro', Helvetica, Arial, serif;
        }

        .payment-result {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: end;
            -ms-flex-pack: end;
            justify-content: flex-end;
            margin-top: 10px;
            margin-bottom: 30px;
        }

        .payment-result__id {
            opacity: 0.4;
            color: #222222;
            font: 400 14px/24px 'Gotham Pro', Helvetica, Arial, serif;
        }

        .payment-method__icon {
            padding: 6px;
            background-color: #ffffff;
            -webkit-box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.1);
            margin-left: 10px;
        }

        /* American Express */

        .payment-method__icon_aexp {
            background: url('https://clickup.com/for-pdf-files/images/payment/aexp.jpg') no-repeat center #ffffff;
            background-size: 30px 30px;
            width: 36px;
            height: 36px;
        }

        /* Visa */

        .payment-method__icon_visa {
            background: url('https://clickup.com/for-pdf-files/images/payment/visa.png') no-repeat center center #ffffff;
            background-size: 30px 10px;
            width: 36px;
            height: 16px;
        }

        /* MasterCard */

        .payment-method__icon_mc {
            background: url('https://clickup.com/for-pdf-files/images/payment/mc.png') no-repeat center #ffffff;
            background-size: 30px 23px;
            width: 36px;
            height: 29px;
        }

        /* Discover */

        .payment-method__icon_discover {
            background: url('https://clickup.com/for-pdf-files/images/payment/discover.jpg') no-repeat center #ffffff;
            background-size: 30px 5px;
            width: 36px;
            height: 11px;
        }

        /* Diners Club */

        .payment-method__icon_dc {
            background: url('https://clickup.com/for-pdf-files/images/payment/dc.png') no-repeat center #ffffff;
            background-size: 30px 22px;
            width: 36px;
            height: 28px;
        }

        /* JCB */

        .payment-method__icon_jcb {
            background: url('https://clickup.com/for-pdf-files/images/payment/jcb.png') no-repeat center #ffffff;
            background-size: 30px 23px;
            width: 36px;
            height: 29px;
        }

        .payment-method__desc {
            opacity: 0.4;
            color: #222222;
            font: 400 14px/24px 'Gotham Pro', Helvetica, Arial, serif;
            margin-top: 5px;
        }

        .greyed-text {
            opacity: 0.4;
            color: #222222;
        }

        .payment-result__icon {
            margin-left: 20px;
            height: 30px;
            width: 70px;
            padding-top: 4px;
            font: 500 16px/16px 'Gotham Pro', Helvetica, Arial, serif;
            text-transform: uppercase;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            justify-content: center;
        }

        .payment-result__icon_ok {
            background-color: #67cb48;
            color: #ffffff;
        }
    </style>
</head>

<body>
    <div class='section-header'>
        <div class='section-header-block'>
            <div class='wrapper section-header__row'>
                <div class='section-header__left'>
                    <div class='section-header__logo'>
                        <img src='https://clickup.com/for-pdf-files/images/clickup2_white.png' width='163'
                            height='40' />
                    </div>
                    <div class='section-header__divider'></div>
                    <div class='section-header__date'>December 29, 2020</div>
                    <div class='section-header__title'>Thanks for your order!</div>
                </div>

                <div class='section-header__right'>
                    <b>Mango Technologies, Inc. DBA ClickUp</b>
                    <br />
                    350 Tenth Ave.
                    <br />
                    Suite 500
                    <br />
                    San Diego, CA 92101
                    <br />
                    1 888 625 4258 | clickup.com
                    <br />
                    EIN: 81-1650374
                </div>
            </div>
        </div>
    </div>
    </div>

    <div class='section-body'>
        <div class='section-body-block'>
            <div class='wrapper'>
                <table class='row_info'>
                    <tr>
                        <td valign='top'>
                            <div class='title'>Invoice memo</div>
                            <div class='header header__first'>
                                <div class='header__name'>RoleTestOwner</div>
                                <div class='header__additional'>
                                    
                                </div>
                            </div>
                        </td>
                        <td valign='top'>
                            <div class='payment-result'>
                                <div class='payment-result__icon payment-result__icon_ok'>Paid</div>
                            </div>
                            <div class='header header_right'>
                                <nobr>T800013-122920</nobr>
                            </div>
                        </td>
                    </tr>
                </table>

                <div class='row row_item'>
                    <div class='title'>
                        Product
                    </div>
                    <div class='header '>
                        Business Plan (yearly) 12/29/20 - 1/6/22
                    </div>
                    <div class='boxes'>
                        <div class='box'>
                            <div class='box-title'>Unit</div>
                            <div class='box-value '>1 Seat</div>
                        </div>
                        <div class='box wide'>
                            <div class='box-title'>Rate</div>
                            <div class='box-value '>&dollar;108.00
                                <span class='box-value_sm'>/ year</span>
                            </div>
                        </div>
                        <div class='box'>
                            <div class='box-title'>Price</div>
                            <div class='box-value '>&dollar;108.00</div>
                        </div>
                    </div>
                    <div class='payment-result__id'>Transaction ID: dddddddd</div>
                </div>

                <div class='row row_payment-info'>
                    <div class='row_payment-info_left'>
                        <div class='title'>
                            Payment Info
                        </div>

                        <div class='payment-method'>
                            <div class='payment-method__name'>Credit Card, ***0061</div>
                            <div class='payment-method__icon payment-method__icon_visa '></div>
                        </div>

                        <div class='payment-method__desc'>Billed in USD.</div>
                    </div>

                    <div class='row_payment-info_right'>
                        

                        <div class='box_flex box_flex_last'>
                            <div class='box_flex_title box_flex_title_bold'>Total</div>
                            <div class='box-value box-value_bold'>&dollar;108.00</div>
                        </div>

                        <div class='payment-method__desc'>&nbsp;</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>

</html>
"
`;

exports[`Get Invoices Endpoint Tests should return success for invoices for business workspace. 1`] = `
Object {
  "invoices": Array [
    Object {
      "amount": "108.00",
      "date": "2021-01-06",
      "id": "T800013-010621",
    },
    Object {
      "amount": "108.00",
      "date": "2020-12-29",
      "id": "T800013-122920",
    },
  ],
}
`;
