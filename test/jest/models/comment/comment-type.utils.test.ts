import { CommentTypeStr } from '@clickup/comment/domain';
import { _validateCommentMetadata } from '../../../../src/models/comment/comment-type.utils';
import * as db from '../../../../src/utils/db';

jest.mock('../../../../src/utils/comment/db', () => ({
    isNewCommentsDbEnabledForWorkspace: jest.fn().mockResolvedValue(true),
}));

jest.mock('../../../../src/utils/db', () => ({
    commentsReadQueryAsync: jest.fn(),
}));

describe('CommentTypeUtils', () => {
    describe('_validateCommentMetadata', () => {
        it('should return the comment type data for post comment', async () => {
            jest.spyOn(db, 'commentsReadQueryAsync').mockResolvedValue({
                rows: [{ comment_category_exists: true }],
            });

            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: 1,
                comment_type: CommentTypeStr.POST,
                comment_type_data: {
                    subtype_id: 3,
                    title: 'title',
                    cover_image: 'image',
                },
            });

            expect(result).toEqual({
                comment_type: 1,
                comment_subtype_id: 3,
                extra: {
                    version: 1,
                    title: 'title',
                    cover_image: 'image',
                },
            });
        });

        it('should return the comment type data for AI comment', async () => {
            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: 1,
                comment_type: CommentTypeStr.AI,
                comment_type_data: {
                    ai_result_id: '1',
                    conversation_id: '10',
                },
            });

            expect(result).toEqual({
                comment_type: 2,
                extra: {
                    version: 1,
                    ai_result_id: '1',
                    conversation_id: '10',
                },
            });
        });

        it('should return the comment type data for AI via Brain comment', async () => {
            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: 1,
                comment_type: CommentTypeStr.AI_VIA_BRAIN,
                comment_type_data: {
                    ai_result_id: '1',
                    conversation_id: '10',
                },
            });

            expect(result).toEqual({
                comment_type: 4,
                extra: {
                    version: 1,
                    ai_result_id: '1',
                    conversation_id: '10',
                },
            });
        });

        it('should return the comment type data for syncup comment', async () => {
            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: CommentTypeStr.SYNCUP,
            });

            expect(result).toEqual({
                comment_type: undefined,
                comment_subtype_id: undefined,
                extra: undefined,
            });
        });

        it('should return stub comment with source comment id', async () => {
            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: 8,
                comment_type_data: {
                    source_comment_id: '123',
                    source_comment_parent_id: '456',
                },
            });

            expect(result).toEqual({
                comment_type: undefined,
                comment_subtype_id: undefined,
                extra: {
                    version: 2,
                    source_comment_id: '123',
                    source_comment_parent_id: '456',
                },
            });
        });

        it('should throw an error if source comment id is not supported for non-chat comments', async () => {
            await expect(
                _validateCommentMetadata({
                    workspace_id: '1',
                    type: 3,
                    comment_type_data: {
                        source_comment_id: '123',
                    },
                })
            ).rejects.toThrow('stub comment is only supported for chat channel comments');
        });

        it('should return source comment with show in root parent', async () => {
            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: 2,
                comment_type_data: {
                    stub_comment_id: '10',
                },
            });

            expect(result).toEqual({
                comment_type: undefined,
                comment_subtype_id: undefined,
                extra: {
                    version: 2,
                    stub_comment_id: '10',
                },
            });
        });

        it('should throw an error if root parent stub comment id is not supported for non-chat comments', async () => {
            await expect(
                _validateCommentMetadata({
                    workspace_id: '1',
                    type: 1,
                    comment_type_data: {
                        stub_comment_id: '10',
                    },
                })
            ).rejects.toThrow('stub_comment_id is only supported for chat thread reply comments');
        });

        it('should include email metadata when via is email and comment_by_email is provided', async () => {
            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: 1,
                email_metadata: {
                    via: 'email',
                    comment_by_email: '<EMAIL>',
                    email_subject: 'Test Subject',
                    cc: ['<EMAIL>', '<EMAIL>'],
                },
            });

            expect(result).toEqual({
                comment_type: undefined,
                comment_subtype_id: undefined,
                extra: {
                    email_metadata: {
                        via: 'email',
                        comment_by_email: '<EMAIL>',
                        email_subject: 'Test Subject',
                        cc: ['<EMAIL>', '<EMAIL>'],
                    },
                },
            });
        });

        it('should combine email metadata with other comment type data', async () => {
            jest.spyOn(db, 'commentsReadQueryAsync').mockResolvedValue({
                rows: [{ comment_category_exists: true }],
            });

            const result = await _validateCommentMetadata({
                workspace_id: '1',
                type: 1,
                comment_type: CommentTypeStr.POST,
                comment_type_data: {
                    subtype_id: 3,
                    title: 'Post Title',
                    cover_image: 'cover.jpg',
                },
                email_metadata: {
                    via: 'email',
                    comment_by_email: '<EMAIL>',
                    email_subject: 'Test Subject',
                    cc: ['<EMAIL>'],
                },
            });

            expect(result).toEqual({
                comment_type: 1,
                comment_subtype_id: 3,
                extra: {
                    version: 1,
                    title: 'Post Title',
                    cover_image: 'cover.jpg',
                    email_metadata: {
                        via: 'email',
                        comment_by_email: '<EMAIL>',
                        email_subject: 'Test Subject',
                        cc: ['<EMAIL>'],
                    },
                },
            });
        });
    });
});
