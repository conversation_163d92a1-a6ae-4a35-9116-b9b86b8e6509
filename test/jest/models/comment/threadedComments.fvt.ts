/**
 * Threaded comments functional tests
 * @group functional/models/comment
 */

import config from 'config';

import ClickUpContext from '../../../../src/jest-context/ClickUpContext';
import { promiseReadQuery } from '../../../../src/utils/db';

describe('Threaded comments', () => {
    test('Parent comment version is correctly updated', async () => {
        const COMMENT_TYPES = config.get<Record<string, number>>('comments.types');
        const context = new ClickUpContext();

        const task = await context.provideTask({ name: 'Task A' });

        const parentComment = await context.provideTaskComment({ taskId: task.id, content: 'parent comment content' });
        const [initialParentCommentVersion] = await getVersions([parentComment.id]);

        const comment = await context.provideGenericComment({
            comment: 'child comment content',
            parent: parentComment.id,
            type: COMMENT_TYPES.comment,
        });

        const [initialCommentVersion, parentCommentVersionAfterChildCreation] = await getVersions([
            comment.id,
            parentComment.id,
        ]);
        expect(parentCommentVersionAfterChildCreation).toBeGreaterThan(initialParentCommentVersion); // parent version updated

        await context.commentDelete(String(comment.id));

        const [commentVersionAfterDelete, parentCommentVersionAfterDelete] = await getVersions([
            comment.id,
            parentComment.id,
        ]);
        expect(commentVersionAfterDelete).toBeGreaterThan(initialCommentVersion); // version updated
        expect(parentCommentVersionAfterDelete).toBeGreaterThan(parentCommentVersionAfterChildCreation); // parent version updated

        await context.commentUndoDelete(String(comment.id));

        const [commentVersionAfterUndoDelete, parentCommentVersionAfterUndoDelete] = await getVersions([
            comment.id,
            parentComment.id,
        ]);
        expect(commentVersionAfterUndoDelete).toBeGreaterThan(commentVersionAfterDelete); // version updated
        expect(parentCommentVersionAfterUndoDelete).toBeGreaterThan(parentCommentVersionAfterDelete); // parent version updated
    });
});

function getVersions(commentIds: number[]): Promise<number[]> {
    const query = `
        SELECT version
        FROM task_mgmt.object_versions
        WHERE
            object_type = 'comment'
            AND object_id = $1`;

    return Promise.all(
        commentIds.map(async commentId => {
            const { rows } = await promiseReadQuery(query, [commentId]);
            const versionNumber = parseInt(rows[0].version, 10);
            expect(versionNumber).not.toBeNaN();
            return versionNumber;
        })
    );
}
