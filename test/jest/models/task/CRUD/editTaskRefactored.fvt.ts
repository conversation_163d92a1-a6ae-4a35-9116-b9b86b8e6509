/* eslint-disable jest/expect-expect */
import { RestClient, RestResponseError } from '@clickup/rest-client';

import ClickUpContext from '@clickup-legacy/jest-context/ClickUpContext';
import { readAsync, writeAsync } from '@clickup-legacy/utils/db2';
import { HierarchyPermissionLevel } from '@clickup/utils/authorization/models';
import { random5 } from '../../../../sdk/helpers';
import { TeamRoles } from '../../../../../src/libs/common/types';
import { TaskInternalProperties } from '../../../../../src/models/task/interfaces/Task';
import { EntityType } from '@clickup/utils/constants';
/**
 * @group functional/models/task
 */

describe('editTaskRefactored', () => {
    jest.setTimeout(180_000);
    const context = new ClickUpContext();
    let subcategoryId: string;
    let tasks: {
        id: string;
        name: string;
        team: string;
        date_created: number;
        date_updated: number;
        text_content: string;
    }[];
    let client: RestClient;
    let allowedUserId: number;
    let forbiddenUserId: number;

    beforeAll(async () => {
        const { id: alphaTeamId } = await context.provideTeam({ name: 'Alpha' });
        const alphaUser = await context.provideUser({ username: 'Creator' });
        client = alphaUser.client;
        await context.teamAddUser(alphaTeamId, alphaUser.id);

        const alphaUser2 = await context.provideUser({ username: 'Allowed' });
        await context.teamAddUser(alphaTeamId, alphaUser2.id);
        allowedUserId = alphaUser2.id;

        const otherContext = new ClickUpContext();
        const { id: bravoTeamId } = await otherContext.provideTeam({ name: 'Bravo' });
        const bravoUser = await otherContext.provideUser({ username: 'Forbidden' });
        const bravoUserId = bravoUser.id;
        await otherContext.teamAddUser(bravoTeamId, bravoUserId);
        forbiddenUserId = bravoUserId;

        const { id: projectId } = await context.provideProject({ name: 'Project A', teamId: alphaTeamId }, { client });
        const { id: categoryId } = await context.provideCategory({ projectId }, { client });
        subcategoryId = (await context.provideSubcategory({ categoryId }, { client })).id;
        const taskNames = [
            'Hierarchy Task for Assignee',
            'Access Check Task for Assignee',
            'Hierarchy Task for Follower',
            'Access Check Task for Follower',
        ];
        tasks = await Promise.all(taskNames.map((name: string) => context.provideTask({ name, subcategoryId })));
    });

    describe('assignee validation', () => {
        it('allows assignees in same team', async () => {
            const task = tasks[1];
            await context.provideTaskAssignee({ taskId: task.id, assignees: [allowedUserId] });
            const result = await context.getTask({ taskId: task.id });
            expect(result.assignees.length).toBe(1);
            expect(result.assignees[0].id).toBe(allowedUserId);
        });

        it('prevents assignees in different team when use access check %s', async () => {
            const task = tasks[1];
            let authzError: RestResponseError;
            try {
                await context.provideTaskAssignee({ taskId: task.id, assignees: [forbiddenUserId] });
            } catch (error) {
                if (error instanceof RestResponseError) {
                    authzError = error;
                }
            }
            expect(authzError).toBeDefined();
            const body = authzError.res.json as any;
            expect(body.ECODE).toBe('ITEM_087');
        });

        it('should not add or remove duplicate assignees', async () => {
            const task = tasks[1];
            await context.provideTaskAssignee({ taskId: task.id, assignees: [allowedUserId] });
            await context.provideTaskAssignee({ taskId: task.id, rem: [allowedUserId, allowedUserId] });
            const result = await context.getTask({ taskId: task.id });
            expect(result.assignees.length).toBe(0);

            const { history } = await context.getTaskHistory(
                { taskId: task.id, hist_fields: ['assignee'] },
                { client }
            );
            expect(history).toHaveLength(2);
            expect(history[0].field).toBe('assignee_rem');
            expect(history[0].after).toBeUndefined();
            expect(history[0].before.id).toBe(allowedUserId);
            expect(history[0].user.id).toBe(allowedUserId);

            expect(history[1].field).toBe('assignee_add');
            expect(history[1].after.id).toBe(allowedUserId);
            expect(history[1].before).toBeUndefined();
            expect(history[1].user.id).toBe(allowedUserId);
        });
    });

    describe('follower validation', () => {
        it.only('followers are added and removed from the tracking tables correctly for edit task', async () => {
            const task = tasks[3];

            await context.provideTaskFollower({ taskId: task.id, followers: [allowedUserId] });
            let result = await readAsync('SELECT * FROM task_mgmt.followers WHERE task_id = $1 AND userid = $2', [
                task.id,
                allowedUserId,
            ]);
            expect(result.rows.length).toBe(1);

            // removing the follower should remove from `followers` table and add to `shared_entity_unfollowers`
            await context.provideTaskFollower({ taskId: task.id, rem: [allowedUserId] });
            result = await readAsync('SELECT * FROM task_mgmt.followers WHERE task_id = $1 AND userid = $2', [
                task.id,
                allowedUserId,
            ]);
            expect(result.rows.length).toBe(0);
            result = await readAsync('SELECT * FROM task_mgmt.shared_entity_unfollowers WHERE user_id = $1 AND entity_type = $2 AND entity_id = $3', [
                allowedUserId,
                EntityType.TASK,
                task.id,
            ]);
            expect(result.rows.length).toBe(1);
            expect(result.rows[0]).toMatchObject({
                user_id: allowedUserId,
                entity_type: EntityType.TASK,
                entity_id: task.id,
            });

            // Adding them back should remove them from `shared_entity_unfollowers`
            await context.provideTaskFollower({ taskId: task.id, followers: [allowedUserId] });
            result = await readAsync('SELECT * FROM task_mgmt.shared_entity_unfollowers WHERE user_id = $1 AND entity_type = $2 AND entity_id = $3', [
                allowedUserId,
                EntityType.TASK,
                task.id,
            ]);
            expect(result.rows.length).toBe(0);
        });

        it.only('followers are added and removed from the tracking tables correctly for edit tasks', async () => {
            const task = tasks[3];

            await context.editTasks({ item_ids: [task.id], followers: { add: [allowedUserId] } });
            let result = await readAsync('SELECT * FROM task_mgmt.followers WHERE task_id = $1 AND userid = $2', [
                task.id,
                allowedUserId,
            ]);
            expect(result.rows.length).toBe(1);

            // removing the follower should remove from `followers` table and add to `shared_entity_unfollowers`
            await context.editTasks({ item_ids: [task.id], followers: { rem: [allowedUserId] } });
            result = await readAsync('SELECT * FROM task_mgmt.followers WHERE task_id = $1 AND userid = $2', [
                task.id,
                allowedUserId,
            ]);
            expect(result.rows.length).toBe(0);
            result = await readAsync('SELECT * FROM task_mgmt.shared_entity_unfollowers WHERE user_id = $1 AND entity_type = $2 AND entity_id = $3', [
                allowedUserId,
                EntityType.TASK,
                task.id,
            ]);
            expect(result.rows.length).toBe(1);
            expect(result.rows[0]).toMatchObject({
                user_id: allowedUserId,
                entity_type: EntityType.TASK,
                entity_id: task.id,
            });

            // Adding them back should remove them from `shared_entity_unfollowers`
            await context.editTasks({ item_ids: [task.id], followers: { add: [allowedUserId] } });
            result = await readAsync('SELECT * FROM task_mgmt.shared_entity_unfollowers WHERE user_id = $1 AND entity_type = $2 AND entity_id = $3', [
                allowedUserId,
                EntityType.TASK,
                task.id,
            ]);
            console.log(result.rows);
            expect(result.rows.length).toBe(0);
        });

        it('allows followers in same team', async () => {
            const task = tasks[3];

            await context.provideTaskFollower({ taskId: task.id, followers: [allowedUserId] });
            const result = await readAsync('SELECT * FROM task_mgmt.followers WHERE task_id = $1 AND userid = $2', [
                task.id,
                allowedUserId,
            ]);
            expect(result.rows.length).toBe(1);
        });

        it('prevents followers in same team', async () => {
            const task = tasks[3];

            let authzError: RestResponseError;
            try {
                await context.provideTaskFollower({ taskId: task.id, followers: [forbiddenUserId] });
            } catch (error) {
                if (error instanceof RestResponseError) {
                    authzError = error;
                }
            }
            expect(authzError).toBeDefined();
            const body = authzError.res.json as any;
            expect(body.ECODE).toBe('ITEM_096');
        });
    });

    describe('changing task parent', () => {
        let teamId;
        let admin;

        beforeAll(async () => {
            ({ id: teamId } = await context.provideTeam());
            admin = await context.provideUser({ prefix: 'admin' });
            await context.teamAddUser(teamId, admin.id, TeamRoles.Owner);
            await context.provideTeamFeature(
                {
                    teamId,
                    features: {
                        nested_subtasks: true,
                        nested_subtasks_level: 2,
                    },
                },
                { client: admin.client }
            );
        });
        async function changeParent(task, newParent) {
            const updatedTask = await context.taskUpdate(
                {
                    taskId: task.id,
                    parent: newParent.id,
                },
                { client: admin.client }
            );
            return updatedTask;
        }

        it('should allow moving a task to a new parent', async () => {
            const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
            const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
            const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
            const updatedTask = await changeParent(task2, task1);
            expect(updatedTask.parent).toEqual(task1.id);
        });
        it('should not allow moving a task to a new parent and exceeding the nested_subtasks_level limit', async () => {
            const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
            const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
            const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
            const task4 = await context.provideTask({ name: 'task4', parent: task3.id }, { client: admin.client });
            await expect(() => changeParent(task2, task1)).rejects.toThrow(/Level of nested subtasks is limited/);
        });
        it('should allow putting 4 tasks into one tree', async () => {
            const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
            const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
            const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
            const task4 = await context.provideTask({ name: 'task4', parent: task2.id }, { client: admin.client });
            const updatedTask = await changeParent(task2, task1);
            expect(updatedTask.parent).toEqual(task1.id);
        });

        describe('subtask loop prevention', () => {
            beforeAll(async () => {
                await context.provideTeamFeature(
                    {
                        teamId,
                        features: {
                            nested_subtasks: true,
                            nested_subtasks_level: 5,
                        },
                    },
                    { client: admin.client }
                );
            });

            it('should not allow setting a task parent to one of its own subtasks', async () => {
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const task2 = await context.provideTask({ name: 'task2', parent: task1.id }, { client: admin.client });
                const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });

                await expect(() => changeParent(task1, task3)).rejects.toThrow(
                    /Cannot set a task to be a subtask of one of its own subtasks/
                );
            });

            it('should not allow setting a subtask parent to one of its own subtasks', async () => {
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const task2 = await context.provideTask({ name: 'task2', parent: task1.id }, { client: admin.client });
                const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
                await expect(() => changeParent(task2, task3)).rejects.toThrow(
                    /Cannot set a task to be a subtask of one of its own subtasks/
                );
            });

            it('should allow setting a subtask parent to a different task with the same parent but different subtask_parent', async () => {
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const [task2, task3, task4] = await Promise.all([
                    context.provideTask({ name: 'task2', parent: task1.id }, { client: admin.client }),
                    context.provideTask({ name: 'task3', parent: task1.id }, { client: admin.client }),
                    context.provideTask({ name: 'task4', parent: task1.id }, { client: admin.client }),
                ]);

                const updatedTask1 = (await changeParent(task4, task3)) as TaskInternalProperties;
                expect(updatedTask1.subtask_parent).toEqual(task3.id);

                const updatedTask2 = (await changeParent(task4, task2)) as TaskInternalProperties;
                expect(updatedTask2.subtask_parent).toEqual(task2.id);
            });
        });

        describe('with task subtask limits', () => {
            const descriptionModifier = 'should not allow';
            const testExpectation = /Maximum number of subtasks/;
            beforeAll(async () => {
                await context.selectPnpQaTestPlan(teamId, { client: admin.client });
                await context.provideTeamFeature(
                    {
                        teamId,
                        features: {
                            nested_subtasks: true,
                            nested_subtasks_level: 7,
                        },
                    },
                    { client: admin.client }
                );
            });

            async function runExpectation(fn: () => Promise<any>, expectedError: RegExp | false) {
                if (expectedError) {
                    await expect(fn).rejects.toThrow(expectedError);
                } else {
                    expect(await fn()).toBeDefined();
                }
            }

            it(`should allow giving a 3 task tree a new parent`, async () => {
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
                const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
                const task4 = await context.provideTask({ name: 'task4', parent: task2.id }, { client: admin.client });
                const updatedTask = await changeParent(task2, task1);
                expect(updatedTask.parent).toEqual(task1.id);
            });
            it(`${descriptionModifier} putting 4 task tree a new parent`, async () => {
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
                const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
                const task4 = await context.provideTask({ name: 'task4', parent: task3.id }, { client: admin.client });
                const task5 = await context.provideTask({ name: 'task5', parent: task4.id }, { client: admin.client });
                await runExpectation(() => changeParent(task2, task1), testExpectation);
            });
            it(`${descriptionModifier} putting a single task into a full tree`, async () => {
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
                const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
                const task4 = await context.provideTask({ name: 'task4', parent: task2.id }, { client: admin.client });
                const task5 = await context.provideTask({ name: 'task5', parent: task2.id }, { client: admin.client });
                await runExpectation(() => changeParent(task1, task3), testExpectation);
            });
            it(`${descriptionModifier} putting a two task tree into a 3 task tree`, async () => {
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
                const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
                const task4 = await context.provideTask({ name: 'task4', parent: task2.id }, { client: admin.client });
                const task5 = await context.provideTask({ name: 'task5', parent: task1.id }, { client: admin.client });
                await runExpectation(() => changeParent(task1, task2), testExpectation);
            });
            it(`${descriptionModifier} too big of a tree even if nested subtasks are off`, async () => {
                await context.provideTeamFeature(
                    {
                        teamId,
                        features: {
                            nested_subtasks: false,
                        },
                    },
                    { client: admin.client }
                );
                const task1 = await context.provideTask({ name: 'task1' }, { client: admin.client });
                const task2 = await context.provideTask({ name: 'task2' }, { client: admin.client });
                const task3 = await context.provideTask({ name: 'task3', parent: task2.id }, { client: admin.client });
                const task4 = await context.provideTask({ name: 'task4', parent: task2.id }, { client: admin.client });
                const task5 = await context.provideTask({ name: 'task5', parent: task2.id }, { client: admin.client });
                await runExpectation(() => changeParent(task1, task2), testExpectation);
            });
        });
    });

    describe('Default permission level', () => {
        it('will set the default permission level to the value provided', async () => {
            const context_ = new ClickUpContext();
            const { id: teamId } = await context_.provideTeam({ plan: 4 });
            const owner = await context_.provideUser({ prefix: 'owner' });
            await context_.teamAddUser(teamId, owner.id, TeamRoles.Owner);
            const task = await context_.provideTask({ name: 'task1' }, { client: owner.client });
            const updatedTask = await context_.taskUpdate({
                taskId: task.id,
                default_permission_level: HierarchyPermissionLevel.CAN_EDIT,
            });

            const { rows: taskMemberRows }: any = await readAsync(
                `SELECT * FROM task_mgmt.task_members WHERE task_id = $1 and userid = $2`,
                [task.id, owner.id]
            );

            expect(updatedTask.default_permission_level).toBe(HierarchyPermissionLevel.CAN_EDIT);
            expect(taskMemberRows[0].permission_level).toBe(HierarchyPermissionLevel.CAN_CREATE_AND_EDIT);
        });

        it('update task default permission level while having own row in ACL, shouldnt insert privacy action', async () => {
            const context_ = new ClickUpContext();
            const { id: teamId } = await context_.provideTeam({ plan: 4 });
            const owner = await context_.provideUser({ prefix: 'owner' });
            await context_.teamAddUser(teamId, owner.id, TeamRoles.Owner);
            const task = await context_.provideTask({ name: 'task1' }, { client: owner.client });

            await writeAsync(
                `INSERT INTO task_mgmt.task_members(task_id, userid, permission_level, workspace_id) VALUES($1, $2, $3, $4)`,
                [task.id, owner.id, HierarchyPermissionLevel.CAN_CREATE_AND_EDIT, teamId]
            );

            await context_.taskUpdate({
                taskId: task.id,
                default_permission_level: HierarchyPermissionLevel.CAN_EDIT,
            });

            const privacyResult = await readAsync(
                `SELECT * from task_mgmt.task_privacy where task_id = $1 and userid = $2 and member = $2`,
                [task.id, owner.id]
            );

            expect(privacyResult.rows.length).toBe(0);
        });

        it('should fail in ff ws to set granular default permission level', async () => {
            const context_ = new ClickUpContext();
            const owner_ = await context_.provideUser({
                email: `test-${random5()}@test.com`,
            });

            const workspaceId = (
                await context_.provideTeam({
                    plan: 1,
                })
            ).id;
            await context.teamAddUser(workspaceId, owner_.id);

            const task = await context_.provideTask({ name: `task1-${random5()}` });

            await expect(
                context_.taskUpdate({
                    taskId: task.id,
                    default_permission_level: HierarchyPermissionLevel.CAN_EDIT,
                })
            ).rejects.toThrow();
        });
    });
});
