import ClickUpContext from '@clickup-legacy/jest-context/ClickUpContext';
import moment from 'moment-timezone';
import { RestClient } from '@clickup/rest-client';
import TaskScenarioBuilder, { List } from '../../../helpers/taskScenarioBuilder';

/**
 * @group functional/models/task
 */
describe('create task with due date', () => {
    let context: ClickUpContext;
    let client: RestClient;
    let builder: TaskScenarioBuilder;
    let list: List;

    beforeAll(async () => {
        context = new ClickUpContext();
        builder = new TaskScenarioBuilder(context);
        await builder.init({ username: 'john_doe' });
        await builder.withSpace();
        await builder.withFolder();
        await builder.withLists([{}]);

        ({
            lists: [list],
            client,
        } = builder.build());
    });

    describe('when different combinations of due_date, due_date_time and timezone are passed in', () => {
        test('should return the same due_date if only due_date is passed', async () => {
            const due_date = getNextFridayInMs();
            const res = await context.provideTask(
                {
                    subcategoryId: list.id,
                    name: 'Task with due date time',
                    due_date,
                },
                { client }
            );

            expect(parseInt(res.due_date, 10)).toBe(due_date);
        });

        test('should return the 4am of day of due_date if due_date and due_date_time = false is passed', async () => {
            const due_date = getNextFridayInMs();
            const res = await context.provideTask(
                {
                    subcategoryId: list.id,
                    name: 'Task with due date',
                    due_date,
                    due_date_time: false,
                },
                { client }
            );

            expect(parseInt(res.due_date, 10)).toBe(getNextFridayInMs({ to4Am: true }));
        });

        test('should return due_date in different tz when due_date and timezone is passed', async () => {
            const due_date = getNextFridayInMs({ timezone: 'Asia/Manila' });
            const res = await context.provideTask(
                {
                    subcategoryId: list.id,
                    name: 'Task with due date in different tz',
                    due_date,
                    timezone: 'Asia/Manila',
                },
                { client }
            );

            expect(parseInt(res.due_date, 10)).toBe(due_date);
        });

        test('should return due_date as 4am of the day in different tz due_date and timezone is passed', async () => {
            const due_date = getNextFridayInMs({ to4Am: true, timezone: 'Asia/Manila' });
            const res = await context.provideTask(
                {
                    subcategoryId: list.id,
                    name: 'Task with due date in different tz on start of day',
                    due_date,
                    due_date_time: false,
                    timezone: 'Asia/Manila',
                },
                { client }
            );

            expect(parseInt(res.due_date, 10)).toBe(due_date);
        });
    });
});

/**
 * Helper method for constructing the next Friday in milliseconds.
 * @param option
 */
function getNextFridayInMs(option: { to4Am?: boolean; timezone?: string } = {}): number {
    const { to4Am = false, timezone } = option;

    // Get the current time in the specified timezone or local time
    const now = timezone ? moment.tz(timezone) : moment();
    const nextFriday = now.clone().day(5);

    // If today is after Friday, move to the next week's Friday
    if (now.day() > 5) {
        nextFriday.add(1, 'week');
    }

    // If ignoreTime is true, reset time to 00:00:00
    if (to4Am) {
        nextFriday.set({ hour: 4, minute: 0, second: 0, millisecond: 0 });
    }

    // Return the epoch time in milliseconds
    return nextFriday.valueOf();
}
