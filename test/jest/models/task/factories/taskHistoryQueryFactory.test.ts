import {
    prepareMarkTaskForDeletionLogQuery,
    LogContext,
} from '../../../../../src/models/task/factories/taskHistoryQueryFactory';

describe('prepareMarkTaskForDeletionLogQuery', () => {
    const now = Date.now();
    beforeEach(() => {
        jest.useFakeTimers();
        jest.setSystemTime(now);
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    test('should successfully build a simple mark for deletion log query', () => {
        const workspaceId = 1;
        const taskIds = ['2', '3'];
        const userId = 4;
        const isMarkedForDeletion = true;

        const result = prepareMarkTaskForDeletionLogQuery(workspaceId, taskIds, userId, isMarkedForDeletion);
        expect(result.params).toEqual([
            workspaceId,
            'deleted',
            now,
            userId,
            isMarkedForDeletion,
            undefined,
            ...taskIds,
        ]);
        expect(result.query.replace(/\s/g, '')).toEqual(
            `
INSERT INTO task_mgmt.task_history(workspace_id, field, date, userid, after, data, task_id)
VALUES ($1, $2, $3, $4, $5, $6, $7),($1, $2, $3, $4, $5, $6, $8)
RETURNING id,task_id
`.replace(/\s/g, '')
        );
    });

    test('should successfully build a simple unmark for deletion log query', () => {
        const workspaceId = 1;
        const taskIds = ['2', '3'];
        const userId = 4;
        const isMarkedForDeletion = false;

        const result = prepareMarkTaskForDeletionLogQuery(workspaceId, taskIds, userId, isMarkedForDeletion);
        expect(result.params).toEqual([
            workspaceId,
            'deleted',
            now,
            userId,
            isMarkedForDeletion,
            undefined,
            ...taskIds,
        ]);
        expect(result.query.replace(/\s/g, '')).toEqual(
            `
INSERT INTO task_mgmt.task_history(workspace_id, field, date, userid, after, data, task_id)
VALUES ($1, $2, $3, $4, $5, $6, $7),($1, $2, $3, $4, $5, $6, $8)
RETURNING id,task_id
`.replace(/\s/g, '')
        );
    });

    test('should successfully build a mark for deletion log query with context', () => {
        const workspaceId = 1;
        const taskIds = ['2', '3'];
        const userId = 4;
        const isMarkedForDeletion = true;
        const context: LogContext = { list_ids: [333] };

        const result = prepareMarkTaskForDeletionLogQuery(workspaceId, taskIds, userId, isMarkedForDeletion, context);
        expect(result.params).toEqual([workspaceId, 'deleted', now, userId, isMarkedForDeletion, context, ...taskIds]);
        expect(result.query.replace(/\s/g, '')).toEqual(
            `
INSERT INTO task_mgmt.task_history(workspace_id, field, date, userid, after, data, task_id)
VALUES ($1, $2, $3, $4, $5, $6, $7),($1, $2, $3, $4, $5, $6, $8)
RETURNING id,task_id
`.replace(/\s/g, '')
        );
    });

    test('should return an undefined query object if task ids are empty', () => {
        const workspaceId = 1;
        const taskIds = [];
        const userId = 4;
        const isMarkedForDeletion = true;

        const result = prepareMarkTaskForDeletionLogQuery(workspaceId, taskIds, userId, isMarkedForDeletion);
        expect(result).toBeUndefined();
    });

    test('should throw an error if task for logging exceeds the acceptable amount', () => {
        const workspaceId = 1;
        const taskIds = [];
        const userId = 4;
        const isMarkedForDeletion = true;

        for (let i = 0; i < 50_001; i++) {
            taskIds.push(i);
        }

        expect(() =>
            prepareMarkTaskForDeletionLogQuery(workspaceId, taskIds, userId, isMarkedForDeletion)
        ).toThrowError('Too many tasks to log');
    });
});
