<?xml version="1.0" encoding="utf-8"?>
<EntityDescriptor ID="_d6b09ae5-68af-4e7e-8ecc-84d99d31f6ec"
    entityID="https://sts.windows.net/a86ad2c0-efb9-4961-bded-81765f898dd1/"
    xmlns="urn:oasis:names:tc:SAML:2.0:metadata">
    <Signature xmlns="http://www.w3.org/2000/09/xmldsig#">
        <SignedInfo>
            <CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" />
            <SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256" />
            <Reference URI="#_d6b09ae5-68af-4e7e-8ecc-84d99d31f6ec">
                <Transforms>
                    <Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" />
                    <Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" />
                </Transforms>
                <DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256" />
                <DigestValue>Frl9JWsgHHQwo87e7Ig2dPaLFSE3oCslZ0I2LGOj1Do=</DigestValue>
            </Reference>
        </SignedInfo>
        <SignatureValue>
            Rq41TKAGdInzjK1mA2x6FerJCGN+SvjunYW3qjZ2zJOEKhwgT1bdDoZJGZBVTmH4CW29rVmhhE4HymndgXlVlShFvvnQeUVka3PtmzTjQH2ZK5I33oKzB8cj8/coGNSjI+D9oXOj3a56etNepjXIaKBhBQ5yhmn+zfOXy/mvgS3gYX2KW7CTIwpJCS1S8V4U0dtCKpEeLNKcylBdJEH6NRShoBeM8qnQccscMDvVPRvlx18mFBsbFgXRbN2bZv/a7i5rxaypwzSYQ6wMYkahvSWW1yZXb5VoYn2mvS2+gCWe7FtaHL0mvZbcKn3EeGIJE9bogcusVNgvcbKFiSsNSg==</SignatureValue>
        <KeyInfo>
            <X509Data>
                <X509Certificate>
                    CERTVALUE
                </X509Certificate>
            </X509Data>
        </KeyInfo>
    </Signature>
    <RoleDescriptor xsi:type="fed:SecurityTokenServiceType"
        protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706">
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIIC8DCCAdigAwIBAgIQLIm9PefrbrBCiJu8xOdVczANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMzA0MDMyMDUyNThaFw0yNjA0MDMyMDUyNThaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsOkchcuVBA5bHIyg++F0r6CYfMhIUhyWSD2MmtTsaEPLm6FEVoMWAxgfZiNZbkgPiK3g8ye8s5bacnsnJ9PDrF17uHKP3n072nso/Ag6bhA4Qdn5jCeYVWElA4XtOrX2QinVQcSPU2BdE7DJvi9QuM4ZWr58wNNhiULUnmOIkamvzR8ZD3qmF9T+eI4WslLu8um7JSp8uL7Iva8sIYfli/J0G+og+pt0LInyCIaB0omCCzDYKd9CBrXdAKJGahq6c+QFU99nHI0lUJzY8Rh5RGdKQJSPzprCWRvul/GnoXMw3ibDmUhxZSrb0HTDMI6KivdYtfQbdXu9iFprJ4C/yQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAje/4hOoXf3HLyQmSXI8p6WTJs1A8zBNs2FLP7yY3JHsudwEG4aSZ+UbQbyyvFJRCKTwYB6Emiifg+PJYsT/HJwtTHqcajuT5tsE5iPBpy7iN8T5CEFuBkLRZAtQ/HTXl2JLYC3iV8vbsYlp4CNr3fb2Q41qgU86xqYIHX/dWPAnBVmLKxrCMCDGUPWDuMW3J4mLRXTKkpN4rxtpUuGzXf+Z3IOIlxv48b/XhvfvS+KBGOXOjl7ZdBvIgV+dWexQvzcjP17z5QgK+sn8BbpY/MZwG+lBssiUBzOImP3h0kiZmtmdd6BwgwoY/lJKsGPR2zKWjqbeCoF+yfpVKYORQp
                    </X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        <fed:ClaimTypesOffered>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Name</auth:DisplayName>
                <auth:Description>The mutable display name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType
                Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Subject</auth:DisplayName>
                <auth:Description>An immutable, globally unique, non-reusable identifier of the user
                    that is unique to the application for which a token is issued.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Given Name</auth:DisplayName>
                <auth:Description>First name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Surname</auth:DisplayName>
                <auth:Description>Last name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/displayname"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Display Name</auth:DisplayName>
                <auth:Description>Display name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/nickname"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Nick Name</auth:DisplayName>
                <auth:Description>Nick name of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType
                Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationinstant"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Authentication Instant</auth:DisplayName>
                <auth:Description>The time (UTC) when the user is authenticated to Windows Azure
                    Active Directory.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType
                Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/authenticationmethod"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Authentication Method</auth:DisplayName>
                <auth:Description>The method that Windows Azure Active Directory uses to
                    authenticate users.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/objectidentifier"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>ObjectIdentifier</auth:DisplayName>
                <auth:Description>Primary identifier for the user in the directory. Immutable,
                    globally unique, non-reusable.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/tenantid"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>TenantId</auth:DisplayName>
                <auth:Description>Identifier for the user's tenant.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/identityprovider"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>IdentityProvider</auth:DisplayName>
                <auth:Description>Identity provider for the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Email</auth:DisplayName>
                <auth:Description>Email address of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/groups"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Groups</auth:DisplayName>
                <auth:Description>Groups of the user.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/accesstoken"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>External Access Token</auth:DisplayName>
                <auth:Description>Access token issued by external identity provider.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/expiration"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>External Access Token Expiration</auth:DisplayName>
                <auth:Description>UTC expiration time of access token issued by external identity
                    provider.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/identity/claims/openid2_id"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>External OpenID 2.0 Identifier</auth:DisplayName>
                <auth:Description>OpenID 2.0 identifier issued by external identity provider.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/claims/groups.link"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>GroupsOverageClaim</auth:DisplayName>
                <auth:Description>Issued when number of user's group claims exceeds return limit.</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/role"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>Role Claim</auth:DisplayName>
                <auth:Description>Roles that the user or Service Principal is attached to</auth:Description>
            </auth:ClaimType>
            <auth:ClaimType Uri="http://schemas.microsoft.com/ws/2008/06/identity/claims/wids"
                xmlns:auth="http://docs.oasis-open.org/wsfed/authorization/200706">
                <auth:DisplayName>RoleTemplate Id Claim</auth:DisplayName>
                <auth:Description>Role template id of the Built-in Directory Roles that the user is
                    a member of</auth:Description>
            </auth:ClaimType>
        </fed:ClaimTypesOffered>
        <fed:SecurityTokenServiceEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/a86ad2c0-efb9-4961-bded-81765f898dd1/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:SecurityTokenServiceEndpoint>
        <fed:PassiveRequestorEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/a86ad2c0-efb9-4961-bded-81765f898dd1/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:PassiveRequestorEndpoint>
    </RoleDescriptor>
    <RoleDescriptor xsi:type="fed:ApplicationServiceType"
        protocolSupportEnumeration="http://docs.oasis-open.org/wsfed/federation/200706"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:fed="http://docs.oasis-open.org/wsfed/federation/200706">
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIIC8DCCAdigAwIBAgIQLIm9PefrbrBCiJu8xOdVczANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMzA0MDMyMDUyNThaFw0yNjA0MDMyMDUyNThaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsOkchcuVBA5bHIyg++F0r6CYfMhIUhyWSD2MmtTsaEPLm6FEVoMWAxgfZiNZbkgPiK3g8ye8s5bacnsnJ9PDrF17uHKP3n072nso/Ag6bhA4Qdn5jCeYVWElA4XtOrX2QinVQcSPU2BdE7DJvi9QuM4ZWr58wNNhiULUnmOIkamvzR8ZD3qmF9T+eI4WslLu8um7JSp8uL7Iva8sIYfli/J0G+og+pt0LInyCIaB0omCCzDYKd9CBrXdAKJGahq6c+QFU99nHI0lUJzY8Rh5RGdKQJSPzprCWRvul/GnoXMw3ibDmUhxZSrb0HTDMI6KivdYtfQbdXu9iFprJ4C/yQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAje/4hOoXf3HLyQmSXI8p6WTJs1A8zBNs2FLP7yY3JHsudwEG4aSZ+UbQbyyvFJRCKTwYB6Emiifg+PJYsT/HJwtTHqcajuT5tsE5iPBpy7iN8T5CEFuBkLRZAtQ/HTXl2JLYC3iV8vbsYlp4CNr3fb2Q41qgU86xqYIHX/dWPAnBVmLKxrCMCDGUPWDuMW3J4mLRXTKkpN4rxtpUuGzXf+Z3IOIlxv48b/XhvfvS+KBGOXOjl7ZdBvIgV+dWexQvzcjP17z5QgK+sn8BbpY/MZwG+lBssiUBzOImP3h0kiZmtmdd6BwgwoY/lJKsGPR2zKWjqbeCoF+yfpVKYORQp</X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        <fed:TargetScopes>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>https://sts.windows.net/a86ad2c0-efb9-4961-bded-81765f898dd1/</wsa:Address>
            </wsa:EndpointReference>
        </fed:TargetScopes>
        <fed:ApplicationServiceEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/a86ad2c0-efb9-4961-bded-81765f898dd1/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:ApplicationServiceEndpoint>
        <fed:PassiveRequestorEndpoint>
            <wsa:EndpointReference xmlns:wsa="http://www.w3.org/2005/08/addressing">
                <wsa:Address>
                    https://login.microsoftonline.com/a86ad2c0-efb9-4961-bded-81765f898dd1/wsfed</wsa:Address>
            </wsa:EndpointReference>
        </fed:PassiveRequestorEndpoint>
    </RoleDescriptor>
    <IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
        <KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIIC8DCCAdigAwIBAgIQLIm9PefrbrBCiJu8xOdVczANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMzA0MDMyMDUyNThaFw0yNjA0MDMyMDUyNThaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsOkchcuVBA5bHIyg++F0r6CYfMhIUhyWSD2MmtTsaEPLm6FEVoMWAxgfZiNZbkgPiK3g8ye8s5bacnsnJ9PDrF17uHKP3n072nso/Ag6bhA4Qdn5jCeYVWElA4XtOrX2QinVQcSPU2BdE7DJvi9QuM4ZWr58wNNhiULUnmOIkamvzR8ZD3qmF9T+eI4WslLu8um7JSp8uL7Iva8sIYfli/J0G+og+pt0LInyCIaB0omCCzDYKd9CBrXdAKJGahq6c+QFU99nHI0lUJzY8Rh5RGdKQJSPzprCWRvul/GnoXMw3ibDmUhxZSrb0HTDMI6KivdYtfQbdXu9iFprJ4C/yQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAje/4hOoXf3HLyQmSXI8p6WTJs1A8zBNs2FLP7yY3JHsudwEG4aSZ+UbQbyyvFJRCKTwYB6Emiifg+PJYsT/HJwtTHqcajuT5tsE5iPBpy7iN8T5CEFuBkLRZAtQ/HTXl2JLYC3iV8vbsYlp4CNr3fb2Q41qgU86xqYIHX/dWPAnBVmLKxrCMCDGUPWDuMW3J4mLRXTKkpN4rxtpUuGzXf+Z3IOIlxv48b/XhvfvS+KBGOXOjl7ZdBvIgV+dWexQvzcjP17z5QgK+sn8BbpY/MZwG+lBssiUBzOImP3h0kiZmtmdd6BwgwoY/lJKsGPR2zKWjqbeCoF+yfpVKYORQp</X509Certificate>
                </X509Data>
            </KeyInfo>
        </KeyDescriptor>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://login.microsoftonline.com/a86ad2c0-efb9-4961-bded-81765f898dd1/saml2" />
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
            Location="https://login.microsoftonline.com/a86ad2c0-efb9-4961-bded-81765f898dd1/saml2" />
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
            Location="https://login.microsoftonline.com/a86ad2c0-efb9-4961-bded-81765f898dd1/saml2" />
    </IDPSSODescriptor>
</EntityDescriptor>