#!/bin/bash

# Brewfile content embedded in the script
# This is a list of all the apps by default
# It is embedded in the script to avoid having to download it from a remote source
BREWFILE_CONTENT='
brew "awscli"
brew "bash"
brew "coreutils"
brew "docker-compose"
brew "gh"
brew "git"
brew "go"
brew "helm"
brew "jq"
brew "kubernetes-cli"
brew "mercurial"
brew "nvm"
brew "openjdk@21"
brew "python-setuptools"
brew "retry"
brew "ruff"
brew "tilt"
brew "uv"
brew "yq"
brew "dotenvx/brew/dotenvx"
brew "telepresenceio/telepresence/telepresence-oss"
cask "1password-cli"
cask "sdm"
cask "session-manager-plugin"
'

# Function to install Homebrew
install_brew() {
    echo "Homebrew is not installed. Installing Homebrew..."
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >>~/.zprofile
    eval "$(/opt/homebrew/bin/brew shellenv)"
}

# Check if Homebrew is installed
if ! command -v brew &>/dev/null; then
    install_brew
else
    echo "Homebrew is already installed."
fi

echo "Updating Homebrew..."
brew update

if ! xcode-select -v &>/dev/null; then
    echo "Installing Xcode command line tools..."
    xcode-select --install
else
    echo "Xcode command line tools already installed."
fi


echo "Installing apps..."
# Create temporary Brewfile and install using brew bundle
echo "$BREWFILE_CONTENT" | brew bundle install --file=-

# Setup Python 3.13 using uv
if ! uv python find 3.13 &>/dev/null; then
    echo "installing python 3.13.* with uv..."
    uv python install 3.13
else
    echo "python 3.13.* is already installed"
fi

# Create a symlink for the JDK installed by Homebrew
echo "Creating symlink for the JDK installed by Homebrew..."
echo "This requires sudo, you will be prompted for your password." 
sudo ln -sfn /opt/homebrew/opt/openjdk/libexec/openjdk.jdk \
     /Library/Java/JavaVirtualMachines/openjdk.jdk

echo "Installation of apps is complete! Restart your terminal to load the new settings."
