{"openapi": "3.0.0", "paths": {"/notification-settings-service-v3": {"get": {"operationId": "AppController_getData", "parameters": [], "responses": {"200": {"description": ""}}}}, "/notification-settings-service-v3/health": {"get": {"operationId": "HealthController_health", "parameters": [], "responses": {"200": {"description": "", "content": {"text/html": {"schema": {"type": "string"}}}}}, "tags": ["Health"]}}, "/notification-settings-service-v3/health/detail": {"get": {"operationId": "HealthController_healthDetail", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthDetailResponse"}}}}}, "tags": ["Health"]}}}, "info": {"title": "Notification Settings Service", "description": "Notification Settings Service API", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"HealthDetailResponse": {"type": "object", "properties": {"status": {"type": "string", "description": "The overall status of the Health Check"}, "info": {"type": "object", "description": "The info object contains information of each health indicator which is of status \"up\""}, "error": {"type": "object", "description": "The error object contains information of each health indicator which is of status \"down\""}, "details": {"type": "object", "description": "The details object contains information of every health indicator"}}, "required": ["status"]}}}, "externalDocs": {"description": "Visit our doc", "url": "https://clickup.com/features/docs"}}