import { AxiosInstance } from 'axios';

export interface WorkspaceGroupRto {
    id: number;
    initials: string;
    name: string;
}

export const getWorkspaceGroups = async ({
    workspaceId,
    httpClient,
}: {
    workspaceId: number;
    httpClient: AxiosInstance;
}): Promise<WorkspaceGroupRto[]> => {
    const getGroupsEndpoint = `user/v1/team/${workspaceId}/group`;

    const {
        data: { groups },
    } = await httpClient.get<{ groups: WorkspaceGroupRto[] }>(getGroupsEndpoint);

    return groups;
};
