import 'reflect-metadata';

import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { HttpModule } from '@nestjs/axios';
import { MiddlewareConsumer, Module } from '@nestjs/common';

import { HealthController } from '@clickup/health/core';
import { AuthenticationMiddleware, UtilsAuthenticationModule } from '@clickup/utils/authentication';
import { UtilsAuthorizationModule } from '@clickup/utils/authorization';
import { UtilsConfigModule } from '@clickup/utils/config';
import { UtilsDbModule } from '@clickup/utils/db';
import { UtilsFeatureFlagModule } from '@clickup/utils/feature-flag';
import { UtilsLoggingModule } from '@clickup/utils/logging';
import { UtilsMetricsModule } from '@clickup/utils/metrics';

import { OidcCallbackController } from './oidc-connections/oidc-callback.controller';
import { OidcConnectionsController } from './oidc-connections/oidc-connections.controller';
import { OidcConnectionsRepository } from './oidc-connections/oidc-connections.repository';
import { OidcConnectionsService } from './oidc-connections/oidc-connections.service';

export const allControllers = [OidcConnectionsController, OidcCallbackController];

@Module({
    imports: [
        AutomapperModule.forRoot({
            strategyInitializer: classes(),
        }),
        HttpModule,
        UtilsConfigModule,
        UtilsDbModule,
        UtilsLoggingModule,
        UtilsMetricsModule,
        UtilsFeatureFlagModule,
        UtilsAuthenticationModule,
        UtilsAuthorizationModule,
    ],
    controllers: [HealthController, ...allControllers],
    providers: [OidcConnectionsService, OidcConnectionsRepository],
})
export class AppModule {
    async configure(consumer: MiddlewareConsumer) {
        consumer.apply(AuthenticationMiddleware).forRoutes(OidcConnectionsController);
    }
}
