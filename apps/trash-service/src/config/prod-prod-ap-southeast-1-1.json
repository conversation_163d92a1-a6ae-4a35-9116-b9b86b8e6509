{"db": {"connections": {"docHistoryReader": {"dbname": "clickup", "secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:ProdApSoutheast11DochistoryReader"}, "docHistoryWriter": {"dbname": "clickup", "secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:ProdApSoutheast11DochistoryWriter"}, "taskReadQueryPool": {"secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:AmazonAurora_prod-ap-southeast-1-1-tasks-db-tasks_user_read"}, "taskReadWriteQueryPool": {"secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:AmazonAurora_prod-ap-southeast-1-1-tasks-db-tasks_user_rw"}, "teamActivityReader": {"dbname": "teamactivity", "secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:ProdApSoutheast11TeamactivityReader"}, "teamActivityWriter": {"dbname": "teamactivity", "secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:ProdApSoutheast11TeamactivityWriter"}, "syncupDbRead": {"dbname": "clickup", "secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:AmazonAurora_prod-ap-southeast-1-1-syncup_db-clickup_user_read"}, "syncupDbWrite": {"dbname": "clickup", "secret": "aws/json/arn:aws:secretsmanager:ap-southeast-1::secret:AmazonAurora_prod-ap-southeast-1-1-syncup_db-clickup_user_rw"}}}}