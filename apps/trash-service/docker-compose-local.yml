version: '3.9'
services:
    trash-service:
        image: trash-service:1.0
        container_name: trash-service
        network_mode: 'host'
        environment:
            DOCKER_LOCAL: 'app_only'
            AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID:?Missing AWS_ACCESS_KEY_ID environment variable}
            AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY:?Missing AWS_SECRET_ACCESS_KEY environment variable}
        ports:
            - 3333:80
        platform: linux/arm64
