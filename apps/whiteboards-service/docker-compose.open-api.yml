version: '3.9'

services:
    openapi_generator:
        build:
            context: ../../tools/openapi-generator
            args:
                api_url: swagger-files/whiteboards-service-swagger.json
                package_name: '@time-loop/whiteboards-service-client'
                service_prefix: Whiteboards
                # Generate axios only, we will create angular later.
                generators: axios #,angular
            dockerfile: Dockerfile
        volumes:
            - ../../libs/whiteboards/service/client/src:/local/out
