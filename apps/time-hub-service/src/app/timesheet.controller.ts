import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    ParseIntPipe,
    Post,
    Query,
    Req,
    UseGuards,
} from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { snakeCase } from 'lodash';

import {
    TimesheetAsUserGuard,
    TimesheetEntitlementGuard,
    TimesheetTaskGuard,
    TimesheetTeamViewGuard,
} from '@clickup/time-hub/authorization';
import { GetTimesheetOptions, TimesheetService } from '@clickup/time-hub/timesheet';
import { DBClient } from '@clickup/utils/db';
import type { SimpleClient } from '@clickup/utils/db-types';
import type { ClickUpRequest } from '@clickup/utils/http-types';
import { FormatLogger } from '@clickup/utils/logging';

import { AttachBulkTasksDto } from './models/attach-bulk-tasks.dto';
import { TimeHubAutomapperProfile } from './models/automapper';
import { EditTimesheetOptionsDto } from './models/edit-timesheet-options.dto';
import { GetTimesheetOptionsDto } from './models/get-timesheet-options.dto';

@Controller('/workspace/:workspaceId/timesheet')
@ApiTags('Time Hub', 'Timesheet')
export class TimesheetController {
    constructor(
        private readonly mapperProfile: TimeHubAutomapperProfile,
        private readonly dbClient: DBClient,
        private readonly timesheetService: TimesheetService,
        private readonly logger: FormatLogger
    ) {}

    @UseGuards(TimesheetAsUserGuard)
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({ status: HttpStatus.OK })
    @Get('/tasks')
    async getTimesheetGroupByTask(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Query() getTimesheetOptionsDto: GetTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;
        const getTimesheetOptions: GetTimesheetOptions = this.mapperProfile.mapper.map(
            getTimesheetOptionsDto,
            GetTimesheetOptionsDto,
            GetTimesheetOptions
        );

        this.logger.info({
            message: '[Time Hub Service] Fetching timesheet group by task',
            metadata: {
                workspaceId,
                userid,
                approvalId: getTimesheetOptionsDto.approval_id,
            },
        });

        const timesheet = await this.dbClient.readAsyncFunction((simpleClient: SimpleClient) =>
            this.timesheetService.fetchTaskTimesheet(userid, workspaceId, simpleClient, getTimesheetOptions)
        );

        return {
            timesheet: this.snakeifyObject(timesheet),
        };
    }

    @UseGuards(TimesheetAsUserGuard)
    @Get('/entries')
    async getTimesheetGroupByEntry(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Query() getTaskTimesheetOptionsDto: GetTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;
        const getTimesheetOptions: GetTimesheetOptions = this.mapperProfile.mapper.map(
            getTaskTimesheetOptionsDto,
            GetTimesheetOptionsDto,
            GetTimesheetOptions
        );

        const timesheet = await this.dbClient.readAsyncFunction(simpleClient =>
            this.timesheetService.fetchEntryTimesheet(userid, workspaceId, simpleClient, getTimesheetOptions)
        );

        return {
            timesheet: this.snakeifyObject(timesheet),
        };
    }

    @UseGuards(TimesheetAsUserGuard)
    @Get('/task/:taskId/entries')
    async getTimesheetTaskEntries(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Param('taskId') taskId: string,
        @Query() getTimesheetOptionsDto: GetTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;
        const getTimesheetOptions: GetTimesheetOptions = this.mapperProfile.mapper.map(
            getTimesheetOptionsDto,
            GetTimesheetOptionsDto,
            GetTimesheetOptions
        );

        const result = await this.dbClient.readAsyncFunction(simpleClient =>
            this.timesheetService.fetchTimesheetTaskEntries(
                userid,
                workspaceId,
                taskId,
                simpleClient,
                getTimesheetOptions
            )
        );

        return this.snakeifyObject(result);
    }

    @UseGuards(TimesheetAsUserGuard)
    @Get('/tasks/unattached')
    async getTimesheetUnattachedEntries(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Query() getTimesheetOptionsDto: GetTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;
        const getTimesheetOptions: GetTimesheetOptions = this.mapperProfile.mapper.map(
            getTimesheetOptionsDto,
            GetTimesheetOptionsDto,
            GetTimesheetOptions
        );

        const result = await this.dbClient.readAsyncFunction(simpleClient =>
            this.timesheetService.fetchTimesheetUnattachedEntries(
                userid,
                workspaceId,
                simpleClient,
                getTimesheetOptions
            )
        );

        return this.snakeifyObject(result);
    }

    @UseGuards(TimesheetAsUserGuard)
    @Get('/previous-tasks')
    async getPreviousTasksOnTimesheet(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Query() getTimesheetOptionsDto: GetTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;

        const getTimesheetOptions: GetTimesheetOptions = this.mapperProfile.mapper.map(
            getTimesheetOptionsDto,
            GetTimesheetOptionsDto,
            GetTimesheetOptions
        );

        return this.dbClient.readAsyncFunction(simpleClient =>
            this.timesheetService.fetchPreviousTasksOnTimesheet(userid, workspaceId, simpleClient, getTimesheetOptions)
        );
    }

    @UseGuards(TimesheetTaskGuard, TimesheetEntitlementGuard, TimesheetAsUserGuard)
    @Post('/tasks')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({ status: HttpStatus.OK })
    async attachBulkTasksToTimesheet(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Body() { start_of_week: startOfWeek, task_ids: taskIds, as_user: asUser }: AttachBulkTasksDto
    ) {
        const { user: userid } = req.userContext;

        const { accessibleTasks, inaccessibleTasks } = await this.dbClient.writeAsyncFunction(
            (simpleClient: SimpleClient) =>
                this.timesheetService.attachTasksToTimesheet(userid, workspaceId, taskIds, startOfWeek, simpleClient, {
                    asUser,
                })
        );

        const taskMap = await this.dbClient.readAsyncFunction(simpleClient =>
            this.timesheetService.populateTaskData(userid, workspaceId, taskIds, simpleClient)
        );

        return {
            conflict: !!inaccessibleTasks.length,
            tasks: accessibleTasks.map(taskId => taskMap[taskId]).filter(Boolean),
            inaccessible_tasks: inaccessibleTasks.map(taskId => taskMap[taskId]).filter(Boolean),
        };
    }

    @UseGuards(TimesheetTaskGuard, TimesheetEntitlementGuard, TimesheetAsUserGuard)
    @Delete('/task/:taskId')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({ status: HttpStatus.OK })
    async deleteTaskRowFromTimesheet(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Param('taskId') taskId: string,
        @Query() { start_of_week: startOfWeek, as_user: asUser }: EditTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;
        return this.dbClient.writeAsyncFunction((simpleClient: SimpleClient) =>
            this.timesheetService.deleteRowFromTimesheet(
                userid,
                workspaceId,
                startOfWeek,
                simpleClient,
                { asUser },
                taskId
            )
        );
    }

    @Delete('unattached')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({ status: HttpStatus.OK })
    @UseGuards(TimesheetEntitlementGuard)
    async deleteUnattachedRowFromTimesheet(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Query() { start_of_week: startOfWeek, as_user: asUser }: EditTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;
        return this.dbClient.writeAsyncFunction((simpleClient: SimpleClient) =>
            this.timesheetService.deleteRowFromTimesheet(
                userid,
                workspaceId,
                startOfWeek,
                simpleClient,
                { asUser },
                undefined,
                true
            )
        );
    }

    @UseGuards(TimesheetTeamViewGuard)
    @Get('/team')
    @HttpCode(HttpStatus.OK)
    @ApiOkResponse({ status: HttpStatus.OK })
    async getTeamTimesheet(
        @Req() req: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Query() getTimesheetOptionsDto: GetTimesheetOptionsDto
    ) {
        const { user: userid } = req.userContext;
        const getTimesheetOptions: GetTimesheetOptions = this.mapperProfile.mapper.map(
            getTimesheetOptionsDto,
            GetTimesheetOptionsDto,
            GetTimesheetOptions
        );

        const result = await this.dbClient.readAsyncFunction((simpleClient: SimpleClient) =>
            this.timesheetService.getTeamTimesheet(userid, workspaceId, getTimesheetOptions, simpleClient)
        );

        return this.snakeifyObject(result, ['user']);
    }

    private snakeifyObject(object: object, skipKeys: string[] = []) {
        return Object.entries(object).reduce((acc, [key, value]) => {
            if (skipKeys.includes(key)) {
                acc[key] = value;
            } else if (Array.isArray(value)) {
                acc[snakeCase(key)] = value.map(entry =>
                    typeof entry === 'object' && value !== null ? this.snakeifyObject(entry, skipKeys) : entry
                );
            } else if (typeof value === 'object' && value !== null) {
                acc[snakeCase(key)] = this.snakeifyObject(value, skipKeys);
            } else {
                acc[snakeCase(key)] = value;
            }
            return acc;
        }, {} as Record<any, any>);
    }
}
