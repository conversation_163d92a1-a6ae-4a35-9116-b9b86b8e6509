import { ApiProperty } from '@nestjs/swagger';
import { ArrayMaxSize, IsArray, IsOptional, IsString } from 'class-validator';

export class GenerateTaskTypeSuggestionDto {
    /**
     * Plain english description of the automation.
     */
    @ApiProperty()
    @IsString()
    @IsOptional()
    content: string;

    @ApiProperty()
    @IsArray()
    @ArrayMaxSize(100)
    @IsOptional()
    taskIds: string[];
}
