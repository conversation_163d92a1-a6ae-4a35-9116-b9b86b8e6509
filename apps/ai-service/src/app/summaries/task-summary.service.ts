import { HttpException, Inject, Injectable } from '@nestjs/common';
import _ from 'lodash';
import pLimit from 'p-limit';

import {
    AI_GLOBAL,
    AiGlobal,
    CompletionPrompts,
    ErrorCodes,
    SseStreamResponse,
    StreamingResult,
    TaskId,
    WorkspaceId,
} from '@clickup/ai/core';
import { SummaryFormats } from '@clickup/field/ai';
import { SimpleSubtaskModel, TaskModel } from '@clickup/task/domain-model';
import { ConfigService } from '@clickup/utils/config';

import { ChatCompletionsService } from '../completions/chat-completions.service';
import { ActivityDataService } from '../shared/clickup-data/activity-data.service';
import { CommentDataService } from '../shared/clickup-data/comment-data.service';
import { CommentWithUsers } from '../shared/clickup-data/interfaces/extended';
import { Comment } from '../shared/clickup-data/interfaces/monolith';
import { TaskDataService } from '../shared/clickup-data/task-data.service';
import { RecursiveCharacterTextSplitter } from '../shared/langchain/text_splitter';
import { OpenAiChatClient } from '../shared/openai/openai-chat.service';
import { PromptService } from '../shared/openai/prompt.service';
import { ChatModel } from '../shared/openai/types';
import { countTokens, isInputTooLarge, maxTokensForModel } from '../shared/openai/utils/tokens';
import { removeHyperlinks } from '../shared/string-utils';

@Injectable()
export class TaskSummarizationService {
    /**
     * How much text do we need to perform the final summary of this task?
     * Prevents the AI from making up details if there isn't enough data for a
     * real summary.
     */
    readonly minLengthForFinalSummary: number = 250;

    /**
     * When calculating the max size of each component, this is the
     * minimum # tokens that should be reserved for the response from OpenAI.
     */
    readonly tokensReservedForResponse = 250;

    /**
     * How long (in tokens) do tasks/subtasks/comments have to be before we
     * summarize them individually before combining them with the other content?
     */
    minTokensForIndividualSummary(numComponents: number, model: ChatModel): number {
        const maxTokens = maxTokensForModel(model);
        return Math.floor((maxTokens - this.tokensReservedForResponse) / numComponents);
    }

    constructor(
        private readonly completionService: ChatCompletionsService,
        private readonly dataService: TaskDataService,
        private readonly activityDataService: ActivityDataService,
        private readonly configService: ConfigService,
        private readonly commentDataService: CommentDataService,
        private readonly promptService: PromptService,
        private readonly openAiClient: OpenAiChatClient,
        @Inject(AI_GLOBAL) private readonly g: AiGlobal
    ) {}

    /**
     * Summarize a task with AI, includes data from related objects (such as comments).
     */
    async summarize(
        workspaceId: string,
        userId: string,
        taskId: string,
        res: SseStreamResponse,
        format?: SummaryFormats
    ): Promise<StreamingResult> {
        const content = await this.getContentLong(workspaceId, taskId);
        let prompt = CompletionPrompts.TaskSummary;
        if (format) {
            switch (format) {
                case 'short':
                    prompt = CompletionPrompts.TaskSummaryShort;
                    break;
                case 'detailed':
                    prompt = CompletionPrompts.TaskSummaryDetailed;
                    break;
                case 'bullet':
                    prompt = CompletionPrompts.TaskSummary;
                    break;
                default:
                    prompt = CompletionPrompts.TaskSummary;
            }
        }
        return this.completionService.streamResponse(prompt, { content }, res, workspaceId, userId);
    }

    /**
     * Summarize recent hist_items and comments related to this task
     */
    async summarizeActivity(
        workspaceId: string,
        userId: string,
        taskId: string,
        minDate: string,
        maxDate: string | undefined,
        res: SseStreamResponse,
        format?: SummaryFormats
    ): Promise<StreamingResult> {
        const [recentComments, recentActivity, taskContent] = await Promise.all([
            this.dataService.fetchTaskComments(workspaceId, taskId, { minDate, maxDate }),
            this.dataService.fetchTaskActivity(workspaceId, taskId, { minDate, maxDate }),
            this.fetchAndSummarizeTaskContent(workspaceId, taskId),
        ]);
        const parentCommentIds = _.uniq(
            recentComments.map(c => (c.type === this.configService.get('comments.types.task') ? c.id : c.parent))
        );

        const commentsChunk = await this.formatCommentsForActivity(parentCommentIds, Number(minDate), taskContent);
        const activityChunk = await this.activityDataService.formatHistItemLog(recentActivity);

        if (!commentsChunk && !activityChunk && !this.g.isClickbotRequest()) {
            throw new HttpException(
                {
                    message: 'No updates from the the requested time range.',
                    err: 'Input is too short',
                    ECODE: ErrorCodes.TooShort,
                },
                400
            );
        }

        let content = '';
        if (activityChunk) {
            content += `Activity Log:\n\n${activityChunk}\n\n`;
        }
        if (commentsChunk) {
            content += `Comments:\n\n${commentsChunk}`;
        }
        if (!commentsChunk && !activityChunk) {
            content += 'No updates.';
        }
        content = content.trim();

        let prompt = CompletionPrompts.TaskActivitySummary;
        if (format) {
            switch (format) {
                case 'short':
                    prompt = CompletionPrompts.TaskActivitySummaryShort;
                    break;
                case 'detailed':
                    prompt = CompletionPrompts.TaskActivitySummaryDetailed;
                    break;
                case 'bullet':
                    prompt = CompletionPrompts.TaskActivitySummary;
                    break;
                default:
                    prompt = CompletionPrompts.TaskActivitySummary;
            }
        }

        return this.completionService.streamResponse(prompt, { content }, res, workspaceId, userId);
    }

    /**
     * Summarize text content w/ logic to split into multiple API calls if needed.
     */
    async summarizeContent(
        content: string,
        prompt: CompletionPrompts,
        promptNext: CompletionPrompts,
        separators: string[]
    ): Promise<string> {
        let chatRequest = await this.promptService.get(prompt, { userText: content });
        const model = chatRequest.model as ChatModel;
        const tokens = countTokens(content, model);

        // If the content is already summary length, just return it
        if (tokens < this.minTokensForIndividualSummary(3, model)) {
            return content;
        }

        // Occationally content contains extremely long URLs or Base64 encoded images
        // neither of which is typically useful for summarization and can make content
        // too large for the API call & awkward to split up.
        content = removeHyperlinks(content);
        if (tokens < this.minTokensForIndividualSummary(3, model)) {
            return content;
        }

        // If the content can fit into a single AI API call, do that
        if (!isInputTooLarge(chatRequest)) {
            const resp = await this.openAiClient.apiCall(chatRequest);
            return resp.choices[0].message.content;
        }

        // Otherwise, we need to chunk it up
        const maxChunkSize = await this.completionService.maxAccumalatedChunkSize(prompt, promptNext);
        const splitter = new RecursiveCharacterTextSplitter({
            // `chunkSize` is in characters, but `maxChunkedRequestSize` returns
            // tokens. Each token is ~4 characters long, but it's better to be a
            // little short than to go over and get an "input too large" exception.
            chunkSize: Math.floor(maxChunkSize * 3.5),
            // We don't want any duplicate "overlapping" content
            chunkOverlap: 0,
            // If possible, try to split on comment boundary
            separators,
        });
        let accumulator = '';
        const chunks = await splitter.splitText(content);
        for (const chunk of chunks) {
            if (accumulator === '') {
                chatRequest = await this.promptService.get(prompt, { userText: chunk });
            } else {
                chatRequest = await this.promptService.get(promptNext, { userText: chunk, accumulator });
            }
            const resp = await this.openAiClient.apiCall(chatRequest);
            accumulator = resp.choices[0].message.content;
        }
        return accumulator;
    }

    async fetchAndSummarizeTaskContent(workspaceId: string, taskId: string): Promise<string> {
        const task = await this.dataService.fetchData(workspaceId, taskId);
        return this.summarizeTaskContent(task);
    }

    /**
     * Generate a summary for the content inside the task object itself. Will be
     * combined with summaries of related objects for the final full summary.
     */
    async summarizeTaskContent(task: TaskModel): Promise<string> {
        const taskTextContent = await this.dataService.formatTask(task);
        const separators = ['\n', '.', '!', '?', ';', ',', ' ']; // Break on end of sentence
        return this.summarizeContent(
            taskTextContent,
            CompletionPrompts.Summary,
            CompletionPrompts.SummaryNext,
            separators
        );
    }

    async fetchAndSummarizeTaskComments(workspaceId: string, taskId: string): Promise<string> {
        const comments = await this.dataService.fetchTaskComments(workspaceId, taskId);
        return this.summarizeTaskComments(comments);
    }

    /**
     * Generate a summary for comments associated with a task.
     */
    async summarizeTaskComments(comments: CommentWithUsers[]): Promise<string> {
        const commentTextContent = await this.dataService.formatComments(comments);
        const separators = ['- Comment', '    - Reply', '\n']; // break on start of comment
        return this.summarizeContent(
            commentTextContent,
            CompletionPrompts.TaskSummaryComments,
            CompletionPrompts.TaskSummaryCommentsNext,
            separators
        );
    }

    async fetchAndSummarizeSubtasks(workspaceId: string, taskId: string): Promise<string> {
        const substasks = await this.dataService.loadSubtasks(workspaceId, taskId);
        return this.summarizeSubtasks(substasks);
    }

    /**
     * Generate a summary for comments associated with a task.
     */
    async summarizeSubtasks(subtasks: SimpleSubtaskModel[]): Promise<string> {
        const subtasksText = this.dataService.formatSubtasks(subtasks);
        const separators = ['- [ ]', '- [ x ]', ' ']; // break on new subtask
        return this.summarizeContent(
            subtasksText,
            CompletionPrompts.Summary,
            CompletionPrompts.SummaryNext,
            separators
        );
    }

    /**
     * Resolve summaries of all components within a task and then generate text
     * that can be sent for a final summary.
     */
    async combineData(
        taskPromise: Promise<string>,
        subtasksPromise: Promise<string>,
        commentsPromise: Promise<string>
    ): Promise<string> {
        return Promise.all([taskPromise, subtasksPromise, commentsPromise]).then(([task, subtasks, comments]) => {
            let text = task;
            if (subtasks) {
                text += `\n\n## Subtasks:\n${subtasks}`;
            }
            if (comments) {
                text += `\n\n## Comments:\n${comments}`;
            }
            return text;
        });
    }

    /**
     * Loads and formats the "task context". A big string that contains a lot of different task data.
     */
    async getContent(workspaceId: WorkspaceId, taskId: TaskId): Promise<string> {
        return this.combineData(
            this.fetchAndSummarizeTaskContent(workspaceId, taskId),
            this.fetchAndSummarizeSubtasks(workspaceId, taskId),
            this.fetchAndSummarizeTaskComments(workspaceId, taskId)
        );
    }

    /**
     * Get a version of the "task context" that does not do any intermediate summaries.
     * All the raw description/comments will be included
     */
    async getContentLong(workspaceId: WorkspaceId, taskId: TaskId): Promise<string> {
        return this.combineData(
            this.dataService.fetchData(workspaceId, taskId).then(task => this.dataService.formatTask(task)),
            this.dataService
                .loadSubtasks(workspaceId, taskId)
                .then(subtasks => this.dataService.formatSubtasks(subtasks)),
            this.dataService
                .fetchTaskComments(workspaceId, taskId)
                .then(comments => this.dataService.formatComments(comments))
        );
    }

    async formatCommentsForActivity(commentIds: string[], minDate: number, taskContent: string): Promise<string> {
        const asyncPool = pLimit(16); // Put a cap on concurrent API calls
        const individualSummaries = await Promise.all(
            commentIds.map(commentId =>
                asyncPool(async () => {
                    const thread = await this.commentDataService.loadThread(commentId);
                    const [oldComments, newComments] = _.partition(thread, c => Number(c.date) < minDate);

                    if (oldComments.length === 0) {
                        return this.formatSummarizedCommentChunkForActivity(
                            newComments,
                            taskContent,
                            commentIds.length
                        );
                    }

                    const previousSummary = await this.formatSummarizedCommentChunkForActivity(
                        oldComments,
                        taskContent,
                        commentIds.length
                    );
                    const newCommentChunk = await this.commentDataService.formatThread(newComments);
                    const chatRequest = await this.promptService.get(CompletionPrompts.DeltaCommentThreadSummary, {
                        userText: newCommentChunk,
                        conversationContext: previousSummary,
                        taskContent,
                    });
                    const resp = await this.openAiClient.apiCall(chatRequest);
                    return resp.choices[0].message.content;
                })
            )
        );
        return individualSummaries.join('\n--- END ---\n');
    }

    async formatSummarizedCommentChunkForActivity(
        comments: Comment[],
        taskContent: string,
        totalNumThreads: number
    ): Promise<string> {
        const commentChunk = await this.commentDataService.formatThread(comments);
        const promptType = CompletionPrompts.ActivityCommentThreadSummary;
        const chatRequest = await this.promptService.get(promptType, { userText: commentChunk, taskContent });
        const model = chatRequest.model as ChatModel;
        if (countTokens(commentChunk, model) < this.minTokensForIndividualSummary(totalNumThreads, model)) {
            return commentChunk;
        }
        const resp = await this.openAiClient.apiCall(chatRequest);
        return resp.choices[0].message.content;
    }
}
