import { Get, HttpC<PERSON>, HttpStatus, Param, ParseBoolPipe, Query, UseGuards } from '@nestjs/common';

import { AiFieldGuard, AiFieldPaywallUsageService } from '@clickup/field/ai';
import { ActingUserId } from '@clickup/utils/authentication';
import { WorkspaceUserOverride } from '@clickup/utils/authorization';
import { WorkspaceUserType } from '@clickup/utils/authorization/models';

import { AiController, AiRoute } from '../../shared/decorators/controller.decorators';

@AiController({ tags: 'Custom Fields Rate Limiting', prefix: '/fields' })
export class AiFieldsRateLimitingController {
    constructor(private readonly aiFieldPaywallUsageService: AiFieldPaywallUsageService) {}

    @AiRoute({ description: 'Used to check rate limits for AI Fields (called from Slapdash before any logic)' })
    @Get('/check-usage')
    @HttpCode(HttpStatus.NO_CONTENT)
    @WorkspaceUserOverride(WorkspaceUserType.WorkspaceUserOrClickbotRequired)
    @UseGuards(AiFieldGuard)
    checkAiFieldRateLimit(): undefined {
        return undefined;
    }

    @AiRoute({ description: 'Used to increment rate limits for AI Fields (called from Slapdash after all the logic)' })
    @Get('/increment-usage')
    @HttpCode(HttpStatus.NO_CONTENT)
    @WorkspaceUserOverride(WorkspaceUserType.WorkspaceUserOrClickbotRequired)
    increaseAiFieldRateLimit(
        @ActingUserId() userId: number,
        @Param('workspaceId') workspaceId: string,
        @Query('fromAutomation', ParseBoolPipe) isAutoAI: boolean
    ): Promise<void> {
        return this.aiFieldPaywallUsageService.incrementUsage({
            userId,
            workspaceId,
            isAutoAI,
            increment: 1,
        });
    }
}
