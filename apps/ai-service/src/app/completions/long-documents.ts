import { CompletionPrompts, SseStreamLlm } from '@clickup/ai/core';

import { splitTokens } from '../shared/openai/utils/tokens';

// Inputs for operations on very long documents that need to be split into chunks
export interface AccumlatedCompletionInput {
    userText: string;
    firstPrompt: CompletionPrompts;
    followupPrompt: CompletionPrompts;
    responseStream: SseStreamLlm;
    userId: string;
    chatId: string;
    options?: ChainedCompletionOptions;
}

export interface ChunkedCompletionInput {
    userText: string;
    promptType: CompletionPrompts;
    responseStream: SseStreamLlm;
    userId: string;
    chatId: string;
    substitutions?: Record<string, string>;
}

export interface ChainedCompletionOptions {
    /**
     * Text to put between each chunk
     */
    chunkDelimiter?: string;

    /**
     * The max length of the prompt to allocate to the accumulator
     */
    maxAccumulatorLength?: number;
}

export interface DocumentSplitter {
    /**
     * Split 1 large document into chunks
     */
    split(text: string): string[];
}

export class EvenChunkSplitter {
    /**
     * When splitting text, wow far backwards (in tokens) should we look when
     * trying to avoid splitting in the middle of a word.
     */
    readonly maxBackTrackSize = 10;

    constructor(private maxChunkSize: number) {}

    /**
     * Split a long input into smaller pieces to be fed to the model individually.
     * Tries to avoid splitting in the middle of a word if possible.
     *
     * @param text The text from the user to split
     */
    split(text: string): string[] {
        // @TODO: currently all the models we use have the same encodings, but
        //        we should take model into account in case that changes in the
        //        future.
        const tokens = splitTokens(text, 'gpt-3.5-turbo');

        // Calculate ideal chunk size
        const numChunks = Math.ceil(tokens.length / this.maxChunkSize);
        const chunkSize = Math.ceil(tokens.length / numChunks);

        const chunks = [];
        let start = 0;
        let end = chunkSize;
        while (start < tokens.length) {
            // Avoid splitting in the middle of a word.
            //
            // Some words are split into multiple tokens, so if we split by token
            // we could end up splitting a word in half. To determine if we are
            // splitting in the middle of a word, we look if there is another token
            // and if it starts with whitespace or not.
            //
            // If we are in the middle of a word, we backtrack until we find a word
            // boundary or we exceed `maxBackTrackSize` tries and give up.
            let i = 0;
            const originalEnd = end;
            while (tokens[end] !== undefined && !/^\s/.test(tokens[end])) {
                i++;
                end--;
                if (i >= this.maxBackTrackSize || end <= start) {
                    end = originalEnd;
                    break;
                }
            }

            chunks.push(tokens.slice(start, end).join(''));
            start = end;
            end = start + chunkSize;
        }
        return chunks;
    }
}
