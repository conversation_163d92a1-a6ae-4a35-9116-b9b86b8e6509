import 'reflect-metadata';

import { Module } from '@nestjs/common';
import { AxiosError } from 'axios';
import axiosRetry from 'axios-retry';

import { CommentsApiModule } from '@clickup/gateway-client/api/comments';
import { ScheduledCommentsApiModule } from '@clickup/gateway-client/api/scheduled-comments';
import { TemporalWorkerModule } from '@clickup/temporal/worker';
import { ActivitiesService, CommentsScheduledTemporalWorkflows } from '@clickup/temporal/workflows/comments-scheduled';
import { UtilsMetricsModule } from '@clickup/utils/metrics';

@Module({
    imports: [
        TemporalWorkerModule.forRoot({
            useActivitiesClass: ActivitiesService,
            useWorkflowsClass: CommentsScheduledTemporalWorkflows,
            imports: [
                CommentsApiModule,
                ScheduledCommentsApiModule.register({
                    retryConfig: {
                        retries: 5,
                        retryDelay: axiosRetry.exponentialDelay,
                        retryCondition: (error: AxiosError) =>
                            axiosRetry.isNetworkError(error) ||
                            axiosRetry.isRetryableError(error) || // 5xx errors
                            error.response?.status === 429,
                    },
                }),
                UtilsMetricsModule,
            ],
        }),
    ],
})
export class AppModule {}
