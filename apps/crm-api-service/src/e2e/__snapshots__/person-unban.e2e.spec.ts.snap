// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`User Unban should unban single person 1`] = `
Object {
  "crm.user_notes": Array [
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "date_posted": Any<Number>,
        "date_updated": null,
        "id": 2,
        "note": "unban",
        "person_id": 20001,
        "poster": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
        "sticky": false,
      },
      "oldRow": null,
      "schema": "crm",
      "table": "user_notes",
    },
  ],
  "task_mgmt.banned_users": Array [
    Object {
      "command": "DELETE",
      "level": "ROW",
      "newRow": null,
      "oldRow": Object {
        "admin": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
        "date": Any<Number>,
        "note": null,
        "note_id": 123,
        "userid": 20001,
      },
      "schema": "task_mgmt",
      "table": "banned_users",
    },
  ],
}
`;

exports[`User Unban should unban single person 2`] = `
Array [
  Object {
    "id": 10001,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 10002,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 10003,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 10004,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 20001,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 20002,
    "invalidate_tokens_before": "111111111111",
  },
  Object {
    "id": 20003,
    "invalidate_tokens_before": "111111111111",
  },
  Object {
    "id": 20004,
    "invalidate_tokens_before": "111111111111",
  },
]
`;

exports[`User Unban should unban single person in shard 1`] = `
Array [
  Object {
    "body": Object {
      "action": "unban_user",
      "data": Object {
        "emails": Array [],
        "reason": "",
      },
    },
    "team_id": "10000",
    "userid": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
  },
  Object {
    "body": Object {
      "action": "unban_user",
      "data": Object {
        "emails": Array [],
        "reason": "",
      },
    },
    "team_id": "20000",
    "userid": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
  },
]
`;

exports[`User Unban should unban single person in shard 2`] = `
Array [
  Object {
    "id": 10001,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 10002,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 10003,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 10004,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 20001,
    "invalidate_tokens_before": "0",
  },
  Object {
    "id": 20002,
    "invalidate_tokens_before": "111111111111",
  },
  Object {
    "id": 20003,
    "invalidate_tokens_before": "111111111111",
  },
  Object {
    "id": 20004,
    "invalidate_tokens_before": "111111111111",
  },
]
`;
