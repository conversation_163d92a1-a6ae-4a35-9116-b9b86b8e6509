// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Team Internal Guest Email Domain Exclusions API PUT /v1/team/:team_id/internalGuestEmailDomainExclusions should handle error from workspace service 1`] = `
Object {
  "ECODE": "WORKSPACE_002",
  "err": "Error while fetching workspace details",
  "message": "Error while fetching workspace details",
  "status": 500,
  "timestamp": Any<String>,
}
`;

exports[`Team Internal Guest Email Domain Exclusions API PUT /v1/team/:team_id/internalGuestEmailDomainExclusions should reject if domain_exclusions is missing 1`] = `
Object {
  "err": "Bad Request Exception",
  "error": "Bad Request",
  "message": Array [
    "domain_exclusions should not be null or undefined",
    "each value in domain_exclusions must be a string",
    "domain_exclusions must be an array",
  ],
  "status": 400,
  "statusCode": 400,
  "timestamp": Any<String>,
}
`;

exports[`Team Internal Guest Email Domain Exclusions API PUT /v1/team/:team_id/internalGuestEmailDomainExclusions should reject if domain_exclusions is not an array 1`] = `
Object {
  "err": "Bad Request Exception",
  "error": "Bad Request",
  "message": Array [
    "domain_exclusions must be an array",
  ],
  "status": 400,
  "statusCode": 400,
  "timestamp": Any<String>,
}
`;

exports[`Team Internal Guest Email Domain Exclusions API PUT /v1/team/:team_id/internalGuestEmailDomainExclusions should reject if notes is missing 1`] = `
Object {
  "err": "Bad Request Exception",
  "error": "Bad Request",
  "message": Array [
    "notes should not be empty",
    "notes must be a string",
  ],
  "status": 400,
  "statusCode": 400,
  "timestamp": Any<String>,
}
`;

exports[`Team Internal Guest Email Domain Exclusions API PUT /v1/team/:team_id/internalGuestEmailDomainExclusions should update internal guest email domain exclusions 1`] = `
Object {
  "task_mgmt.team_edit_audit": Array [
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "body": Object {
          "action": "update_internal_guest_email_domain_exclusions",
          "domain_exclusions": Object {
            "passed": Array [
              "new-domain.com",
            ],
            "previous": Array [
              "existing-domain.com",
            ],
            "updated": Array [
              "new-domain.com",
            ],
          },
          "guests_domains": Array [],
          "notes": "Test notes for domain exclusion update",
        },
        "date": Any<Number>,
        "id": Any<String>,
        "team_id": 10101,
        "userid": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "team_edit_audit",
    },
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "body": Object {
          "action": "rollup_scheduled",
          "billed_users_this_cycle": 1,
          "process_name": "CRM",
          "reason": "crm_edit_internal_guest_domain_exclusions",
          "reason_details": "Test notes for domain exclusion update",
        },
        "date": Any<Number>,
        "id": Any<String>,
        "team_id": 10101,
        "userid": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "team_edit_audit",
    },
  ],
  "task_mgmt.teams_to_charge": Array [
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "data": Object {
          "billed_users_this_cycle": 1,
          "free_seats": 0,
          "reason": "crm_edit_internal_guest_domain_exclusions",
        },
        "date": Any<Number>,
        "date_scheduling": null,
        "date_updating": null,
        "node_scheduling": null,
        "node_updating": null,
        "processed": false,
        "team_id": 10101,
        "token": null,
        "userid": 10101,
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "teams_to_charge",
    },
  ],
}
`;

exports[`Team Internal Guest Email Domain Exclusions API PUT /v1/team/:team_id/internalGuestEmailDomainExclusions should update internal guest email domain exclusions with guest domains 1`] = `
Object {
  "task_mgmt.team_edit_audit": Array [
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "body": Object {
          "action": "update_internal_guest_email_domain_exclusions",
          "domain_exclusions": Object {
            "passed": Array [
              "new-domain.com",
            ],
            "previous": Array [
              "existing-domain.com",
            ],
            "updated": Array [
              "new-domain.com",
            ],
          },
          "guests_domains": Array [
            "guest-domain.com",
          ],
          "notes": "Test notes for domain exclusion update with guest domains",
        },
        "date": Any<Number>,
        "id": Any<String>,
        "team_id": 10101,
        "userid": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "team_edit_audit",
    },
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "body": Object {
          "action": "rollup_scheduled",
          "billed_users_this_cycle": 1,
          "process_name": "CRM",
          "reason": "crm_edit_internal_guest_domain_exclusions",
          "reason_details": "Test notes for domain exclusion update with guest domains",
        },
        "date": Any<Number>,
        "id": Any<String>,
        "team_id": 10101,
        "userid": "9d0991f0-c5cf-4069-ae7c-4c458da78b9e",
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "team_edit_audit",
    },
  ],
  "task_mgmt.teams_to_charge": Array [
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "data": Object {
          "billed_users_this_cycle": 1,
          "free_seats": 0,
          "reason": "crm_edit_internal_guest_domain_exclusions",
        },
        "date": Any<Number>,
        "date_scheduling": null,
        "date_updating": null,
        "node_scheduling": null,
        "node_updating": null,
        "processed": false,
        "team_id": 10101,
        "token": null,
        "userid": 10101,
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "teams_to_charge",
    },
  ],
}
`;
