import { Injectable } from '@nestjs/common';

import { TeamEditAuditService } from '@clickup/billing/team-audit';
import { batchProcess, CreateUserNoteResult, CrmUserId, CustomerUserId, TeamId } from '@clickup/crm/shared-code';
import type { SimpleObjectVersionClient } from '@clickup/object-version-common';
import { ConfigService } from '@clickup/utils/config';
import { ShardIdConfig } from '@clickup/utils/config-types';
import { DBClient } from '@clickup/utils/db';
import { FormatLogger } from '@clickup/utils/logging';

@Injectable()
export class PersonBanRepository {
    private readonly currentShard: ShardIdConfig;

    constructor(
        private readonly dbClient: DBClient,
        private readonly logger: FormatLogger,
        private readonly configService: ConfigService,
        private readonly teamEditAuditService: TeamEditAuditService
    ) {
        this.currentShard = this.configService.get<ShardIdConfig>('sharding.shard_id');
    }

    async banUsers(userToNoteId: CreateUserNoteResult[], adminId: CrmUserId, client: SimpleObjectVersionClient) {
        const now = Date.now();
        const userIds = userToNoteId.map(e => e.userId);
        const noteIds = userToNoteId.map(e => e.noteId);

        await client.queryAsync(
            'UPDATE task_mgmt.users SET invalidate_tokens_before = 2000000000000 WHERE id = ANY($1)',
            [userIds]
        );

        await client.queryAsync(
            'INSERT INTO task_mgmt.banned_users (userid, admin, date, note_id) SELECT * FROM UNNEST ($1::int[], $2::uuid[], $3::bigint[], $4::bigint[]) ON CONFLICT (userid) DO NOTHING',
            [userIds, Array(userIds.length).fill(adminId), Array(userIds.length).fill(now), noteIds]
        );
    }

    async hidePublicViews(userIds: CustomerUserId[]) {
        const batchSize = 1000;
        await Promise.all([
            batchProcess(batchSize, 'person_ban_views', batch =>
                this.dbClient
                    .writeAsync(
                        `UPDATE task_mgmt.views
                         SET public = false,
                             form_active = false
                         WHERE view_id in (SELECT view_id
                                           FROM task_mgmt.views
                                           WHERE creator = ANY ($1)
                                             AND (public IS NOT false OR form_active IS NOT false)
                                           LIMIT $2)
                         RETURNING view_id`,
                        [userIds, batch]
                    )
                    .catch(err => {
                        this.logger.error({ msg: 'Failed to update views', err, userIds });
                        throw err;
                    })
            ),
            batchProcess(batchSize, 'person_ban_view_docs', batch =>
                this.dbClient
                    .writeAsync(
                        `UPDATE task_mgmt.view_docs
                         SET public = false
                         WHERE id in (SELECT id
                                      FROM task_mgmt.view_docs
                                      WHERE creator = ANY ($1)
                                        AND public IS NOT false
                                      LIMIT $2)
                         RETURNING id`,
                        [userIds, batch]
                    )
                    .catch(err => {
                        this.logger.error({ msg: 'Failed to update view docs based on creator', err, userIds });
                        throw err;
                    })
            ),
            batchProcess(batchSize, 'person_ban_view_docs', batch =>
                this.dbClient
                    .writeAsync(
                        `UPDATE task_mgmt.view_docs
                         SET public = false
                         WHERE id IN (SELECT view_docs.id
                                      FROM task_mgmt.view_docs
                                               JOIN task_mgmt.views ON view_docs.view_id = views.view_id
                                               JOIN task_mgmt.team_members
                                                    ON team_members.team_id = views.team_id AND team_members.userid = ANY ($1)
                                      WHERE views.creator = ANY ($1)
                                        AND view_docs.public IS NOT false
                                      LIMIT $2)
                         RETURNING id`,
                        [userIds, batch]
                    )
                    .catch(err => {
                        this.logger.error({ msg: 'Failed to update view docs based on parent view', err, userIds });
                        throw err;
                    })
            ),
        ]);
    }

    async hidePublicTasks(userIds: CustomerUserId[]) {
        const batchSize = 1000;

        await batchProcess(batchSize, 'person_ban_tasks', batch =>
            this.dbClient
                .writeAsync(
                    `WITH limited_tasks AS (
                        SELECT int_id
                        FROM task_mgmt.tasks
                        WHERE creator = ANY($1)
                          AND public IS NOT false
                        LIMIT $2
                    )
                     UPDATE task_mgmt.tasks
                     SET public = false
                     WHERE creator = ANY($1)
                       AND public IS NOT false
                       AND tasks.int_id IN (
                         SELECT int_id
                         FROM limited_tasks
                         LIMIT $2
                     )`,
                    [userIds, batch]
                )
                .catch(err => {
                    this.logger.error({ msg: 'Failed to update tasks', err, userIds });
                    throw err;
                })
        );
    }

    async createWorkspaceNotesForUsers(userIds: CustomerUserId[], action: string, reason: string, adminId: CrmUserId) {
        const workspaceIdsResult = await this.dbClient.readAsync<{ team_id: TeamId }>(
            `SELECT DISTINCT tm.team_id
             FROM task_mgmt.team_members tm
                      LEFT JOIN task_mgmt.workspace_to_shard wts
                                ON tm.team_id = wts.workspace_id
             WHERE tm.userid = ANY ($1)
               AND tm.deleted IS NOT TRUE
               AND wts.shard_id = $2`,
            [userIds, this.currentShard]
        );

        const workspaceIds = workspaceIdsResult.rows.map(row => row.team_id);

        const promises = workspaceIds.map(async workspaceId => {
            try {
                const emailsResult = await this.dbClient.readAsync<{ email: string }>(
                    `SELECT email
                 FROM task_mgmt.team_members tm
                      JOIN task_mgmt.users u ON tm.userid = u.id
                 WHERE tm.team_id = $1
                   AND tm.deleted IS NOT TRUE
                   AND tm.userid = ANY($2)`,
                    [workspaceId, userIds]
                );

                const userEmails = emailsResult.rows.map(row => row.email);

                await this.teamEditAuditService
                    .storeTeamUpdated(adminId, workspaceId, {
                        action,
                        data: {
                            reason,
                            emails: userEmails,
                        },
                    })
                    .catch(err => {
                        this.logger.error({
                            msg: 'Failed to insert CRM edit audit',
                            adminId,
                            team_id: workspaceId,
                            err,
                        });
                    });
            } catch (err) {
                this.logger.error({
                    msg: 'Failed to fetch member emails for workspace',
                    team_id: workspaceId,
                    err,
                });
            }
        });

        return Promise.all(promises);
    }

    async unbanUser(personId: CustomerUserId, globalDbClient: SimpleObjectVersionClient) {
        return Promise.all([
            globalDbClient.queryAsync('DELETE FROM task_mgmt.banned_users WHERE userid = $1', [personId]),
            globalDbClient.queryAsync('UPDATE task_mgmt.users SET invalidate_tokens_before = 0 WHERE id = $1', [
                personId,
            ]),
        ]);
    }
}
