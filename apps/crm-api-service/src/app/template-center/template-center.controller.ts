import { Body, Controller, Delete, Get, HttpException, Param, Post, Put } from '@nestjs/common';
import { ApiBearerAuth, ApiCreatedResponse, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';

import { GetTemplateUseCasesDro, TemplateUseCaseDro } from './dro/template-use-cases.dro';
import { CreateTemplateUseCaseDto } from './dto/create-template-use-case.dto';
import { UpdateTemplateUseCaseDto } from './dto/update-template-use-case.dto';
import { TemplateUseCasesService } from './template-use-cases.service';

@ApiTags('Template Center')
@ApiBearerAuth()
@Controller('v1/templateCenter')
export class TemplateCenterController {
    constructor(private readonly templateUseCasesService: TemplateUseCasesService) {}

    @Get('use_cases')
    @ApiOperation({ summary: 'Get template use cases' })
    @ApiOkResponse({ type: GetTemplateUseCasesDro })
    async getUseCases(): Promise<GetTemplateUseCasesDro> {
        return this.templateUseCasesService.getUseCases();
    }

    @Post('use_cases')
    @ApiOperation({ summary: 'Create a new template use case' })
    @ApiCreatedResponse({ type: TemplateUseCaseDro })
    async createUseCase(@Body() data: CreateTemplateUseCaseDto): Promise<TemplateUseCaseDro> {
        try {
            return await this.templateUseCasesService.createUseCase(data);
        } catch (err) {
            // Legacy error handling - pass through DB errors with status
            if ((err as any).status) {
                throw new HttpException(err, (err as any).status);
            }
            throw err;
        }
    }

    @Put('use_cases/:use_case_id')
    @ApiOperation({ summary: 'Update a template use case' })
    @ApiOkResponse({ description: 'Template use case updated successfully' })
    async updateUseCase(
        @Param('use_case_id') useCaseId: string,
        @Body() data: UpdateTemplateUseCaseDto
    ): Promise<Record<string, never>> {
        try {
            return await this.templateUseCasesService.updateUseCase(useCaseId, data);
        } catch (err) {
            // Legacy error handling - pass through DB errors with status
            if ((err as any).status) {
                throw new HttpException(err, (err as any).status);
            }
            throw err;
        }
    }

    @Delete('use_cases/:use_case_id')
    @ApiOperation({ summary: 'Delete a template use case' })
    @ApiOkResponse({ description: 'Template use case deleted successfully' })
    async deleteUseCase(@Param('use_case_id') useCaseId: string): Promise<void> {
        try {
            return await this.templateUseCasesService.deleteUseCase(useCaseId);
        } catch (err) {
            // Legacy error handling - pass through DB errors with specific ECODE TC_016
            if ((err as any).status) {
                throw new HttpException(err, (err as any).status);
            }
            throw err;
        }
    }
}
