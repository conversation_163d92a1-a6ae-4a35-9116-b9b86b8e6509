# copytask-temporal-worker

## Run the project

### Prerequisites

Before you can run the project, you need to install `nx` and the `nestjs` CLI

```
npm install -g nx
npm i -g @nestjs/cli
npm i
```

### Run command

Nx provide simple wrappers to run an app inside the repo.
Our app name is `copytask-temporal-worker`, so to start the app you can run the following command:

```
nx run copytask-temporal-worker:serve:development
```

### Run Dependencies

#### Temporal

Make sure Temporal is running locally:

```
docker-compose --profile temporal up
```

## Docker

### Building Image

Use the script `docker-build.sh` to build the container.

To build the docker image manually run the following command:

```
docker buildx build --load --secret id=npmrc,src=$HOME/.npmrc -t copytask-temporal-worker:1.0 -f ./apps/copytask-temporal-worker/Dockerfile .
```

<hr>

## Development

### Running Locally

You can run the build image locally using the following command:
You will need to provide AWS credentials to the container. You can copy it from the file `~/.aws/credentials`

```
docker run -e AWS_ACCESS_KEY_ID=<yourID> -e AWS_SECRET_ACCESS_KEY=<yourKey> -e DOCKER=true -e DOCKER_LOCAL=app_only -p 3333:3333 copytask-temporal-worker:latest
```

### Debugging

In VSCode:

1. Cmd + Shift + P to open command pallet and type: "JavaScript Debug Terminal" to open a terminal window that automatically connects to the debug host.
2. Run in the terminal `npx nx run copytask-temporal-worker:inspect`

In WebStorm:

1. Run in the terminal `npx nx run copytask-temporal-worker:inspect`
2. Choose Attached debugger from debug config options (edit the configuration to choose the correct port, in this case 9300)

### Running tests

Run main copytask-temporal-worker tests:

```bash
npx nx run copytask-temporal-worker:test
```

Run tests for affected files:

```bash
npx nx affected:test
```
