import { AsyncStorage, initContext } from '@clickup/shared/utils-async-storage';
import { ClickUpError } from '@clickup-legacy/utils/errors';

import { IStep } from '../interfaces/IStep';
import { IWorkflow, IWorkflowResponse } from '../interfaces/IWorkflow';
import { Outcome } from '../interfaces/WMSLogger';
import {
    CommonContext,
    DeleteWorkspaceResponse,
    DeletionContext,
    ICommonContext,
    MergingContext,
    Services,
    WorkspaceResponse,
} from '../io/input';
import { GenericBDRResponse, GenericDeleteWorkspaceResponse } from '../io/output';

export abstract class WMSWorkflow implements IWorkflow {
    abstract prepGenericResponse(success: boolean, errCode: string, errMessage: string): IWorkflowResponse;

    protected abstract context: ICommonContext;

    protected abstract services: Services<unknown>;

    protected abstract start: IStep;

    protected abstract name: string;

    protected abstract log(startTime: number): void;

    protected abstract logError(startTime: number, ex?: any): void;

    cancel(): void {
        this.start.cancel();
    }

    async run(): Promise<IWorkflowResponse> {
        const startTime = Date.now();
        try {
            this.services.logger.log(this.context, {
                step: `${this.name}Run`,
                outcome: Outcome.starting,
            });

            const asyncStorage = AsyncStorage.getInstance();

            return await initContext(asyncStorage, async () => {
                const workspaceId = this.context?.workspaceId;
                const context = asyncStorage.getContext();
                context.unverifiedWorkspaceId = Number(workspaceId);
                context.verifiedWorkspace = {
                    requestState: 'workspaceResolved',
                    untrustedId: Number(workspaceId),
                    debug: {
                        calls: [],
                        idConflictDetected: false,
                        headerConflictDetected: false,
                        shardConflictDetected: false,
                    },
                };
                this.context = await this.start.execute(this.context);

                const resp = this.prepGenericResponse(true, '', '');
                this.log(startTime);
                return resp;
            });
        } catch (ex) {
            this.logError(startTime, ex);
            if (ex instanceof ClickUpError) {
                return this.prepGenericResponse(false, (ex as ClickUpError).ECODE, (ex as ClickUpError).msg as string);
            }
            return this.prepGenericResponse(false, '', 'Workflow Execution Failed! Refer to logs for more details');
        }
    }

    async describe(): Promise<string> {
        return `flowchart TB\n${this.start.describe()}`;
    }
}
export class MigrationWorkflow extends WMSWorkflow {
    prepGenericResponse(success: boolean, errCode: string, errMessage: string): GenericBDRResponse {
        return new GenericBDRResponse(this.context.jobId, this.prepWorkspaceResponse(), success, errCode, errMessage);
    }

    private prepWorkspaceResponse(): WorkspaceResponse {
        return new WorkspaceResponse(
            this.context.workspaceId,
            this.context.jobId,
            this.context.sourceShardId,
            this.context.destinationShardId,
            this.context.datastore
        );
    }

    protected log(startTime: number) {
        this.services.logger.log(this.context, {
            step: `${this.name}Run`,
            outcome: Outcome.completed,
            duration: (Date.now() - startTime) * 1000 * 1000, // This is a datadog standard attribute and it expects the value in nanoseconds
        });
    }

    protected logError(startTime: number, ex?: any) {
        this.services.logger.error(this.context, {
            error: ex,
            step: `${this.name}Run`,
            outcome: Outcome.failed,
            duration: (Date.now() - startTime) * 1000 * 1000,
        });
    }

    constructor(
        protected start: IStep,
        protected name: string,
        protected context: CommonContext,
        protected services: Services<unknown>
    ) {
        super();
    }
}

export class DeletionWorkflow extends WMSWorkflow {
    prepGenericResponse(success: boolean, errCode: string, errMessage: string): GenericDeleteWorkspaceResponse {
        return new GenericDeleteWorkspaceResponse(
            this.context.jobId,
            this.prepWorkspaceResponse(),
            success,
            errCode,
            errMessage
        );
    }

    private prepWorkspaceResponse(): DeleteWorkspaceResponse {
        return new DeleteWorkspaceResponse(
            this.context.workspaceId,
            this.context.jobId,
            this.context.shardId,
            this.context.datastore
        );
    }

    protected log(startTime: number) {
        this.services.logger.log(this.context, {
            step: `${this.name}Run`,
            outcome: Outcome.completed,
            duration: (Date.now() - startTime) * 1000 * 1000, // This is a datadog standard attribute and it expects the value in nanoseconds
        });
    }

    protected logError(startTime: number, ex?: any) {
        this.services.logger.error(this.context, {
            error: ex,
            step: `${this.name}Run`,
            outcome: Outcome.failed,
            duration: (Date.now() - startTime) * 1000 * 1000,
        });
    }

    constructor(
        protected start: IStep,
        protected name: string,
        protected context: DeletionContext,
        protected services: Services<unknown>
    ) {
        super();
    }
}

export class MergingWorkflow extends WMSWorkflow {
    prepGenericResponse(success: boolean, errCode: string, errMessage: string): IWorkflowResponse {
        return {
            workflowId: this.context.jobId,
            success,
            errorCode: errCode,
            errMsg: errMessage,
            workspace: this.prepWorkspaceResponse(),
            planId: this.context.planId,
            sourceWorkspaceId: this.context.workspaceId,
            destinationWorkspaceId: this.context.destinationWorkspaceId,
        } as IWorkflowResponse;
    }

    private prepWorkspaceResponse(): WorkspaceResponse {
        return new WorkspaceResponse(
            this.context.workspaceId,
            this.context.jobId,
            this.context.shardId,
            this.context.shardId,
            this.context.datastore
        );
    }

    protected log(startTime: number) {
        this.services.logger.log(this.context, {
            step: `${this.name}Run`,
            outcome: Outcome.completed,
            duration: (Date.now() - startTime) * 1000 * 1000, // This is a datadog standard attribute and it expects the value in nanoseconds
        });
    }

    protected logError(startTime: number, ex?: any) {
        this.services.logger.error(this.context, {
            error: ex,
            step: `${this.name}Run`,
            outcome: Outcome.failed,
            duration: (Date.now() - startTime) * 1000 * 1000,
        });
    }

    constructor(
        protected start: IStep,
        protected name: string,
        protected context: MergingContext,
        protected services: Services<unknown>
    ) {
        super();
    }
}
