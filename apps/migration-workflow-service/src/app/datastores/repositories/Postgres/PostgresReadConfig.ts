export interface PostgresReadConfig {
    retries: {
        [tableName: string]: number;
        default: number;
    };
    defaultReadTimeout: {
        [tableName: string]: number;
        default: number;
    };
    maxReadTimeout: {
        [tableName: string]: number;
        default: number;
    };
    cursorInitTimeout: {
        [tableName: string]: number;
        default: number;
    };
    proceedToVerificationOnCopyFailure: {
        [tableName: string]: boolean;
        default: boolean;
    };
    useOptimizedQueries: {
        [tableName: string]: boolean;
        default: boolean;
    };
    cursorLimit: {
        [tableName: string]: number;
        default: number;
    };
}
