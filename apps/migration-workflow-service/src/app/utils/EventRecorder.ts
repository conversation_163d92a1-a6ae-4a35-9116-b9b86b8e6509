import { FormatLogger } from '@clickup/utils/logging';

export interface IEventRecorder {
    recordEvent(workspaceId: number, eventType: string, additionalData: any): void;
    onModuleDestroy(): Promise<void>;
}

export interface IEventStore {
    storeEvent(workspaceId: number, eventType: string, additionalData: any): Promise<void>;
}

export class EventRecorder implements IEventRecorder {
    private eventPromises: Promise<void>[] = [];

    constructor(private eventStores: IEventStore[]) {}

    recordEvent(workspaceId: number, eventType: string, additionalData: any): void {
        // normalize additional data
        const data = JSON.parse(JSON.stringify(additionalData));
        for (const eventStore of this.eventStores) {
            const promise = eventStore.storeEvent(workspaceId, eventType, data);
            // Self-deleting promise
            promise.then(() => {
                this.eventPromises.splice(this.eventPromises.indexOf(promise), 1);
            });
            this.eventPromises.push(promise);
        }
    }

    async onModuleDestroy(): Promise<void> {
        await Promise.all(this.eventPromises);
    }
}

export class LogEventStore implements IEventStore {
    constructor(private logger: FormatLogger) {}

    async storeEvent(workspaceId: number, eventType: string, additionalData: any): Promise<void> {
        this.logger.log(
            {
                message: eventType,
                data: additionalData,
                workspaceId,
            },
            'EventRecorder'
        );
    }
}
