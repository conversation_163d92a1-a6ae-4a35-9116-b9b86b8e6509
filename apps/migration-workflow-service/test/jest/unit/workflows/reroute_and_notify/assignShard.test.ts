import { WorkspaceMigrationState } from '../../../../../src/app/enums/MigrationObject';
import { AssignShard } from '../../../../../src/app/workflows/reroute_and_notify/assignShard';
import { getCommonContext, setupMockServices } from '../utils/workflows';

const makeShardAssignmentsMock = jest.fn();

describe('AssignShard step', () => {
    jest.setTimeout(100_000);

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('goes through happy path', async () => {
        const servicesMock = setupMockServices();

        const assignShard = new AssignShard(servicesMock);
        const context = getCommonContext();

        await assignShard.execute(context);

        expect(servicesMock.shardAssignmentClient.makeShardAssignment).toHaveBeenCalledTimes(1);
        expect(servicesMock.shardAssignmentClient.makeShardAssignment).toHaveBeenCalledWith({
            workspaceId: context.workspaceId,
            shardId: context.destinationShardId,
            previousShardId: context.sourceShardId,
            microshardId: 1,
        });

        expect(servicesMock.migrationStateManager.updateMigration).toHaveBeenCalledTimes(1);
        expect(servicesMock.migrationStateManager.updateMigration).toHaveBeenCalledWith(context.jobId, {
            state: WorkspaceMigrationState.WorkspaceShardAssigned,
        });
    });

    // This test is too long for regular unit tests
    it.skip('goes through retries', async () => {
        const servicesMock = setupMockServices();
        jest.useFakeTimers();
        const assignShard = new AssignShard(servicesMock);

        makeShardAssignmentsMock.mockRejectedValueOnce(new Error('test error'));

        await assignShard.execute(getCommonContext());

        expect(makeShardAssignmentsMock).toHaveBeenCalledTimes(2);

        expect(servicesMock.migrationStateManager.updateMigration).toHaveBeenCalledTimes(1);
        expect(servicesMock.migrationStateManager.updateMigration).toHaveBeenCalledWith(getCommonContext().jobId, {
            state: WorkspaceMigrationState.WorkspaceShardAssigned,
        });
    });
});
