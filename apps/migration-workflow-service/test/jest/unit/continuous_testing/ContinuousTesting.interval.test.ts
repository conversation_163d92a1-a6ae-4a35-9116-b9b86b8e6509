import { mock } from 'jest-mock-extended';

import { WorkspaceHandshakeClient } from '@clickup/data-platform/handshake-client';
import { LocalShards, ShardId } from '@clickup/data-platform/wms/environments';
import { DummyConfigService } from '@clickup/utils/config';
import { FormatLogger } from '@clickup/utils/logging';

import { BDRMigrationScheduleService } from '../../../../src/app/api/services/scheduling.service';
import { DataStoreType } from '../../../../src/app/api/types/request';
import { ContinuousTestingJob } from '../../../../src/app/continuous_testing/ContinuousTesting.interval';
import { findWorkspacesToSchedule, TestWorkspaces } from '../../../../src/app/continuous_testing/TestWorkspaces';
import { MigrationState } from '../../../../src/app/enums/MigrationState';
import { MigrationWorkflowPreset } from '../../../../src/app/enums/WorkflowPreset';
import { WorkspaceDataStoreMigrationPlanDTO } from '../../../../src/app/migration_state_management/DTO/WorkspaceDataStoreMigrationPlan.dto';
import { WorkspaceDataStoreMigrationPlanManager } from '../../../../src/app/migration_state_management/WorkspaceDataStoreMigrationPlanManager';

type WorkspaceMigrationPlan = Record<number, Partial<Record<DataStoreType, WorkspaceDataStoreMigrationPlanDTO>>>;

describe('ContinuousTesting job', () => {
    it('cleans workspaces properly', async () => {
        // Setup
        const planManagerMock = mock<WorkspaceDataStoreMigrationPlanManager>();
        const configServiceMock = new DummyConfigService({
            sharding: {
                shard_id: 'g001',
            },
        });
        const loggerMock = mock<FormatLogger>();
        const bdrMigrationScheduleMock = mock<BDRMigrationScheduleService>();
        const mockHandshakeClient = mock<WorkspaceHandshakeClient>();

        mockHandshakeClient.resolveWorkspaceToShardHandshake.mockImplementation(async (workspaceId: string) => {
            if (workspaceId === '1') {
                return { shardId: 'g001' } as any;
            }
            return { shardId: 'na001' } as any;
        });

        const testWorkspaces: TestWorkspaces<LocalShards> = {
            [ShardId.G001]: {
                [ShardId.G001]: [],
                [ShardId.NA001]: ['1'],
            },
            [ShardId.NA001]: {
                [ShardId.G001]: ['3'],
                [ShardId.NA001]: [],
            },
        };

        const migrator = new ContinuousTestingJob(
            configServiceMock,
            planManagerMock,
            bdrMigrationScheduleMock,
            mockHandshakeClient,
            [DataStoreType.BDR],
            testWorkspaces,
            loggerMock
        );

        const workspaceMigrationPlans: WorkspaceMigrationPlan = {
            1: {
                [DataStoreType.BDR]: new WorkspaceDataStoreMigrationPlanDTO({
                    workspaceId: 1,
                    state: MigrationState.MigrationSeeded,
                    sourceShardId: 'na001',
                    destinationShardId: 'g001',
                    datastore: DataStoreType.BDR,
                }),
            },
            3: {
                [DataStoreType.BDR]: new WorkspaceDataStoreMigrationPlanDTO({
                    workspaceId: 3,
                    state: MigrationState.MigrationSeeded,
                    sourceShardId: 'na001',
                    destinationShardId: 'g001',
                    datastore: DataStoreType.BDR,
                }),
            },
        };

        planManagerMock.getMigrationPlansByWorkspaceId.mockImplementation(async (workspaceId: number) => {
            if (workspaceMigrationPlans[workspaceId]) {
                return Object.values(workspaceMigrationPlans[workspaceId]);
            }
            return [];
        });

        // Act
        await migrator.runTests();

        // Assert
        // Only workspace '3' should be migrated, as a forward migration
        expect(bdrMigrationScheduleMock.migrateWorkspaces).toBeCalledTimes(1);
        expect(bdrMigrationScheduleMock.migrateWorkspaces).toBeCalledWith(
            [expect.objectContaining({ workspaceId: 3 })],
            -1,
            MigrationWorkflowPreset.MultiDatastoreMigration,
            undefined,
            false,
            {}
        );
    });

    it('finds workspaces to schedule', async () => {
        // findWorkspacesToSchedule
        // should find all possbile candidates for workspaces that should be either migrated to this shard or rolled back
        const localTestWorkspaces: TestWorkspaces<LocalShards> = {
            [ShardId.G001]: {
                [ShardId.G001]: [],
                [ShardId.NA001]: ['1'],
            },
            [ShardId.NA001]: {
                [ShardId.G001]: ['3'],
                [ShardId.NA001]: [],
            },
        };
        expect(findWorkspacesToSchedule(localTestWorkspaces, ShardId.G001)).toStrictEqual({
            forward: [
                {
                    workspaceId: 3,
                    sourceShardId: ShardId.NA001,
                    destinationShardId: ShardId.G001,
                },
            ],
            rollback: [
                {
                    workspaceId: 1,
                    sourceShardId: ShardId.NA001,
                    destinationShardId: ShardId.G001,
                },
            ],
        });

        expect(findWorkspacesToSchedule(localTestWorkspaces, ShardId.NA001)).toStrictEqual({
            forward: [
                {
                    workspaceId: 1,
                    sourceShardId: ShardId.G001,
                    destinationShardId: ShardId.NA001,
                },
            ],
            rollback: [
                {
                    workspaceId: 3,
                    sourceShardId: ShardId.G001,
                    destinationShardId: ShardId.NA001,
                },
            ],
        });
    });

    describe('isEligibleForMigration', () => {
        it('returns true if the criteria are satisfied', async () => {
            const mockPlanManager = mock<WorkspaceDataStoreMigrationPlanManager>();
            const mockHandshakeClient = mock<WorkspaceHandshakeClient>();

            const job = new ContinuousTestingJob(
                new DummyConfigService({}),
                mockPlanManager,
                mock<BDRMigrationScheduleService>(),
                mockHandshakeClient,
                [DataStoreType.BDR],
                {},
                mock<FormatLogger>()
            );

            mockPlanManager.getMigrationPlansByWorkspaceId.mockResolvedValue([
                new WorkspaceDataStoreMigrationPlanDTO({ workspaceId: 1, state: MigrationState.MigrationSeeded }),
            ]);
            mockHandshakeClient.resolveWorkspaceToShardHandshake.mockResolvedValue({ shardId: ShardId.G001 } as any);

            expect(await job.isEligibleForMigration(1, ShardId.G001, ShardId.NA001)).toBe(true);
        });

        it('returns false if the assigned shardId doesnt match sourceShardId', async () => {
            const mockPlanManager = mock<WorkspaceDataStoreMigrationPlanManager>();
            const mockHandshakeClient = mock<WorkspaceHandshakeClient>();

            const job = new ContinuousTestingJob(
                new DummyConfigService({}),
                mockPlanManager,
                mock<BDRMigrationScheduleService>(),
                mockHandshakeClient,
                [DataStoreType.BDR],
                {},
                mock<FormatLogger>()
            );

            mockPlanManager.getMigrationPlansByWorkspaceId.mockResolvedValue([
                new WorkspaceDataStoreMigrationPlanDTO({
                    workspaceId: 1,
                    state: MigrationState.MigrationSeeded,
                }),
            ]);
            mockHandshakeClient.resolveWorkspaceToShardHandshake.mockResolvedValue({ shardId: ShardId.NA001 } as any);

            expect(await job.isEligibleForMigration(1, ShardId.G001, ShardId.NA001)).toBe(false);
        });

        it('returns false if the migration is still running', async () => {
            const mockPlanManager = mock<WorkspaceDataStoreMigrationPlanManager>();
            const mockHandshakeClient = mock<WorkspaceHandshakeClient>();

            const job = new ContinuousTestingJob(
                new DummyConfigService({}),
                mockPlanManager,
                mock<BDRMigrationScheduleService>(),
                mockHandshakeClient,
                [DataStoreType.BDR],
                {},
                mock<FormatLogger>()
            );

            mockPlanManager.getMigrationPlansByWorkspaceId.mockResolvedValue([
                new WorkspaceDataStoreMigrationPlanDTO({
                    workspaceId: 1,
                    state: MigrationState.MigrationRunning,
                }),
            ]);
            mockHandshakeClient.resolveWorkspaceToShardHandshake.mockResolvedValue({ shardId: ShardId.G001 } as any);

            expect(await job.isEligibleForMigration(1, ShardId.G001, ShardId.NA001)).toBe(false);
        });
    });
});
