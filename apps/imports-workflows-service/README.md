# imports-workflows-service

[ClickUp Doc](https://staging.clickup.com/333/v/dc/ad-168880/ad-599261)

## Run the project

### Prerequisites

Before you can run the project, you need to install `nx` and the `nestjs` CLI

```
npm install -g nx
npm i -g @nestjs/cli
npm i
```

### Run command

Nx provide simple wrappers to run an app inside the repo.
Our app name is `imports-workflows-service`, so to start the app you can run the following command:

```
nx serve imports-workflows-service
```

<hr>

## Docker

### Building Image

Use the script `docker-build.sh` to build the container.

To build the docker image manually run the following command:

```
docker buildx build --load --secret id=npmrc,src=$HOME/.npmrc -t imports-workflows-service:1.0 -f ./apps/imports-workflows-service/Dockerfile .
```

<hr>

## Development

### Running Locally

You can run the build image locally using the following command:
You will need to provide AWS credentials to the container. You can copy it from the file `~/.aws/credentials`

```
docker run -e AWS_ACCESS_KEY_ID=<yourID> -e AWS_SECRET_ACCESS_KEY=<yourKey> -e DOCKER=true -e DOCKER_LOCAL=app_only -p 3333:3333 imports-workflows-service:latest
```

Once running you can access it via with URL `http://localhost:3333/imports-workflows-service-v3/`

### Debugging

In VSCode:

1. Cmd + Shift + P to open command pallet and type: "JavaScript Debug Terminal" to open a terminal window that automatically connects to the debug host.
2. Run in the terminal `npx nx run imports-workflows-service:inspect`

In WebStorm:

1. Run in the terminal `npx nx run imports-workflows-service:inspect`
2. Choose Attached debugger from debug config options (edit the configuration to choose the correct port, in this case 9300)

### Disabling OCM cache

By default OCM cache is enabled but can be controlled via a feature flag (in a few cases as of writing this, if you find places you want to control/disable cache expand the support for the feature flag!).

To control the feature flag via [split.io local override feature](https://help.split.io/hc/en-us/articles/360020448791-JavaScript-SDK#localhost-mode):

Create a `.split.yml` file in this root repository directory and add the following.

```
- use-ocm-with-new-imports-workflows-service
    treatment: 'off'
```

### Generate a library

A library in this context is a single or a group of service layers that contains business logic
and the related request & response contracts. We call request contracts DTO & response contracts RTO.

To generate a new library, run the following command:

```
npx nx generate lib --directory <folder-name> --name <library-name>
```

-   Select "@nx/nest:library" for template.
-   Directory is prefixed by ./libs and creates a directory with `--name`
    .e.g `nx generate lib --directory task --name link` will create: `libs/task/link/...`
-   `--directory <folder-name>` should be the top level directory under `./libs` which is either the service name or a business unit.

### Creating a service layer in a library

A service layer is the glue between controller and model/repositories. This is where our business logic lives.

To create a new service layer, first figure out where you want to create the service layer. `./workspace.json` contains a list of all projects.

1. `npx nx generate @nx/nest:service <my-new-service> --project=<project-name> --directory=lib -d` this will dry run and list the files it will create with destinations.
2. Once verified, run the command again. This time without the `-d` flag. We don't need to prefix our service name with "service", since the template will prefix it automatically.
   And we will store the generated service under `lib` directory, which is our current convention.

```
npx nx generate @nx/nest:service task-field-value --project=task-field-value --directory=lib`
```

### Running tests

Run main imports-workflows-service tests:

```bash
npx nx run imports-workflows-service:test
```

Run tests for affected files:

```bash
npx nx affected:test
```
