import { ObjectType } from '@time-loop/ovm-object-version';

import { ExperienceField } from '@clickup/field/contracts';
import { FieldExperienceRequest } from '@clickup/field/domain-model';
import { FieldExperienceBulkRequestDto } from '@clickup/field/dto';

// eslint-disable-next-line @nx/enforce-module-boundaries,@nx/workspace/enforce-module-boundaries
import { testMapping } from '../../../../../libs/field/core/src/lib/test-utils/mapping/base.profile.spec.utils';
import { FieldExperienceProfile } from './field-experience.profile';

describe('FieldExperienceProfile', () =>
    testMapping(
        FieldExperienceProfile,
        FieldExperienceBulkRequestDto,
        FieldExperienceRequest,
        {
            ids: ['field id 1', 'field id 2'],
            fields: [ExperienceField.Locations],
            locationsLimit: 42,
            workspaceId: 334,
        },
        (source, destination) => {
            expect(destination.keys).toEqual(
                source.ids.map(id => ({ object_id: id, object_type: ObjectType.CUSTOM_FIELD, workspace_id: 334 }))
            );
            expect(destination.fields).toEqual(source.fields);
            expect(destination.locationsLimit).toEqual(source.locationsLimit);
        }
    ));
