-- set retention to 2 months for type 0, drop tables beyond 2 months
UPDATE partman.part_config 
SET infinite_time_partitions = true,
    retention = '2 months',
    retention_keep_table=false
WHERE parent_table = 'task_mgmt.audit_events_0';

UPDATE partman.part_config 
SET infinite_time_partitions = true,
    retention = '2 months',
    retention_keep_table=false
WHERE parent_table = 'task_mgmt.audit_trace_summaries_0';


-- set retention to 7 months for type 1, drop tables beyond 7 months
UPDATE partman.part_config 
SET infinite_time_partitions = true,
    retention = '7 months',
    retention_keep_table=false
WHERE parent_table = 'task_mgmt.audit_events_1';

UPDATE partman.part_config 
SET infinite_time_partitions = true,
    retention = '7 months',
    retention_keep_table=false
WHERE parent_table = 'task_mgmt.audit_trace_summaries_1';