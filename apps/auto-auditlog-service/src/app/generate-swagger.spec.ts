import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { createMock } from '@golevelup/ts-jest';
import { INestApplication } from '@nestjs/common';
import { OpenAPIObject } from '@nestjs/swagger';
import { PathsObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { Test, TestingModule } from '@nestjs/testing';
import { getEntityManagerToken, getRepositoryToken } from '@nestjs/typeorm';
import { writeFileSync } from 'fs';
import { EntityManager, Repository } from 'typeorm';

import { OpenAPIConfig, setupOpenAPIDocumentation } from '@clickup/bootstrap';
import { EntitlementsModule } from '@clickup/entitlements';
import { NestMonitoringModule } from '@clickup/nest/monitoring';
import { UtilsAuthorizationModule } from '@clickup/utils/authorization';
import { UtilsConfigModule } from '@clickup/utils/config';
import { UtilsFeatureFlagModule } from '@clickup/utils/feature-flag';
import { UtilsLoggingModule } from '@clickup/utils/logging';

import { AuditEventsService } from '../audit-events/audit-events.service';
import { AuditEventEntity } from '../audit-events/entities/auditEvent.entity';
import { TraceSummaryEntity } from '../audit-events/entities/TraceSummaries.entity';
import { TraceSummaryService } from '../audit-events/trace-summaries.service';
import { ProducerService } from '../kafka-client/producer.service';
import { PartitionMgmtModule } from '../partition-mgmt/partition-mgmt.module';
import { GLOBAL_PREFIX } from '../setup';
import { AuditEventsS2SController } from './s2s-auditlog.controller';
import { WorkspaceAuditLogPublicController } from './workspace-auditlog.public.controller';

describe('Generate AuditLog Service Swagger Spec', () => {
    jest.setTimeout(180_000);

    let testApp: TestingModule;
    let app: INestApplication;

    beforeAll(async () => {
        testApp = await Test.createTestingModule({
            imports: [
                NestMonitoringModule,
                PartitionMgmtModule,
                UtilsAuthorizationModule,
                UtilsConfigModule.forTest(),
                EntitlementsModule,
                UtilsFeatureFlagModule.forTest(),
                UtilsLoggingModule,
            ],
            controllers: [WorkspaceAuditLogPublicController, AuditEventsS2SController],
            providers: [
                AuditEventsService,
                TraceSummaryService,
                {
                    provide: getEntityManagerToken(),
                    useClass: EntityManager,
                },
                {
                    provide: getRepositoryToken(AuditEventEntity),
                    useClass: Repository,
                },
                {
                    provide: getRepositoryToken(TraceSummaryEntity),
                    useClass: Repository,
                },
                {
                    provide: ProducerService,
                    useValue: createMock<ProducerService>(),
                },
            ],
        }).compile();

        app = testApp.createNestApplication();
        await app.init();
    });

    afterAll(async () => {
        await app?.close();
    });

    it('should generate Swagger spec', async () => {
        app.setGlobalPrefix(GLOBAL_PREFIX);

        const openAPIconfig: OpenAPIConfig = {
            apiPrefix: GLOBAL_PREFIX,
            title: 'AuditLog Service',
            description: 'ClickUp AuditLog Services',
            version: '1.0',
        };

        const document = await setupOpenAPIDocumentation(app, openAPIconfig, true, false, false);

        // setup internal
        writeFileSync(
            './tools/openapi-generator/swagger-files/auto-auditlog-service-swagger.json',
            JSON.stringify(document, null, 2)
        );

        // setup public
        modifyDocumentForClickUpAPISite(document);
        writeFileSync(
            './tools/openapi-generator/swagger-files/public-auditlog-api-swagger.json',
            JSON.stringify(document, null, 2)
        );

        expect(document).toBeDefined();

        app.setGlobalPrefix('');
    });

    // rewrites the prefix on the swagger docs so we don't need to manually update
    function modifyDocumentForClickUpAPISite(document: OpenAPIObject): void {
        document.servers.push({
            url: 'https://api.clickup.com/api/v3',
        });

        const newPaths: PathsObject = {};

        Object.keys(document.paths).forEach(path => {
            newPaths[path.replace('/auto-auditlog-service/v1/public/audit-logs/query/', '/')] = document.paths[path];
        });

        document.paths = newPaths;
    }
});
