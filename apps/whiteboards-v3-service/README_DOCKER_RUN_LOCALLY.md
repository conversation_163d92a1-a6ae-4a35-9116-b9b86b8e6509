# Run Docker locally

This is generic information on how to run docker locally. If your team has customized some things, then you will need to make some changes. This separate reademe was created as it is too difficult to maintain the same information in all those other README files as things started to drift for multiple squads.

## Docker

### Building Image

Use the script `docker-build.sh` to build the container. If you want to run the build without this script, then refer to your teams README file.

### Running Locally

You can run the build image locally using the script `docker-run-locally.sh`. You NEED to use this instead of manually running the Docker image via the CLI as thes script will inject the necessary secrets from 1Password using the `op` CLI.

-   This will assume your container name is the same as the folder name under `/clickup/apps/<your_folder_name>`. If your container has a different name, then this script will not work.
