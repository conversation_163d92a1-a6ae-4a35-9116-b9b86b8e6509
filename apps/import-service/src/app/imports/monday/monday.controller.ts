import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { MondayBoardsRto, MondayUsersRto } from '@clickup/imports/types';
import { CurrentUserId } from '@clickup/utils/authentication';
import { WorkspaceUserOverride } from '@clickup/utils/authorization';
import { WorkspaceUserType } from '@clickup/utils/authorization/models';

import { TokensService } from '../tokens/tokens.service';
import { MondayService } from './monday.service';

@ApiBearerAuth()
@Controller('monday')
@ApiTags('Monday')
export class MondayController {
    constructor(private readonly mondayService: MondayService, private readonly tokenService: TokensService) {}

    @Get('/boards')
    @ApiOperation({
        operationId: 'getMondayBoards',
        description: 'Get Monday boards',
    })
    @ApiResponse({
        type: MondayBoardsRto,
        status: 200,
    })
    @WorkspaceUserOverride(WorkspaceUserType.NoUserRequired) // skip APP_GUARD since workspaceId not in request
    async getMondayBoards(
        @Param('workspaceId') workspaceId: number,
        @CurrentUserId() userId: number
    ): Promise<MondayBoardsRto> {
        return this.mondayService.getMondayBoards(userId);
    }

    @Get('/users')
    @ApiOperation({
        operationId: 'getMondayUsers',
        description: 'Get Monday users',
    })
    @ApiResponse({
        type: MondayUsersRto,
        status: 200,
    })
    @WorkspaceUserOverride(WorkspaceUserType.NoUserRequired) // skip APP_GUARD since workspaceId not in request
    async getMondayUsers(
        @Param('workspaceId') workspaceId: number,
        @CurrentUserId() userId: number
    ): Promise<MondayUsersRto> {
        return this.mondayService.getMondayUsers(userId);
    }
}
