import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsInt } from 'class-validator';

import {
    DEFAULT_EMOJI_SKIN_TONE,
    DEFAULT_MAX_DMS_IN_SIDEBAR,
    DEFAULT_SHOW_SIDEBAR_LABELS,
    DEFAULT_SIDEBAR_STYLE,
    EmojiSkinToneEnum,
    SidebarStyleEnum,
} from './user-settings.enum';
import { UserSettings } from './user-settings.interface';

export class UserSettingsDto implements UserSettings {
    @AutoMap({ type: () => String })
    @ApiProperty({
        enum: SidebarStyleEnum,
        default: DEFAULT_SIDEBAR_STYLE,
        required: true,
    })
    @IsEnum(SidebarStyleEnum)
    sidebar_style: SidebarStyleEnum = DEFAULT_SIDEBAR_STYLE;

    @AutoMap({ type: () => String })
    @ApiProperty({
        enum: EmojiSkinToneEnum,
        default: DEFAULT_EMOJI_SKIN_TONE,
        required: true,
    })
    @IsEnum(EmojiSkinToneEnum)
    emoji_skin_tone: EmojiSkinToneEnum = DEFAULT_EMOJI_SKIN_TONE;

    @AutoMap({ type: () => Number })
    @ApiProperty({
        required: true,
        default: DEFAULT_MAX_DMS_IN_SIDEBAR,
    })
    @IsInt()
    max_dms_in_sidebar: number = DEFAULT_MAX_DMS_IN_SIDEBAR;

    @AutoMap({ type: () => Boolean })
    @ApiProperty({
        required: true,
        default: DEFAULT_SHOW_SIDEBAR_LABELS,
    })
    @IsBoolean()
    show_sidebar_labels: boolean = DEFAULT_SHOW_SIDEBAR_LABELS;
}
