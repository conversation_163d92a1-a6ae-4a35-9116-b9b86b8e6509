import { ExecutionContext, Injectable } from '@nestjs/common';
import { ObjectKey, ObjectType } from '@time-loop/ovm-object-version';
import { Request } from 'express';

import { AuthorizationGuard, AuthorizationService } from '@clickup/utils/authorization';
import type { Permission, PermissionConstants } from '@clickup/utils/authorization/models';
import { ConfigService } from '@clickup/utils/config';

/**
 * Only allow users to hide chat rooms they have access to.
 *
 * Hidden chat rooms are a per user setting, so it's not currently a security issue if we
 * created a 'hidden room' entry for a room the user doesn't have access to or didn't exist.
 *
 * But to ensure clean data & to prevent any potential future issues, this guard will prevent
 * them from hiding rooms they don't have access to.
 */
@Injectable()
export class ChatRoomAccessGuard extends AuthorizationGuard {
    private readonly permissionConstants: PermissionConstants;

    constructor(protected override readonly authorizationService: AuthorizationService, configService: ConfigService) {
        super(authorizationService);
        this.permissionConstants = configService.get<PermissionConstants>('permission_constants');
    }

    protected getObjectsAndPermissions(context: ExecutionContext): Map<ObjectKey, Permission[]> {
        const request = context.switchToHttp().getRequest<Request>();
        const { workspaceId, roomId } = request.params;
        if (!workspaceId) {
            throw new Error('Missing workspace ID');
        }
        if (!roomId) {
            throw new Error('Missing room ID');
        }
        const obj: ObjectKey = {
            object_id: String(roomId),
            object_type: ObjectType.VIEW,
            workspace_id: Number(workspaceId),
        };
        const permissions: string[] = [this.permissionConstants.can_read];
        return new Map([[obj, permissions]]);
    }
}
