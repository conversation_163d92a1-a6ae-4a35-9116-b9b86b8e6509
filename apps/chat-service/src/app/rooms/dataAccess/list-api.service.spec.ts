import { Test } from '@nestjs/testing';
import { AxiosInstance } from 'axios';
import sinon from 'sinon';

import { EntityType } from '@clickup/utils/constants';
import { MONOLITH_HTTP_CLIENT } from '@clickup/utils/http-client';

import { chatAutoMocker } from '../../../testing/helpers.spec.utils';
import { ListAPIService, ListNameTakenError } from './list-api.service';

describe('List API Service tests', () => {
    let service: ListAPIService;
    let apiClient: sinon.SinonStubbedInstance<AxiosInstance>;

    beforeAll(async () => {
        const moduleRef = await Test.createTestingModule({
            providers: [ListAPIService],
        })
            .useMocker(chatAutoMocker)
            .compile();

        service = await moduleRef.get(ListAPIService);
        apiClient = await moduleRef.resolve(MONOLITH_HTTP_CLIENT);
    });

    describe('createList tests', () => {
        beforeEach(() => {
            sinon.reset();
        });

        it('Creates a list under a folder', async () => {
            apiClient.post.resolves({
                data: { id: 'new-list-id' },
            });

            expect(await service.createList(EntityType.FOLDER, 'aaa-bbb', 'list name')).toEqual('new-list-id');
            sinon.assert.calledWithExactly(apiClient.post, '/hierarchy/v1/category/aaa-bbb/subcategory', {
                name: 'list name',
                subcategory_type: 2,
            });
        });

        it('Creates a private list under a folder', async () => {
            apiClient.post.resolves({
                data: { id: 'new-list-id' },
            });

            expect(await service.createList(EntityType.FOLDER, 'aaa-bbb', 'list name', true)).toEqual('new-list-id');
            sinon.assert.calledWithExactly(apiClient.post, '/hierarchy/v1/category/aaa-bbb/subcategory', {
                name: 'list name',
                private: true,
                subcategory_type: 2,
            });
        });

        it('Creates a list under a space', async () => {
            apiClient.post.resolves({
                data: { id: 'new-list-id-88' },
            });

            expect(await service.createList(EntityType.SPACE, 'zzzz-333', 'some list name')).toEqual('new-list-id-88');
            sinon.assert.calledWithExactly(apiClient.post, '/hierarchy/v1/project/zzzz-333/subcategory', {
                name: 'some list name',
                subcategory_type: 2,
            });
        });

        it('Raises a ListNameTakenError if the API response has the expected error code', async () => {
            const error = new Error() as any;
            error.response = {
                data: {
                    ECODE: 'SUBCAT_016',
                },
            };
            apiClient.post.rejects(error);
            expect(() => service.createList(EntityType.SPACE, 'zzzz-333', 'some list name')).rejects.toThrow(
                ListNameTakenError
            );
        });

        it('Raises any other API error', async () => {
            apiClient.post.rejects(new Error('Some unhandled error'));
            expect(() => service.createList(EntityType.SPACE, 'zzzz-333', 'some list name')).rejects.toThrow(
                'Some unhandled error'
            );
        });
    });
});
