import { Injectable } from '@nestjs/common';
import knex, { Knex } from 'knex';

import { EntityType, ViewParentType, ViewType, ViewVisibility } from '@clickup/utils/constants';
import { QueryParams } from '@clickup/utils/db-types';
import { SimpleClient } from '@clickup-legacy/utils/interfaces/TransactionClient';

@Injectable()
export class ChatOnboardingRepository {
    private readonly knex: Knex;

    constructor() {
        this.knex = knex({ client: 'pg', wrapIdentifier: value => value });
    }

    async getHasOnboardedChat(options: {
        simpleClient: SimpleClient;
        userId: string;
        workspaceId: string;
    }): Promise<boolean> {
        const { simpleClient, userId, workspaceId } = options;
        const query = `
            SELECT
                team_members.chat_onboarding_date
            FROM task_mgmt.team_members
                WHERE
            userid = $1 AND team_id = $2
        `;
        const params = [userId, workspaceId];

        const result = await simpleClient.queryAsync(query, params);

        if (result.rows.length && (result.rows[0] as any).chat_onboarding_date) {
            return true;
        }

        return false;
    }

    /**
     * Mark the user as having onboarded for chat
     * Returns true if the user was marked as onboarded, false if they were already onboarded
     */
    async setHasOnboardedChat(options: {
        simpleClient: SimpleClient;
        userId: string;
        workspaceId: string;
    }): Promise<boolean> {
        const { simpleClient, userId, workspaceId } = options;
        const query = `
            UPDATE task_mgmt.team_members
                SET chat_onboarding_date = $1
            WHERE
                userid = $2 AND team_id = $3 AND chat_onboarding_date IS NULL
        `;
        const params = [Date.now(), userId, workspaceId];
        const results = await simpleClient.queryAsync(query, params);
        return (results?.rowCount ?? 0) !== 0;
    }

    async hasCanonicalChatView(options: {
        simpleClient: SimpleClient;
        viewIds: string[];
        workspaceId: string;
    }): Promise<boolean> {
        const { simpleClient, viewIds, workspaceId } = options;
        if (!viewIds.length) {
            return false;
        }

        const query = `
            SELECT 1
            FROM task_mgmt.chat_views
            WHERE chat_views.workspace_id = $1 
                AND chat_views.is_canonical_channel IS TRUE
                AND chat_views.view_id = ANY($2)
            LIMIT 1;
        `;

        const params = [workspaceId, viewIds];

        const result = await simpleClient.queryAsync(query, params);

        return !!result.rows?.length;
    }

    async getViewMembers(options: {
        simpleClient: SimpleClient;
        viewIds: string[];
        workspaceId: string;
    }): Promise<string[]> {
        const { simpleClient, viewIds, workspaceId } = options;
        if (!viewIds.length) {
            return [];
        }
        const query = `
            SELECT userid
            FROM task_mgmt.view_members
            WHERE workspace_id = $1 AND view_id = ANY($2)
        `;

        const params = [workspaceId, viewIds];
        const result = await simpleClient.queryAsync<{ userid: number }>(query, params);
        return result.rows.map(row => row.userid.toString());
    }

    /**
     * Get the followers of the chat rooms the guest user has access to
     *
     * Given a list of folders and lists that have been shared with a guest
     * find all the followers of any public channels in those locations.
     */
    async getSharedFollowersForGuestUser(options: {
        simpleClient: SimpleClient;
        workspaceId: string;
        sharedLocations: { id: number; type: EntityType }[];
        followedChannels: string[];
    }): Promise<string[]> {
        const { simpleClient, workspaceId, sharedLocations, followedChannels } = options;
        if (!sharedLocations.length && !followedChannels.length) {
            return [];
        }

        const query = this.knex
            .from<{ userid: number }>('task_mgmt.views as v')
            .distinct('vf.userid')
            .join('task_mgmt.view_followers AS vf', 'vf.view_id', 'v.view_id')
            .where('v.team_id', workspaceId)
            .where('vf.userid', '>', 0) // exclude ClickBot and AI Agent
            .whereNot('v.deleted', true) // exclude deleted views
            .where('v.visibility', ViewVisibility.public)
            .where('v.type', ViewType.conversation) // exclude non-channel views
            .where(q => {
                if (sharedLocations.length) {
                    q.whereIn(
                        ['v.parent_id', 'v.parent_type'], // ← composite key
                        sharedLocations.map(({ id, type }) => [id, type]) // ← [[id1,type1], …]
                    );
                }
                if (followedChannels.length) {
                    q.orWhereIn('v.view_id', followedChannels);
                }
            });

        const { sql, bindings } = query.toSQL().toNative();
        const result = await simpleClient.queryAsync<{ userid: number }>(sql, <QueryParams>bindings);
        return result.rows.map(row => row.userid.toString());
    }
}
