/**
 * Returns the first non-empty, non-whitespace only string in the list of values.
 * The last argument should be a non-empty string to avoid an undefined return value.
 *
 * i.e., `coalesceStrings(undefined, '', '  ', DEFAULT_VALUE) === DEFAULT_VALUE`
 */
export const coalesceStrings = (...values: [...(string | null | undefined)[], string]): string | undefined =>
    values.find(value => value?.trim && value.trim());
