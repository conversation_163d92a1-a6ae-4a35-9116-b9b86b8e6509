import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { writeFileSync } from 'fs';

import { OpenAPIConfig, setupOpenAPIDocumentation } from '@clickup/bootstrap';
import { NestMonitoringModule } from '@clickup/nest/monitoring';

import { GLOBAL_PREFIX } from '../setup';
import { AppController } from './app.controller';
import { AppService } from './app.service';

describe('Generate Deploy Test Service Swagger Spec', () => {
    let testApp: TestingModule;
    let app: INestApplication;

    beforeAll(async () => {
        testApp = await Test.createTestingModule({
            imports: [
                AutomapperModule.forRoot({
                    strategyInitializer: classes(),
                }),
                NestMonitoringModule,
            ],
            controllers: [AppController],
            providers: [AppService],
        }).compile();

        app = testApp.createNestApplication();
        await app.init();
    });

    afterAll(async () => {
        await app?.close();
    });

    it('should generate Swagger spec', async () => {
        app.setGlobalPrefix(GLOBAL_PREFIX);

        const openAPIconfig: OpenAPIConfig = {
            apiPrefix: GLOBAL_PREFIX,
            title: 'Deploy Test Service',
            description: 'Deploy Test Service API',
            version: '1.0',
        };

        const document = await setupOpenAPIDocumentation(app, openAPIconfig, true, false, false);

        writeFileSync(
            './tools/openapi-generator/swagger-files/deploy-test-service-swagger.json',
            JSON.stringify(document, null, 2)
        );

        expect(document).toBeDefined();

        app.setGlobalPrefix('');
    });
});
