import { ObjectKey, ObjectType } from '@time-loop/ovm-object-version';

import { ObjectKeySet } from '@clickup/object-version-cache-common';
import { leafObjectTypesSupportedForEgress } from '@clickup/utils/authorization';
import type { DbObjectAcl } from '@clickup/utils/authorization/models';
import { DbObjectAclImportState, hierarchyObjectTypes, RoleMetadataSource } from '@clickup/utils/authorization/models';
import { ClickUpObject } from '@clickup/utils/db/testing';

// Configuration for the boolean fields that can be updated.
export const booleanFields = ['private', 'archived', 'deleted'] as const;
export type BooleanField = typeof booleanFields[number];
export const availableBooleanProperties: Record<string, string[]> = {
    [ObjectType.SPACE]: ['private', 'archived', 'deleted'],
    [ObjectType.FOLDER]: ['private', 'archived', 'deleted'],
    [ObjectType.LIST]: ['private', 'archived', 'deleted'],
    [ObjectType.TASK]: ['private', 'archived', 'deleted'],
    [ObjectType.CUSTOM_FIELD]: ['private', 'deleted'],
};

/**
 * Model of a single object in the database.
 */
export class DbObjectModel {
    constructor(
        public obj: ClickUpObject,
        public acl: DbObjectAcl | undefined,
        public parents: ObjectKeySet<ObjectKey>,
        public parentNames: string[],
        public states: DbObjectAclImportState[] = [],
        public ovmState: 'pending' | 'processing' = 'pending'
    ) {}

    public get name(): string {
        return this.obj.name;
    }

    public get key(): ObjectKey {
        return this.obj.key;
    }

    public get shouldHaveObjectAcl(): boolean {
        return (
            this.acl != null &&
            this.obj.template !== true &&
            (hierarchyObjectTypes.has(this.key.object_type) ||
                Object.keys(this.acl?.user_acl ?? {}).length > 0 ||
                Object.keys(this.acl?.group_acl ?? {}).length > 0)
        );
    }

    /**
     * OVM guarantees an object will not be processed in parallel.
     * This function emulates that guarantee by waiting for the object to be processed before running the callback.
     * Has an internal timeout to avoid infinite loops in case of bugs in the test.
     */
    public async processInSequence(callback: () => Promise<void>): Promise<void> {
        const timeoutIncrement = 100;
        let timeout = 0;

        while (this.ovmState === 'processing') {
            if (timeout >= 60_000) {
                throw new Error(`Object ${this.name} is still being processed after 1 minute`);
            }
            await new Promise(resolve => setTimeout(resolve, timeoutIncrement));
            timeout += timeoutIncrement;
        }

        this.ovmState = 'processing';
        await callback();
        this.ovmState = 'pending';
    }
}

/**
 * Model of the egress + role subtype workers system.
 *
 * This is a simplified model of our entire authz workers system,
 * in a world where we don't have to worry about external storage
 * and distributed systems.
 */
export class AuthzWorkersModel {
    constructor(
        public readonly workspaceId: number,
        public readonly workspaceName: string,
        public readonly dbObjectsByName: Record<string, DbObjectModel>,
        public readonly hasObjectAclsRow: Set<string> = new Set(),
        public state: 'ready' | 'importing' = 'ready'
    ) {}

    public dbObjectsOfType(objectType: ObjectType): DbObjectModel[] {
        return Object.values(this.dbObjectsByName).filter(o => o.key.object_type === objectType);
    }

    public getAcls(): Record<string, DbObjectAcl> {
        return Object.fromEntries(Array.from(this.hasObjectAclsRow, acl => [acl, this.dbObjectsByName[acl].acl]));
    }

    public async rebuildAuthzWorkspaceWorkflow(args: {
        importId: string;
        defaultTemporalConfig: any;
        anyNumber: any;
    }): Promise<void> {
        const { importId, defaultTemporalConfig, anyNumber } = args;

        // setObjectsToPending
        await setImmediatePromise(() => {
            for (const dbObject of Object.values(this.dbObjectsByName)) {
                if (dbObject.acl != null) {
                    dbObject.acl.import_id = importId;
                    dbObject.acl.import_started_at = anyNumber;
                    dbObject.acl.import_state = DbObjectAclImportState.Pending;
                    dbObject.states.push(DbObjectAclImportState.Pending);
                }
            }
        });

        // processObjectAclsBatch
        await setImmediatePromise(() => {
            for (const [dbObjectName, dbObject] of Object.entries(this.dbObjectsByName)) {
                if (dbObject.shouldHaveObjectAcl && dbObject.acl.import_state !== DbObjectAclImportState.Streamed) {
                    dbObject.acl.import_started_at = anyNumber;
                    dbObject.acl.import_completed_at = anyNumber; // was updated inside the workflow
                    dbObject.acl.import_id = importId;
                    dbObject.acl.import_state = DbObjectAclImportState.Complete;
                    dbObject.states.push(DbObjectAclImportState.Complete);
                    this.hasObjectAclsRow.add(dbObjectName);
                }
            }
        });

        // deleteEmptyObjectAcls
        await setImmediatePromise(() => {
            for (const [dbObjectName, dbObject] of Object.entries(this.dbObjectsByName)) {
                if (
                    dbObject.acl != null &&
                    dbObject.acl.private !== true &&
                    leafObjectTypesSupportedForEgress.has(dbObject.acl.object_type) &&
                    Object.keys(dbObject.acl.user_acl).length === 0 &&
                    Object.keys(dbObject.acl.group_acl).length === 0
                ) {
                    this.hasObjectAclsRow.delete(dbObjectName);
                }
            }
        });

        // deletePendingObjectAcls
        await setImmediatePromise(() => {
            for (const [dbObjectName, dbObject] of Object.entries(this.dbObjectsByName)) {
                if (dbObject.acl?.import_state === DbObjectAclImportState.Pending) {
                    this.hasObjectAclsRow.delete(dbObjectName);
                }
            }
        });

        // completeObjectAclsWorkspace
        await setImmediatePromise(() => {
            const workspace = this.dbObjectsByName[this.workspaceName];
            workspace.acl.import_id = importId;
            workspace.acl.import_started_at = anyNumber;
            workspace.acl.import_completed_at = anyNumber;
            workspace.acl.import_state = DbObjectAclImportState.Complete;
            workspace.acl.version_vector = {
                0: anyNumber, // this was updated by the workflow
            };
            workspace.acl.metadata = {
                import_metadata: {
                    args: {
                        importId,
                        workspaceId: this.workspaceId,
                        ...defaultTemporalConfig,
                    },
                    stats: {
                        customField: {
                            objectsFetched: anyNumber,
                            objectsUpserted: anyNumber,
                            pagesProcessed: anyNumber,
                        },
                        emptyAclsCleanedUp: anyNumber,
                        folder: {
                            objectsFetched: anyNumber,
                            objectsUpserted: anyNumber,
                            pagesProcessed: anyNumber,
                        },
                        list: {
                            objectsFetched: anyNumber,
                            objectsUpserted: anyNumber,
                            pagesProcessed: anyNumber,
                        },
                        orphanedAclsDeleted: anyNumber,
                        space: {
                            objectsFetched: anyNumber,
                            objectsUpserted: anyNumber,
                            pagesProcessed: anyNumber,
                        },
                        task: {
                            objectsFetched: anyNumber,
                            objectsUpserted: anyNumber,
                            pagesProcessed: anyNumber,
                        },
                    },
                },
                role_metadata: {
                    source: RoleMetadataSource.Temporal,
                },
            };
            workspace.states.push(DbObjectAclImportState.Complete);
        });
    }

    public async updateBooleanField(
        dbObject: DbObjectModel,
        fanoutChildren: DbObjectModel[],
        field: BooleanField,
        value: boolean,
        versionVector: Record<number, number>,
        anyNumber: any
    ): Promise<void> {
        dbObject.obj[field] = value;
        for (const object of [dbObject, ...fanoutChildren]) {
            await setImmediatePromise(() => {
                if (object.acl != null) {
                    if (hierarchyObjectTypes.has(object.key.object_type)) {
                        object.acl[field] =
                            value || object.parentNames.some(p => this.dbObjectsByName[p].acl?.[field] === true);
                    } else {
                        object.acl[field] = value;
                    }
                    object.acl.version_vector = versionVector;
                    object.acl.streaming_modified_at = anyNumber;
                    object.acl.import_state = DbObjectAclImportState.Streamed;
                    object.states.push(DbObjectAclImportState.Streamed);

                    if (object.shouldHaveObjectAcl) {
                        this.hasObjectAclsRow.add(object.name);
                    } else {
                        this.hasObjectAclsRow.delete(object.name);
                    }
                }
            });
        }
    }
}

// Helper to run setImmediate as a promise.
// This is used to simulate the asynchronous nature of the OVM event handlers and Temporal workflows.
function setImmediatePromise(fn: () => void): Promise<void> {
    return new Promise(resolve =>
        setImmediate(() => {
            fn();
            resolve();
        })
    );
}
