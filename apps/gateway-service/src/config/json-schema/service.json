{"$schema": "http://json-schema.org/draft-07/schema", "description": "The configuration for a single backend service", "type": "object", "additionalProperties": false, "properties": {"$schema": {"description": "The JSON Schema used in this file.", "type": "string", "default": "../json-schema/service.json"}, "domain": {"description": "The domain the service belongs to.", "enum": ["ai", "automation", "cards", "chat", "cloud-platform", "comments", "core", "crm", "dashboard", "data-platform", "docs", "eng-prod", "field", "growth", "hierarchy", "imports", "inbox", "integration", "project-management", "scheduling", "search", "security", "tasks", "templates", "user-platform", "views", "whiteboard"]}, "endpoints": {"type": "array", "description": "Your API contract, a.k.a the list of all endpoints recognized by this gateway for your service. You can only control a subset of the available properties. \n\nSee: https://www.krakend.io/docs/endpoints/", "items": {"type": "object", "required": ["backend_url_pattern", "endpoint", "method", "operation_id", "responses"], "additionalProperties": false, "properties": {"operation_id": {"description": "The unique identifier of the operation in the API, used to generate the documentation and the OpenAPI schema. It must be unique across all the endpoints of all services and BFFs.", "type": "string", "pattern": "^[a-z]+([A-Z][a-z0-9]+)*$"}, "operation_order": {"description": "The order of the operation in the [Public API documentation](https://developer.clickup.com).", "type": "integer", "minimum": 1}, "operation_section": {"description": "The section name used for identifying a group of related endpoints in the [Public API documentation](https://developer.clickup.com). By default, endpoints are grouped by the first token of the endpoint path after the workspace ID.", "type": "string", "pattern": "^[A-Z][a-z]+( [A-Z][a-z]+)*( \\([A-Z][a-z]+\\))?$", "examples": ["Your Section Name", "<PERSON><PERSON> (Experimental)"]}, "endpoint": {"description": "The exact string resource URL you want to expose. You can use `{placeholders}` to use variables when needed. Endpoints must start with `/data/v3/users/`, `/data/v3/workspaces/{workspace_id}/`, `/ui/v3/users/`, or `/ui/v3/workspaces/{workspace_id}/`.\n\nSee: https://www.krakend.io/docs/endpoints/", "type": "string", "examples": ["/data/v3/users/new_service/new_endpoint/{foo}/{bar}", "/data/v3/users/{user_id}/new_service/new_endpoint/{foo}/{bar}", "/data/v3/utils/new_service/new_endpoint/{foo}/{bar}", "/data/v3/workspaces/{workspace_id}/new_service/new_endpoint/{foo}/{bar}", "/ui/v3/users/new_service/new_endpoint/{foo}/{bar}", "/ui/v3/users/{user_id}/new_service/new_endpoint/{foo}/{bar}", "/ui/v3/workspaces/{workspace_id}/new_service/new_endpoint/{foo}/{bar}"], "pattern": "^\\/(api|data|ui)\\/v3\\/(workspaces\\/{workspace_id}|users(\\/{user_id})?|utils)(\\/{?[a-z\\d]+(_[a-z\\d]+)*}?|\\/pageListing)*$"}, "method": {"description": "The method supported by this endpoint. Create multiple endpoint entries if you need different methods.\n\nSee: https://www.krakend.io/docs/endpoints/", "type": "string", "enum": ["GET", "POST", "PUT", "PATCH", "DELETE"]}, "backend_url_pattern": {"description": "The path inside the service (no protocol, no host, no method).\n\nSee: https://www.krakend.io/docs/backends/", "type": "string", "examples": ["/new_service/core/users/new_endpoint/{foo}/{bar}", "/new_service/core/workspaces/{workspace_id}/new_endpoint/{foo}/{bar}", "/new_service/experience/users/new_endpoint/{foo}/{bar}", "/new_service/experience/workspaces/{workspace_id}/new_endpoint/{foo}/{bar}"]}, "summary": {"description": "A short summary for the endpoint. Use the description field for the longest explanation.", "type": "string"}, "description": {"description": "An introductory, optionally verbose, explanation of the endpoint. CommonMark syntax is supported, see http://commonmark.org/help/.", "type": "string"}, "deprecated": {"description": "If set to true, the endpoint will be considered deprecated and marked as such in the API documentation.", "type": "boolean", "default": false}, "external": {"description": "If set to true, the endpoint will be considered **EXTERNAL** and **WILL** be exposed to the Internet. External endpoints can still be accessed via the internal API Gateway.", "type": "boolean", "default": false}, "global": {"description": "When enabled, this endpoint becomes accessible in global shards in addition to workspace shards.", "type": "boolean", "default": false}, "input_headers": {"description": "Defines the list of all headers allowed to reach the backend when passed.\nThe API Gateway will **always** pass the following headers from the client to the backend: `Authorization`, `Content-Length`, `Content-Type`. This list is **case-insensitive**. You can declare headers in lowercase, uppercase, or mixed.\nAn entry `[\"<PERSON><PERSON>\"]` forwards all cookies, and a single star element `[\"*\"]` as value forwards everything to the backend (**it's safer to avoid this option**), including cookies. See [headers forwarding](https://www.krakend.io/docs/endpoints/parameter-forwarding/#headers-forwarding)", "type": "array", "default": [], "uniqueItems": true, "items": {"type": "string"}}, "input_params": {"description": "Sets a description and a schema for the URL parameters (e.g.: `/foo/{param}`) required in the endpoint. Make sure to write the param exactly as in the endpoint definition.\n\nSee: https://www.krakend.io/docs/enterprise/developer/openapi/", "type": "object", "additionalProperties": false, "patternProperties": {"^[a-z\\d]+(_[a-z\\d]+)*$": {"description": "The definition of a URL parameter.", "type": "object", "additionalProperties": false, "properties": {"description": {"description": "The description of the parameter.", "type": "string"}, "ref": {"description": "The name of the schema that will be used as definition of the parameter. Notice that the path `#/components/schemas/` is not needed.", "type": "string", "examples": ["your_schema_name"]}}}}}, "input_query_strings": {"description": "Defines the exact list of quey strings parameters that are allowed to reach the backend.\nBy default, the API Gateway won't pass any query string to the backend.\n\nDO NOT USE `*`\n\nSee: https://www.krakend.io/docs/endpoints/parameter-forwarding/", "type": "object", "additionalProperties": false, "patternProperties": {".*": {"description": "The definition of a query string parameter.", "type": "object", "required": ["required"], "additionalProperties": false, "properties": {"description": {"description": "The description of the query string.", "type": "string"}, "deprecated": {"description": "If set to true, the query string will be considered deprecated and marked as such in the API documentation.", "type": "boolean", "default": false}, "ref": {"description": "The name of the schema that will be used as definition of the query string. Notice that the path `#/components/schemas/` is not needed.", "type": "string", "examples": ["your_schema_name"]}, "required": {"description": "Set to `true` when this query string is required.", "type": "boolean"}}}}}, "authentication": {"description": "The authentication configuration for this endpoint. By default, the endpoint requires JWT authentication via the Authorization header.", "type": "object", "additionalProperties": false, "properties": {"requires_clickbot": {"description": "If set to true, the endpoint can only be called by ClickBot.", "type": "boolean", "default": false}, "skip_auth_reason": {"description": "The reason why the authentication is skipped for this endpoint. If this field is present, the endpoint will be public and won't require any authentication.", "type": "string"}}}, "rate_limit": {"description": "The rate limit configuration for this endpoint. If not set, the settings are taken from the \"global\" rate limiter configuration.", "type": "object", "additionalProperties": false, "properties": {"bucket": {"description": "The prefix used for all keys associated with this rate limiter.", "type": "string", "maxLength": 100, "pattern": "^[a-z0-9_]+$"}, "cost": {"description": "The cost of the requests associated with this rate limiter.", "type": "integer", "default": 1, "minimum": 1}, "key": {"default": ["authorization_header"], "description": "The key used to identify the parameters which contribute to rate limiting. The order of keys does not matter.", "items": {"type": "string", "enum": ["authorization_header", "client_name", "request_path", "user_id", "workspace_id"]}, "type": "array", "uniqueItems": true}, "limit_per_minute": {"description": "The maximum number of requests allowed per minute.", "type": "integer", "minimum": 1}, "skip_rate_limit_reason": {"description": "The reason why the rate limit is skipped for this endpoint. If this field is present, the endpoint will be public and will NOT be rate limited at the API Gateway. It is HIGHLY recommended that the backend service implements its own rate limiting to prevent abuse, as the gateway will not enforce any limits.", "type": "string"}}, "required": []}, "request": {"description": "Describes the request needed to consume the endpoint. If a JSON Schema validation exists, it takes precedence when generating the documentation. An example use case is when you need to document a `multipart/form-data` request body.\n\nSee: https://www.krakend.io/docs/enterprise/developer/openapi/", "examples": [{"description": "Page not found", "example": {"status": "KO"}}], "type": "object", "additionalProperties": false, "properties": {"content_type": {"description": "The content type returned by this error.", "type": "string", "default": "application/json"}, "description": {"description": "The description of the payload this endpoint accepts.\n\nSee: https://www.krakend.io/docs/enterprise/developer/openapi/", "type": "string"}, "example": {"description": "A free form JSON object or a string you would like to show as a sample request of the endpoint.", "type": ["string", "object", "array", "boolean", "integer", "null", "number"]}, "ref": {"description": "The name of the schema that will be used as definition of the accepted request. Notice that the path `#/components/schemas/` is not needed.", "examples": ["your_schema_name"], "type": "string"}}}, "responses": {"description": "Describes the different status codes returned by this endpoint. Each key is the definition of the status code, represented by a string. E.g., `200` (success), `500` (internal error), etc.\n\nSee: https://www.krakend.io/docs/enterprise/developer/openapi/", "type": ["object"], "examples": [{"404": {"description": "Page not found"}}], "additionalProperties": false, "patternProperties": {"default|^[0-9]+$": {"type": "object", "additionalProperties": false, "properties": {"content_type": {"description": "The content type returned by this error, e.g., `application/json`", "type": "string", "default": "application/json"}, "description": {"description": "The description of this error code, e.g., `Page not found`.\n\nSee: https://www.krakend.io/docs/enterprise/developer/openapi/", "type": "string"}, "ref": {"description": "The name of the schema that will be used as definition of the response. Notice that the path `#/components/schemas/` is not needed.", "type": "string", "examples": ["your_schema_name"]}}}}}, "timeout": {"description": "The duration you write in the timeout represents the **whole duration of the pipe**, so it counts the time all your backends take to respond and the processing of all the components involved in the endpoint (the request, fetching data, manipulation, etc.). Usually specified in seconds (`s`) or milliseconds (`ms`. e.g.: `2000ms` or `2s`). If you don't set any timeout, the timeout is taken from the entry in the service level, or to the system's default.", "type": "string", "$ref": "#/$defs/timeunit", "examples": ["2s", "1500ms"], "default": "3s"}}, "if": {"properties": {"endpoint": {"not": {"pattern": "^/api/"}}}}, "then": {"properties": {"operation_order": {"not": {}}, "operation_section": {"not": {}}}}}}, "legacy_endpoints": {"type": "array", "description": "The legacy endpoints of the service. These endpoints will be forwarded as-is by the API Gateway, without any additional feature.", "items": {"type": "object", "properties": {"endpoint": {"description": "The exact string resource URL you want to expose. You can use `{placeholders}` to use variables when needed. \n\nSee: https://www.krakend.io/docs/endpoints/", "type": "string"}, "method": {"description": "The method supported by this endpoint. Create multiple endpoint entries if you need different methods.\n\nSee: https://www.krakend.io/docs/endpoints/", "type": "string", "enum": ["GET", "POST", "PUT", "PATCH", "DELETE"]}}, "required": ["endpoint", "method"]}}, "route_to_ecs_from_eks": {"description": "If set to true, the API Gateway running in EKS will route requests to the version of this service running in ECS.", "type": "boolean", "default": false}, "route_to_headless_service": {"description": "The shards where the API Gateway will route requests to the Kubernetes headless service.", "type": "array", "items": {"type": "string"}, "examples": [["qa-us-east-2-1"]]}, "backend_variants": {"description": "A list of backend variants that can be targeted by the internal API Gateway. When a variant is specified (e.g., 'internal'), the internal API Gateway will forward traffic to that variant of the service (e.g., 'internal-my-service' instead of 'my-service') if the `X-Cu-Backend-Variant` request header matches the variant name. This enables traffic segregation by allowing you to deploy separate instances of your service for different purposes. This separation helps with resource isolation, security, and independent scaling of workloads.", "type": "array", "items": {"type": "string", "enum": ["chat", "internal"]}, "uniqueItems": true}, "schemas": {"type": "object", "description": "The JSON Schemas you can reuse inside endpoint definitions using `ref`.", "examples": [{"Pet": {"type": "object", "required": ["id", "name"]}}], "additionalProperties": false, "patternProperties": {".*": {"description": "JSON Schema as an object", "type": "object"}}}}, "required": ["$schema", "domain", "endpoints"], "$defs": {"timeunit": {"type": "string", "pattern": "^[0-9]+(ns|ms|us|µs|s|m|h)$", "description": "The amount of time you want to assign followed by its unit (e.g.: `2s`, `200ms`). Valid time units are: ns, us, (or µs), ms, s, m, h."}}}