import { CrmUserId } from '../../../interfaces';
import { CrmTeamEditAuditService } from '../../teamAudit/teamAudit';
import {
    getIdentityProviderById,
    insertNote,
    removeIdentityProviderIfEmpty,
    removeIdentityProviderWorkspace,
    withGlobalTransaction,
    withShardTransaction,
} from '../repository';
import { ValidationError } from '../types';
import { InputParams } from './types';

export async function removeWorkspaceFromIdentityProvider(userId: CrmUserId, inputParams: InputParams): Promise<void> {
    const identityProvider = await getIdentityProviderById(inputParams.idpId);
    if (!identityProvider) {
        throw new ValidationError('Invalid input params', ['Identity provider not found']);
    }

    const auditService = await CrmTeamEditAuditService.forTeam(inputParams.workspaceId);

    await withShardTransaction(inputParams.workspaceId, async shardClient => {
        await withGlobalTransaction(async globalClient => {
            await removeIdentityProviderWorkspace(
                { workspaceId: inputParams.workspaceId, idpId: inputParams.idpId },
                { client: globalClient }
            );

            await removeIdentityProviderIfEmpty(inputParams.idpId, { client: globalClient });

            await insertNote({
                workspaceId: inputParams.workspaceId,
                note: inputParams.note,
                poster: userId,
                action: `Removed from identity provider: ${identityProvider.name}`,
            });

            await auditService.storeTeamUpdated(
                userId,
                inputParams.workspaceId,
                {
                    action: 'Removed from identity provider',
                    idp_id: identityProvider.id,
                    idp_name: identityProvider.name,
                },
                shardClient
            );
        });
    });
}
