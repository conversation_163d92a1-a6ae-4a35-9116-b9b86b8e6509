import type { WorkspaceRolePermissions } from '@clickup/utils/authorization/models';
import { DbQueryResult } from '@clickup-legacy/utils/interfaces/DbQueryResult';

import db from '../../../utils/db';

export function getAllWorkspaceRolePermissions(teamId: number): Promise<DbQueryResult<WorkspaceRolePermissions>> {
    const query = `SELECT * FROM task_mgmt.team_role_permissions WHERE team_id = $1`;
    return db.shardedReplicaQueryAsync(query, [teamId], teamId);
}

export function getWorkspaceRolePermissionsColumns(teamId: number): Promise<DbQueryResult<{ column_name: string }>> {
    const query = `
        SELECT column_name
        FROM information_schema.columns
        WHERE 
            table_schema = 'task_mgmt'
            AND table_name   = 'team_role_permissions'
    `;

    return db.shardedReplicaQueryAsync(query, [], teamId);
}
