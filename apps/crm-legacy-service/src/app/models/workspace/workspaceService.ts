import { getLogger } from '@clickup/shared/utils-logging';

import { HttpClient } from '../http/httpClient';
import { shardingRouter } from '../sharding/shardingRouter';
import { WorkspaceServiceClient } from './workspace.service.client';

const loggerName = 'WorkspaceServiceClient';
const logger = getLogger(loggerName);

export const API_RESPONSE_TIMEOUT = 10_000;

export const CrmHttpClient = new HttpClient(undefined, API_RESPONSE_TIMEOUT);
export const WorkspaceService = new WorkspaceServiceClient(loggerName, logger, CrmHttpClient, shardingRouter);
