import config from 'config';
import { NextFunction, Response } from 'express';
import { memoize } from 'lodash';

import { getLogger } from '@clickup/shared/utils-logging';

import { CrmRequest } from '../../interfaces';
import { recordPermissionAccessAttempt } from './permLogs';
import resources from './resources';
import user from './user';

const _RUN_LOCAL = config.get<string>('env') === 'local';
/* @NOTE: all CRM permission checks are disabled locally for a smoother startup experience
    please see all uses of _RUN_LOCAL to enable local permissions
*/

export interface Permission {
    permission_id: string;
}

const logger = getLogger('middleware');

const invertObject = function invertObject(obj: Record<string, string>) {
    const result: Record<string, string> = {};
    Object.keys(obj).forEach(key => {
        result[obj[key]] = key;
    });
    return result;
};

export const CRM_PERMISSIONS: () => Record<string, string> = memoize(() =>
    invertObject(config.get<Record<string, string>>('crm_permissions'))
);

const _ROUTE_REQUIRED_PERMISSIONS_V2: () => Record<string, string[]> = memoize(() => ({
    dsr: [CRM_PERMISSIONS().process_dsr],
    dsrs: [CRM_PERMISSIONS().process_dsr],
    loginPermissionsV2: [CRM_PERMISSIONS().login_override],
    permissions: [CRM_PERMISSIONS().admin],
    statusMessageV2: [CRM_PERMISSIONS().admin, CRM_PERMISSIONS().send_toast],
    users: [CRM_PERMISSIONS().admin],
    userV2: [CRM_PERMISSIONS().admin],
}));

/*
 * @returns tuple of arrays, [accepted_permissions, missing_permissions].
 */
const comparePermissionSet = (
    user_permissions: Permission[],
    required_permissions: Permission[]
): [Permission[], Permission[]] => {
    const user_key: Record<number, boolean> = {};

    const missing_permissions: Permission[] = [];
    const accepted_permissions: Permission[] = [];

    user_permissions.forEach(row => {
        user_key[Number(row.permission_id)] = true;
    });

    required_permissions.forEach(row => {
        if (!user_key[Number(row.permission_id)]) {
            missing_permissions.push(row);
        } else {
            accepted_permissions.push(row);
        }
    });
    return [accepted_permissions, missing_permissions];
};

function _checkRoutePermissions(
    req: CrmRequest,
    resp: Response,
    next: NextFunction,
    requestPermissionsNeeded: string[]
) {
    if (_RUN_LOCAL) {
        // @NOTE: remove this if testing permissions locally, but you will require admin perm 101 to access them
        next();
        return;
    }

    const requestor_id = req.decoded_token.user;
    if (!requestor_id) {
        resp.send({
            err: 'Invalid requestor id',
            err_code: 403,
            url: req.path,
        });
        return;
    }

    if (!requestPermissionsNeeded || !Array.isArray(requestPermissionsNeeded) || !requestPermissionsNeeded.length) {
        logger.error({
            msg: 'Endpoint incorrectly protected, consider fixing it ',
            url: req.path,
            requestPermissionsNeeded,
        });
        resp.send({
            err: 'Endpoint incorrectly protected, please report',
            err_code: 403,
            url: req.path,
        });
        return;
    }

    const permissions_needed = requestPermissionsNeeded.map(x => ({ permission_id: x }));

    if (!permissions_needed || !Array.isArray(permissions_needed) || !permissions_needed.length) {
        logger.error({ msg: 'Endpoint incorrectly protected, consider fixing it ', url: req.path, permissions_needed });
        resp.send({
            err: 'Endpoint incorrectly protected, please report',
            err_code: 403,
            url: req.path,
        });
        return;
    }

    user.getUserPermissions(requestor_id, (err: unknown, rows: Permission[]) => {
        if (err) {
            resp.send(err);
            return;
        }
        let accepted_permissions: Permission[] = [];
        let missing_permissions: Permission[] = [];

        if (rows && rows.length) {
            [accepted_permissions, missing_permissions] = comparePermissionSet(rows, permissions_needed);

            if (missing_permissions.length) {
                resp.send({
                    err: 'Insufficient user permissions to access this endpoint',
                    err_code: 403,
                    permissions_needed,
                    missing_permissions,
                    url: req.path,
                });
            } else {
                next();
            }
        } else {
            missing_permissions = permissions_needed;
            resp.send({
                err: 'Insufficient user permissions to access this endpoint',
                err_code: 403,
                permissions_needed,
                missing_permissions: permissions_needed,
                url: req.path,
            });
        }
        recordPermissionAccessAttempt(requestor_id, accepted_permissions, missing_permissions);
    });
}

export function checkRoutePermissions(req: CrmRequest, resp: Response, next: NextFunction) {
    if (_RUN_LOCAL) {
        // @NOTE: remove this if testing permissions locally, but you will require admin perm 101 to access them
        next();
        return;
    }

    const { url } = req;
    const parsedUrl = url.split('/');
    const permissions_needed = _ROUTE_REQUIRED_PERMISSIONS_V2()[parsedUrl[2]];

    _checkRoutePermissions(req, resp, next, permissions_needed);
}

export function checkUsersTeamPerms(req: CrmRequest, resp: Response, next: NextFunction) {
    if (_RUN_LOCAL) {
        // @NOTE: remove this if testing permissions locally
        next();
        return;
    }
    const requestor_id = req.decoded_token.user;
    const user_id = req.params.person_id;

    let permissions_needed: Permission[] = [];

    resources.getPersonsTeamResourcePerms(user_id, (err: unknown, rows: Permission[]) => {
        if (err) {
            resp.send(err);
            return;
        }
        let accepted_permissions: Permission[] = [];
        let missing_permissions: Permission[] = [];

        if (rows && rows.length) {
            permissions_needed = rows;

            user.getUserPermissions(requestor_id, (userErr: unknown, userRows: Permission[]) => {
                if (userErr) {
                    resp.send(userErr);
                    return;
                }
                if (userRows && userRows.length) {
                    [accepted_permissions, missing_permissions] = comparePermissionSet(userRows, permissions_needed);

                    if (missing_permissions.length) {
                        resp.send({
                            err: 'Insufficient resource permissions to access this team',
                            err_code: 403,
                            permissions_needed,
                            missing_permissions,
                            user_id,
                        });
                    } else {
                        next();
                    }
                } else {
                    resp.send({
                        err: 'Insufficient resource permissions to access this team',
                        err_code: 403,
                        permissions_needed,
                        missing_permissions: permissions_needed,
                        user_id,
                    });
                }
                recordPermissionAccessAttempt(requestor_id, accepted_permissions, missing_permissions);
            });
        } else {
            next();
        }
    });
}

export function makeRoutePermissionCheck(permissions: string[]) {
    return (req: CrmRequest, resp: Response, next: NextFunction) => {
        if (!permissions || !Array.isArray(permissions) || !permissions.length) {
            resp.send({
                err: 'Endpoint incorrectly protected, please report',
                err_code: 403,
            });
            return;
        }

        _checkRoutePermissions(req, resp, next, permissions);
    };
}

export default {
    checkRoutePermissions,
    checkUsersTeamPerms,
    makeRoutePermissionCheck,
    CRM_PERMISSIONS,
    comparePermissionSet,
};
