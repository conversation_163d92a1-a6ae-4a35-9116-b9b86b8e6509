import { getLogger } from '@clickup/shared/utils-logging';
import { PlanOverridesService } from '@clickup-legacy/libs/billing/plan/planOverridesService';
import { MrrService } from '@clickup-legacy/libs/billing/price/mrrService';

import { CrmUserId } from '../../interfaces';
import { PerShardService } from '../../utils/PerShardService';
import { CrmPromoCodeService } from '../promoCode/promoCodeService';
import { CrmPlanService } from './crmPlanService';

const logger = getLogger('mrrService');

const planOverridesService = new PlanOverridesService(logger);
export const CrmMrrService = new PerShardService<MrrService<CrmUserId>>(logger, shardId => {
    const planService = CrmPlanService.forShard(shardId);
    const promoCodeService = CrmPromoCodeService.forShard(shardId);

    return new MrrService(logger, planService, promoCodeService, planOverridesService);
});
