const config = require('config');
const { memoize } = require('lodash');

const { retry } = require('@clickup-legacy/libs/common/utils/retry');
const { TransactionClientImpl } = require('@clickup/utils/db-lite');
const { getLogger } = require('@clickup/shared/utils-logging');
const { AsyncStorage } = require('@clickup/shared/utils-async-storage');
const { isLocalOrTest, isAnyProd } = require('@clickup-legacy/utils/environment');
const { default: PoolManager } = require('./connections/PoolManager');
const { ConnectionPoolType, ShardConnectionPoolType } = require('./connections/interface/interface');
const availableShards = require('./db/services/availableShardsService');

const logger = getLogger('db');

const longRunningLimit = memoize(() => config.db.longRunningLimit || 10000);

const shards = availableShards.getAllBdrShardIds();

const getGlobalPool = async (options = {}, replicaPool = false) => {
    try {
        const connectionPool = !isLocalOrTest
            ? PoolManager().getShardConnectionPoolByType(
                  availableShards.getGlobalBdrShardId(),
                  replicaPool
                      ? ShardConnectionPoolType.ReplicaShardedQueryConnectionPool
                      : ShardConnectionPoolType.ShardedQueryConnectionPool,
                  replicaPool ? [ShardConnectionPoolType.ShardedQueryConnectionPool] : []
              )
            : PoolManager().getNonShardConnectionPoolByType(ConnectionPoolType.GlobalConnectionPoolForLocalDev);

        if (options.masterId) {
            return connectionPool.getPoolByMasterId(options.masterId);
        }

        return connectionPool.getPool();
    } catch (err) {
        logger.error({
            msg: `Cannot find globalConnectionPool`,
            stack: err.stack,
            globalBdrShardId: availableShards.getGlobalBdrShardId(),
            err,
        });

        throw err;
    }
};

const getEmailsSentQueryConnectionPool = async read => {
    const { emails_sent } = config.db;

    try {
        if (!emails_sent) {
            if (!isLocalOrTest) {
                logger.error('Emails_sent pool config not provided');
                throw new Error('Emails_sent pool config not provided');
            }

            // In local environments where emails_sent doesn't exist, we use the global DB instead
            // RDS query (segment-info) defaults to Batch 38
            return await getGlobalPool();
        }

        return read
            ? PoolManager()
                  .getNonShardConnectionPoolByType(ConnectionPoolType.EmailsSentReadQueryConnectionPool)
                  .getPool()
            : PoolManager()
                  .getNonShardConnectionPoolByType(ConnectionPoolType.EmailsSentWriteQueryConnectionPool)
                  .getPool();
    } catch (err) {
        logger.error({
            msg: 'Cannot find emails sent query connection pool',
            stack: err.stack,
            globalBdrShardId: availableShards.getGlobalBdrShardId(),
            err,
        });

        throw err;
    }
};

const getShardedQueryConnectionPool = async (shardId, options = {}) => {
    try {
        if (shards.includes(shardId)) {
            const shardedQueryConnectionPool = PoolManager().getShardConnectionPoolByType(
                shardId,
                ShardConnectionPoolType.ShardedQueryConnectionPool
            );

            if (options.masterId) {
                return shardedQueryConnectionPool.getPoolByMasterId(options.masterId);
            }

            return shardedQueryConnectionPool.getPool();
        } else {
            if (shardId !== 'g001') {
                logger.error({ msg: 'Unknown shardId', shardId, stack: new Error().stack });

                return null;
            }

            return isLocalOrTest
                ? PoolManager().getNonShardConnectionPoolByType(ConnectionPoolType.QueryConnectionPool).getPool()
                : await getGlobalPool();
        }
    } catch (err) {
        logger.error({
            msg: 'Cannot find shardedQueryConnectionPool or queryConnectionPool',
            shardId,
            stack: err.stack,
            err,
        });

        throw err;
    }
};

const getReplicaShardedQueryConnectionPool = async shardId => {
    let replicaShardedQueryConnectionPool;

    try {
        if (shards.includes(shardId)) {
            replicaShardedQueryConnectionPool = PoolManager()
                .getShardConnectionPoolByType(shardId, ShardConnectionPoolType.ReplicaShardedQueryConnectionPool, [
                    ShardConnectionPoolType.ShardedQueryConnectionPool,
                ])
                .getPool();
        } else {
            if (shardId !== 'g001') {
                logger.error({ msg: 'Unknown shardId', shardId, stack: new Error().stack });

                return null;
            }

            replicaShardedQueryConnectionPool = isLocalOrTest
                ? PoolManager().getNonShardConnectionPoolByType(ConnectionPoolType.QueryConnectionPool).getPool()
                : await getGlobalPool({}, true);
        }
    } catch (err) {
        logger.error({
            msg: 'Cannot find replicaShardedQueryConnectionPool, shardedQueryConnectionPool or replicaQueryConnectionPool',
            shardId,
            stack: err.stack,
            err,
        });

        throw err;
    }

    return replicaShardedQueryConnectionPool;
};

function tryDone(done, err) {
    try {
        done(err);
    } catch (e) {
        // ignore
    }
}

function getOptionsAndCb(_options_or_cb, _cb) {
    let options = _options_or_cb;
    let cb = _cb;

    if (!cb) {
        cb = options;
        options = {};
    }

    return { options, cb };
}

// cb is function(err, client, done)
/**
 * Retrieves a global database connection.
 * @param {function} [err_cb] - The error callback function.
 * @param _options_or_cb
 * @param {function} [_cb=undefined] - The callback function that will be called with the connection.
 */
async function getGlobalConn(err_cb, _options_or_cb = undefined, _cb = undefined) {
    // eslint-disable-next-line prefer-const
    let { options, cb } = getOptionsAndCb(_options_or_cb, _cb);

    // one argument, no options, no cb
    if (!cb) {
        cb = err_cb;
        err_cb = null;
    }

    // connection pools may be uninitialized here due to static initialization order issues
    // retry a few times
    const _pool = await retry(() => getGlobalPool(options), 'getGlobalConn', logger);

    const callerContext = AsyncStorage.getInstance().getContext();
    _pool.connect((err, client, done) => {
        AsyncStorage.getInstance().run(callerContext, () => {
            if (err && err_cb) {
                tryDone(done);
                err_cb({ err: 'Internal server error', status: 500, ECODE: 'DB_001' });
            } else {
                const esteTimeout = setTimeout(() => {
                    logger.warn({
                        msg: 'getGlobalConn - long running',
                    });
                }, longRunningLimit());
                const _done = function () {
                    clearTimeout(esteTimeout);
                    tryDone(done);
                };
                if (_pool?.getConnectionHostDetails?.().masterId !== undefined && client?.masterId !== null) {
                    client.masterId = _pool?.getConnectionHostDetails?.().masterId;
                }
                cb(err, client, _done);
            }
        });
    });
}
exports.getGlobalConn = getGlobalConn;

async function getGlobalConnPromise(options = {}) {
    return new Promise((resolve, reject) => {
        const cb = (err, client, done) => {
            if (err) {
                reject(err);
            } else {
                resolve({ client, done });
            }
        };
        getGlobalConn(cb, options, cb);
    });
}
exports.getGlobalConnPromise = getGlobalConnPromise;

function rollback(client, done) {
    const callerContext = AsyncStorage.getInstance().getContext();
    client.query('ROLLBACK', err => {
        AsyncStorage.getInstance().run(callerContext, () => {
            tryDone(done, err);
        });
    });
}
exports.rollback = rollback;

async function rollbackPromise(client) {
    return new Promise((resolve, reject) => {
        const callerContext = AsyncStorage.getInstance().getContext();
        client.query('ROLLBACK', err => {
            AsyncStorage.getInstance().run(callerContext, () => {
                if (err) {
                    reject();
                } else {
                    resolve();
                }
            });
        });
    });
}
exports.rollbackPromise = rollbackPromise;

/**
 *
 * @param _pool
 * @param query
 * @param params
 * @param queryContext - { workspace_id, shard_id, source, longRunningLimit, statementTimeoutSeconds }
 */
async function singleQueryAsync(_pool, query, params, queryContext) {
    let client;

    try {
        client = await _pool.connect();
    } catch (err) {
        logger.error({
            msg: `Failed to connect to pool in ${queryContext?.source || 'singleQuery'}`,
            team_id: queryContext?.workspace_id,
            ws_shard_id: queryContext?.shard_id,
            query_text: query,
            query_params: params,
            pool: {
                poolName: _pool?.getPoolName?.(),
                connectionHost: _pool?.getConnectionHostDetails?.(),
            },
            err,
        });
        // eslint-disable-next-line no-throw-literal
        throw { err: 'Internal server error', status: 500, ECODE: 'DB_008' };
    }

    const esteTimeout = setTimeout(() => {
        logger.warn({
            msg: `${queryContext?.source || 'singleQuery'} - long running`,
            team_id: queryContext?.workspace_id,
            ws_shard_id: queryContext?.shard_id,
            query_text: query,
            query_params: params,
        });
    }, queryContext?.longRunningLimit ?? longRunningLimit());

    try {
        if (Number.isInteger(queryContext?.statementTimeoutSeconds)) {
            // This transaction is needed because we can't set statement_timeout as a param in pool config due to
            // pgbouncer not supporting it https://stackoverflow.com/questions/64860298/how-to-use-statement-timeout-with-pgbouncer-in-transaction-mode
            await client.query('BEGIN');
            await client.query(`SET LOCAL statement_timeout = ${queryContext.statementTimeoutSeconds * 1000}`);
        }

        const result = await client.query(query, params);

        if (Number.isInteger(queryContext?.statementTimeoutSeconds)) {
            await client.query('COMMIT');
        }

        if (result && queryContext?.shard_id) {
            result._shard = queryContext?.shard_id;
        }

        return result;
    } catch (e) {
        if (Number.isInteger(queryContext?.statementTimeoutSeconds)) {
            await rollbackPromise(client);
        }
        throw e;
    } finally {
        clearTimeout(esteTimeout);
        client.release();
    }
}

/**
 *
 * @param _pool
 * @param queries
 * @param queryContext { workspace_id, shard_id, source, statementTimeoutSeconds }
 * @returns {Promise<unknown>}
 */
async function batchQueriesAsync(_pool, queries, queryContext) {
    let client;

    try {
        client = await _pool.connect();
    } catch (err) {
        logger.error({
            msg: `Failed to connect to pool in ${queryContext?.source || 'batchQueriesAsync'}`,
            team_id: queryContext?.workspace_id,
            ws_shard_id: queryContext?.shard_id,
            pool: {
                poolName: _pool?.getPoolName?.(),
                connectionHost: _pool?.getConnectionHostDetails?.(),
            },
            err,
        });
        // eslint-disable-next-line no-throw-literal
        throw { err: 'Internal server error', status: 500, ECODE: 'DB_001' };
    }

    try {
        await client.query('BEGIN');

        if (Number.isInteger(queryContext?.statementTimeoutSeconds)) {
            await client.query(`SET LOCAL statement_timeout = ${queryContext.statementTimeoutSeconds * 1000}`);
        }

        const promiseResults = await Promise.all(queries.map(query => client.query(query.query, query.params)));

        await client.query('COMMIT');

        return promiseResults.map(result => result.rows ?? []).reduce((acc, val) => acc.concat(val), []);
    } catch (err) {
        logger.error({
            msg: 'query failed',
            team_id: queryContext?.workspace_id,
            ws_shard_id: queryContext?.shard_id,
            err,
        });
        await rollbackPromise(client);
        throw err;
    } finally {
        client.release();
    }
}

/**
 *
 * @param _pool
 * @param queries
 * @param queryContext { workspace_id, shard_id, source, statementTimeoutSeconds }
 * @returns {Promise<unknown>}
 */
async function batchQueriesSeriesAsync(_pool, queries, queryContext) {
    let client;

    try {
        client = await _pool.connect();
    } catch (err) {
        logger.error({
            msg: `Failed to connect to pool in ${queryContext?.source || 'batchQueriesAsync'}`,
            team_id: queryContext?.workspace_id,
            ws_shard_id: queryContext?.shard_id,
            pool: {
                poolName: _pool?.getPoolName?.(),
                connectionHost: _pool?.getConnectionHostDetails?.(),
            },
            err,
        });
        // eslint-disable-next-line no-throw-literal
        throw { err: 'Internal server error', status: 500, ECODE: 'DB_001' };
    }

    try {
        await client.query('BEGIN');

        if (Number.isInteger(queryContext?.statementTimeoutSeconds)) {
            await client.query(`SET LOCAL statement_timeout = ${queryContext.statementTimeoutSeconds * 1000}`);
        }

        let rows = [];
        for (const query of queries) {
            const result = await client.query(query.query, query.params);
            rows = result.rows.concat(rows);
        }
        await client.query('COMMIT');

        return rows;
    } catch (err) {
        logger.error({
            msg: 'query failed',
            team_id: queryContext?.workspace_id,
            ws_shard_id: queryContext?.shard_id,
            err,
        });
        await rollbackPromise(client);
        throw err;
    } finally {
        client.release();
    }
}

function globalSingleQuery(query, params, options_or_cb, _cb) {
    // eslint-disable-next-line prefer-const
    let { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    if (!cb) {
        // 2 args, no options, no params
        cb = params;
    }

    globalSingleQueryAsync(query, params, options)
        .then(result => cb(null, result))
        .catch(cb);
}

async function globalSingleQueryAsync(query, params, options = {}) {
    const _pool = await getGlobalPool(options);

    return singleQueryAsync(_pool, query, params, {
        source: 'globalSingleQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.globalSingleQuery = globalSingleQuery;
exports.globalSingleQueryAsync = globalSingleQueryAsync;
exports.globalReadQuery = globalSingleQuery;
exports.globalWriteQuery = globalSingleQuery;

// set here to avoid circular dependency
const { getShardIdFromWorkspace } = require('./db/services/shardService');
const { PoolCheckClient } = require('./connections/clients/PoolCheckClient');

async function shardedSingleQuery(query, params, workspace_id, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    shardedSingleQueryAsync(query, params, workspace_id, options)
        .then(result => cb(null, result))
        .catch(cb);
}

async function shardedSingleQueryAsync(query, params, workspace_id, options = {}) {
    const shard_id = await getShardIdFromWorkspace(workspace_id);
    const _pool = await getShardedQueryConnectionPool(shard_id);

    if (![...shards, 'g001'].includes(shard_id)) {
        logger.warn({
            msg: 'Unknown shardId, check if team_id is valid or if workspace_to_shard is missing a row.',
            shard_id,
            team_id: workspace_id,
            stack: new Error().stack,
        });
    }

    return singleQueryAsync(_pool, query, params, {
        workspace_id,
        shard_id,
        source: 'shardedSingleQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.shardedSingleQuery = shardedSingleQuery;
exports.shardedSingleQueryAsync = shardedSingleQueryAsync;

async function knownShardQuery(query, params, shard_id, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    knownShardQueryPromise(query, params, shard_id, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.knownShardQuery = knownShardQuery;

async function knownShardQueryPromise(query, params, shard_id, options = {}) {
    const _pool = await getShardedQueryConnectionPool(shard_id);

    return singleQueryAsync(_pool, query, params, {
        shard_id,
        source: 'knownShardQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.knownShardQueryPromise = knownShardQueryPromise;

async function knownShardReplicaQuery(query, params, shard_id, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    knownShardReplicaQueryPromise(query, params, shard_id, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.knownShardReplicaQuery = knownShardReplicaQuery;

async function knownShardReplicaQueryPromise(query, params, shard_id, options = {}) {
    const _pool = await getReplicaShardedQueryConnectionPool(shard_id);

    return singleQueryAsync(_pool, query, params, {
        shard_id,
        source: 'knownShardReplicaQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.knownShardReplicaQueryPromise = knownShardReplicaQueryPromise;

// note this requires you to null your first param if there is no err_cb, unlike getConn
async function getShardedConn(err_cb, workspace_id, cb) {
    if (!cb) {
        cb = err_cb;
        err_cb = null;
    }

    const shard_id = await getShardIdFromWorkspace(workspace_id);

    const _pool = await retry(() => getShardedQueryConnectionPool(shard_id), 'getShardedConn', logger);

    const callerContext = AsyncStorage.getInstance().getContext();
    _pool.connect((err, client, done) => {
        AsyncStorage.getInstance().run(callerContext, () => {
            if (err && err_cb) {
                tryDone(done);
                logger.error({
                    msg: 'Failed to to get a sharded connection',
                    team_id: workspace_id,
                    ws_shard_id: shard_id,
                    err,
                    ECODE: 'DB_010',
                });
                err_cb({ err: 'Internal server error', status: 500, ECODE: 'DB_010' });
            } else {
                const _done = function doneFunction() {
                    tryDone(done);
                };

                if (_pool?.getConnectionHostDetails?.().masterId !== undefined && client?.masterId !== null) {
                    client.masterId = _pool?.getConnectionHostDetails?.().masterId;
                }
                cb(err, client, _done);
            }
        });
    });
}
exports.getShardedConn = getShardedConn;

async function getShardedConnPromise(workspace_id) {
    return new Promise((resolve, reject) => {
        getShardedConn((err, client, done) => {
            if (err) {
                reject(err);
            } else {
                resolve({ client, done });
            }
        }, workspace_id);
    });
}
exports.getShardedConnPromise = getShardedConnPromise;

// note this requires you to null your first param if there is no err_cb, unlike getConn
async function getKnownShardedConn(err_cb, shard_id, cb) {
    if (!cb) {
        cb = err_cb;
        err_cb = null;
    }

    const _pool = await retry(() => getShardedQueryConnectionPool(shard_id), 'getKnownShardedConn', logger);

    const callerContext = AsyncStorage.getInstance().getContext();
    _pool.connect((err, client, done) => {
        AsyncStorage.getInstance().run(callerContext, () => {
            if (err && err_cb) {
                tryDone(done);
                logger.error({
                    msg: 'Failed to get a sharded connection',
                    ws_shard_id: shard_id,
                    err,
                    ECODE: 'DB_010',
                });
                err_cb({ err: 'Internal server error', status: 500, ECODE: 'DB_010' });
            } else {
                const _done = function doneFunction() {
                    tryDone(done);
                };

                if (client && !client.error_listener_attached) {
                    client.error_listener_attached = true;
                    client.on('error', client_err => {
                        logger.debug({ msg: 'Client conn error', client_err });
                    });
                }

                client.pool = _pool;
                client.masterId = _pool.masterId;

                if (_pool?.getConnectionHostDetails?.().masterId !== undefined && client?.masterId !== null) {
                    client.masterId = _pool?.getConnectionHostDetails?.().masterId;
                }
                cb(err, client, _done);
            }
        });
    });
}
exports.getKnownShardedConn = getKnownShardedConn;

async function getKnownShardedConnPromise(shard_id) {
    return new Promise((resolve, reject) => {
        getKnownShardedConn((err, client, done) => {
            if (err) {
                reject(err);
            } else {
                resolve({ client, done });
            }
        }, shard_id);
    });
}
exports.getKnownShardedConnPromise = getKnownShardedConnPromise;

function globalLongSingleQuery(query, params, options_or_cb, _cb) {
    // eslint-disable-next-line prefer-const
    let { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    if (!cb) {
        cb = params;
    }

    globalLongSingleQueryAsync(query, params, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.globalLongSingleQuery = globalLongSingleQuery;

async function globalLongSingleQueryAsync(query, params, options = {}) {
    const _pool = await getGlobalPool(options);

    return singleQueryAsync(_pool, query, params, {
        source: 'globalLongSingleQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.globalLongSingleQueryAsync = globalLongSingleQueryAsync;

async function emailsSentReadQueryAsync(query, params, options = {}) {
    const _pool = await getEmailsSentQueryConnectionPool(true);

    return singleQueryAsync(_pool, query, params, {
        source: 'emailsSentReadQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.emailsSentReadQueryAsync = emailsSentReadQueryAsync;

async function emailsSentWriteQueryAsync(query, params, options = {}) {
    const _pool = await getEmailsSentQueryConnectionPool(false);

    return singleQueryAsync(_pool, query, params, {
        source: 'emailsSentWriteQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.emailsSentWriteQueryAsync = emailsSentWriteQueryAsync;

function globalBatchQueriesSeries(queries, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    globalBatchQueriesSeriesAsync(queries, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.globalBatchQueriesSeries = globalBatchQueriesSeries;

async function globalBatchQueriesSeriesAsync(queries, options = {}) {
    const _pool = await getGlobalPool(options);

    return batchQueriesSeriesAsync(_pool, queries, {
        source: 'globalBatchQueriesSeriesAsync',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.globalBatchQueriesSeriesAsync = globalBatchQueriesSeriesAsync;

// takes in query array [{query: <query string>, params: <query params array>}]
function globalBatchQueries(queries, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    globalBatchQueriesAsync(queries, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.globalBatchQueries = globalBatchQueries;

async function globalBatchQueriesAsync(queries, options = {}) {
    const _pool = await getGlobalPool(options);

    return batchQueriesAsync(_pool, queries, {
        source: 'globalBatchQueriesAsync',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.globalBatchQueriesAsync = globalBatchQueriesAsync;

async function shardedBatchQueriesAsync(queries, workspace_id, options = {}) {
    const shard_id = await getShardIdFromWorkspace(workspace_id);
    const _pool = await getShardedQueryConnectionPool(shard_id);

    return batchQueriesAsync(_pool, queries, {
        source: 'shardedBatchQueriesAsync',
        workspace_id,
        statementTimeoutSeconds: options.statementTimeoutSeconds,
        shard_id,
    });
}
exports.shardedBatchQueriesAsync = shardedBatchQueriesAsync;

async function knownShardBatchQueriesAsync(queries, shard_id, options = {}) {
    const _pool = await getShardedQueryConnectionPool(shard_id);

    return batchQueriesAsync(_pool, queries, {
        source: 'knownShardBatchQueriesAsync',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
        shard_id,
    });
}
exports.knownShardBatchQueriesAsync = knownShardBatchQueriesAsync;

function knownShardBatchQueries(queries, shard_id, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    knownShardBatchQueriesAsync(queries, shard_id, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.knownShardBatchQueries = knownShardBatchQueries;

function globalIntegrationQuery(query, params, options_or_cb, _cb) {
    // eslint-disable-next-line prefer-const
    let { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    if (!cb) {
        cb = params;
    }

    globalIntegrationQueryPromise(query, params, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.globalIntegrationQuery = globalIntegrationQuery;

async function globalIntegrationQueryPromise(query, params, options = {}) {
    const _pool = await getGlobalPool(options);

    return singleQueryAsync(_pool, query, params, {
        source: 'globalIntegrationQueryPromise',
        longRunningLimit: 15000,
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.globalIntegrationQueryPromise = globalIntegrationQueryPromise;

function globalReplicaQuery(query, params, options_or_cb, _cb) {
    // eslint-disable-next-line prefer-const
    let { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    if (!cb) {
        cb = params;
    }

    globalPromiseReplicaQuery(query, params, options)
        .then(result => cb(null, result))
        .catch(cb);
}
exports.globalReplicaQuery = globalReplicaQuery;

async function globalPromiseReplicaQuery(query, params, options = {}) {
    const _pool = await getGlobalPool(options, true);

    let result;
    try {
        result = await singleQueryAsync(_pool, query, params, {
            source: 'globalPromiseReplicaQuery',
            statementTimeoutSeconds: options.statementTimeoutSeconds,
        });
    } catch (err) {
        if (options.dont_fallback_to_master) {
            throw err;
        } else {
            result = await globalIntegrationQueryPromise(query, params, options);
        }
    }

    return result;
}
exports.globalPromiseReplicaQuery = globalPromiseReplicaQuery;
// alias to keep up with the *Async convention
exports.globalReplicaQueryAsync = globalPromiseReplicaQuery;

async function shardedReplicaQuery(query, params, workspace_id, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    shardedReplicaQueryAsync(query, params, workspace_id, options)
        .then(result => cb(null, result))
        .catch(cb);
}

async function shardedReplicaQueryAsync(query, params, workspace_id, options = {}) {
    const shard_id = await getShardIdFromWorkspace(workspace_id);
    const _pool = await getReplicaShardedQueryConnectionPool(shard_id);

    if (![...shards, 'g001'].includes(shard_id)) {
        logger.warn({
            msg: 'Unknown shardId, check if team_id is valid or if workspace_to_shard is missing a row.',
            shard_id,
            team_id: workspace_id,
            stack: new Error().stack,
        });
    }

    return singleQueryAsync(_pool, query, params, {
        workspace_id,
        shard_id,
        source: 'shardedReplicaQuery',
        statementTimeoutSeconds: options.statementTimeoutSeconds,
    });
}
exports.shardedReplicaQuery = shardedReplicaQuery;
exports.shardedReplicaQueryAsync = shardedReplicaQueryAsync;

function getTableColumns(client, table_name, cb) {
    let query = `SELECT column_name FROM information_schema.columns WHERE table_name = $1`;
    const params = table_name.split('.').reverse();
    if (params.length > 1) {
        query += ` AND table_schema = $2`;
    }

    const table_columns = [];
    const callerContext = AsyncStorage.getInstance().getContext();
    client.query(query, params, (err, result) => {
        AsyncStorage.getInstance().run(callerContext, () => {
            if (err) {
                cb({
                    err: 'Internal server error',
                    status: 500,
                    ECODE: 'DB_007',
                });
                logger.error({
                    msg: 'Failed to get column names',
                    status: 500,
                    ECODE: 'DB_007',
                    err,
                });
                return;
            }

            result.rows.forEach(row => table_columns.push(row.column_name));

            cb(null, table_columns);
        });
    });
}
exports.getTableColumns = getTableColumns;

function connectAndGetTableColumns(table_name, options_or_cb, _cb) {
    const { options, cb } = getOptionsAndCb(options_or_cb, _cb);

    getGlobalConn(cb, options, (errConn, client, done) => {
        getTableColumns(client, table_name, (err, valid_columns) => {
            done();
            cb(err, valid_columns);
        });
    });
}
exports.connectAndGetTableColumns = connectAndGetTableColumns;

exports.disconnect = async () => {
    await Promise.all(
        [
            ...(isLocalOrTest ? [ConnectionPoolType.QueryConnectionPool] : []),
            ...(config.emails_sent ? [ConnectionPoolType.EmailsSentReadQueryConnectionPool] : []),
            ...(config.emails_sent ? [ConnectionPoolType.EmailsSentWriteQueryConnectionPool] : []),
        ].map(connectionPoolType => PoolManager().getNonShardConnectionPoolByType(connectionPoolType).endPools())
    );

    await Promise.all(
        shards.map(shardId =>
            PoolManager()
                .getShardConnectionPoolByType(shardId, ShardConnectionPoolType.ShardedQueryConnectionPool)
                .endPools()
        )
    );

    await Promise.all(
        shards.map(shardId =>
            PoolManager()
                .getShardConnectionPoolByType(shardId, ShardConnectionPoolType.ReplicaShardedQueryConnectionPool)
                .endPools()
        )
    );

    if (isLocalOrTest) {
        await PoolManager()
            .getNonShardConnectionPoolByType(ConnectionPoolType.GlobalConnectionPoolForLocalDev)
            .endPools();
    }
};

async function getTransactionClientAsync(shardId, clientOptions) {
    const { client, done } = await getKnownShardedConnPromise(shardId);
    try {
        return new TransactionClientImpl(client, clientOptions);
    } catch (err) {
        if (client) {
            client.release();
        }
        throw err;
    } finally {
        done();
    }
}

exports.getTransactionClientAsync = getTransactionClientAsync;
