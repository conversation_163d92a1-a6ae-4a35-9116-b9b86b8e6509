// Jest <PERSON> v1, https://goo.gl/fbAQLP

exports[`TransactionHistoryRecord fromRawTransactionHistoryRecord should create TransactionHistoryRecord from raw transaction history record database model 1`] = `
Object {
  "created_at": "1979-04-14T09:14:52.232Z",
  "discounted_ppu": 10,
  "end_at": "2100-09-11T05:38:44.324Z",
  "event_type": "plan_renewal",
  "id": Any<String>,
  "line_item_details": Object {
    "frequency": "monthly",
    "item": "Unlimited 2306",
    "type": "plan",
  },
  "original_end_at": "2107-01-13T01:12:04.324Z",
  "ppu": 10,
  "qty": 1,
  "start_at": "1979-04-14T09:14:52.232Z",
  "subtotal": 10,
  "tax_amount": 0,
  "team_id": 32939239239,
  "total_paid": 10,
  "transaction_details": Object {
    "creditUsed": "0.00",
    "prorate": "1.0000",
  },
  "transaction_id": "some_mocked_transaction_id",
  "transaction_triggered_by": "CRM",
}
`;

exports[`TransactionHistoryRecord fromTransactionData should create TransactionHistoryRecord from transaction data 1`] = `
Object {
  "created_at": "1979-04-14T09:14:52.232Z",
  "discounted_ppu": 10,
  "end_at": "2107-01-13T01:12:04.324Z",
  "event_type": "plan_renewal",
  "id": Any<String>,
  "line_item_details": Object {
    "frequency": "monthly",
    "item": "8",
    "type": "plan",
  },
  "original_end_at": "2107-01-13T01:12:04.324Z",
  "ppu": 10,
  "qty": 1,
  "start_at": "1979-04-14T09:14:52.232Z",
  "subtotal": 10,
  "tax_amount": 0,
  "team_id": 32939239239,
  "total_paid": 10,
  "transaction_details": Object {
    "creditUsed": "0.00",
    "prorate": "1.0000",
  },
  "transaction_id": "some_mocked_transaction_id",
  "transaction_triggered_by": "CRM",
}
`;

exports[`TransactionHistoryRecord setEndAt should successfully update amended end of subscription history record 1`] = `
Object {
  "created_at": "1979-04-14T09:14:52.232Z",
  "discounted_ppu": 10,
  "end_at": "2100-09-11T05:38:44.324Z",
  "event_type": "plan_renewal",
  "id": Any<String>,
  "line_item_details": Object {
    "frequency": "monthly",
    "item": "8",
    "type": "plan",
  },
  "original_end_at": "2107-01-13T01:12:04.324Z",
  "ppu": 10,
  "qty": 1,
  "start_at": "1979-04-14T09:14:52.232Z",
  "subtotal": 10,
  "tax_amount": 0,
  "team_id": 32939239239,
  "total_paid": 10,
  "transaction_details": Object {
    "creditUsed": "0.00",
    "prorate": "1.0000",
  },
  "transaction_id": "some_mocked_transaction_id",
  "transaction_triggered_by": "CRM",
}
`;
