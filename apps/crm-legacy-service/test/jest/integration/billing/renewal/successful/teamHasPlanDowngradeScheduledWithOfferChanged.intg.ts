/**
 * @jest-environment ../../libs/testing/jest-db-snapshot-environment/src/index.ts
 * @jest-environment-options { "dbSnapshotConfigOverride": { "monitoredTables": ["task_mgmt.teams", "task_mgmt.transactions", "task_mgmt.team_billing_info", "task_mgmt.team_downgrade_requests", "task_mgmt.team_audit"] } }
 */
import '../../../../../setupDisconnect';
import '../mocks';

import { PlanIds, PlanTransactionLineItemType, WorkspacePaymentsCycle } from '@clickup/billing/types';
import { getPerTableSnapshot } from '@clickup/testing/jest-db-snapshot-environment';
import { RenewalPlanChangeAuditAction } from '@clickup-legacy/libs/billing/audit/taskMgmtTeamAudit/types/renewalPlanChange';
import { RequestedSkuPayload } from '@clickup-legacy/libs/billing/downgrades/DowngradeRequest';
import { PlanOfferId } from '@clickup-legacy/libs/billing/plan/interface';

import { TestWorkspace } from '../../../../../tools/billing';
import { expectBraintreeTransactionCalls } from '../../../../../tools/billing/utils/commonExpects/expectBraintreeTransactions';
import {
    DefaultRenewalTeamUpdateMatcher,
    teamAuditMatcher,
    teamBilledPlanIdMatcher,
    teamDowngradeRequestFinalizeMatcher,
    teamUpgradeWithoutNBDMatcher,
    TransactionWithPendingStateMatcher,
} from '../../../../../tools/billing/utils/matchers';
import { getSingleRenewableTeam, renewalAsync } from '../utils';

describe('Renewal Interval', () => {
    // $9 since renewal will be processed on Unlimited plan ($9/month)
    const expectedBraintreeTransactions = [9];

    const exampleDowngradeRequestSku: RequestedSkuPayload = {
        type: PlanTransactionLineItemType.Plan,
        source: PlanIds.Business,
        target: PlanIds.Unlimited,
    };

    beforeAll(async () => {
        await TestWorkspace.setupEnvironment({
            workspace: {
                ...getSingleRenewableTeam(),
                cycles: WorkspacePaymentsCycle.Monthly,
                plan: PlanIds.Business,
                billedPlan: PlanIds.Business,
                downgradeRequests: [{ requestedSku: exampleDowngradeRequestSku }],
                custom_data: {
                    // This offer does not contain plan to which workspace agreed to downgrade
                    plan_offer_id: PlanOfferId.Unlimited202305Teams,
                },
            },
        });
    });

    it('should downgrade team and bill for full cycle when downgrade request is scheduled', async () => {
        await renewalAsync();

        const difference = await getPerTableSnapshot();

        expectBraintreeTransactionCalls(expectedBraintreeTransactions);

        // Plan should be changed to the target plan due to downgrade request being processed for plan
        expect(difference).toMatchSnapshot({
            'task_mgmt.teams': [
                DefaultRenewalTeamUpdateMatcher,
                teamUpgradeWithoutNBDMatcher(PlanIds.Business, PlanIds.Unlimited),
            ],
            'task_mgmt.team_billing_info': [
                { table: 'team_billing_info' },
                teamBilledPlanIdMatcher(PlanIds.Business, PlanIds.Unlimited),
            ],
            'task_mgmt.transactions': TransactionWithPendingStateMatcher,
            'task_mgmt.team_downgrade_requests': [teamDowngradeRequestFinalizeMatcher(exampleDowngradeRequestSku)],
            'task_mgmt.team_audit': [teamAuditMatcher(RenewalPlanChangeAuditAction.TeamDowngradedAtRenewal)],
        });
    });
});
