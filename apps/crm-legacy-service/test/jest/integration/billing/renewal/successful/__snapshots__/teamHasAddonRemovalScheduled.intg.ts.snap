// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`Renewal Interval should downgrade team and bill for full cycle when add-on downgrade request is scheduled 1`] = `
Object {
  "task_mgmt.team_addons": Array [
    Object {
      "command": "DELETE",
      "level": "ROW",
      "newRow": null,
      "oldRow": Object {
        "added_by": Any<Number>,
        "addon_id": "ai_meeting_e_2501",
        "custom_price": 100,
        "date_added": Any<Number>,
        "team_id": 10002,
        "total": 1,
      },
      "schema": "task_mgmt",
      "table": "team_addons",
    },
  ],
  "task_mgmt.team_audit": Array [
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "action": "addons_downgraded_at_renewal",
        "data": Object {
          "new_addons": Array [],
          "old_addons": Array [
            "ai_meeting_e_2501",
          ],
          "reason": "Addons Downgraded due to downgrade at renewal",
        },
        "date": Any<Number>,
        "team_id": Any<Number>,
        "userid": -1,
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "team_audit",
    },
  ],
  "task_mgmt.team_billing_info": Array [
    Object {
      "command": "UPDATE",
      "level": "ROW",
      "newRow": Object {
        "billed_plan_id": 3,
        "billed_users_this_cycle": 1,
        "custom_data": null,
        "migrate_from_plan_id": null,
        "migrate_to_plan_id": null,
        "migration_date": null,
        "next_bill_date": 1672531200000,
        "suppress_banners_end": null,
        "suppress_banners_start": null,
        "team_id": 10002,
      },
      "oldRow": Object {
        "billed_plan_id": 3,
        "billed_users_this_cycle": null,
        "custom_data": null,
        "migrate_from_plan_id": null,
        "migrate_to_plan_id": null,
        "migration_date": null,
        "next_bill_date": null,
        "suppress_banners_end": null,
        "suppress_banners_start": null,
        "team_id": 10002,
      },
      "schema": "task_mgmt",
      "table": "team_billing_info",
    },
  ],
  "task_mgmt.team_downgrade_requests": Array [
    Object {
      "command": "UPDATE",
      "level": "ROW",
      "newRow": Object {
        "canceled_at": null,
        "canceling_crm_user": null,
        "canceling_user": null,
        "id": Any<String>,
        "processed_at": Any<String>,
        "scheduled_at": Any<String>,
        "scheduling_crm_user": null,
        "scheduling_user": Any<Number>,
        "sku_type": "ai_meeting",
        "source_sku": "ai_meeting_e_2501",
        "target_sku": null,
        "workspace_id": 10002,
      },
      "oldRow": Object {
        "canceled_at": null,
        "canceling_crm_user": null,
        "canceling_user": null,
        "id": Any<String>,
        "processed_at": null,
        "scheduled_at": Any<String>,
        "scheduling_crm_user": null,
        "scheduling_user": Any<Number>,
        "sku_type": "ai_meeting",
        "source_sku": "ai_meeting_e_2501",
        "target_sku": null,
        "workspace_id": 10002,
      },
      "schema": "task_mgmt",
      "table": "team_downgrade_requests",
    },
  ],
  "task_mgmt.teams": Array [
    Object {
      "command": "UPDATE",
      "level": "ROW",
      "newRow": Object {
        "action_count": null,
        "action_limit_override": null,
        "active": null,
        "address": null,
        "admin_global_delete": null,
        "admin_public_share_override": null,
        "ai_enabled": null,
        "allow_org_signup": null,
        "allow_skip_2fa": null,
        "assign_gh_review_requests": null,
        "attributor": null,
        "auto_101_sent": null,
        "auto_90_sent": null,
        "automation_count_reset_on": null,
        "automation_enabled": null,
        "automation_limit_reached": null,
        "avatar_key": null,
        "azure_sso": null,
        "base_guests_override": null,
        "billed_users_this_cycle": 1,
        "billingexceptionpopupdismissed": null,
        "business_base_guests_override": null,
        "business_guests_per_seat_override": null,
        "business_plus_base_guests_override": null,
        "business_plus_guests_per_seat_override": null,
        "business_plus_ppu_monthly_override": null,
        "business_plus_ppu_yearly_override": null,
        "business_plus_price_monthly_override": null,
        "business_plus_price_yearly_override": null,
        "business_ppu_monthly_override": null,
        "business_ppu_yearly_override": null,
        "business_price_monthly_override": null,
        "business_price_yearly_override": null,
        "can_add_guests": null,
        "can_remove_guests": null,
        "cancelpromo": null,
        "charge_for_internal_guests": null,
        "color": null,
        "color_theme": null,
        "creator": null,
        "credit": null,
        "credit_updating": null,
        "custom_fields_legacy_ordering": null,
        "custom_sprint_duration": null,
        "cycles": "monthly",
        "cycles_applied": null,
        "cycles_next_cycle": null,
        "dashboard_data_date": null,
        "dashboards_enabled": null,
        "data_removal_max_days": null,
        "data_retention_min_days": null,
        "date_created": 1672358400000,
        "date_suspended": null,
        "date_transacting": null,
        "date_updating": null,
        "default_payment_token": Any<String>,
        "deleted": false,
        "disable_never_expire_pub_links": null,
        "disable_public_sharing": null,
        "disable_template_pub_sharing": null,
        "docs_home": null,
        "downgrade_on_renewal": null,
        "emails_as_replies": null,
        "enable_codox": null,
        "enable_recorder": null,
        "enterprise_base_guests_override": null,
        "enterprise_guests_per_seat_override": null,
        "enterprise_plan_available": null,
        "enterprise_ppu_monthly_override": null,
        "enterprise_ppu_yearly_override": null,
        "enterprise_price_monthly_override": null,
        "enterprise_price_yearly_override": null,
        "estimates_per_assignee": null,
        "extended_free_trial": false,
        "extra_comment_reactions": null,
        "failed_transaction_attempts": 0,
        "free_seats": 0,
        "gantt_trial_end": null,
        "giphy": null,
        "goals_usage_count": null,
        "google_sso": null,
        "grace_period_end": null,
        "guests_per_seat_override": null,
        "hide_everything_board": null,
        "hide_everything_calendar": null,
        "hipaa_compliant": null,
        "hosted_secret": null,
        "hours_per_day": null,
        "id": 10002,
        "is_ai_hidden": null,
        "is_vip": null,
        "last_admin_inbound": null,
        "last_admin_outbound": null,
        "last_inbound": null,
        "last_non_drip_admin_inbound": null,
        "last_non_drip_admin_outbound": null,
        "last_non_drip_inbound": null,
        "last_non_drip_outbound": null,
        "last_non_drip_owner_inbound": null,
        "last_non_drip_owner_outbound": null,
        "last_outbound": null,
        "last_owner_inbound": null,
        "last_owner_outbound": null,
        "lineup": null,
        "live_view": null,
        "live_view_display_time": null,
        "manually_paying": null,
        "members_can_add_seats": null,
        "microsoft_365_preview": null,
        "milestone_avatar_key": null,
        "milestone_title": null,
        "minimum_seats": null,
        "name": "workspace_10002",
        "nested_subtasks": null,
        "nested_subtasks_level": null,
        "next_bill_date": 1672531200000,
        "next_bill_date_updating": null,
        "next_renewal_retry_date": null,
        "next_rollup_retry_date": null,
        "node_transacting": null,
        "node_updating": null,
        "number_of_team_users": null,
        "offloading_state": null,
        "okta_sso": null,
        "onboarding_complete_time": null,
        "onetool": null,
        "orderindexes_fixed": null,
        "owner": 10002,
        "owner_control_private_spaces": null,
        "payment_failed": false,
        "personal_team": null,
        "personal_views": null,
        "plan_id": 3,
        "plan_updated": 1672358400000,
        "points_estimate_rollup": null,
        "points_per_assignee": null,
        "points_scale": null,
        "porfolio_usage_count": null,
        "ppu_monthly_override": null,
        "ppu_yearly_override": null,
        "previous_tools": null,
        "price_discount": null,
        "price_monthly_override": null,
        "price_yearly_override": null,
        "priority": null,
        "promo_code": null,
        "promo_code_added": null,
        "pub_links_max_year": null,
        "quick_create_statuses": null,
        "requested_region": null,
        "require_2fa": null,
        "require_sso": null,
        "reseller": null,
        "resource_management": null,
        "rollup_grace_period_end": null,
        "sales_type": "self serve",
        "saml_post_binding": null,
        "saml_sso": null,
        "scim_token": null,
        "sent_bb_email": null,
        "sent_gh_email": false,
        "sent_gl_email": null,
        "sent_slack_email": false,
        "service_status": 1,
        "setup_step": null,
        "should_encrypt": null,
        "signed_attachments": null,
        "skip_recaptcha": null,
        "slack_channel": null,
        "slack_hook_token": null,
        "slack_team_id": null,
        "slack_team_name": null,
        "slack_token": null,
        "sp_cert": null,
        "sso_enabled_date": null,
        "storage_override": null,
        "storage_override3": null,
        "storage_per_user_override": null,
        "storage_used_bytes": null,
        "stored_promo_code": null,
        "subtasks_in_multiple_lists": null,
        "summary_tasks": null,
        "task_relationships": null,
        "tasks_in_multiple_lists": null,
        "tax_exempt": null,
        "threaded_comments": null,
        "time_estimate_display_hours": null,
        "time_estimate_rollup": null,
        "time_in_status": null,
        "time_tracking_display_hours": null,
        "time_tracking_rollup": null,
        "tray_action_count": null,
        "tray_action_limit_override": null,
        "tray_trigger_count": null,
        "tray_trigger_limit_override": null,
        "trial_count": null,
        "trigger_count": null,
        "trigger_limit_override": null,
        "universal_search": null,
        "unstarted_status_group": null,
        "user_presence": null,
        "users_cycle_updating": null,
        "using_bitbucket": null,
        "using_github": false,
        "using_gitlab": null,
        "v2_beta": null,
        "v2_beta_start": null,
        "v2_favorites_converted": null,
        "view_limit_override": null,
        "was_trial": false,
        "week_trial_email_sent": false,
        "wip_limit": null,
        "workspace_no_tax": null,
        "zoom": null,
      },
      "oldRow": Object {
        "action_count": null,
        "action_limit_override": null,
        "active": null,
        "address": null,
        "admin_global_delete": null,
        "admin_public_share_override": null,
        "ai_enabled": null,
        "allow_org_signup": null,
        "allow_skip_2fa": null,
        "assign_gh_review_requests": null,
        "attributor": null,
        "auto_101_sent": null,
        "auto_90_sent": null,
        "automation_count_reset_on": null,
        "automation_enabled": null,
        "automation_limit_reached": null,
        "avatar_key": null,
        "azure_sso": null,
        "base_guests_override": null,
        "billed_users_this_cycle": 1,
        "billingexceptionpopupdismissed": null,
        "business_base_guests_override": null,
        "business_guests_per_seat_override": null,
        "business_plus_base_guests_override": null,
        "business_plus_guests_per_seat_override": null,
        "business_plus_ppu_monthly_override": null,
        "business_plus_ppu_yearly_override": null,
        "business_plus_price_monthly_override": null,
        "business_plus_price_yearly_override": null,
        "business_ppu_monthly_override": null,
        "business_ppu_yearly_override": null,
        "business_price_monthly_override": null,
        "business_price_yearly_override": null,
        "can_add_guests": null,
        "can_remove_guests": null,
        "cancelpromo": null,
        "charge_for_internal_guests": null,
        "color": null,
        "color_theme": null,
        "creator": null,
        "credit": null,
        "credit_updating": null,
        "custom_fields_legacy_ordering": null,
        "custom_sprint_duration": null,
        "cycles": "monthly",
        "cycles_applied": null,
        "cycles_next_cycle": null,
        "dashboard_data_date": null,
        "dashboards_enabled": null,
        "data_removal_max_days": null,
        "data_retention_min_days": null,
        "date_created": 1672358400000,
        "date_suspended": null,
        "date_transacting": null,
        "date_updating": null,
        "default_payment_token": Any<String>,
        "deleted": false,
        "disable_never_expire_pub_links": null,
        "disable_public_sharing": null,
        "disable_template_pub_sharing": null,
        "docs_home": null,
        "downgrade_on_renewal": null,
        "emails_as_replies": null,
        "enable_codox": null,
        "enable_recorder": null,
        "enterprise_base_guests_override": null,
        "enterprise_guests_per_seat_override": null,
        "enterprise_plan_available": null,
        "enterprise_ppu_monthly_override": null,
        "enterprise_ppu_yearly_override": null,
        "enterprise_price_monthly_override": null,
        "enterprise_price_yearly_override": null,
        "estimates_per_assignee": null,
        "extended_free_trial": false,
        "extra_comment_reactions": null,
        "failed_transaction_attempts": 0,
        "free_seats": 0,
        "gantt_trial_end": null,
        "giphy": null,
        "goals_usage_count": null,
        "google_sso": null,
        "grace_period_end": null,
        "guests_per_seat_override": null,
        "hide_everything_board": null,
        "hide_everything_calendar": null,
        "hipaa_compliant": null,
        "hosted_secret": null,
        "hours_per_day": null,
        "id": 10002,
        "is_ai_hidden": null,
        "is_vip": null,
        "last_admin_inbound": null,
        "last_admin_outbound": null,
        "last_inbound": null,
        "last_non_drip_admin_inbound": null,
        "last_non_drip_admin_outbound": null,
        "last_non_drip_inbound": null,
        "last_non_drip_outbound": null,
        "last_non_drip_owner_inbound": null,
        "last_non_drip_owner_outbound": null,
        "last_outbound": null,
        "last_owner_inbound": null,
        "last_owner_outbound": null,
        "lineup": null,
        "live_view": null,
        "live_view_display_time": null,
        "manually_paying": null,
        "members_can_add_seats": null,
        "microsoft_365_preview": null,
        "milestone_avatar_key": null,
        "milestone_title": null,
        "minimum_seats": null,
        "name": "workspace_10002",
        "nested_subtasks": null,
        "nested_subtasks_level": null,
        "next_bill_date": 1669852800000,
        "next_bill_date_updating": null,
        "next_renewal_retry_date": null,
        "next_rollup_retry_date": null,
        "node_transacting": null,
        "node_updating": Any<String>,
        "number_of_team_users": null,
        "offloading_state": null,
        "okta_sso": null,
        "onboarding_complete_time": null,
        "onetool": null,
        "orderindexes_fixed": null,
        "owner": 10002,
        "owner_control_private_spaces": null,
        "payment_failed": false,
        "personal_team": null,
        "personal_views": null,
        "plan_id": 3,
        "plan_updated": 1672358400000,
        "points_estimate_rollup": null,
        "points_per_assignee": null,
        "points_scale": null,
        "porfolio_usage_count": null,
        "ppu_monthly_override": null,
        "ppu_yearly_override": null,
        "previous_tools": null,
        "price_discount": null,
        "price_monthly_override": null,
        "price_yearly_override": null,
        "priority": null,
        "promo_code": null,
        "promo_code_added": null,
        "pub_links_max_year": null,
        "quick_create_statuses": null,
        "requested_region": null,
        "require_2fa": null,
        "require_sso": null,
        "reseller": null,
        "resource_management": null,
        "rollup_grace_period_end": null,
        "sales_type": "self serve",
        "saml_post_binding": null,
        "saml_sso": null,
        "scim_token": null,
        "sent_bb_email": null,
        "sent_gh_email": false,
        "sent_gl_email": null,
        "sent_slack_email": false,
        "service_status": 1,
        "setup_step": null,
        "should_encrypt": null,
        "signed_attachments": null,
        "skip_recaptcha": null,
        "slack_channel": null,
        "slack_hook_token": null,
        "slack_team_id": null,
        "slack_team_name": null,
        "slack_token": null,
        "sp_cert": null,
        "sso_enabled_date": null,
        "storage_override": null,
        "storage_override3": null,
        "storage_per_user_override": null,
        "storage_used_bytes": null,
        "stored_promo_code": null,
        "subtasks_in_multiple_lists": null,
        "summary_tasks": null,
        "task_relationships": null,
        "tasks_in_multiple_lists": null,
        "tax_exempt": null,
        "threaded_comments": null,
        "time_estimate_display_hours": null,
        "time_estimate_rollup": null,
        "time_in_status": null,
        "time_tracking_display_hours": null,
        "time_tracking_rollup": null,
        "tray_action_count": null,
        "tray_action_limit_override": null,
        "tray_trigger_count": null,
        "tray_trigger_limit_override": null,
        "trial_count": null,
        "trigger_count": null,
        "trigger_limit_override": null,
        "universal_search": null,
        "unstarted_status_group": null,
        "user_presence": null,
        "users_cycle_updating": null,
        "using_bitbucket": null,
        "using_github": false,
        "using_gitlab": null,
        "v2_beta": null,
        "v2_beta_start": null,
        "v2_favorites_converted": null,
        "view_limit_override": null,
        "was_trial": false,
        "week_trial_email_sent": false,
        "wip_limit": null,
        "workspace_no_tax": null,
        "zoom": null,
      },
      "schema": "task_mgmt",
      "table": "teams",
    },
  ],
  "task_mgmt.transactions": Array [
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "body": Object {
          "randId": Any<String>,
          "status": "internal_pending",
        },
        "created": Any<Number>,
        "data": Object {
          "addons_data": Array [],
          "addons_price": 0,
          "basePrice": 0,
          "baseRate": 19,
          "credit": "0.00",
          "creditAfter": "0.00",
          "cycles": "monthly",
          "discount": "0.00",
          "end_of_cycle": 1672531200000,
          "free_seats": 0,
          "guestSeats": 0,
          "guest_seats": 0,
          "lineItems": Array [
            Object {
              "auditMessages": Array [
                Object {
                  "action": "crm renewal",
                  "msg": "Unified renewal
{\\"subtotal\\":\\"19.00\\",\\"totalDiscount\\":\\"0.00\\",\\"effective\\":{\\"lump\\":0,\\"ppu\\":19},\\"discount\\":{\\"lump\\":0,\\"ppu\\":0},\\"list\\":{\\"lump\\":0,\\"ppu\\":19},\\"qty\\":1,\\"prorate\\":\\"1.0000\\",\\"priceExplanations\\":[\\"business_tier\\",\\"unified_renewal\\"],\\"creditUsed\\":\\"0.00\\",\\"auditMessages\\":[],\\"billDate\\":\\"2022-12-01T00:00:00.000Z\\",\\"startDate\\":\\"2022-12-01T00:00:00.000Z\\",\\"endDate\\":\\"2023-01-01T00:00:00.000Z\\"}",
                },
              ],
              "billDate": "2022-12-01T00:00:00.000Z",
              "billingEvent": "renewal",
              "creditUsed": "0.00",
              "description": "Business Plan (monthly) 11/30/22 - 12/31/22",
              "discountLumpSum": "0.00",
              "discountPerUnit": "0.00",
              "effectivePriceLumpSum": "0.00",
              "effectivePricePerUnit": "19.00",
              "endDate": "2023-01-01T00:00:00.000Z",
              "invalidatedPromo": null,
              "lineNumber": "renewal_1",
              "listPriceLumpSum": "0.00",
              "listPricePerUnit": "19.00",
              "promo": null,
              "prorate": "1.0000",
              "qty": 1,
              "seatsDistribution": Object {
                "allowedGuests": 10,
                "chargedInTotal": 1,
                "chargedMembers": 1,
                "chargedSeatsToAccommodatedGuestsOverage": 0,
                "freeSeats": 0,
                "guestCount": 0,
                "guestsPerSeat": 5,
                "limitedMemberCount": 0,
                "minimumSeats": null,
              },
              "sku": Object {
                "frequency": "monthly",
                "id": "Business",
                "type": "plan",
              },
              "startDate": "2022-12-01T00:00:00.000Z",
              "subtotal": "19.00",
              "tax": Object {
                "amount": "0.00",
                "code": "SW054003",
                "name": "Sales",
                "rate": "0.00000",
                "taxExempt": "0.00",
                "taxableAmount": "0.00",
              },
              "total": "19.00",
              "totalDiscount": "0.00",
              "unit": "seat",
            },
          ],
          "managedServicesAmount": 0,
          "managedServicesTaxData": Object {
            "taxAmount": 0,
            "taxRate": 0,
          },
          "memberCount": 1,
          "minimum_seats": null,
          "plan_id": 3,
          "ppu": "19.00",
          "price": "19.00",
          "promo": null,
          "reason": "recurring",
          "saasAmount": 19,
          "saasTaxData": Object {
            "taxAmount": 0,
            "taxRate": 0,
          },
          "subtotal": "19.00",
          "taxAmount": 0,
          "taxInfo": Object {
            "taxDeducted": false,
            "taxSystem": "none",
          },
          "taxRate": 0,
          "total_seats_charged": 1,
        },
        "failed": false,
        "failure_cause": null,
        "initial_transaction_id": Any<String>,
        "invoice_sent": null,
        "manual_payment": false,
        "source": 0,
        "team_id": 10002,
        "transaction_id": Any<String>,
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "transactions",
    },
    Object {
      "command": "INSERT",
      "level": "ROW",
      "newRow": Object {
        "body": Any<Object>,
        "created": Any<Number>,
        "data": Object {
          "addons_data": Array [],
          "addons_price": 0,
          "basePrice": 0,
          "baseRate": 19,
          "credit": "0.00",
          "creditAfter": "0.00",
          "cycles": "monthly",
          "discount": "0.00",
          "end_of_cycle": 1672531200000,
          "free_seats": 0,
          "guestSeats": 0,
          "guest_seats": 0,
          "lineItems": Array [
            Object {
              "auditMessages": Array [
                Object {
                  "action": "crm renewal",
                  "msg": "Unified renewal
{\\"subtotal\\":\\"19.00\\",\\"totalDiscount\\":\\"0.00\\",\\"effective\\":{\\"lump\\":0,\\"ppu\\":19},\\"discount\\":{\\"lump\\":0,\\"ppu\\":0},\\"list\\":{\\"lump\\":0,\\"ppu\\":19},\\"qty\\":1,\\"prorate\\":\\"1.0000\\",\\"priceExplanations\\":[\\"business_tier\\",\\"unified_renewal\\"],\\"creditUsed\\":\\"0.00\\",\\"auditMessages\\":[],\\"billDate\\":\\"2022-12-01T00:00:00.000Z\\",\\"startDate\\":\\"2022-12-01T00:00:00.000Z\\",\\"endDate\\":\\"2023-01-01T00:00:00.000Z\\"}",
                },
              ],
              "billDate": "2022-12-01T00:00:00.000Z",
              "billingEvent": "renewal",
              "creditUsed": "0.00",
              "description": "Business Plan (monthly) 11/30/22 - 12/31/22",
              "discountLumpSum": "0.00",
              "discountPerUnit": "0.00",
              "effectivePriceLumpSum": "0.00",
              "effectivePricePerUnit": "19.00",
              "endDate": "2023-01-01T00:00:00.000Z",
              "invalidatedPromo": null,
              "lineNumber": "renewal_1",
              "listPriceLumpSum": "0.00",
              "listPricePerUnit": "19.00",
              "promo": null,
              "prorate": "1.0000",
              "qty": 1,
              "seatsDistribution": Object {
                "allowedGuests": 10,
                "chargedInTotal": 1,
                "chargedMembers": 1,
                "chargedSeatsToAccommodatedGuestsOverage": 0,
                "freeSeats": 0,
                "guestCount": 0,
                "guestsPerSeat": 5,
                "limitedMemberCount": 0,
                "minimumSeats": null,
              },
              "sku": Object {
                "frequency": "monthly",
                "id": "Business",
                "type": "plan",
              },
              "startDate": "2022-12-01T00:00:00.000Z",
              "subtotal": "19.00",
              "tax": Object {
                "amount": "0.00",
                "code": "SW054003",
                "name": "Sales",
                "rate": "0.00000",
                "taxExempt": "0.00",
                "taxableAmount": "0.00",
              },
              "total": "19.00",
              "totalDiscount": "0.00",
              "unit": "seat",
            },
          ],
          "managedServicesAmount": 0,
          "managedServicesTaxData": Object {
            "taxAmount": 0,
            "taxRate": 0,
          },
          "memberCount": 1,
          "minimum_seats": null,
          "plan_id": 3,
          "ppu": "19.00",
          "price": "19.00",
          "promo": null,
          "reason": "recurring",
          "saasAmount": 19,
          "saasTaxData": Object {
            "taxAmount": 0,
            "taxRate": 0,
          },
          "subtotal": "19.00",
          "taxAmount": 0,
          "taxInfo": Object {
            "taxDeducted": false,
            "taxSystem": "none",
          },
          "taxRate": 0,
          "total_seats_charged": 1,
        },
        "failed": false,
        "failure_cause": null,
        "initial_transaction_id": Any<String>,
        "invoice_sent": null,
        "manual_payment": false,
        "source": 0,
        "team_id": 10002,
        "transaction_id": Any<String>,
      },
      "oldRow": null,
      "schema": "task_mgmt",
      "table": "transactions",
    },
    Object {
      "command": "DELETE",
      "level": "ROW",
      "newRow": null,
      "oldRow": Object {
        "body": Object {
          "randId": Any<String>,
          "status": "internal_pending",
        },
        "created": Any<Number>,
        "data": Object {
          "addons_data": Array [],
          "addons_price": 0,
          "basePrice": 0,
          "baseRate": 19,
          "credit": "0.00",
          "creditAfter": "0.00",
          "cycles": "monthly",
          "discount": "0.00",
          "end_of_cycle": 1672531200000,
          "free_seats": 0,
          "guestSeats": 0,
          "guest_seats": 0,
          "lineItems": Array [
            Object {
              "auditMessages": Array [
                Object {
                  "action": "crm renewal",
                  "msg": "Unified renewal
{\\"subtotal\\":\\"19.00\\",\\"totalDiscount\\":\\"0.00\\",\\"effective\\":{\\"lump\\":0,\\"ppu\\":19},\\"discount\\":{\\"lump\\":0,\\"ppu\\":0},\\"list\\":{\\"lump\\":0,\\"ppu\\":19},\\"qty\\":1,\\"prorate\\":\\"1.0000\\",\\"priceExplanations\\":[\\"business_tier\\",\\"unified_renewal\\"],\\"creditUsed\\":\\"0.00\\",\\"auditMessages\\":[],\\"billDate\\":\\"2022-12-01T00:00:00.000Z\\",\\"startDate\\":\\"2022-12-01T00:00:00.000Z\\",\\"endDate\\":\\"2023-01-01T00:00:00.000Z\\"}",
                },
              ],
              "billDate": "2022-12-01T00:00:00.000Z",
              "billingEvent": "renewal",
              "creditUsed": "0.00",
              "description": "Business Plan (monthly) 11/30/22 - 12/31/22",
              "discountLumpSum": "0.00",
              "discountPerUnit": "0.00",
              "effectivePriceLumpSum": "0.00",
              "effectivePricePerUnit": "19.00",
              "endDate": "2023-01-01T00:00:00.000Z",
              "invalidatedPromo": null,
              "lineNumber": "renewal_1",
              "listPriceLumpSum": "0.00",
              "listPricePerUnit": "19.00",
              "promo": null,
              "prorate": "1.0000",
              "qty": 1,
              "seatsDistribution": Object {
                "allowedGuests": 10,
                "chargedInTotal": 1,
                "chargedMembers": 1,
                "chargedSeatsToAccommodatedGuestsOverage": 0,
                "freeSeats": 0,
                "guestCount": 0,
                "guestsPerSeat": 5,
                "limitedMemberCount": 0,
                "minimumSeats": null,
              },
              "sku": Object {
                "frequency": "monthly",
                "id": "Business",
                "type": "plan",
              },
              "startDate": "2022-12-01T00:00:00.000Z",
              "subtotal": "19.00",
              "tax": Object {
                "amount": "0.00",
                "code": "SW054003",
                "name": "Sales",
                "rate": "0.00000",
                "taxExempt": "0.00",
                "taxableAmount": "0.00",
              },
              "total": "19.00",
              "totalDiscount": "0.00",
              "unit": "seat",
            },
          ],
          "managedServicesAmount": 0,
          "managedServicesTaxData": Object {
            "taxAmount": 0,
            "taxRate": 0,
          },
          "memberCount": 1,
          "minimum_seats": null,
          "plan_id": 3,
          "ppu": "19.00",
          "price": "19.00",
          "promo": null,
          "reason": "recurring",
          "saasAmount": 19,
          "saasTaxData": Object {
            "taxAmount": 0,
            "taxRate": 0,
          },
          "subtotal": "19.00",
          "taxAmount": 0,
          "taxInfo": Object {
            "taxDeducted": false,
            "taxSystem": "none",
          },
          "taxRate": 0,
          "total_seats_charged": 1,
        },
        "failed": false,
        "failure_cause": null,
        "initial_transaction_id": Any<String>,
        "invoice_sent": null,
        "manual_payment": false,
        "source": 0,
        "team_id": 10002,
        "transaction_id": Any<String>,
      },
      "schema": "task_mgmt",
      "table": "transactions",
    },
  ],
}
`;
