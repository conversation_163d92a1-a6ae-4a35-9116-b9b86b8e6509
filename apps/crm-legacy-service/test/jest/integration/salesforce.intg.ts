/**
 * @jest-environment ../../libs/testing/jest-db-snapshot-environment/src/index.ts
 * @jest-environment-options { "dbSnapshotConfigOverride": { "monitoredTables": [] } }
 */

import '../../setupDisconnect';
import '../_mocks/configMocks';
import '../_mocks/splitMock';

import config from 'config';
// eslint-disable-next-line import/no-extraneous-dependencies
import request from 'supertest';

import { getExpressApp } from '../../../src/app/expressApp';
import { TestWorkspace } from '../../tools/billing';

jest.mock('../../../src/app/models/http/httpClient');

// eslint-disable-next-line import/first
import { HttpClient } from '../../../src/app/models/http/httpClient'; // Import the original class for typing
// eslint-disable-next-line import/first
import { clearSalesforceCache, clearSalesforceToken } from '../../../src/app/models/salesforce';

// Type casting for mocked components
const MockedHttpClient = HttpClient as jest.MockedClass<typeof HttpClient>;
const mockedGet = jest.fn(); // Mock function for HttpClient get
const mockedPost = jest.fn(); // Mock function for HttpClient post

// Mock implementation for HttpClient constructor and methods
MockedHttpClient.mockImplementation(
    () =>
        ({
            get: mockedGet,
            post: mockedPost,
            patch: jest.fn(), // Add other methods if needed
        } as unknown as HttpClient) // Use unknown type assertion for simplicity
);

describe('salesforce API', () => {
    let testWorkspace: TestWorkspace;
    const authToken = 'Trust crmTestUser';
    const app = getExpressApp();

    beforeAll(async () => {
        // Setup workspace and a test user with a JWT token
        testWorkspace = await TestWorkspace.setupEnvironment({ workspace: { id: '7784618465' } });

        // Setup mock config values needed by salesforce.ts
        config.env = 'prod';
    });

    beforeEach(async () => {
        jest.clearAllMocks();
        mockedGet.mockReset();
        mockedPost.mockReset();

        // Reset HttpClient mocks - assume token refresh works on first try for simplicity in most tests
        // Mock the token refresh call (POST to login.salesforce.com)
        mockedPost.mockResolvedValueOnce({
            data: { access_token: 'test-access-token' },
            status: 200,
            statusText: 'OK',
            headers: {},
            config: {},
        });

        clearSalesforceCache();
        clearSalesforceToken();
    });

    afterEach(() => {
        jest.clearAllMocks();
        clearSalesforceCache();
        mockedGet.mockReset();
        mockedPost.mockReset();
    });

    describe('GET /v1/frontUser/salesforceInfo', () => {
        const testEmail = '<EMAIL>';

        it('should return empty object if email query param is missing', async () => {
            const res = await request(app).get('/v1/frontUser/salesforceInfo').set('Authorization', authToken);

            expect(res.status).toEqual(200);
            expect(res.body).toEqual({});
            expect(mockedGet).not.toHaveBeenCalled(); // No Salesforce calls made
        });

        it('should fetch and return Lead info if found', async () => {
            // Mock SOSL response for Lead/Contact search
            mockedGet.mockResolvedValueOnce({
                data: {
                    searchRecords: [
                        { Id: 'lead123', attributes: { type: 'Lead' }, Owner: { Name: 'Lead Owner' }, Status: 'Open' },
                    ],
                },
                status: 200,
                headers: {},
                config: {},
            });
            // Mock SOQL response for related Accounts (empty in this case)
            mockedGet.mockResolvedValueOnce({
                data: {
                    searchRecords: [],
                }, // No related accounts
                status: 200,
                headers: {},
                config: {},
            });
            // Mock SOQL response for Opportunities (empty)
            mockedGet.mockResolvedValueOnce({
                data: {
                    searchRecords: [],
                }, // No opportunities
                status: 200,
                headers: {},
                config: {},
            });

            const res = await request(app)
                .get('/v1/frontUser/salesforceInfo')
                .set('Authorization', authToken)
                .query({ email: testEmail });

            expect(res.status).toEqual(200);
            expect(res.body).toEqual({
                Lead: [
                    {
                        id: 'lead123',
                        type: 'Lead',
                        owner_name: 'Lead Owner',
                        status: 'Open',
                        // last_updated is not mocked here, might be undefined or null
                    },
                ],
                // No Account or Opportunity expected
            });

            expect(mockedPost).toHaveBeenCalledTimes(1); // Token refresh
            expect(mockedGet).toHaveBeenCalledTimes(2); // SOSL Lead/Contact, domain search; no account or contact search
        });

        it('should fetch Contact, related Account, and related Opportunity info', async () => {
            const contactId = 'cont456';
            const accountId = 'acc789';

            expect(mockedGet).not.toHaveBeenCalled(); // No Salesforce calls made

            // Verify mock functions are properly configured
            expect(mockedGet).toEqual(expect.any(Function));

            // Mock SOSL for Contact
            mockedGet.mockResolvedValueOnce({
                data: {
                    searchRecords: [
                        { Id: contactId, attributes: { type: 'Contact' }, Owner: { Name: 'Contact Owner' } },
                    ],
                },
                status: 200,
                headers: {},
                config: {},
            });
            // Mock SOQL for Account related to Contact
            mockedGet.mockResolvedValueOnce({
                data: {
                    records: [
                        {
                            Id: accountId,
                            attributes: { type: 'Account' },
                            Name: 'Test Account',
                            Owner: { Name: 'Acc Owner' },
                            CSM__r: { Name: 'CSM Name' },
                        },
                    ],
                },
                status: 200,
                headers: {},
                config: {},
            });
            // Mock SOQL for Opportunity related to Account
            mockedGet.mockResolvedValueOnce({
                data: {
                    records: [
                        // Opportunity 1 (Closed, Older)
                        {
                            Id: 'oppCO',
                            attributes: { type: 'Opportunity' },
                            Amount: 1000,
                            StageName: 'Closed Lost',
                            Owner: { Name: 'Old Opp Owner' },
                            LastModifiedDate: '2023-01-15T00:00:00Z',
                            CloseDate: '2023-01-15', // Salesforce returns date string
                            AccountId: accountId,
                        },
                        // Opportunity 2 (Closed, Most Recent)
                        {
                            Id: 'oppCR',
                            attributes: { type: 'Opportunity' },
                            Amount: 5000,
                            StageName: 'Closed Won',
                            Owner: { Name: 'Recent Opp Owner' },
                            LastModifiedDate: '2023-10-25T00:00:00Z',
                            CloseDate: '2023-10-25',
                            AccountId: accountId,
                        },
                        // Opportunity 3 (Working)
                        {
                            Id: 'oppW1',
                            attributes: { type: 'Opportunity' },
                            Amount: 7500,
                            StageName: 'Prospecting',
                            Owner: { Name: 'Work Opp Owner 1' },
                            LastModifiedDate: '2023-10-01T00:00:00Z',
                            CloseDate: '2023-11-30',
                            AccountId: accountId,
                        },
                        // Opportunity 4 (Working)
                        {
                            Id: 'oppW2',
                            attributes: { type: 'Opportunity' },
                            Amount: 12000,
                            StageName: 'Negotiation/Review',
                            Owner: { Name: 'Work Opp Owner 2' },
                            LastModifiedDate: '2023-10-10T00:00:00Z',
                            CloseDate: '2023-12-15',
                            AccountId: accountId,
                        },
                        // Opportunity 5 (Working, no explicit CloseDate from SF)
                        {
                            Id: 'oppW3_noCD',
                            attributes: { type: 'Opportunity' },
                            Amount: 3000,
                            StageName: 'Qualification',
                            Owner: { Name: 'Work Opp Owner 3' },
                            LastModifiedDate: '2023-09-01T00:00:00Z',
                            CloseDate: null, // Explicitly null from SF
                            AccountId: accountId,
                        },
                    ],
                },
                status: 200,
                headers: {},
                config: {},
            });

            const res = await request(app)
                .get('/v1/frontUser/salesforceInfo')
                .set('Authorization', authToken)
                .query({ email: testEmail });

            expect(res.status).toEqual(200);
            expect(res.body).toEqual({
                Contact: [{ id: contactId, type: 'Contact', owner_name: 'Contact Owner' }],
                Account: [{ id: accountId, type: 'Account', owner_name: 'Acc Owner', csm_name: 'CSM Name' }],
                Opportunity: [
                    // Working Opportunities
                    {
                        id: 'oppW1',
                        type: 'Opportunity',
                        amount: 7500,
                        stage: 'Prospecting',
                        owner_name: 'Work Opp Owner 1',
                        last_updated: '2023-10-01T00:00:00Z',
                        close_date: '2023-11-30',
                    },
                    {
                        id: 'oppW2',
                        type: 'Opportunity',
                        amount: 12000,
                        stage: 'Negotiation/Review',
                        owner_name: 'Work Opp Owner 2',
                        last_updated: '2023-10-10T00:00:00Z',
                        close_date: '2023-12-15',
                    },
                    {
                        id: 'oppW3_noCD',
                        type: 'Opportunity',
                        amount: 3000,
                        stage: 'Qualification',
                        owner_name: 'Work Opp Owner 3',
                        last_updated: '2023-09-01T00:00:00Z',
                        // close_date should be undefined here as flattenSalesforceRecord won't set it if CloseDate is null
                    },
                    // Most Recently Closed
                    {
                        id: 'oppCR',
                        type: 'Opportunity',
                        amount: 5000,
                        stage: 'Closed Won',
                        owner_name: 'Recent Opp Owner',
                        last_updated: '2023-10-25T00:00:00Z',
                        close_date: '2023-10-25',
                    },
                    {
                        id: 'oppCO',
                        type: 'Opportunity',
                        amount: 1000,
                        stage: 'Closed Lost',
                        owner_name: 'Old Opp Owner',
                        last_updated: '2023-01-15T00:00:00Z',
                        close_date: '2023-01-15', // Salesforce returns date string
                    },
                ],
            });
            expect(mockedGet).toHaveBeenCalledTimes(3); // SOSL, SOQL Account, SOQL Opp
        });

        it('should handle Salesforce API errors gracefully', async () => {
            // Mock SOSL call failing
            mockedGet.mockRejectedValueOnce({
                isAxiosError: true,
                response: { status: 500, data: [{ message: 'SF Error', errorCode: 'QUERY_TIMEOUT' }] },
            });

            const res = await request(app)
                .get('/v1/frontUser/salesforceInfo')
                .set('Authorization', authToken)
                .query({ email: testEmail });

            expect(res.body).toEqual({ err: 'SF Error' }); // Error message from SF
            expect(res.status).toEqual(500);
        });
    });

    describe('POST /v1/frontUser/createSalesforceLead', () => {
        const leadData = {
            name: 'Test User',
            email: '<EMAIL>',
            company: 'Test Company Inc.',
            source: 'TestSource',
            description: 'Test lead description',
            segment: 'SMB', // Valid segment
            userid: 'user-123',
            teamid: 'team-456',
        };
        const leadCreateUrl = expect.stringContaining('/services/data/v50.0/sobjects/Lead/');

        it('should return 400 if required fields are missing', async () => {
            const incompleteData = { ...leadData, email: undefined as unknown as string }; // Missing email
            const res = await request(app)
                .post('/v1/frontUser/createSalesforceLead')
                .set('Authorization', authToken)
                .send(incompleteData);

            expect(res.status).toEqual(400);
            expect(res.body).toEqual({ err: 'Missing required fields' });
            expect(mockedPost).not.toHaveBeenCalledWith(leadCreateUrl, expect.any(Object), expect.any(Object));
        });

        it('should successfully create a lead and return the Salesforce ID', async () => {
            const salesforceId = 'sfLeadId123';
            // Mock the lead creation POST call (after token refresh)
            mockedPost.mockResolvedValueOnce({
                data: { id: salesforceId, success: true, errors: [] },
                status: 201, // Created
                statusText: 'Created',
                headers: {},
                config: {},
            });

            const res = await request(app)
                .post('/v1/frontUser/createSalesforceLead')
                .set('Authorization', authToken)
                .send(leadData);

            expect(res.status).toEqual(200); // Handler sends 200 on success
            expect(res.body).toEqual({ id: salesforceId });

            expect(mockedPost).toHaveBeenCalledTimes(2); // 1 for token refresh, 1 for lead create
            expect(mockedPost).toHaveBeenCalledWith(
                leadCreateUrl,
                expect.objectContaining({
                    FirstName: 'Test',
                    LastName: 'User',
                    Email: leadData.email,
                    Company: leadData.company,
                    Lead_Stage__c: 'Open (MQL/PQL)',
                    Lead_Trigger__c: leadData.source,
                    Segment__c: leadData.segment,
                    cuuserid__c: leadData.userid,
                    cuworkspaceid__c: leadData.teamid,
                }),
                expect.any(Object) // Headers
            );
        });

        it('should default segment to SMB if provided segment is invalid', async () => {
            const salesforceId = 'sfLeadId456';
            const invalidSegmentData = { ...leadData, segment: 'InvalidSegment' };
            mockedPost.mockResolvedValueOnce({
                // Mock lead creation
                data: { id: salesforceId, success: true, errors: [] },
                status: 201,
                statusText: 'Created',
                headers: {},
                config: {},
            });

            const res = await request(app)
                .post('/v1/frontUser/createSalesforceLead')
                .set('Authorization', authToken)
                .send(invalidSegmentData);

            expect(res.status).toEqual(200);
            expect(res.body).toEqual({ id: salesforceId });

            expect(mockedPost).toHaveBeenCalledTimes(2);
            // Verify that the segment sent to Salesforce was defaulted to SMB
            expect(mockedPost).toHaveBeenCalledWith(
                leadCreateUrl,
                expect.objectContaining({ Segment__c: 'SMB' }), // Defaulted segment
                expect.any(Object)
            );
        });

        it('should handle Salesforce API errors during lead creation', async () => {
            // Mock the lead creation POST call failing
            mockedPost.mockRejectedValueOnce({
                isAxiosError: true,
                response: {
                    status: 400,
                    data: [{ message: 'Missing required field: Company', errorCode: 'REQUIRED_FIELD_MISSING' }],
                },
            });

            const res = await request(app)
                .post('/v1/frontUser/createSalesforceLead')
                .set('Authorization', authToken)
                .send(leadData);

            expect(res.status).toEqual(500);
            expect(res.body).toEqual({ err: 'Missing required field: Company' });
            expect(mockedPost).toHaveBeenCalledTimes(2); // token refresh, failed create attempt
        });
    });
});
