{"name": "lambda-aurora-user-rotation", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "sourceRoot": "apps/lambda-aurora-user-rotation/aurora_user_rotation", "release": {"version": {"generator": "@nxlv/python:release-version", "useLegacyVersioning": true}}, "tags": [], "targets": {"lock": {"executor": "@nxlv/python:run-commands", "options": {"command": "uv lock", "cwd": "apps/lambda-aurora-user-rotation"}}, "add": {"executor": "@nxlv/python:add", "options": {}}, "update": {"executor": "@nxlv/python:update", "options": {}}, "remove": {"executor": "@nxlv/python:remove", "options": {}}, "build": {"executor": "@nxlv/python:build", "outputs": ["{projectRoot}/dist"], "options": {"outputPath": "apps/lambda-aurora-user-rotation/dist", "publish": false, "lockedVersions": true, "bundleLocalDependencies": true}}, "lint": {"executor": "@nxlv/python:ruff-check", "outputs": [], "options": {"lintFilePatterns": ["aurora_user_rotation", "tests"]}}, "test": {"executor": "@nxlv/python:run-commands", "outputs": ["{workspaceRoot}/reports/apps/lambda-aurora-user-rotation/unittests", "{workspaceRoot}/coverage/apps/lambda-aurora-user-rotation"], "options": {"commands": [{"command": "uv run pytest tests/", "forwardAllArgs": false}], "cwd": "apps/lambda-aurora-user-rotation"}}, "container": {"executor": "@nx-tools/nx-container:build", "dependsOn": ["build", "test"], "defaultConfiguration": "development", "options": {"engine": "docker", "platforms": ["linux/arm64"], "cwd": "apps/lambda-aurora-user-rotation"}, "configurations": {"development": {"metadata": {"images": ["158160757288.dkr.ecr.us-east-1.amazonaws.com/lambda-aurora-user-rotation"], "tags": ["type=raw,value=local-dev"]}}, "release": {"metadata": {"images": ["158160757288.dkr.ecr.us-east-1.amazonaws.com/lambda-aurora-user-rotation"], "tags": ["type=raw,value=${DOCKER_TAG}"]}}}}}}