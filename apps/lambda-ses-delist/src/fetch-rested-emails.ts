import { SuppressedDestinationSummary } from '@aws-sdk/client-sesv2';
import { subDays } from 'date-fns';

import { sesClient } from './clients';
import { fetchSuppressedDestinations } from './fetch-suppressed-emails';
import { sleep as defaultSleep } from './sleep';

/**
 * Default fuzzer that multiplies the step size by a random factor between 0.8 and 1.2
 */
export function defaultFuzzer(stepSize: number): number {
    const fuzz = 0.8 + Math.random() * 0.4;
    return Math.max(1, Math.floor(stepSize * 2 * fuzz));
}

/**
 * Generate a rested function which tests against a supplied date.
 */
export function defaultDateCheck(noYoungerThan: Date): (email: SuppressedDestinationSummary) => boolean {
    return (email: SuppressedDestinationSummary) => email.LastUpdateTime > noYoungerThan;
}

/**
 * Fetches a batch of emails that have been in suppression long enough to be considered for delisting
 */
export async function fetchRestedEmails(
    batchSize: number,
    endDate: Date,
    windowSizeDays: number,
    maxRetries: number,
    initialDelay: number,
    now: Date,
    isEmailTooFresh: (email: SuppressedDestinationSummary) => boolean,
    fuzzer: (stepSize: number) => number = defaultFuzzer,
    sleep: (delay: number) => Promise<void> = defaultSleep
): Promise<SuppressedDestinationSummary[]> {
    const cutoffDate = subDays(now, 365 * 2); // Stop after looking back twice the recovery period

    let stepSize = windowSizeDays; // Start with the configured window size
    let currentEndDate = endDate;
    do {
        const currentStartDate = subDays(currentEndDate, stepSize);
        const suppressedEmails = await fetchSuppressedDestinations(
            sesClient,
            batchSize,
            currentStartDate,
            currentEndDate,
            maxRetries,
            initialDelay,
            sleep
        );
        // Return emails if we have some and none are too fresh
        if (suppressedEmails.length > 0 && !suppressedEmails.some(isEmailTooFresh)) {
            return suppressedEmails;
        }
        stepSize = fuzzer(stepSize * 2);
        currentEndDate = subDays(currentEndDate, stepSize);
    } while (currentEndDate >= cutoffDate);
    return []; // Return empty array if we couldn't find any emails within our search window
}
