import { Injectable } from '@nestjs/common';

import { UnlimitedRuleLimit } from '@clickup/paywall/core';

import { PaywallTier } from '../common/interfaces/paywallTiers';
import { EntitlementName } from '../common/types/entitlement';
import { RuleResetType } from '../common/types/ruleResetType.type';

@Injectable()
export class TiersService {
    private tiers: PaywallTier[] = [
        {
            tier_id: 1,
            tier_name: 'FREE FOREVER',
            version: 1,
            tier_rules: [
                // Automations
                {
                    rule_name: EntitlementName.AutomationAction,
                    rule_limit: 100,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.AutomationTriggerCounts,
                    rule_limit: 50,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.ConditionsPerTrigger,
                    rule_limit: 1,
                    rule_reset_type: RuleResetType.Forever,
                },
                {
                    rule_name: EntitlementName.ActionsPerTrigger,
                    rule_limit: 1,
                    rule_reset_type: RuleResetType.Forever,
                },

                // Custom Fields
                {
                    rule_name: EntitlementName.CustomFieldAutomatedAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.CustomFieldManualAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },

                // Tasks
                {
                    rule_name: EntitlementName.TaskAutomatedAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.TaskManualAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
            ],
        },
        {
            tier_id: 2,
            tier_name: 'BUSINESS',
            version: 1,
            tier_rules: [
                // Automations
                {
                    rule_name: EntitlementName.AutomationAction,
                    rule_limit: 300,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.AutomationTriggerCounts,
                    rule_limit: 200,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.ConditionsPerTrigger,
                    rule_limit: 1,
                    rule_reset_type: RuleResetType.Forever,
                },
                {
                    rule_name: EntitlementName.ActionsPerTrigger,
                    rule_limit: 1,
                    rule_reset_type: RuleResetType.Forever,
                },

                // Custom Fields
                {
                    rule_name: EntitlementName.CustomFieldAutomatedAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.CustomFieldManualAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },

                // Tasks
                {
                    rule_name: EntitlementName.TaskAutomatedAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.TaskManualAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
            ],
        },
        {
            tier_id: 3,
            tier_name: 'BUSINESS PLUS',
            version: 1,
            tier_rules: [
                // Automations
                {
                    rule_name: EntitlementName.AutomationAction,
                    rule_limit: 3000,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.AutomationTriggerCounts,
                    rule_limit: 400,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.ConditionsPerTrigger,
                    rule_limit: 11,
                    rule_reset_type: RuleResetType.Forever,
                },
                {
                    rule_name: EntitlementName.ActionsPerTrigger,
                    rule_limit: 6,
                    rule_reset_type: RuleResetType.Forever,
                },

                // Custom Fields
                {
                    rule_name: EntitlementName.CustomFieldAutomatedAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.CustomFieldManualAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },

                // Tasks
                {
                    rule_name: EntitlementName.TaskAutomatedAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
                {
                    rule_name: EntitlementName.TaskManualAiUsages,
                    rule_limit: UnlimitedRuleLimit,
                    rule_reset_type: RuleResetType.Monthly,
                },
            ],
        },
    ];

    public getAllTiers(): PaywallTier[] {
        return this.tiers;
    }

    public getOneTierBy(tierId: number): PaywallTier {
        return this.tiers.find(oo => oo.tier_id === Number(tierId));
    }
}
