import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Transform, Type } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';

export class RollupOptionsDto {
    @ApiPropertyOptional({
        name: 'rollup[calculate]',
        description: 'Calculate rollup values of relationship section fields',
    })
    @Expose()
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === 'true')
    calculate?: boolean;

    @ApiPropertyOptional({
        name: 'rollup[splitDependencies]',
        description: 'Split dependencies of relationship rollup fields',
    })
    @Expose()
    @IsOptional()
    @IsBoolean()
    @Transform(({ value }) => value === 'true')
    splitDependencies?: boolean;
}
