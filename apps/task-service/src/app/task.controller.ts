import type { Mapper } from '@automapper/core';
import { createMap, forMember, fromValue, mapFrom } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    NotImplementedException,
    Param,
    ParseIntPipe,
    Patch,
    Post,
    Put,
    Req,
    UseGuards,
    UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOkResponse, ApiOperation, ApiResponse, ApiTags, getSchemaPath } from '@nestjs/swagger';
import { VersionedObjectData } from '@time-loop/ovm-object-version';

import { SdChangeDetectorService } from '@clickup/sd/change-detector';
import {
    CopyTasksGuard,
    CreateTaskGuard,
    DeleteTaskGuard,
    EditTasksGuard,
    GetTaskGuard,
    GetTaskOrTaskAsObjectGuard,
    GetTasksGuard,
    UpdateTaskGuard,
} from '@clickup/task/authorization';
import { getDBConnCount, TaskService } from '@clickup/task/core';
import { TaskModel } from '@clickup/task/domain-model';
import {
    CopyTasksDto,
    CreateTaskDto,
    EditTasksDto,
    GetOrderindexesDto,
    GetTasksDto,
    UpdateTaskDto,
} from '@clickup/task/dto';
import { TaskErrorCodes } from '@clickup/task/error';
import { TaskOrderindexOptions, TaskOrderindexSchema, TaskOrderindexService } from '@clickup/task/orderindex';
import { TaskUserService } from '@clickup/task/user';
import { WorkspaceUserOverride } from '@clickup/utils/authorization';
import { CustomFieldsAccessCheck } from '@clickup/utils/authorization/interceptors';
import { WorkspaceUserType } from '@clickup/utils/authorization/models';
import { BulkApi } from '@clickup/utils/bulk';
import { EntityType } from '@clickup/utils/constants';
import { DBClient } from '@clickup/utils/db';
import { ResourceException, ResourceType } from '@clickup/utils/exceptions';
import { FeatureFlagService } from '@clickup/utils/feature-flag';
import type { ClickUpRequest } from '@clickup/utils/http-types';
import type { SimpleClient } from '@clickup-legacy/utils/interfaces/TransactionClient';

import {
    CoreBulkCustomFieldAccessCheckOptions,
    CoreGetTaskCustomFieldAccessCheckOptions,
    CoreListTasksCustomFieldAccessCheckOptions,
} from './access/custom-field-access.interceptor-options';
import { GetBulkTasksDto } from './dtos/get-bulk-tasks.dto';
import { PrefetchBulkTasksDto } from './dtos/prefetch-bulk-tasks.dto';
import { TaskBulkRequestDto } from './dtos/task-bulk-request.dto';
import { TaskAsObjectBulkResponseInterceptor } from './interceptors/task-as-object-bulk-response-interceptor';

@ApiBearerAuth()
@Controller('/core/:workspaceId/tasks')
@ApiTags('Tasks')
export class TaskController {
    constructor(
        @InjectMapper() private readonly mapper: Mapper,
        private readonly dbClient: DBClient,
        private readonly taskOrderindexService: TaskOrderindexService,
        private readonly taskService: TaskService,
        private readonly taskUserService: TaskUserService,
        private readonly sdChangeDetectorService: SdChangeDetectorService,
        private readonly featureFlagService: FeatureFlagService
    ) {
        createMap(
            this.mapper,
            GetOrderindexesDto,
            TaskOrderindexOptions,
            forMember(
                (destination: TaskOrderindexOptions) => destination.workplaceId,
                fromValue(0) // Set this manually
            ),
            forMember(
                (destination: TaskOrderindexOptions) => destination.type,
                mapFrom(src => src.type)
            )
        );
    }

    @UseGuards(GetTaskOrTaskAsObjectGuard)
    @Get('/:taskId')
    @ApiResponse({
        schema: { $ref: getSchemaPath(TaskModel) },
    })
    @ApiOkResponse({ type: TaskModel })
    @UseInterceptors(CustomFieldsAccessCheck(CoreGetTaskCustomFieldAccessCheckOptions))
    @WorkspaceUserOverride(WorkspaceUserType.WorkspaceUserOrClickbotRequired)
    async getTaskCore(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Param('taskId') taskId: string
    ): Promise<TaskModel> {
        return this.dbClient.readAsyncFunction(
            async (simpleClient: SimpleClient) => {
                const task = await this.taskService.getTask(taskId, workspaceId || undefined, simpleClient);

                if (task !== undefined && task.states.is_deleted && task.merged_to) {
                    throw new ResourceException(
                        ResourceType.TASK,
                        {
                            message: 'Task not found, deleted',
                            ECODE: TaskErrorCodes.TaskMerged,
                            meta: { merged_task_id: task.merged_to },
                        },
                        HttpStatus.NOT_FOUND
                    );
                }

                return task;
            },
            { useTransaction: false, connectionCount: getDBConnCount(this.featureFlagService, 'getTask') }
        );
    }

    /**
     * Making it a POST because no server parses the body of a GET request.
     * Changing the HttpCode to 200 for this specific POST.
     * cf:
     * > the response status code is always 200 by default, except for POST requests which are 201
     * https://docs.nestjs.com/controllers#status-code
     */
    @Post('/bulk')
    @HttpCode(200)
    @ApiOkResponse({ status: 200, type: GetBulkTasksDto })
    @BulkApi({ key: 'tasks', entityType: EntityType.TASK })
    @UseInterceptors(
        TaskAsObjectBulkResponseInterceptor,
        CustomFieldsAccessCheck(CoreBulkCustomFieldAccessCheckOptions)
    )
    @WorkspaceUserOverride(WorkspaceUserType.WorkspaceUserOrClickbotRequired)
    async getBulkTasks(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Body() bulkRequestDto: TaskBulkRequestDto
    ): Promise<VersionedObjectData<TaskModel>[]> {
        return this.dbClient.readAsyncFunction(
            (simpleClient: SimpleClient) =>
                this.taskService.getBulkTasks(bulkRequestDto, workspaceId || bulkRequestDto.workspaceId, simpleClient),
            { useTransaction: false, connectionCount: getDBConnCount(this.featureFlagService, 'getBulkTasks') }
        );
    }

    /**
     * Load tasks in bulk from the bdr master into the cache
     * @param workspaceId
     * @param bulkRequestDto
     * @returns { prefetched: string[]}
     */
    @Post('/bulkPrefetch')
    @HttpCode(200)
    @ApiOkResponse({ status: 200, type: PrefetchBulkTasksDto })
    @WorkspaceUserOverride(WorkspaceUserType.ClickbotRequired)
    async prefetchBulkTasks(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Body() bulkRequestDto: TaskBulkRequestDto
    ): Promise<PrefetchBulkTasksDto> {
        const results = await this.dbClient.readAsyncFunction(
            (simpleClient: SimpleClient) =>
                this.taskService.getBulkTasksForBulkPrefetch(
                    bulkRequestDto,
                    workspaceId || bulkRequestDto.workspaceId,
                    simpleClient
                ),
            {
                useReplica: false,
                useTransaction: false,
                connectionCount: getDBConnCount(this.featureFlagService, 'prefetchBulkTasks'),
            }
        );

        return {
            prefetched: results.filter(result => !!result.value).map(result => result.version.object_id),
        };
    }

    /**
     * For backwards compatibility, until all FE calls moved to /bulk
     * @param workspaceId
     * @param getTasksDto
     * @returns
     */
    @ApiOperation({
        operationId: 'getTasksByList',
        deprecated: true,
    })
    @UseGuards(GetTasksGuard)
    @Post('/list')
    @HttpCode(200)
    @ApiOkResponse({ status: 200, type: TaskModel, isArray: true })
    @UseInterceptors(CustomFieldsAccessCheck(CoreListTasksCustomFieldAccessCheckOptions))
    async getTasks(@Param('workspaceId', ParseIntPipe) workspaceId: number, @Body() getTasksDto: GetTasksDto) {
        return this.dbClient.readAsyncFunction(
            (simpleClient: SimpleClient) =>
                this.taskService.getTasks(getTasksDto.taskIds, workspaceId || undefined, simpleClient),
            { useTransaction: false, connectionCount: getDBConnCount(this.featureFlagService, 'getTasks') }
        );
    }

    /**
     * @deprecated it doesn't work at all (500 for every request). The logic is invalid and should be removed together with libs/task/orderindex.
     */
    @UseGuards(GetTasksGuard)
    @Post('/orderindex')
    @HttpCode(200)
    @ApiOkResponse({ status: 200, type: TaskOrderindexSchema })
    async getOrderIndexes(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Body() getOrderindexesDto: GetOrderindexesDto
    ) {
        const taskOrderindexOptions = this.mapper.map<GetOrderindexesDto, TaskOrderindexOptions>(
            getOrderindexesDto,
            GetOrderindexesDto,
            TaskOrderindexOptions
        );
        taskOrderindexOptions.workplaceId = workspaceId;

        return this.dbClient.readAsyncFunction(
            (simpleClient: SimpleClient) =>
                this.taskOrderindexService.getOrderindexes(taskOrderindexOptions, simpleClient),
            { useTransaction: false }
        );
    }

    @UseGuards(CreateTaskGuard)
    @Post()
    @ApiResponse({ status: 201, description: 'The record has been successfully created.' })
    @ApiResponse({ status: 403, description: 'Forbidden.' })
    async createTask(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Body() createTaskDto: CreateTaskDto
    ): Promise<TaskModel> {
        return this.dbClient.writeAsyncFunction(async (simpleClient: SimpleClient) =>
            this.taskService.createTask(createTaskDto, simpleClient)
        );
    }

    @UseGuards(UpdateTaskGuard)
    @Patch('/:taskId')
    async updateTask(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Param('taskId') taskId: string,
        @Body() updateTaskDto: UpdateTaskDto
    ): Promise<TaskModel> {
        return this.dbClient.writeAsyncFunction(async (simpleClient: SimpleClient) =>
            this.taskService.updateTask(taskId, updateTaskDto, simpleClient)
        );
    }

    @UseGuards(DeleteTaskGuard)
    @Delete('/:taskId')
    async deleteTask(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Param('taskId') taskId: string
    ): Promise<void> {
        return this.dbClient.writeAsyncFunction(async (simpleClient: SimpleClient) =>
            this.taskService.deleteTask(taskId, simpleClient)
        );
    }

    @UseGuards(EditTasksGuard)
    @Put()
    async editTasks(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Body() editTaskDto: EditTasksDto
    ): Promise<TaskModel[]> {
        throw new NotImplementedException('edit tasks not implemented', TaskErrorCodes.NotImplemented);

        // const versionUpdates = editTaskDto.taskIds.map(taskId => ({
        //     object_id: String(taskId),
        //     object_type: ObjectType.TASK,
        //     workspace_id: workspaceId,
        //     operation: OperationType.UPDATE,
        // }));
        // return this.dbClient.writeAsyncFunction(
        //     async (simpleClient: SimpleClient) => this.taskService.editTasks(editTaskDto, simpleClient),
        //     { versionUpdates }
        // );
    }

    @UseGuards(CopyTasksGuard)
    @Post('copy')
    async copyTasks(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Body() copyTasksDto: CopyTasksDto
    ): Promise<TaskModel[]> {
        return this.dbClient.writeAsyncFunction(async (simpleClient: SimpleClient) =>
            this.taskService.copyTasks(copyTasksDto, simpleClient)
        );
    }

    /**
     * Marks the task as viewed by the current user
     * @param workspaceId
     * @param taskId
     * @param req
     */
    @UseGuards(GetTaskGuard)
    @Put('/:taskId/viewed')
    @ApiOkResponse()
    @ApiOperation({
        operationId: 'markTaskViewed',
        description: 'Marks this task as viewed by the current user',
    })
    async markTaskViewed(
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Param('taskId') taskId: string,
        @Req() req: ClickUpRequest
    ) {
        const userId: number = req.userContext?.user;
        if (
            userId &&
            taskId &&
            this.sdChangeDetectorService.shouldReportAssetViewed(req.header('Referer'), req.header('User-Agent'))
        ) {
            this.sdChangeDetectorService.reportAssetViewed(userId.toString(), taskId, 'task');
        }

        await this.dbClient.writeAsyncFunction(async (simpleClient: SimpleClient) => {
            const task = await this.taskService.getTask(taskId, workspaceId || undefined, simpleClient);

            if (task === undefined || !task.subcategory) {
                throw new ResourceException(ResourceType.TASK, { message: 'Task not found' }, HttpStatus.NOT_FOUND);
            }

            return this.taskUserService.markTaskAsViewed(userId, taskId, task.subcategory, workspaceId, simpleClient);
        });
    }
}
