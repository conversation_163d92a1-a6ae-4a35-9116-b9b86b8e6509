import { Injectable } from '@nestjs/common';

import { ConfigService } from '@clickup/utils/config';

@Injectable()
export class RedisConnectionConfigProvider {
    constructor(private readonly configService: ConfigService) {}

    async provide(): Promise<RedisConnectionConfig> {
        return this.configService.get<RedisConnectionConfig>('billing-usage-service.redis');
    }
}

export interface RedisConnectionConfig {
    host: string;
    port: number;
    useTls?: boolean;
}
