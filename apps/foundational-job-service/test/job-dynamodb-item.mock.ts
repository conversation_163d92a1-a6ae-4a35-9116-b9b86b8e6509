import { randomUUID } from 'crypto';

import { DynamoItemValueType } from '@clickup/utils/db';

import { JobDynamoDBItem } from '../src/app/jobs/domain/job.dynamodb-item';
import { JOB_ID_KEY, WORKSPACE_ID_KEY } from '../src/app/jobs/domain/job.dynamodb-key';
import { JobDomain, JobStatus, JobType } from '../src/app/jobs/domain/job.model';

export const createJobDynamoDBItemMock = (partial?: Partial<JobDynamoDBItem>): JobDynamoDBItem =>
    Object.assign(new JobDynamoDBItem(), {
        [WORKSPACE_ID_KEY]: { [DynamoItemValueType.String]: '333' },
        [JOB_ID_KEY]: { [DynamoItemValueType.String]: randomUUID() },
        details: { [DynamoItemValueType.String]: '{}' },
        domain: { [DynamoItemValueType.String]: JobDomain.Field },
        created_at: { [DynamoItemValueType.String]: new Date().toISOString() },
        created_by: { [DynamoItemValueType.String]: '62836' },
        status: { [DynamoItemValueType.String]: JobStatus.Pending },
        type: { [DynamoItemValueType.String]: JobType.Merge },
        ...partial,
    });
