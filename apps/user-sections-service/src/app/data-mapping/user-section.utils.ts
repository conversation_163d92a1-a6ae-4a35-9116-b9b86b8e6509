import { Mapper } from '@automapper/core';

import {
    ExtraDashboardDataEntity,
    ExtraDataEntity,
    ExtraPageDataEntity,
    ExtraTaskDataEntity,
    ExtraViewDataEntity,
} from '../data-access/extra-item-data/extra-item-data.entity';
import {
    ExtraDashboardDataRto,
    ExtraPageDataRto,
    ExtraTaskDataRto,
    ExtraViewDataRto,
} from '../response/user-section-item-extra-data.rto';
import { ItemObjectType } from '../user-sections.enums';

/**
 * Utility for mapping ExtraData entity to RTO
 *
 * @param args.object_type - what type of entity is this extra data for? (used to determine which mapper to use)
 * @param args.mapper - the mapper to use
 * @param args.extra_data - the extra data to map
 * @returns the mapped extra data
 */
export function mappingForExtraData(args: {
    object_type: ItemObjectType;
    mapper: Mapper;
    extra_data: ExtraDataEntity;
}) {
    const { object_type, mapper, extra_data } = args;
    switch (object_type) {
        case ItemObjectType.View:
            return mapper.map(extra_data, ExtraViewDataEntity, ExtraViewDataRto);
        case ItemObjectType.Page:
            return mapper.map(extra_data, ExtraPageDataEntity, ExtraPageDataRto);
        case ItemObjectType.Task:
            return mapper.map(extra_data, ExtraTaskDataEntity, ExtraTaskDataRto);
        case ItemObjectType.Dashboard:
            return mapper.map(extra_data, ExtraDashboardDataEntity, ExtraDashboardDataRto);
        default:
            return extra_data;
    }
}
