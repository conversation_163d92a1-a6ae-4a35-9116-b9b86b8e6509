#!/bin/bash
set -e

AWS_ACCESS_KEY_ID=$(op read "op://Engineering/clickup-app-backend-local-IAM-user-keys/AccessKeyId")
AWS_SECRET_ACCESS_KEY=$(op read "op://Engineering/clickup-app-backend-local-IAM-user-keys/SecretAccessKey")

#get container directory
script_dir=$(pwd)

# Use grep to extract the directory name
working_directory_name=$(echo "$script_dir" | grep -oE '[^/]+$')

echo "Directory name used for image name (next command adds ':1.0'): $working_directory_name"

docker run \
    -e AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID \
    -e AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY \
    -e DOCKER=true \
    -e DOCKER_LOCAL=app_only \
    -p 3346:80 \
    "$working_directory_name:1.0"
