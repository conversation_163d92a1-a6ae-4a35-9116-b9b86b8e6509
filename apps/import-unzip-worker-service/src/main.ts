/**
 * This is not a production server yet!
 * This is only a minimal backend to get started.
 */

import '@cspotcode/source-map-support/register'; // todo - switch to native --enable-source-maps once we're running >= node 18.8.0

import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';

import { configureApp } from '@clickup/bootstrap';
import { configure_settings, setAppNameAndEnv } from '@clickup/nest/bootstrap-settings';
import { NestMonolithInstance } from '@clickup/nest/monolith-app-context';
import { ConfigService } from '@clickup/utils/config';
import { ClickUpTracer } from '@clickup-legacy/utils/tracer';

// This initializes the tracer and dd logging. It needs to run immediately in the app.
const tracer = new ClickUpTracer({ app_name: 'import-unzip-worker-service', log_injection: true });

const APP_NAME = 'import-unzip-worker-service';

async function bootstrap() {
    Logger.log(`🔄 Starting ${APP_NAME}...`);

    // TODO: Remove / change log once everything is working
    Logger.log({ env: process.env, argv: process.argv });

    Logger.log('Setting app name and env...');
    setAppNameAndEnv();
    Logger.log('Finished setting app name and env');

    Logger.log('Configuring settings...');
    await configure_settings();
    Logger.log('Finished configuring settings');

    Logger.log('Loading app module...');
    const { AppModule } = await import('./app/app.module'); // <-- you need to lazy load this _after_ the legacy code
    Logger.log('Loaded app module');

    Logger.log('Creating app module...');
    const app = await NestFactory.create(AppModule, {
        bufferLogs: true,
        logger: Logger,
        // Without this, the app just hangs when there's an error. It seems unintuitive, but setting `abortOnError` to
        // `false` (`true` by default) actually causes the app to crash and surfaces the error.
        abortOnError: false,
    });
    NestMonolithInstance.appContext = app;
    Logger.log('Created app module');

    Logger.log('Configuring app...');
    const configSvc = app.get(ConfigService);
    const globalPrefix = 'import-unzip-worker/v1';

    await configureApp(app, {
        globalPrefix,
        setupRateLimiter: true,
        rateLimitPaths: [`/${globalPrefix}/*`],
        corsOptions: {
            origin: [configSvc.get('corsOrigin')],
            credentials: true,
        },
    });
    Logger.log('Finished configuring app');

    app.useGlobalPipes(new ValidationPipe());

    const port = process.env.PORT || process.env.LOCAL_PORT || 3333;

    await app.listen(port);

    Logger.log(`🚀 ${APP_NAME} is running on: http://localhost:${port}`);
}

bootstrap();
