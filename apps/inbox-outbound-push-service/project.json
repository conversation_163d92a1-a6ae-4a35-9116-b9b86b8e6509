{"name": "inbox-outbound-push-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/inbox-outbound-push-service/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@clickup/nx-plugin-esbuild:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"main": "apps/inbox-outbound-push-service/src/main.ts", "outputPath": "dist/apps/inbox-outbound-push-service", "outputFileName": "main.js", "tsConfig": "apps/inbox-outbound-push-service/tsconfig.app.json", "assets": ["apps/inbox-outbound-push-service/src/assets", "apps/inbox-outbound-push-service/src/config", {"input": "apps/inbox-outbound-push-service", "glob": "README.md", "output": "."}, {"input": "apps/inbox-outbound-push-service/src/config", "glob": "**/*", "output": "./config"}]}, "configurations": {"development": {}, "production": {}}}, "budget-check": {}, "budget-update": {}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "inbox-outbound-push-service:build", "inspect": true, "watch": true, "port": 9295}, "configurations": {"development": {"buildTarget": "inbox-outbound-push-service:build:development"}, "production": {"buildTarget": "inbox-outbound-push-service:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/inbox-outbound-push-service/jest.config.ts"}}, "@@internal@@-esbuild": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"main": "apps/inbox-outbound-push-service/src/main.ts", "outputPath": "dist/apps/inbox-outbound-push-service", "outputFileName": "main.js", "tsConfig": "apps/inbox-outbound-push-service/tsconfig.app.json", "assets": []}}}}