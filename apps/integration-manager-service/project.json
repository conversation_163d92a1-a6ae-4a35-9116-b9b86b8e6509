{"name": "integration-manager-service", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/integration-manager-service/src", "projectType": "application", "tags": [], "targets": {"migrate": {"command": "dotenv -e apps/integration-manager-service/dev.env -o -- prisma migrate dev --schema=apps/integration-manager-service/prisma/schema.prisma"}, "generate": {"command": "prisma generate --schema=apps/integration-manager-service/prisma/schema.prisma", "cache": true, "inputs": ["^production", "sharedGlobals", "{projectRoot}/prisma/**/*", {"runtime": "uname -mops"}, {"externalDependencies": ["prisma", "@prisma/client"]}], "outputs": ["{projectRoot}/db"]}, "build": {"dependsOn": ["generate"], "executor": "@clickup/nx-plugin-esbuild:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "development", "options": {"main": "apps/integration-manager-service/src/main.ts", "outputPath": "dist/apps/integration-manager-service", "outputFileName": "main.js", "tsConfig": "apps/integration-manager-service/tsconfig.app.json", "assets": ["apps/integration-manager-service/src/assets", "apps/integration-manager-service/src/config", {"input": "apps/integration-manager-service", "glob": "README.md", "output": "."}, {"input": "apps/integration-manager-service/src/config", "glob": "**/*", "output": "./config"}]}, "configurations": {"development": {}, "production": {}}}, "serve": {"dependsOn": ["generate"], "executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "integration-manager-service:build", "inspect": false, "watch": true}, "configurations": {"development": {"buildTarget": "integration-manager-service:build:development"}, "production": {"buildTarget": "integration-manager-service:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/integration-manager-service/**/*.ts"]}}, "test": {"dependsOn": ["generate"], "executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/integration-manager-service/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "generate-gateway-config": {"executor": "@clickup/app-template:generate-gateway-config", "dependsOn": ["generate-swagger-spec"]}, "generate-swagger-spec": {"executor": "@nx/jest:jest", "options": {"jestConfig": "apps/integration-manager-service/jest.config.ts", "testPathPattern": ["generate-swagger.spec.ts"]}}, "@@internal@@-esbuild": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"main": "apps/integration-manager-service/src/main.ts", "outputPath": "dist/apps/integration-manager-service", "outputFileName": "main.js", "tsConfig": "apps/integration-manager-service/tsconfig.app.json", "assets": []}}}}