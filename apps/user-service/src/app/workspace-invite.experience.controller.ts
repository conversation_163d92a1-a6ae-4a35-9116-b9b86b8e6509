import { Body, Controller, HttpCode, Inject, Param, ParseIntPipe, Post, Req, UseGuards } from '@nestjs/common';
import {
    ApiBearerAuth,
    ApiOkResponse,
    ApiOperation,
    ApiParam,
    ApiResponse,
    ApiTags,
    getSchemaPath,
} from '@nestjs/swagger';
import { ObjectType } from '@time-loop/ovm-object-version';

import { BillingReason } from '@clickup/billing/types';
import { WorkspacesApi } from '@clickup/gateway-client/api/workspaces';
import { ACL_SERVICES, AclEntry, AclKind, AclService, UnsupportedAclObjectType } from '@clickup/user/access';
import { ActingUserId } from '@clickup/utils/authentication';
import type { ClickUpRequest } from '@clickup/utils/http-types';
import { FormatLogger } from '@clickup/utils/logging';

import { WorkspaceInviteAndShareGuard } from '../authorization/workspace-invite-and-share.guard';
import { InviteWorkspaceAndShareResponse } from '../models/invite-workspace-and-share-response';
import { Invitee } from '../models/invitees';
import { InviteShareRequest } from '../models/invte-share-request';

// Local interfaces
interface ExpectedEditTeamPayload {
    members: {
        user: { id: number; email: string };
    }[];
}

interface InvitedUser {
    id: number;
    email: string;
}

@Controller('/invite/experience/:workspaceId')
@ApiTags('WorkspaceInviteExperience')
@ApiBearerAuth()
export class WorkspaceInviteExperienceController {
    constructor(
        @Inject(ACL_SERVICES) private readonly aclServices: Map<ObjectType, AclService>,
        protected readonly logger: FormatLogger,
        protected readonly workspacesApi: WorkspacesApi
    ) {}

    /**
     * API for inviting users to workspace and sharing object with them once invited.
     * (Mainly used for sharing modal)
     */
    @Post('/:objectType/:objectId')
    @HttpCode(200)
    @ApiParam({ name: 'objectType', required: true, description: 'ObjectType', enum: ObjectType })
    @ApiResponse({
        schema: { $ref: getSchemaPath(InviteWorkspaceAndShareResponse) },
    })
    @ApiOperation({
        operationId: 'inviteWorkspaceAndShare',
        description: 'Invites users to workspace then shares an object with those users',
    })
    @ApiOkResponse({ status: 200, type: InviteWorkspaceAndShareResponse })
    @UseGuards(WorkspaceInviteAndShareGuard)
    async inviteToWorkspaceAndShare(
        @Req() request: ClickUpRequest,
        @Param('workspaceId', ParseIntPipe) workspaceId: number,
        @Param('objectType') objectType: ObjectType,
        @Param('objectId') objectId: string,
        @Body() inviteShareRequest: InviteShareRequest,
        @ActingUserId() actingUserId: number
    ): Promise<InviteWorkspaceAndShareResponse> {
        const { authorization } = request.headers;
        const { invitees, permission_level } = inviteShareRequest;
        const aclService = this.getAclService(objectType);

        // Invite users to workspace via legacy edit team api
        const inviteResult = await this.legacyTeamEditServiceCall(workspaceId, invitees, authorization);

        const newlyInvitedUsers = this.getInvitedUsers(workspaceId, invitees, inviteResult);

        // Share object with newly invited workspace members
        const entries: AclEntry[] = newlyInvitedUsers.map(
            user =>
                ({
                    kind: AclKind.User,
                    id: String(user.id),
                    permissionLevel: permission_level,
                } as AclEntry)
        );

        const result = await aclService.patchAcl(
            actingUserId,
            objectId,
            workspaceId,
            { entries },
            BillingReason.AccessInviteWorkspaceAndShare
        );
        return {
            acl: result.get({
                object_id: objectId,
                object_type: objectType,
                workspace_id: workspaceId,
            }),
            invitees: newlyInvitedUsers,
        };
    }

    /**
     *  We will change this implementation to use service lib once we build that out instead of using a s2s call to a legacy api
     */
    private async legacyTeamEditServiceCall(
        workspaceId: number,
        invitees: Invitee[],
        authorizationToken: string
    ): Promise<ExpectedEditTeamPayload> {
        const response = await this.workspacesApi.updateWorkspace(
            { workspaceId, requestBody: { add: invitees } },
            { config: { headers: { Authorization: authorizationToken } } }
        );
        return response.data as ExpectedEditTeamPayload;
    }

    private getInvitedUsers(
        workspaceId: number,
        invitees: Invitee[],
        expectedPayload: ExpectedEditTeamPayload
    ): InvitedUser[] {
        const users: InvitedUser[] = [];
        for (const invitee of invitees ?? []) {
            const normalizedInviteeEmail = invitee.email.toLowerCase().trim();
            const workspaceMember = expectedPayload.members.find(
                member => member?.user?.email === normalizedInviteeEmail
            );
            if (workspaceMember != null) {
                users.push({
                    id: workspaceMember.user.id,
                    email: normalizedInviteeEmail,
                });
            } else {
                this.logger.warn({
                    msg: 'Could not find workspace member from invitees to share object',
                    invitee,
                    normalizedInviteeEmail,
                    workspaceId,
                });
            }
        }

        return users;
    }

    private getAclService(objectType: ObjectType): AclService {
        const aclService = this.aclServices.get(objectType);
        if (!aclService) {
            throw new UnsupportedAclObjectType();
        }
        return aclService;
    }
}
