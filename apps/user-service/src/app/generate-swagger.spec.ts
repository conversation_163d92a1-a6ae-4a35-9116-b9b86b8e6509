import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { writeFileSync } from 'fs';
import { mock } from 'jest-mock-extended';

import { OpenAPIConfig, setupOpenAPIDocumentation } from '@clickup/bootstrap';
import { WorkspacesApi } from '@clickup/gateway-client/api/workspaces';
import { GatewayClientFeatureFlagService } from '@clickup/gateway-client/split-treatments';
import { HealthController } from '@clickup/health/core';
import { ClickUpNestRedisClient, ClickUpNestRedisConnectionModule } from '@clickup/nest/redis';
import { UserAccessModule } from '@clickup/user/access';
import { UserApiModule } from '@clickup/user/api';
import { UserCoreModule } from '@clickup/user/core';
import { WorkspaceUserProfileModule } from '@clickup/user/workspace-user-profile';
import { UtilsAuthenticationModule } from '@clickup/utils/authentication';
import { UtilsAuthorizationModule } from '@clickup/utils/authorization';
import { ConfigService, UtilsConfigModule } from '@clickup/utils/config';
import { UtilsFeatureFlagModule } from '@clickup/utils/feature-flag';
import { GatewayClientModule } from '@clickup/utils/gateway-client';
import { UtilsLoggingModule } from '@clickup/utils/logging';
import { UtilsMetricsModule } from '@clickup/utils/metrics';
import {
    createRedisConnectionConfig,
    GuardedRateLimiterModule,
    GuardedRateLimiterRedisImpl,
    UnitsRateLimiter,
} from '@clickup/utils/units-rate-limiter';

import { ApiAutomapperProfile } from '../models/automapper';
import { AclPublicController } from './acl.public.controller';
import { allControllers } from './app.module';
import { AuthzLegacyImportsService } from './authz.legacy-imports.service';

describe('Generate User Swagger Spec', () => {
    let testApp: TestingModule;
    let app: INestApplication;

    beforeAll(async () => {
        testApp = await Test.createTestingModule({
            imports: [
                AutomapperModule.forRoot({
                    strategyInitializer: classes(),
                }),
                UtilsAuthenticationModule,
                UtilsAuthorizationModule,
                UtilsConfigModule,
                UtilsFeatureFlagModule,
                UtilsLoggingModule,
                UtilsMetricsModule,
                ClickUpNestRedisConnectionModule.forRootAsync({
                    imports: [UtilsConfigModule],
                    configFactory: (configService: ConfigService) => ({
                        connectionName: 'user-service-rate-limiter',
                        host: 'localhost',
                        port: 6379,
                        useTls: false,
                        maxRetriesPerRequest: null,
                        enableOfflineQueue: false,
                        showFriendlyErrorStack: true,
                    }),
                    inject: [ConfigService],
                }),
                GuardedRateLimiterModule.forRoot({
                    imports: [
                        UtilsConfigModule,
                        ClickUpNestRedisConnectionModule.forRootAsync({
                            imports: [UtilsConfigModule],
                            configFactory: createRedisConnectionConfig,
                            inject: [ConfigService],
                        }),
                    ],
                    useFactory: (redisClient: ClickUpNestRedisClient): UnitsRateLimiter =>
                        new GuardedRateLimiterRedisImpl(redisClient),
                    inject: [ClickUpNestRedisClient],
                }),
                UserApiModule,
                UserCoreModule,
                UserAccessModule,
                WorkspaceUserProfileModule,
                GatewayClientModule,
            ],
            controllers: [HealthController, AclPublicController, ...allControllers],
            providers: [
                {
                    provide: AuthzLegacyImportsService,
                    useFactory: AuthzLegacyImportsService.factory,
                },
                {
                    provide: 'EXPORT_PAGE_SIZE',
                    useValue: 100,
                },
                ApiAutomapperProfile,
                {
                    provide: GatewayClientFeatureFlagService,
                    useValue: mock<GatewayClientFeatureFlagService>(),
                },
                {
                    provide: WorkspacesApi,
                    useValue: mock<WorkspacesApi>(),
                },
            ],
        }).compile();

        app = testApp.createNestApplication();
        await app.init();
    });

    afterAll(async () => {
        await app?.close();
    });

    it('should generate swagger spec', async () => {
        app.setGlobalPrefix('v3-user');

        const openAPIconfig: OpenAPIConfig = {
            apiPrefix: 'v3-user',
            title: 'User Service',
            description: 'User Service API definition',
            version: '1.0',
        };
        const document = await setupOpenAPIDocumentation(app, openAPIconfig, true, false, false);
        writeFileSync(
            './tools/openapi-generator/swagger-files/user-service-swagger.json',
            JSON.stringify(document, null, 2)
        );
        expect(document).toBeDefined();
        app.setGlobalPrefix('');
    });
});
