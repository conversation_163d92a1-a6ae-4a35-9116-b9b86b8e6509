import { createMap, forM<PERSON>ber, map<PERSON>rom, Mapper } from '@automapper/core';

import { User, UserProfile, UserStatus } from '@clickup/user/core';

import { UserProfileUpsertRequest } from '../user-profile-upsert-request';
import { UserUpsertRequest } from '../user-upsert-request';

export class UserUpsertRequestMapper {
    public createApiToDomainMap(profileMapper: Mapper) {
        createMap(
            profileMapper,
            UserUpsertRequest,
            User,
            forMember(
                destination => destination.avatar,
                mapFrom(source => ({
                    color: source.color,
                    pictureUrl: source.profile_picture_key,
                }))
            ),
            forMember(
                destination => destination.email,
                mapFrom(source => ({
                    address: source.email,
                }))
            ),
            forMember(
                destination => destination.name,
                mapFrom(source => source.username)
            ),
            forMember(
                destination => destination.security,
                mapFrom(source => ({
                    twofaEnabled: source.twofa_enabled,
                    twofaTotpEnabled: source.twofa_totp_enabled,
                    twofaTextEnabled: source.twofa_text_enabled,
                }))
            ),
            forMember(
                destination => destination.timeSettings,
                mapFrom(source => ({
                    timezone: source.timezone,
                    timezoneOffset: source.timezone_offset,
                    weekStartDay: source.week_start_day,
                    dateFormat: source.date_format,
                    twentyFourHrSetting: source.twenty_four_hr_setting,
                }))
            ),
            forMember(
                destination => destination.settings,
                mapFrom(
                    source =>
                        ({
                            default_workspace_id: source.default_team,
                            // having to do this type casting
                            // because of some weird issue with automapper that believe that the source type is the destination type somehow.
                        } as { default_workspace_id?: number })
                )
            ),
            forMember(
                destination => destination.profile,
                mapFrom(source => profileMapper.map(source.user_profile, UserProfileUpsertRequest, UserProfile))
            ),
            forMember(
                destination => destination.status,
                mapFrom(source => {
                    if (source.deleted) {
                        return UserStatus.Deleted;
                    }
                    return UserStatus.Active;
                })
            ),
            forMember(
                destination => destination.bannedDate,
                mapFrom(source => Number(source.banned_date))
            ),
            forMember(
                destination => destination.dateJoined,
                mapFrom(source => Number(source.date_joined))
            ),
            forMember(
                destination => destination.emailAlias,
                mapFrom(source => source.email_alias)
            ),
            forMember(
                destination => destination.usernameAlias,
                mapFrom(source => source.username_alias)
            )
        );
    }
}
