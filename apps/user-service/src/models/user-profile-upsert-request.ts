import { AutoMap } from '@automapper/classes';
import { ApiExtraModels } from '@nestjs/swagger';

/**
 * Request for upserting user profiles.
 */
@ApiExtraModels()
export class UserProfileUpsertRequest {
    @AutoMap()
    verified_consultant: boolean;

    @AutoMap()
    top_tier_user: boolean;

    @AutoMap()
    verified_ambassador: boolean;

    @AutoMap()
    ai_expert: boolean;

    @AutoMap()
    viewed_verified_consultant: boolean;

    @AutoMap()
    viewed_top_tier_user: boolean;

    @AutoMap()
    viewed_verified_ambassador: boolean;

    @AutoMap()
    viewed_ai_expert: boolean;

    @AutoMap()
    display_profile: boolean;
}
