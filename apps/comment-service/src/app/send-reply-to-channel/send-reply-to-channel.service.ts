import { Inject, Injectable } from '@nestjs/common';
import { AxiosInstance } from 'axios';

import { CommentService } from '@clickup/comment/core';
import {
    Comment as CommentData,
    CreateCommentDto,
    getSourceCommentId,
    isShowInRootParentComment,
    sendCreateCommentToCommentService,
    UpdateCommentDto,
} from '@clickup/comment/domain';
import { ConfigService } from '@clickup/utils/config';
import { EntityType } from '@clickup/utils/constants';
import { DBClient, makeReadSimpleClient } from '@clickup/utils/db';
import { MONOLITH_HTTP_CLIENT } from '@clickup/utils/http-client';
import { MetricsService } from '@clickup/utils/metrics';
import { COMMENTS_DB_CLIENT } from '@clickup-legacy/utils/CommentsDBClient';
import { ClickUpError } from '@clickup-legacy/utils/errors';

const CommentError = ClickUpError.makeNamedError('comment');
const COMMENT_TYPE_VALIDATION_ERROR = 'COMM_084';
const SOURCE_COMMENT_VALIDATION_ERROR = 'SOU_COMM_001';

@Injectable()
export class SendReplyToChannelService {
    constructor(
        private readonly commentService: CommentService,
        @Inject(COMMENTS_DB_CLIENT) private readonly commentsDbClient: DBClient,
        @Inject(MONOLITH_HTTP_CLIENT) private readonly httpClient: AxiosInstance,
        private readonly metricsService: MetricsService
    ) {}

    /**
     * Validate if a reply to a channel should be sent
     */
    shouldSendReplyToChannel(
        comment: CreateCommentDto | UpdateCommentDto,
        entityType: number | undefined,
        entityId: string | undefined
    ): boolean {
        return entityType !== undefined && entityId !== undefined && this.validateCommentType(comment, entityType);
    }

    /**
     * Create a new stub comment.
     */
    async createStubComment(sourceCommentId: string, rootParentId: string, sourceParentId: string) {
        const res = await sendCreateCommentToCommentService(this.httpClient, {
            type: EntityType.VIEW,
            parent: rootParentId,
            comment: [
                {
                    text: '',
                    attributes: {},
                },
            ],
            comment_type_data: {
                source_comment_id: sourceCommentId,
                source_comment_parent_id: sourceParentId,
            },
        });
        this.metricsService.client.distribution('comment.stub_comment.created', 1);
        return res.data;
    }

    private validateCommentType(comment: CreateCommentDto | UpdateCommentDto, entityType: number): boolean {
        if (comment.send_reply_to_channel) {
            if (entityType !== EntityType.COMMENT) {
                throw new CommentError(
                    `send_reply_to_channel is only supported for chat channel comments`,
                    COMMENT_TYPE_VALIDATION_ERROR,
                    400
                );
            }
            return true;
        }
        return false;
    }

    async getSourceCommentRootParent(
        sourceCommentId: string,
        parentId: string,
        workspaceId: number,
        checkForDuplicate?: boolean
    ): Promise<string> {
        const queryResult = await this.commentService.getComments(
            makeReadSimpleClient(this.commentsDbClient, { useReplica: true }),
            [sourceCommentId],
            workspaceId,
            {
                entity: {
                    entityType: EntityType.COMMENT,
                    entityId: parentId,
                },
            },
            true
        );

        if (queryResult.length === 0) {
            throw new CommentError('Source comment not found', SOURCE_COMMENT_VALIDATION_ERROR, 404);
        }

        if (queryResult.length > 1) {
            throw new CommentError('Multiple source comments found', SOURCE_COMMENT_VALIDATION_ERROR, 400);
        }

        const sourceComment = queryResult[0];

        if (sourceComment?.type !== EntityType.COMMENT || !sourceComment?.root_parent_id) {
            throw new CommentError(
                'Source comment is not a reply comment and cannot be used as a source for a stub comment',
                SOURCE_COMMENT_VALIDATION_ERROR,
                400
            );
        }

        if (!sourceComment?.root_parent_id) {
            throw new CommentError(
                'Source comment has no root parent id and cannot be used as a source for a stub comment',
                SOURCE_COMMENT_VALIDATION_ERROR,
                400
            );
        }

        if (checkForDuplicate && isShowInRootParentComment(sourceComment)) {
            throw new CommentError(
                'Source comment is already in the root channel and cannot be used to create a stub comment',
                SOURCE_COMMENT_VALIDATION_ERROR,
                400
            );
        }

        if (getSourceCommentId(sourceComment)) {
            throw new CommentError(
                'Source comment is already a stub comment and cannot be used to create a stub comment',
                SOURCE_COMMENT_VALIDATION_ERROR,
                400
            );
        }

        return sourceComment.root_parent_id;
    }
}
