import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsNumber, IsString, ValidateNested } from 'class-validator';

import { CreateCommentDto, exampleCreateCommentDto } from '@clickup/comment/domain';
import { EntityType } from '@clickup/utils/constants';

export class CreateCommentsScheduledDto {
    @ApiProperty({
        type: CreateCommentDto,
    })
    @Type(() => CreateCommentDto)
    @ValidateNested()
    create_comment_input: CreateCommentDto;

    @ApiProperty()
    @IsString()
    root_entity_id: string;

    @ApiPropertyOptional({
        enum: EntityType,
        enumName: 'EntityType',
    })
    @IsEnum(EntityType)
    root_entity_type?: EntityType;

    @ApiProperty()
    @IsNumber()
    root_entity_subtype: number;

    @ApiProperty()
    @IsString()
    date_to_send_at: string;
}
export const exampleCreateCommentsScheduledDto: CreateCommentsScheduledDto = {
    create_comment_input: exampleCreateCommentDto,
    root_entity_id: '6-999900000044-8',
    root_entity_type: 8,
    root_entity_subtype: 8,
    date_to_send_at: '2021-08-30T16:00:00.000Z',
};

export class CreateCommentsScheduledResponse {
    @ApiProperty()
    @IsNumber()
    comment_scheduled_id: number;
}
