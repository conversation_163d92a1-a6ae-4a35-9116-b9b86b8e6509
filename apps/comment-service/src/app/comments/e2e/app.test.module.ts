import { classes } from '@automapper/classes';
import { AutomapperModule } from '@automapper/nestjs';
import { MiddlewareConsumer, Module, Scope } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
// eslint-disable-next-line import/no-extraneous-dependencies
import Sinon from 'sinon';

import { BadgingDataAccessModule } from '@clickup/badging/data-access';
import { ChatDeltaModule } from '@clickup/chat/delta';
import { ViewFollowersRepository } from '@clickup/chat/rooms';
import { CommentCoreModule, CommentRepository, IsParentTypeOfComment } from '@clickup/comment/core';
import { CommentDbTestingModule } from '@clickup/comment/db/testing';
import { EntitlementsModule } from '@clickup/entitlements';
import { CommentsApiModule } from '@clickup/gateway-client/api/comments';
import { ClickUpNestRedisConnectionModule } from '@clickup/nest/redis';
import { asyncLocalStorageMiddleware } from '@clickup/shared/utils-async-storage-middleware';
import { UserApiModule } from '@clickup/user/api';
import { UserCoreModule } from '@clickup/user/core';
import { AuthenticationMiddleware, UtilsAuthenticationModule } from '@clickup/utils/authentication';
import { UtilsAuthorizationModule } from '@clickup/utils/authorization';
import { ConfigService, UtilsConfigModule } from '@clickup/utils/config';
import { UtilsDbTestingModule } from '@clickup/utils/db/testing';
import { UtilsFeatureFlagModule } from '@clickup/utils/feature-flag';
import { MONOLITH_HTTP_CLIENT } from '@clickup/utils/http-client';
import { UtilsLoggingModule } from '@clickup/utils/logging';

import { SendReplyToChannelModule } from '../../send-reply-to-channel/send-reply-to-channel.module';
import { CommentExtraFieldsService } from '../comment-extra-fields.service';
import { CommentsController } from '../comments.controller';
import { CommentsService } from '../comments.service';
import { CommentsExtraFieldsRepository } from '../comments-extra-fields.repository';
import { CommentsSearchService } from '../helpers/comment-search.service';

// Global stub
const stub = Sinon.stub(axios.create());

@Module({
    imports: [
        AutomapperModule.forRoot({
            strategyInitializer: classes(),
        }),
        UserApiModule,
        UserCoreModule,
        UtilsConfigModule.forTest({
            db_minimums: {
                'task_mgmt.comments': '1000000000000',
                'task_mgmt.comment_ids': '1000000000000',
            },
            db: {
                master_prefix_chat_comments: '8999',
            },
            permission_constants: { can_read: 'can_read' },
            comments: {
                types: new Map([
                    ['approval', 18],
                    ['attachment', 16],
                    ['category', 5],
                    ['comment', 2],
                    ['doc', 14],
                    ['project', 4],
                    ['subcategory', 6],
                    ['task', 1],
                    ['view', 8],
                ]),
            },
        }),
        UtilsDbTestingModule.postgresContainer(),
        UtilsAuthenticationModule,
        UtilsAuthorizationModule,
        UtilsConfigModule,
        UtilsLoggingModule,
        UtilsFeatureFlagModule,
        ClickUpNestRedisConnectionModule.forRootAsync({
            imports: [UtilsConfigModule],
            configFactory: (config: ConfigService) => ({
                connectionName: 'chat-service',
                host: process.env.REDIS_HOST
                    ? process.env.REDIS_HOST.split(':')[0]
                    : config.get<string>('db.redis_host'),
                port: process.env.REDIS_HOST
                    ? Number(process.env.REDIS_HOST.split(':')[1])
                    : config.get<number>('db.redis_port'),
                useTls: !!process.env.REDIS_HOST || config.has('db.redis_tls'),
                maxRetriesPerRequest: null,
                enableOfflineQueue: false,
                showFriendlyErrorStack: true,
            }),
            inject: [ConfigService],
        }),
        CommentCoreModule.forTest(),
        CommentDbTestingModule.postgresContainer(),
        EntitlementsModule,
        BadgingDataAccessModule,
        SendReplyToChannelModule,
        CommentsApiModule,
        ChatDeltaModule,
    ],
    controllers: [CommentsController],
    providers: [
        CommentsService,
        CommentRepository,
        IsParentTypeOfComment,
        CommentExtraFieldsService,
        CommentsExtraFieldsRepository,
        {
            scope: Scope.REQUEST,
            provide: MONOLITH_HTTP_CLIENT,
            useFactory: (): sinon.SinonStubbedInstance<AxiosInstance> => stub,
        },
        ViewFollowersRepository,
        CommentsSearchService,
    ],
    exports: [],
})
export class TestAppModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(asyncLocalStorageMiddleware).forRoutes('*');
        consumer.apply(AuthenticationMiddleware).forRoutes('*');
    }
}
